workflows:
  android-workflow-prod:
    name: Android Workflow Prod
    max_build_duration: 120
    instance_type: linux_x2
    environment:
      java: 17
      android_signing:
        - keystore_reference
      groups:
        - google_credentials
        - vite_values_prod
      vars:
        PACKAGE_NAME: "com.aventur.mobile"
      node: v22.17
    scripts:
      - name: Install npm dependencies for Ionic project
        script: |
          cd frontend && npm install
      - name: Set Android SDK location
        script: |
          echo "sdk.dir=$ANDROID_SDK_ROOT" > "$CM_BUILD_DIR/frontend/mobileapp/android/local.properties"
      - name: Update dependencies and copy web assets to native project
        script: |
          cd frontend && npm run sync:mobileapp
      - name: Build Android release
        script: |
          LATEST_GOOGLE_PLAY_BUILD_NUMBER=$(google-play get-latest-build-number --package-name "$PACKAGE_NAME")
          if [ -z LATEST_GOOGLE_PLAY_BUILD_NUMBER ]; then
            UPDATED_BUILD_NUMBER=$BUILD_NUMBER
          else
            UPDATED_BUILD_NUMBER=$(($LATEST_GOOGLE_PLAY_BUILD_NUMBER + 1))
          fi
          cd $CM_BUILD_DIR/frontend/mobileapp/android
          ./gradlew bundleRelease \
            -PversionCode=$UPDATED_BUILD_NUMBER \
            -PversionName=1.2.$UPDATED_BUILD_NUMBER
    artifacts:
      - frontend/mobileapp/android/app/build/outputs/**/**/*.aab
      - frontend/mobileapp/android/app/build/outputs/**/**/*.apk
    publishing:
      google_play:
        credentials: $GCLOUD_SERVICE_ACCOUNT_CREDENTIALS
        track: alpha
        submit_as_draft: true

  android-workflow-dev:
    name: Android Workflow Dev
    max_build_duration: 120
    instance_type: linux_x2
    environment:
      java: 17
      android_signing:
        - keystore_reference
      groups:
        - google_credentials
        - vite_values_dev
      vars:
        PACKAGE_NAME: "com.aventur.mobile"
      node: v22.17
    scripts:
      - name: Install npm dependencies for Ionic project
        script: |
          cd frontend && npm install
      - name: Set Android SDK location
        script: |
          echo "sdk.dir=$ANDROID_SDK_ROOT" > "$CM_BUILD_DIR/frontend/mobileapp/android/local.properties"
      - name: Update dependencies and copy web assets to native project
        script: |
          cd frontend && npm run sync:mobileapp
      - name: Build Android release
        script: |
          LATEST_GOOGLE_PLAY_BUILD_NUMBER=$(google-play get-latest-build-number --package-name "$PACKAGE_NAME")
          if [ -z LATEST_GOOGLE_PLAY_BUILD_NUMBER ]; then
            UPDATED_BUILD_NUMBER=$BUILD_NUMBER
          else
            UPDATED_BUILD_NUMBER=$(($LATEST_GOOGLE_PLAY_BUILD_NUMBER + 1))
          fi
          cd $CM_BUILD_DIR/frontend/mobileapp/android
          ./gradlew bundleRelease \
            -PversionCode=$UPDATED_BUILD_NUMBER \
            -PversionName=1.2.$UPDATED_BUILD_NUMBER.dev
    artifacts:
      - frontend/mobileapp/android/app/build/outputs/**/**/*.aab
      - frontend/mobileapp/android/app/build/outputs/**/**/*.apk
    publishing:
      google_play:
        credentials: $GCLOUD_SERVICE_ACCOUNT_CREDENTIALS
        track: internal
        submit_as_draft: true

  ios-workflow-prod:
     name: iOS Workflow Prod
     max_build_duration: 120
     instance_type: mac_mini_m2
     integrations:
       app_store_connect: CodeMagic
     environment:
       ios_signing:
          provisioning_profiles:
            - AventurMobileDistributionFeb25
          certificates:
            - DistributionCertificateFeb25
       groups:
         - vite_values_prod
       vars:
         # Ionic Xcode workspace and scheme
         XCODE_WORKSPACE: "App.xcworkspace"
         XCODE_SCHEME: "App"
         APP_STORE_APP_ID: **********
       node: v22.17
       xcode: 16.2
       cocoapods: default
     scripts:
       - name: Install npm dependencies for Ionic project
         script: |
           cd frontend && npm install
       - name: Cocoapods installation
         script: |
           cd frontend/mobileapp/ios/App && pod install
       - name: Update dependencies and copy web assets to native project
         script: |
           cd frontend && npm run sync:mobileapp
       - name: Set up code signing settings on Xcode project
         script: |
           xcode-project use-profiles
       - name: Increment build number
         script: |
           cd $CM_BUILD_DIR/frontend/mobileapp/ios/App
           agvtool new-version -all 1.2.$((PROJECT_BUILD_NUMBER + 1))
       - name: Build ipa for distribution
         script: |
           cd $CM_BUILD_DIR/frontend/mobileapp/ios/App
           xcode-project build-ipa \
             --workspace "$XCODE_WORKSPACE" \
             --scheme "$XCODE_SCHEME" \
             --archive-flags="-destination 'generic/platform=iOS'"
     artifacts:
       - $CM_BUILD_DIR/frontend/mobileapp/ios/App/build/ios/ipa/*.ipa
       - /tmp/xcodebuild_logs/*.log
       - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.app
       - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.dSYM
     publishing:
       app_store_connect:
         auth: integration
         beta_groups:
           - AventurBetaTesters
         submit_to_testflight: true

  ios-workflow-dev:
     name: iOS Workflow Dev
     max_build_duration: 120
     instance_type: mac_mini_m2
     integrations:
       app_store_connect: CodeMagic
     environment:
       ios_signing:
         distribution_type: app_store
         bundle_identifier: com.aventur.mobile
       groups:
         - vite_values_dev
       vars:
         # Ionic Xcode workspace and scheme
         XCODE_WORKSPACE: "App.xcworkspace"
         XCODE_SCHEME: "App"
         APP_STORE_APP_ID: **********
       node: v22.17
       xcode: 16.2
       cocoapods: default
     scripts:
       - name: Install npm dependencies for Ionic project
         script: |
           cd frontend && npm install
       - name: Cocoapods installation
         script: |
           cd frontend/mobileapp/ios/App && pod install
       - name: Update dependencies and copy web assets to native project
         script: |
           cd frontend && npm run sync:mobileapp
       - name: Set up code signing settings on Xcode project
         script: |
           xcode-project use-profiles
       - name: Increment build number
         script: |
           cd $CM_BUILD_DIR/frontend/mobileapp/ios/App
           agvtool new-version -all 1.2.$((PROJECT_BUILD_NUMBER + 1))
       - name: Build ipa for distribution
         script: |
           cd $CM_BUILD_DIR/frontend/mobileapp/ios/App
           xcode-project build-ipa \
             --workspace "$XCODE_WORKSPACE" \
             --scheme "$XCODE_SCHEME" \
             --archive-flags="-destination 'generic/platform=iOS'"
     artifacts:
       - $CM_BUILD_DIR/frontend/mobileapp/ios/App/build/ios/ipa/*.ipa
       - /tmp/xcodebuild_logs/*.log
       - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.app
       - $HOME/Library/Developer/Xcode/DerivedData/**/Build/**/*.dSYM
     publishing:
       app_store_connect:
         auth: integration
         beta_groups:
           - AventurAlphaTesters