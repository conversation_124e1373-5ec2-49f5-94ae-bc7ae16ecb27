repos:
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v9.20.1
    hooks:
      - id: eslint
        entry: eslint -c frontend/webapp/eslint.config.mjs
        additional_dependencies:
          - eslint@9.20.1
          - eslint-plugin-prettier@5.2.3
          - eslint-plugin-tailwindcss@3.18.0
          - eslint-plugin-vue@9.32.0
        types: [file]
        types_or: [javascript, jsx, ts, tsx, vue]
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.4
    hooks:
      - id: ruff
        types_or: [python, pyi, jupyter]
      - id: ruff-format
        types_or: [python, pyi, jupyter]
