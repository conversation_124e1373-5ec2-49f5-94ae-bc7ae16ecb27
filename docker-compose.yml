services:
  nginx:
    depends_on:
      - api
      - cognito
    restart: always
    image: aventur-frontend:latest
    build:
      dockerfile: Dockerfile
      context: ./frontend
    env_file:
      - frontend/webapp/.env
    volumes:
      - ./frontend/webapp/dist/:/usr/share/nginx/html
    ports:
      - "3050:80"
    labels:
      - traefik.enable=true
      - traefik.http.routers.aventur-frontend-http.rule=PathPrefix(`/`)
      - traefik.http.routers.aventur-frontend-http.entrypoints=web

  api:
    image: aventur-backend:latest
    build:
      context: backend/
      dockerfile: Dockerfile
      args:
        - BITBUCKET_TOKEN=${BITBUCKET_TOKEN}
    command: uv run uvicorn api.main:app --host 0.0.0.0 --reload
    environment:
      DB_URL: postgresql+asyncpg://aventur:Aventur1234@db:5432/aventur_db
    env_file:
      - backend/.env
    volumes:
      - ./backend/:/code
    ports:
      - "8000:8000"
    labels:
      - traefik.enable=true
      - traefik.http.routers.aventur-backend-http.rule=PathPrefix(`/api`) || PathPrefix(`/docs`) || PathPrefix(`/redoc`)
      - traefik.http.routers.aventur-backend-http.entrypoints=web
    depends_on:
      - db
      - redis

  db:
    image: postgres:16-alpine3.20
    volumes:
      - postgres_data:/var/lib/postgresql@16/data/
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: aventur
      POSTGRES_PASSWORD: Aventur1234
      POSTGRES_DB: aventur_db
      PGDATA: /var/lib/postgresql/data/pgdata@16

  pgadmin:
    image: dpage/pgadmin4
    depends_on:
      - db
    ports:
      - "5050:5050"
    environment:
      POSTGRES_USER: aventur
      POSTGRES_PASSWORD: Aventur1234
      POSTGRES_DB: aventur_db
      PGADMIN_LISTEN_PORT: 5050
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: Password
    labels:
      - traefik.enable=true
      - traefik.http.routers.aventur-pgadmin-http.rule=Host(`pgadmin.localhost`)
      - traefik.http.services.aventur-pgadmin.loadbalancer.server.port=5050

  cognito:
    image: aventur-cognito-local:latest
    build:
      context: dev-tools/cognito-local
      dockerfile: Dockerfile
    environment:
      CODE: 123456
    ports:
      - "9229:9229"
    volumes:
      - ./dev-tools/cognito-local:/app/.cognito
    labels:
      - traefik.enable=true
      - traefik.http.routers.aventur-cognito-http.rule=Host(`cognito`)
      - traefik.http.services.aventur-cognito.entrypoints=cognito

  traefik:
    image: traefik:v2.6
    command:
      - "--log.level=ERROR"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.cognito.address=:9229"
    ports:
      - "80:80"
      - "8080:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"

  localstack:
    image: localstack/localstack
    ports:
      - "127.0.0.1:4566:4566" # LocalStack Gateway
      - "127.0.0.1:4510-4559:4510-4559" # external services port range
    environment:
      - DEBUG=${DEBUG-}
      - DOCKER_HOST=unix:///var/run/docker.sock
    env_file:
      - backend/.env
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - ./dev-tools/cors-config.json:/opt/code/localstack/cors-config.json
      - ./dev-tools/start-localstack.sh:/etc/localstack/init/ready.d/start-localstack.sh
  lambda_handler:
    build:
      context: backend/
      dockerfile: Dockerfile.lambda
      args:
        - BITBUCKET_TOKEN=${BITBUCKET_TOKEN}
    env_file:
      - backend/.env
    environment:
      LOCALSTACK_ENDPOINT_URL: http://localstack:4566
    volumes:
      - ./backend/:/var/task
    ports:
      - "9000:8080"
    depends_on:
      - localstack
      - db
  redis:
    image: redis:7.4-alpine3.20
    restart: always
    ports:
      - 6379:6379
    environment:
      - REDIS_PASSWORD=my-password
      - REDIS_PORT=6379
    volumes:
      - redis:/data
volumes:
  postgres_data:
  redis:
