options:
  size: 2x
definitions:
  services:
    docker:
      memory: 7168
  steps:
    - step: &build-frontend
        image: node:22.17
        name: Build Frontend
        script:
          - export VITE_ENV=$APP_ENV
          - export VITE_URL=$URL
          - export VITE_AWS_REGION=eu-west-2
          - export VITE_AWS_COGNITO_USER_POOL=$AWS_USER_POOL_ID
          - export VITE_AWS_COGNITO_CLIENT_ID=$AWS_CLIENT_ID
          - export VITE_SENTRY_DNS=$VITE_SENTRY_DNS
          - export VITE_MARKER_IO_PROJECT=$VITE_MARKER_IO_PROJECT
          - export VITE_AMPLITUDE_KEY=$VITE_AMPLITUDE_KEY
          - export NODE_OPTIONS=--max-old-space-size=3072
          - cd frontend && npm install --no-audit
          - npm run build --workspace=webapp
        artifacts:
          - frontend/webapp/dist/**
    - step: &build-backend
        name: Build Backend Docker image
        script:
          - cd backend
          - docker build --build-arg BITBUCKET_TOKEN=$BITBUCKET_TOKEN -t jarvis-backend -t jarvis-backend:latest -t jarvis-backend:$BITBUCKET_BUILD_NUMBER .
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              IMAGE_NAME: jarvis-backend
              TAGS: "$BITBUCKET_BUILD_NUMBER latest"
    - step: &build-lambda-handler
        name: Build Jarvis Lambda Handler Docker image
        script:
          - cd backend
          - docker build --build-arg BITBUCKET_TOKEN=$BITBUCKET_TOKEN -f Dockerfile.lambda -t jarvis-lambda -t jarvis-lambda:latest -t jarvis-lambda:$BITBUCKET_BUILD_NUMBER .
          - pipe: atlassian/aws-ecr-push-image:2.4.2
            variables:
              IMAGE_NAME: jarvis-lambda
              TAGS: "$BITBUCKET_BUILD_NUMBER latest"
    - step: &deploy
        image: nikolaik/python-nodejs:python3.12-nodejs20
        name: Deploy to infra AWS
        script:
          - apt update && apt install -y curl zip
          - npm install -g aws-cdk
          - pip install uv
          - cd $BITBUCKET_CLONE_DIR/infrastructure
          - export UV_LINK_MODE=copy
          - uv run cdk diff $BACKEND_STACK $FRONTEND_STACK
          - uv run cdk deploy $BACKEND_STACK $FRONTEND_STACK --require-approval never
    - step: &js-test
        image: node:22.17
        name: Build and test Frontend
        script:
          - export NODE_OPTIONS=--max-old-space-size=3072
          - cd frontend && npm install --no-audit
          # mobileapp (run test, vue-tsc & build)
          - npm run test --workspace=mobileapp
          - npm run build --workspace=mobileapp
          # webapp (run test, vue-tsc & build)
          - npm run test --workspace=webapp
          - npm run build --workspace=webapp
        condition:
          changesets:
            includePaths:
              - frontend/**
    - step: &js-coverage
        image: node:22.17
        name: JS Code coverage report
        script:
          - cd frontend && npm install
          - npm run coverage --workspace=webapp
          - curl -Os https://uploader.codecov.io/latest/linux/codecov
          - chmod +x codecov
          - ./codecov -t $CODECOV_TOKEN -f coverage/coverage-final.json -F frontend/webapp
        condition:
          changesets:
            includePaths:
              - frontend/**
    - step: &python-coverage
        image: atlassian/default-image:4
        name: Python Code coverage report
        script:
          - export DOCKER_BUILDKIT=0
          - docker-compose -f docker-compose.tests.yml -p aventur_tests run api uv run pytest --disable-warnings --cov=api --cov=app --cov=commands --cov=common --cov=database --cov=timestream --cov-report=xml:/coverage/coverage.xml tests
          - curl -Os https://uploader.codecov.io/latest/linux/codecov
          - chmod +x codecov
          - ./codecov -t $CODECOV_TOKEN -f backend/coverage.xml -F backend
        condition:
          changesets:
            includePaths:
              - backend/**
              - docker-compose.*.yml
        services:
          - docker
    - step: &python-test
        image: atlassian/default-image:4
        name: Build and test Backend
        script:
          - export DOCKER_BUILDKIT=0
          - docker-compose -f docker-compose.tests.yml -p aventur_tests run api uv run ruff check .
          - docker-compose -f docker-compose.tests.yml -p aventur_tests run api uv run ruff format . --check
          - docker-compose -f docker-compose.tests.yml -p aventur_tests run api uv run pytest tests --disable-warnings
        services:
          - docker
        condition:
          changesets:
            includePaths:
              - backend/**
              - bitbucket-pipelines.yml
    - step: &save-version
        name: Save Version
        image: nikolaik/python-nodejs:python3.12-nodejs20
        script:
          - version_number=${BITBUCKET_BRANCH#release/}
          - echo $version_number > VERSION
          - cd frontend/webapp
          - npm version $version_number --allow-same-version
          - cd ../..
          - cd backend
          - uvx --from=toml-cli toml set --toml-path=pyproject.toml project.version $version_number
          - cd ..
          - git add VERSION frontend/webapp/package*.json frontend/package-lock.json backend/pyproject.toml
          - git status
          - git diff-index --quiet HEAD || git commit -m "[skip ci] Updating release with latest version number."
          - git push
    - step: &tag
        name: Tag version
        image: atlassian/default-image:4
        script:
          - git remote set-url origin ${BITBUCKET_GIT_SSH_ORIGIN}
          - version=$(cat VERSION);
          - git push origin :refs/tags/$version
          - git tag -f $version ${BITBUCKET_COMMIT}
          - git push origin --tags

pipelines:
  branches:
    develop:
      - parallel:
          - step: *js-coverage
          - step: *python-coverage
      - stage:
          name: Build and deploy
          deployment: dev
          steps:
            - step: *build-frontend
            - step: *build-backend
            - step: *build-lambda-handler
            - step: *deploy
    release/*:
      - step: *js-test
      - step: *save-version
      - stage:
          name: Build and deploy
          deployment: stg
          steps:
            - step: *build-frontend
            - step: *build-backend
            - step: *build-lambda-handler
            - step: *deploy
    main:
      - step: *js-test
      - step:
          <<: *tag
          trigger: manual
      - stage:
          name: Build and deploy
          deployment: prod
          steps:
            - step: *build-frontend
            - step: *build-backend
            - step: *build-lambda-handler
            - step: *deploy

  pull-requests:
    '**':
      - parallel:
          - step: *js-test
          - step: *python-test
