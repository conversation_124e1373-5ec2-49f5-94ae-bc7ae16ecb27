FROM node:22.13.1-alpine AS builder
WORKDIR /app

RUN apk add --no-cache git yarn

# clone source & install dependencies
RUN git clone https://github.com/jagregory/cognito-local.git .
RUN yarn --frozen-lockfile

# apply patch & build app
COPY adminListGroupsForUser.patch .
RUN git apply adminListGroupsForUser.patch
RUN yarn esbuild src/bin/start.ts --outdir=lib --platform=node --target=node14 --bundle

FROM node:22.13.1-alpine
WORKDIR /app
COPY --from=builder /app/lib .

# configuration and userpool files
COPY ./config.json /app/.cognito/config.js
COPY ./db/clients.json.example /app/.cognito/db/clients.js
COPY ./db/cognito_local.json.example /app/.cognito/db/cognito_local.json

# bindings
EXPOSE 9229
ENV HOST 0.0.0.0
ENV PORT 9229
VOLUME /app/.cognito
ENTRYPOINT ["node", "/app/start.js"]
