{"Users": {"207e927d-2b87-4c64-97b9-37b4b5865d5c": {"Username": "207e927d-2b87-4c64-97b9-37b4b5865d5c", "Password": "!Abc1234", "UserStatus": "CONFIRMED", "Attributes": [{"Name": "sub", "Value": "207e927d-2b87-4c64-97b9-37b4b5865d5c"}, {"Name": "email", "Value": "<EMAIL>"}, {"Name": "email_verified", "Value": "true"}], "UserCreateDate": 1626797842977, "UserLastModifiedDate": 1626797842977, "Enabled": true, "UserMFASettingList": []}}, "Options": {"UsernameAttributes": ["email"], "Id": "cognito_local", "Arn": "arn:aws:cognito-idp:local:local:userpool/cognito_local", "Policies": {"PasswordPolicy": {"MinimumLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireNumbers": true, "RequireSymbols": true, "TemporaryPasswordValidityDays": 7}}}, "Groups": {"client": {"GroupName": "client"}, "superadmin": {"GroupName": "superadmin", "members": ["207e927d-2b87-4c64-97b9-37b4b5865d5c"]}, "adviser": {"GroupName": "adviser"}, "compliance": {"GroupName": "compliance"}, "introducer": {"GroupName": "introducer"}, "paraplanner": {"GroupName": "paraplanner"}, "relationship_manager": {"GroupName": "relationship_manager"}, "case_management": {"GroupName": "case_management"}}}