diff --git forkSrcPrefix/src/targets/adminListGroupsForUser.ts forkDstPrefix/src/targets/adminListGroupsForUser.ts
index e833e68639891ae1c9b1bddda7c748a702a87521..eb2af9e58b53ca43ceffe8ea8cc45c8befbac246 100644
--- forkSrcPrefix/src/targets/adminListGroupsForUser.ts
+++ forkDstPrefix/src/targets/adminListGroupsForUser.ts
@@ -24,7 +24,7 @@ export const AdminListGroupsForUser =
     }
 
     const groups = await userPool.listGroups(ctx);
-    const usersGroups = groups.filter((x) => x.members?.includes(req.Username));
+    const usersGroups = groups.filter((x) => x.members?.includes(user.Username));
 
     return {
       Groups: usersGroups.map(groupToResponseObject(req.UserPoolId)),
