# Style Guide

## Frontend

### Structure

- **components** - here are all `.vue` files. Example: `BaseButton.vue`
- **composables** - here are all composables (hooks in react). Example `useProducts.ts`
- **stores** - all pinia stores.
- **services** - services are like `utils` bur have libraries inside. Rule for it is using abstraction between library and code that uses it.
- **utils** - all utility functions that don't use external library.
- **modules** - here are modules. Modules are encapsulated
- **types** - place for all interfaces and types that are exported/used in more than one place.

### Modules and encapsulation

**modules** - modules are encapsulated parts of frontend. Uses `Frontend Structure` inside.

The rule for having thing inside module than in global `src` directory is that only this module use this part of code.

**Exporting** - if something from module needs to be used by <PERSON><PERSON> in mobileapp or webapp we export it inside `index.ts`
in main module directory. Example: you need from `users` module `MenuProfile.vue` component so you do:

```ts
import MenuProfile from "./components/MenuProfile.vue";

export { MenuProfile };
```

in `modules/users/index.ts`

**Example** - `UserRole` inside `types/userRole.ts` is used only in `auth` module so it can be there. If it'd be used
anywhere but inside this module this needs to be extracted into `src` directory, so it'll be in for example
`src/types/auth/userRole.ts` than in `src/modules/auth/types/userRole.ts`. This way we'll have strict rule what to
encapsulate and what to make global.

### Naming convention

- **all directories** - Always `camelCase`.
- **component files** - Always `PascalCase`. Also we follow these set of rules for naming components:
  - [Base component names](https://vuejs.org/style-guide/rules-strongly-recommended.html#base-component-names)
  - [Tightly coupled component names](https://vuejs.org/style-guide/rules-strongly-recommended.html#tightly-coupled-component-names)
  - [Order of words in component names](https://vuejs.org/style-guide/rules-strongly-recommended.html#order-of-words-in-component-names)
  - [Full-word component names ](https://vuejs.org/style-guide/rules-strongly-recommended.html#full-word-component-names)
- **type files** - Always `PascalCase`.
- **rest of files** - Always `camelCase`.
