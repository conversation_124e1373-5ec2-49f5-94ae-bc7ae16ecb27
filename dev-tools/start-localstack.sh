#!/bin/bash

# Setup bucket
awslocal s3 mb s3://$AWS_BUCKET --region $LOCALSTACK_AWS_REGION
awslocal s3api put-bucket-acl --bucket $AWS_BUCKET --acl public-read
awslocal s3api put-bucket-cors --bucket $AWS_BUCKET --cors-configuration file://cors-config.json

# Setup SQS queues
awslocal sqs create-queue --queue-name $SQS_QUEUE_NAME --attributes "FifoQueue=true" --region $LOCALSTACK_AWS_REGION
awslocal sqs create-queue --queue-name $SQS_DLQ_NAME --region $LOCALSTACK_AWS_REGION

# Verify email
awslocal ses verify-email-identity --email $JARVIS_SENDER_EMAIL_ADDRESS --region $LOCALSTACK_AWS_REGION