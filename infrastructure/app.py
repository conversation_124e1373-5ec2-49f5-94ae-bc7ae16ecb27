#!/usr/bin/env python3

import aws_cdk as cdk
from infrastructure.auth_stack import (
    AventurJarvisAuthStack,
)
from infrastructure.infrastructure_stack import (
    AventurBackendStack,
    AventurFrontendStack,
)
from infrastructure.s3_stack import AventurBucketStack
from infrastructure.waf_stack import AventurWAFStack

app = cdk.App()
env = cdk.Environment(account="************", region="eu-west-2")
env_us = cdk.Environment(account="************", region="us-east-1")

dev_waf_stack = AventurWAFStack(
    app, "AventurWAFStackDev", env=env_us, cross_region_references=True
)

stg_waf_stack = AventurWAFStack(
    app, "AventurWAFStackStg", env=env_us, cross_region_references=True
)

prod_waf_stack = AventurWAFStack(
    app, "AventurWAFStackProd", env=env_us, cross_region_references=True
)

dev_auth_stack = AventurJarvisAuthStack(
    app,
    "AventurJarvisAuthStackDev",
    env=env,
    env_name="dev",
    subdomain="jarvis-dev",
    vpc_name="VPCStackDev/VPCStackDevVPC",
)

stg_auth_stack = AventurJarvisAuthStack(
    app,
    "AventurJarvisAuthStackStg",
    env=env,
    env_name="stg",
    subdomain="jarvis-stg",
    vpc_name="VPCStackDev/VPCStackDevVPC",
)

prod_auth_stack = AventurJarvisAuthStack(
    app,
    "AventurJarvisAuthStackProd",
    env=env,
    env_name="prod",
    subdomain="jarvis",
    vpc_name="VPCStackProd/VPCStackProdVPC",
)

dev_bucket_stack = AventurBucketStack(
    app,
    "AventurBucketStackDev",
    env=env,
    env_name="dev",
)

dev_backend_stack = AventurBackendStack(
    app,
    "AventurBackendStackDev",
    env=env,
    env_name="dev",
    subdomain="jarvis-dev",
    vpc_name="VPCStackDev/VPCStackDevVPC",
    db_stack="PostgresRDSStackDev",
    db_name="AventurJarvisStackDevDB",
    auth_stack=dev_auth_stack,
    bucket_stack=dev_bucket_stack,
)

dev_frontend_stack = AventurFrontendStack(
    app,
    "AventurFrontendStackDev",
    env=env,
    subdomain="jarvis-dev",
    waf=dev_waf_stack.waf,
    cross_region_references=True,
)

stg_bucket_stack = AventurBucketStack(
    app,
    "AventurBucketStackStg",
    env=env,
    env_name="stg",
)

stg_backend_stack = AventurBackendStack(
    app,
    "AventurBackendStackStg",
    env=env,
    env_name="stg",
    subdomain="jarvis-stg",
    vpc_name="VPCStackDev/VPCStackDevVPC",
    db_stack="PostgresRDSStackDev",
    db_name="AventurJarvisStackStgDB",
    auth_stack=stg_auth_stack,
    bucket_stack=stg_bucket_stack,
)

stg_frontend_stack = AventurFrontendStack(
    app,
    "AventurFrontendStackStg",
    env=env,
    subdomain="jarvis-stg",
    waf=stg_waf_stack.waf,
    cross_region_references=True,
)

prod_bucket_stack = AventurBucketStack(
    app,
    "AventurBucketStackProd",
    env=env,
    env_name="prod",
)

prod_backend_stack = AventurBackendStack(
    app,
    "AventurBackendStackProd",
    env=env,
    env_name="prod",
    subdomain="jarvis",
    vpc_name="VPCStackProd/VPCStackProdVPC",
    db_stack="PostgresRDSStackProd",
    db_name="AventurJarvisStackProdDB",
    auth_stack=prod_auth_stack,
    bucket_stack=prod_bucket_stack,
)

prod_frontend_stack = AventurFrontendStack(
    app,
    "AventurFrontendStackProd",
    env=env,
    subdomain="jarvis",
    waf=prod_waf_stack.waf,
    cross_region_references=True,
)

app.synth()
