[project]
name = "jarvis-infrastructure"
version = "1.0.0"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
requires-python = "==3.12.*"
dependencies = [
    "aws-cdk-lib>=2.185.0",
    "aws-lambda-powertools[datamasking,parser]>=3.5.0",
    "aws-solutions-constructs.aws-cloudfront-apigateway-lambda>=2.25.0",
    "constructs>=10.4.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = [
    "infrastructure",
]

[dependency-groups]
dev = [
    "moto[all]>=5.0.28",
    "pytest>=8.3.4",
]
