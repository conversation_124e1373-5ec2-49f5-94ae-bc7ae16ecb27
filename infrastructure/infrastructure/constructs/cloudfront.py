from aws_cdk import (
    Duration,
    RemovalPolicy,
)
from aws_cdk import (
    aws_certificatemanager as acm,
)
from aws_cdk import (
    aws_cloudfront as cloud_front,
)
from aws_cdk import (
    aws_cloudfront_origins as origins,
)
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_route53 as route53,
)
from aws_cdk import (
    aws_route53_targets as route53targets,
)
from aws_cdk import (
    aws_s3 as s3,
)
from aws_cdk import (
    aws_s3_deployment as s3deploy,
)
from constructs import Construct


class AventurCloudFrontS3Construct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        root_domain=None,
        full_api_domain=None,
        full_domain=None,
        waf=None,
    ) -> None:
        super().__init__(scope, construct_id)

        zone = route53.HostedZone.from_lookup(
            self, "AventurHostedZone", domain_name=root_domain
        )

        cert_cloudfront = acm.DnsValidatedCertificate(
            self,
            "CloudfrontCertificate",
            domain_name=f"*.{root_domain}",
            hosted_zone=zone,
            region="us-east-1",
        )

        websiteBucket = s3.Bucket(
            self,
            "S3Bucket",
            bucket_name=full_domain,
            removal_policy=RemovalPolicy.DESTROY,
            auto_delete_objects=True,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            cors=[
                {
                    "allowedOrigins": ["*"],
                    "allowedMethods": [s3.HttpMethods.GET],
                    "maxAge": 3000,
                }
            ],
        )

        api_origin = origins.HttpOrigin(
            full_api_domain, custom_headers={"X-Custom-Header": "aventur12345"}
        )

        aventurWebDistribution = cloud_front.Distribution(
            self,
            "Distribution",
            certificate=cert_cloudfront,
            domain_names=[full_domain],
            default_root_object="index.html",
            default_behavior=cloud_front.BehaviorOptions(
                origin=origins.S3Origin(websiteBucket),
                allowed_methods=cloud_front.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
                viewer_protocol_policy=cloud_front.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
            ),
            additional_behaviors={
                "/api/*": cloud_front.BehaviorOptions(
                    origin=api_origin,
                    cache_policy=cloud_front.CachePolicy.CACHING_DISABLED,
                    origin_request_policy=cloud_front.OriginRequestPolicy.ALL_VIEWER_EXCEPT_HOST_HEADER,
                    allowed_methods=cloud_front.AllowedMethods.ALLOW_ALL,
                    viewer_protocol_policy=cloud_front.ViewerProtocolPolicy.HTTPS_ONLY,
                ),
                "/docs/*": cloud_front.BehaviorOptions(
                    origin=api_origin,
                    cache_policy=cloud_front.CachePolicy.CACHING_DISABLED,
                    origin_request_policy=cloud_front.OriginRequestPolicy.ALL_VIEWER_EXCEPT_HOST_HEADER,
                    allowed_methods=cloud_front.AllowedMethods.ALLOW_ALL,
                    viewer_protocol_policy=cloud_front.ViewerProtocolPolicy.HTTPS_ONLY,
                ),
            },
            error_responses=[
                cloud_front.ErrorResponse(
                    http_status=403,
                    response_http_status=200,
                    response_page_path="/index.html",
                    ttl=Duration.minutes(0),
                ),
            ],
            web_acl_id=waf.attr_arn,
        )

        s3deploy.BucketDeployment(
            self,
            "S3Deployment",
            sources=[s3deploy.Source.asset("../frontend/webapp/dist/")],
            destination_bucket=websiteBucket,
            distribution=aventurWebDistribution,
            memory_limit=512,
        )

        websiteBucket.add_to_resource_policy(
            iam.PolicyStatement(
                sid="Grant Cloudfront Origin Access Identity access to S3 bucket",
                actions=["s3:GetObject"],
                principals=[iam.ServicePrincipal("cloudfront.amazonaws.com")],
                resources=[websiteBucket.arn_for_objects("*")],
                conditions={
                    "StringEquals": {
                        "AWS:SourceArn": f"arn:aws:cloudfront::{scope.account}:distribution/{aventurWebDistribution.distribution_id}"
                    }
                },
            )
        )

        route53.ARecord(
            self,
            "AName",
            record_name=full_domain,
            zone=zone,
            target=route53.RecordTarget.from_alias(
                route53targets.CloudFrontTarget(aventurWebDistribution)
            ),
        )
