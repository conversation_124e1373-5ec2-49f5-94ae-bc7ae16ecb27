import os

from aws_cdk import (
    Duration,
)
from aws_cdk import (
    aws_ec2 as ec2,
)
from aws_cdk import (
    aws_ecr as ecr,
)
from aws_cdk import (
    aws_ecs as ecs,
)
from aws_cdk import (
    aws_ecs_patterns as ecs_patterns,
)
from aws_cdk import (
    aws_elasticloadbalancingv2 as elb,
)
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_route53 as route53,
)
from constructs import Construct


class AventurFargateConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        id: str,
        *,
        vpc=None,
        api_url=None,
        env_vars=None,
        root_domain=None,
    ) -> None:
        super().__init__(scope, id)

        zone = route53.HostedZone.from_lookup(
            self, "AventurHostedZone", domain_name=root_domain
        )

        # Create Fargate Cluster
        self.ecs_cluster = ecs.Cluster(
            self,
            "FargateCluster",
            vpc=vpc,
        )

        exec_role = iam.Role(
            self,
            "TaskExecRole",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
        )
        exec_role.add_managed_policy(
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "AmazonEC2ContainerRegistryPowerUser"
            )
        )

        repo = ecr.Repository.from_repository_name(
            self, "BackendRepository", repository_name="jarvis-backend"
        )
        build_number = os.environ.get("BITBUCKET_BUILD_NUMBER")
        # Create Fargate Service and ALB
        image = ecs_patterns.ApplicationLoadBalancedTaskImageOptions(
            container_name="api",
            image=ecs.ContainerImage.from_ecr_repository(repo, tag=build_number),
            container_port=80,
            environment=env_vars,
            execution_role=exec_role,
            command=[
                "uv",
                "run",
                "uvicorn",
                "api.main:app",
                "--host",
                "0.0.0.0",
                "--port",
                "80",
                "--workers",
                "4",
                "--limit-max-requests",
                "5000",
            ],
        )
        fargate = ecs_patterns.ApplicationLoadBalancedFargateService(
            self,
            "FargateService",
            cluster=self.ecs_cluster,
            cpu=1024,
            memory_limit_mib=2048,
            desired_count=1,
            task_image_options=image,
            protocol=elb.ApplicationProtocol.HTTPS,
            domain_zone=zone,
            domain_name=api_url,
            enable_execute_command=True,
            open_listener=False,
        )
        fargate.target_group.configure_health_check(
            path="/docs",
            timeout=Duration.seconds(30),
            interval=Duration.seconds(60),
            healthy_threshold_count=2,
            unhealthy_threshold_count=2,
        )
        fargate.target_group.set_attribute("deregistration_delay.timeout_seconds", "30")

        security_group = ec2.SecurityGroup(self, "ALBSecurityGroup", vpc=vpc)
        security_group.add_ingress_rule(
            ec2.Peer.prefix_list("pl-93a247fa"), ec2.Port.tcp(443)
        )
        fargate.load_balancer.add_security_group(security_group)
