# infrastructure/constructs/private_api_gateway.py
from aws_cdk import (
    RemovalPolicy,
)
from aws_cdk import (
    aws_apigateway as apigw,
)
from aws_cdk import (
    aws_ec2 as ec2,
)
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_lambda as _lambda,
)
from aws_cdk import aws_logs as logs
from aws_cdk import (
    aws_ssm as ssm,
)
from constructs import Construct


class PrivateApiGatewayConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        vpc: ec2.IVpc,
        vpc_endpoint_id: str,
        lambda_function: _lambda.IFunction,
        env_name: str,
    ) -> None:
        super().__init__(scope, construct_id)

        # Create the CloudWatch Logs role for API Gateway
        api_gateway_log_role = iam.Role(
            self,
            "ApiGatewayLogRole",
            assumed_by=iam.ServicePrincipal("apigateway.amazonaws.com"),
            description="Role for API Gateway to write logs to CloudWatch",
        )

        # Add permissions to write logs
        api_gateway_log_role.add_to_policy(
            iam.PolicyStatement(
                effect=iam.Effect.ALLOW,
                actions=[
                    "logs:CreateLogGroup",
                    "logs:CreateLogStream",
                    "logs:DescribeLogGroups",
                    "logs:DescribeLogStreams",
                    "logs:PutLogEvents",
                    "logs:GetLogEvents",
                    "logs:FilterLogEvents",
                ],
                resources=["*"],
            )
        )

        # Create log group for API Gateway
        log_group = logs.LogGroup(
            self,
            "ApiGatewayAccessLogs",
            retention=logs.RetentionDays.ONE_WEEK,
            removal_policy=RemovalPolicy.DESTROY,
        )

        vpc_endpoint = ec2.InterfaceVpcEndpoint.from_interface_vpc_endpoint_attributes(
            self, "ImportedVpcEndpoint", vpc_endpoint_id=vpc_endpoint_id, port=443
        )

        # Create private API
        api = apigw.LambdaRestApi(
            self,
            "PrivateApiGateway",
            handler=lambda_function,
            proxy=True,
            deploy_options=apigw.StageOptions(
                stage_name=env_name,
                logging_level=apigw.MethodLoggingLevel.INFO,
                access_log_destination=apigw.LogGroupLogDestination(log_group),
                access_log_format=apigw.AccessLogFormat.json_with_standard_fields(
                    caller=True,
                    http_method=True,
                    ip=True,
                    protocol=True,
                    request_time=True,
                    resource_path=True,
                    response_length=True,
                    status=True,
                    user=True,
                ),
            ),
            endpoint_configuration=apigw.EndpointConfiguration(
                types=[apigw.EndpointType.PRIVATE], vpc_endpoints=[vpc_endpoint]
            ),
            cloud_watch_role=True,
            policy=iam.PolicyDocument(
                statements=[
                    iam.PolicyStatement(
                        principals=[iam.AnyPrincipal()],
                        actions=["execute-api:Invoke"],
                        resources=["execute-api:/*"],
                        effect=iam.Effect.DENY,
                        conditions={"StringNotEquals": {"aws:SourceVpc": vpc.vpc_id}},
                    ),
                    iam.PolicyStatement(
                        principals=[iam.AnyPrincipal()],
                        actions=["execute-api:Invoke"],
                        resources=["execute-api:/*"],
                        effect=iam.Effect.ALLOW,
                        conditions={"StringEquals": {"aws:SourceVpc": vpc.vpc_id}},
                    ),
                ]
            ),
        )

        ssm.StringParameter(
            self,
            f"{construct_id}PrivateAPIURLParam",
            parameter_name=f"/aventur-jarvis/{env_name}/PRIVATE_URL_URL",
            string_value=api.url,
            tier=ssm.ParameterTier.ADVANCED,  # Necessary to share with data-processing account
        )
