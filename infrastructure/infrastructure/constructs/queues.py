from aws_cdk import Duration
from aws_cdk import (
    aws_sqs as sqs,
)
from constructs import Construct


class AventurQueuesConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        env_name=None,
    ) -> None:
        super().__init__(scope, construct_id)

        sqs_queue_name = f"aventur-jarvis-{env_name}-queue.fifo"
        sqs_dlq_name = f"aventur-jarvis-{env_name}-dlq.fifo"

        # Create the dead letter queue
        self.sqs_dlq = sqs.Queue(
            self,
            "SqsFifoDlq",
            visibility_timeout=Duration.seconds(600),
            receive_message_wait_time=Duration.seconds(20),  # SQS long polling
            retention_period=Duration.days(7),
            encryption=sqs.QueueEncryption.KMS_MANAGED,
            fifo=True,
            queue_name=sqs_dlq_name,
        )

        # Create the main SQS queue and attach the dead letter queue
        self.sqs_main_fifo_queue = sqs.Queue(
            self,
            "SqsFifoQueue",
            visibility_timeout=Duration.seconds(600),
            receive_message_wait_time=Duration.seconds(20),  # SQS long polling
            retention_period=Duration.days(7),
            encryption=sqs.QueueEncryption.KMS_MANAGED,
            fifo=True,
            queue_name=sqs_queue_name,
            dead_letter_queue=sqs.DeadLetterQueue(
                max_receive_count=1, queue=self.sqs_dlq
            ),
        )
