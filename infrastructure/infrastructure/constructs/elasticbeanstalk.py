import os
from aws_cdk import (
    Stack,
    CfnDeletionPolicy,
)
from aws_cdk import (
    aws_ec2 as ec2,
    aws_route53 as route53,
    aws_iam as iam,
    aws_elasticbeanstalk as elasticbeanstalk,
    aws_s3_assets as s3assets,
    aws_route53_targets as route53targets,
)

from constructs import Construct


class AventurElasticBeanstalkJarvisAppStack(Stack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        app_name="jarvis",
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)

        self.app_name = app_name
        eb_instance_role = iam.Role(
            self,
            f"{app_name}-aws-elasticbeanstalk-ec2-role",
            assumed_by=iam.ServicePrincipal("ec2.amazonaws.com"),
        )

        managed_policy = iam.ManagedPolicy.from_aws_managed_policy_name(
            "AWSElasticBeanstalkWebTier"
        )
        eb_instance_role.add_managed_policy(managed_policy)
        self.profile_name = f"{app_name}-InstanceProfile"

        iam.CfnInstanceProfile(
            self,
            self.profile_name,
            instance_profile_name=self.profile_name,
            roles=[eb_instance_role.role_name],
        )

        self.app = elasticbeanstalk.CfnApplication(
            self, "JarvisApplication", application_name=app_name
        )


class AventurElasticBeanstalkSecurityGroup(Construct):
    def __init__(self, scope: Construct, construct_id: str, *, vpc=None) -> None:
        super().__init__(scope, construct_id)
        public_subnets = vpc.select_subnets(subnet_type=ec2.SubnetType.PUBLIC)

        security_group = ec2.SecurityGroup(self, f"{construct_id}SG", vpc=vpc)

        security_group.add_ingress_rule(ec2.Peer.any_ipv4(), ec2.Port.tcp(80))

        security_group.add_ingress_rule(ec2.Peer.any_ipv4(), ec2.Port.tcp(443))

        security_group.add_ingress_rule(ec2.Peer.any_ipv4(), ec2.Port.tcp(8080))

        for subnet in public_subnets.subnets:
            security_group.add_ingress_rule(
                ec2.Peer.ipv4(subnet.ipv4_cidr_block), ec2.Port.tcp(22)
            )
        self.security_group = security_group


class AventurElasticBeanstalkConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        env_name="dev",
        vpc=None,
        app_name=None,
        app_stack=None,
        env_vars: dict[str, any] = None,
        zone=None,
    ) -> None:
        super().__init__(scope, construct_id)

        sg = AventurElasticBeanstalkSecurityGroup(self, "Security", vpc=vpc)

        option_settings_props = elasticbeanstalk.CfnEnvironment.option_settings = [
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:autoscaling:launchconfiguration",
                option_name="InstanceType",
                value="t3.small",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:elasticbeanstalk:environment",
                option_name="EnvironmentType",
                value="SingleInstance",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:autoscaling:launchconfiguration",
                option_name="RootVolumeType",
                value="gp2",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:autoscaling:launchconfiguration",
                option_name="RootVolumeSize",
                value="20",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:autoscaling:launchconfiguration",
                option_name="IamInstanceProfile",
                value=f"{app_name}-InstanceProfile",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:autoscaling:launchconfiguration",
                option_name="EC2KeyName",
                value="aws-jarvis-eb",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:ec2:vpc", option_name="VPCId", value=vpc.vpc_id
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:ec2:vpc",
                option_name="ELBSubnets",
                value=",".join([str(item.subnet_id) for item in vpc.public_subnets]),
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:ec2:vpc",
                option_name="Subnets",
                value=",".join([str(item.subnet_id) for item in vpc.public_subnets]),
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:ec2:vpc",
                option_name="AssociatePublicIpAddress",
                value="true",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:autoscaling:launchconfiguration",
                option_name="SecurityGroups",
                value=sg.security_group.security_group_id,
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:elasticbeanstalk:cloudwatch:logs",
                option_name="StreamLogs",
                value="true",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:elasticbeanstalk:cloudwatch:logs",
                option_name="DeleteOnTerminate",
                value="false",
            ),
            elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                namespace="aws:elasticbeanstalk:cloudwatch:logs",
                option_name="RetentionInDays",
                value="30",
            ),
            *[
                elasticbeanstalk.CfnEnvironment.OptionSettingProperty(
                    namespace="aws:elasticbeanstalk:application:environment",
                    option_name=name,
                    value=value,
                )
                for name, value in env_vars.items()
            ],
        ]

        elbAppArchive = s3assets.Asset(
            self, "AventurJarvisAppZip", path="../aventur.zip"
        )

        app_version = elasticbeanstalk.CfnApplicationVersion(
            self,
            "JarvisVersion",
            application_name=app_name,
            source_bundle=elasticbeanstalk.CfnApplicationVersion.SourceBundleProperty(
                s3_bucket=elbAppArchive.s3_bucket_name,
                s3_key=elbAppArchive.s3_object_key,
            ),
            description=os.environ.get("BITBUCKET_BUILD_NUMBER"),
        )

        app_version.cfn_options.deletion_policy = CfnDeletionPolicy.RETAIN
        app_version.cfn_options.update_replace_policy = CfnDeletionPolicy.RETAIN

        env = elasticbeanstalk.CfnEnvironment(
            self,
            "JarvisEnvironment",
            cname_prefix=f"{app_name}-{env_name}",
            environment_name=f"{app_name}-{env_name}",
            application_name=app_name,
            solution_stack_name="64bit Amazon Linux 2 v3.5.6 running Docker",
            option_settings=option_settings_props,
            version_label=app_version.ref,
        )

        env.add_depends_on(app_stack.app)

        endpoint_url = f"{app_stack.app_name}-{env_name}.eu-west-2.elasticbeanstalk.com"

        route53.ARecord(
            self,
            f"{construct_id}SiteRecord",
            record_name=env_vars["URL"],
            zone=zone,
            target=route53.RecordTarget.from_alias(
                route53targets.ElasticBeanstalkEnvironmentEndpointTarget(endpoint_url)
            ),
        )
