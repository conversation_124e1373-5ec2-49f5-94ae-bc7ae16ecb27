from aws_cdk import (
    # Duration,
    aws_ec2 as ec2,
    aws_elasticache as elasticache,
)

from constructs import Construct


class AventurElastiCacheConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        id: str,
        *,
        vpc=None,
    ) -> None:
        super().__init__(scope, id)

        redis_sec_group = ec2.SecurityGroup(
            self,
            "SecurityGroup",
            vpc=vpc,
            allow_all_outbound=True,
        )

        private_subnets_ids = [ps.subnet_id for ps in vpc.private_subnets]

        redis_subnet_group = elasticache.CfnSubnetGroup(
            scope=self,
            id="SubnetGroup",
            subnet_ids=private_subnets_ids,
            description="subnet group for redis",
        )

        redis_sec_group.add_ingress_rule(
            peer=ec2.Peer.ipv4("0.0.0.0/0"),
            description="Allow Redis connection",
            connection=ec2.Port.tcp(6379),
        )

        # Elasticache for Redis cluster
        self.redis_cluster = elasticache.CfnCacheCluster(
            scope=self,
            id="Cluster",
            cluster_name=f"{id}Cluster",
            engine="redis",
            cache_node_type="cache.t3.micro",
            num_cache_nodes=1,
            cache_subnet_group_name=redis_subnet_group.ref,
            vpc_security_group_ids=[redis_sec_group.security_group_id],
        )
