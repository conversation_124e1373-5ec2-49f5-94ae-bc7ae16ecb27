from aws_cdk import RemovalPolicy, Stack
from aws_cdk import (
    aws_cognito as cognito,
)
from aws_cdk import (
    aws_kms as kms,
)
from aws_cdk import (
    aws_ssm as ssm,
    aws_ec2 as ec2,
)
from constructs import Construct

from .lambda_functions.cognito_email_sender_lambda import (
    CognitoEmailSenderLambda,
)
from .lambda_functions.cognito_signup_lambda import (
    CognitoSignupLambda,
)


class KMSKeyConstruct(Construct):
    def __init__(
        self, scope: Construct, id_: str, *, env_name: str, kms_name: str
    ) -> None:
        super().__init__(scope, id_)

        self.key = kms.Key(
            self,
            id_,
            alias=f"aventur-{env_name}/{kms_name}",
            key_usage=kms.KeyUsage.ENCRYPT_DECRYPT,
            enable_key_rotation=True,
            removal_policy=RemovalPolicy.DESTROY
            if env_name == "dev"
            else RemovalPolicy.RETAIN,
        )


class AventurJarvisAuthStack(Stack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        env_name="dev",
        subdomain="jarvis-dev",
        vpc_name="",
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)

        vpc = ec2.Vpc.from_lookup(self, "VPC", vpc_name=vpc_name)

        custom_sender_kms_key = KMSKeyConstruct(
            self,
            f"{construct_id}CustomSenderKMSKey",
            env_name=env_name,
            kms_name="custom-sender-lambda",
        )

        user_pool = cognito.UserPool(
            self,
            f"{construct_id}UserPool",
            self_sign_up_enabled=True,
            sign_in_aliases=cognito.SignInAliases(email=True),
            auto_verify=cognito.AutoVerifiedAttrs(email=True),
            sign_in_policy=cognito.SignInPolicy(
                allowed_first_auth_factors=cognito.AllowedFirstAuthFactors(
                    password=True,  # password authentication must be enabled
                    passkey=True,
                )
            ),
            password_policy=cognito.PasswordPolicy(
                min_length=8,
                require_lowercase=True,
                require_digits=True,
                require_uppercase=True,
                require_symbols=True,
            ),
            mfa=cognito.Mfa.OPTIONAL,
            mfa_second_factor=cognito.MfaSecondFactor(sms=False, otp=True, email=True),
            email=cognito.UserPoolEmail.with_ses(
                from_email="<EMAIL>",
                from_name="Aventur",
            ),
            feature_plan=cognito.FeaturePlan.ESSENTIALS,  # Change to "PLUS" for enhanced monitoring and user activity logs
            custom_sender_kms_key=custom_sender_kms_key.key,
            account_recovery=cognito.AccountRecovery.PHONE_AND_EMAIL,  # Fine without SMS MFA, otherwise not recommended
            removal_policy=RemovalPolicy.RETAIN,
        )

        custom_sender_lambda = CognitoEmailSenderLambda(
            self,
            f"{construct_id}CustomSenderLambda",
            env_name=env_name,
            kms_key_arn=custom_sender_kms_key.key.key_arn,
        )

        user_pool.add_trigger(
            cognito.UserPoolOperation.CUSTOM_EMAIL_SENDER,
            custom_sender_lambda.fn,
        )

        signup_lambda = CognitoSignupLambda(
            self, f"{construct_id}SignupLambda", env_name=env_name, vpc=vpc
        )

        user_pool.add_trigger(
            cognito.UserPoolOperation.POST_CONFIRMATION,
            signup_lambda.fn,
        )

        cognito.CfnUserPoolGroup(
            self,
            f"{construct_id}UserPoolGroupAdviser",
            group_name="adviser",
            description="Adviser Group",
            user_pool_id=user_pool.user_pool_id,
        )
        cognito.CfnUserPoolGroup(
            self,
            f"{construct_id}UserPoolGroupSuperAdmin",
            group_name="superadmin",
            description="Superadmin Group",
            user_pool_id=user_pool.user_pool_id,
        )
        cognito.CfnUserPoolGroup(
            self,
            f"{construct_id}UserPoolGroupClient",
            group_name="client",
            description="Client",
            user_pool_id=user_pool.user_pool_id,
        )
        cognito.CfnUserPoolGroup(
            self,
            f"{construct_id}UserPoolGroupCaseManager",
            group_name="case_management",
            description="Case Management Group",
            user_pool_id=user_pool.user_pool_id,
        )
        cognito.CfnUserPoolGroup(
            self,
            f"{construct_id}UserPoolGroupCompliance",
            group_name="compliance",
            description="Compliance Group",
            user_pool_id=user_pool.user_pool_id,
        )
        cognito.CfnUserPoolGroup(
            self,
            f"{construct_id}UserPoolGroupIntroducer",
            group_name="introducer",
            description="Introducer Group",
            user_pool_id=user_pool.user_pool_id,
        )
        cognito.CfnUserPoolGroup(
            self,
            f"{construct_id}UserPoolGroupParaplanner",
            group_name="paraplanner",
            description="Paraplanner Group",
            user_pool_id=user_pool.user_pool_id,
        )
        cognito.CfnUserPoolGroup(
            self,
            f"{construct_id}UserPoolGroupChatbotUser",
            group_name="chatbot_user",
            description="Chatbot User",
            user_pool_id=user_pool.user_pool_id,
        )

        user_pool_client = cognito.UserPoolClient(
            self,
            f"{construct_id}UserPoolClient",
            user_pool=user_pool,
            supported_identity_providers=[
                cognito.UserPoolClientIdentityProvider.COGNITO
            ],
            auth_flows=cognito.AuthFlow(user_srp=True, user=True),
        )

        ssm.StringParameter(
            self,
            f"{construct_id}UserPoolIdParam",
            parameter_name=f"/aventur-jarvis/{env_name}/VITE_AWS_USER_POOL_ID",
            string_value=user_pool.user_pool_id,
        )

        ssm.StringParameter(
            self,
            f"{construct_id}UserPoolClientIdParam",
            parameter_name=f"/aventur-jarvis/{env_name}/VITE_AWS_USER_POOL_CLIENT_ID",
            string_value=user_pool_client.user_pool_client_id,
        )

        self.user_pool_id = user_pool.user_pool_id
        self.user_pool_arn = user_pool.user_pool_arn
        self.user_pool_client_id = user_pool_client.user_pool_client_id
