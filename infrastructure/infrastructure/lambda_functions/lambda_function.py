import os

from aws_cdk import Duration
from aws_cdk import (
    aws_ecr as ecr,
)
from aws_cdk import (
    aws_events as events,
)
from aws_cdk import (
    aws_events_targets as targets,
)
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_lambda as _lambda,
)
from aws_cdk import (
    aws_ssm as ssm,
)
from aws_cdk.aws_lambda_event_sources import SqsEventSource
from constructs import Construct
from infrastructure.auth_stack import AventurJarvisAuthStack
from infrastructure.constructs.queues import AventurQueuesConstruct
from infrastructure.s3_stack import AventurBucketStack


class AventurLambdaConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        auth_stack: AventurJarvisAuthStack = None,
        env_name="dev",
        vpc=None,
        queues: AventurQueuesConstruct = None,
        bucket: AventurBucketStack = None,
        db_url=None,
        redis_host=None,
    ) -> None:
        super().__init__(scope, construct_id)

        # Create role for the lambda handler
        self.lambda_role = iam.Role(
            self,
            "LambdaHanderRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                ),
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaVPCAccessExecutionRole"
                ),
            ],
        )
        self.lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "sqs:ChangeMessageVisibility",
                    "sqs:DeleteMessage",
                    "sqs:GetQueueAttributes",
                    "sqs:GetQueueUrl",
                    "sqs:ReceiveMessage",
                    "sqs:SendMessage",
                ],
                resources=[
                    queues.sqs_main_fifo_queue.queue_arn,
                    queues.sqs_dlq.queue_arn,
                ],
            )
        )
        self.lambda_role.add_to_policy(
            iam.PolicyStatement(actions=["ses:SendEmail"], resources=["*"])
        )
        self.lambda_role.add_to_policy(bucket.document_bucket_policy)
        self.lambda_role.add_to_policy(bucket.document_policy)

        self.lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "timestream:WriteRecords",
                    "timestream:Select",
                    "timestream:DescribeEndpoints",
                    "timestream:ListTables",
                    "timestream:ListDatabases",
                ],
                resources=["*"],
            )
        )

        # Create the lambda handler
        repo = ecr.Repository.from_repository_name(
            self, "LambdaRepository", repository_name="jarvis-lambda"
        )
        lambda_handler = _lambda.DockerImageFunction(
            self,
            "LambdaHandler",
            code=_lambda.DockerImageCode.from_ecr(
                repository=repo, tag_or_digest=os.environ.get("BITBUCKET_BUILD_NUMBER")
            ),
            environment={
                "AWS_CLIENT_ID": auth_stack.user_pool_client_id,
                "AWS_BUCKET": bucket.document_bucket.bucket_name,
                "AWS_USER_POOL_ID": auth_stack.user_pool_id,
                "AWS_TIMESTREAM_REGION": "eu-west-1",
                "DB_URL": db_url,
                "APP_ENV": env_name,
                "LOCAL_S3": "false",
                "LOCAL_SES": "false",
                "LOCAL_SQS": "false",
                "SQS_QUEUE_NAME": queues.sqs_main_fifo_queue.queue_name,
                "SQS_DLQ_NAME": queues.sqs_dlq.queue_name,
                "JARVIS_SENDER_EMAIL_ADDRESS": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/JARVIS_SENDER_EMAIL_ADDRESS"
                ),
                "ADVISER_GROUP_EMAIL_ADDRESS": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/ADVISER_GROUP_EMAIL_ADDRESS"
                ),
                "LAMBDA_CONTEXT": "true",
                "FUNDMENT_URL": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/FUNDMENT_URL"
                ),
                "FUNDMENT_USERNAME": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/FUNDMENT_USERNAME"
                ),
                "FUNDMENT_PASSWORD": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/FUNDMENT_PASSWORD"
                ),
                "TRANSACT_URL": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/TRANSACT_URL"
                ),
                "TRANSACT_ACCESS_CODE": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/TRANSACT_ACCESS_CODE"
                ),
                "TRANSACT_PIN": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/TRANSACT_PIN"
                ),
                "TRANSACT_PROVIDER_KEY": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/TRANSACT_PROVIDER_KEY"
                ),
                "FIDELITY_URL": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/FIDELITY_URL"
                ),
                "FIDELITY_USER_ID": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/FIDELITY_USER_ID"
                ),
                "FIDELITY_PIN": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/FIDELITY_PIN"
                ),
                "POSTMARK_BASE_URL": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/POSTMARK_BASE_URL"
                ),
                "POSTMARK_API_TOKEN": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/POSTMARK_API_TOKEN"
                ),
                "POSTMARK_FROM_ADDRESS": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/POSTMARK_FROM_ADDRESS"
                ),
                "LAMBDA_SENTRY_DSN": "https://<EMAIL>/4506281772449792",
                "TS_DATABASE_NAME": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/TS_DATABASE_NAME"
                ),
                "REDIS_HOST": redis_host,
                "DATA_PROCESSING_BASE_URL": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/DATA_PROCESSING_BASE_URL"
                ),
            },
            timeout=Duration.minutes(10),
            role=self.lambda_role,
            vpc=vpc,
            memory_size=1024,
        )
        self.lambda_function = lambda_handler

        # Connect the lambda to the sqs main queue
        event_source = SqsEventSource(queues.sqs_main_fifo_queue)
        lambda_handler.add_event_source(event_source)

        # Schedule DataCleanupCommand
        event_rule = events.Rule(
            self,
            "DataCleanupSchedule",
            schedule=events.Schedule.cron(minute="0", hour="6", week_day="2-6"),
        )

        event_rule.add_target(
            target=targets.LambdaFunction(
                lambda_handler,
                event=events.RuleTargetInput.from_object(
                    {"command_type": "DataCleanupCommand"}
                ),
            )
        )

        # Schedule pricing feed lambdas
        fundment_rule = events.Rule(
            self,
            "FundmentSchedule",
            schedule=events.Schedule.cron(minute="0", hour="9", week_day="2-6"),
        )
        fundment_rule.add_target(
            target=targets.LambdaFunction(
                lambda_handler,
                event=events.RuleTargetInput.from_object(
                    {"command_type": "FetchFundmentValuationFeed"}
                ),
            )
        )
        transact_rule = events.Rule(
            self,
            "TransactSchedule",
            schedule=events.Schedule.cron(minute="1", hour="8", week_day="2-6"),
        )
        transact_rule.add_target(
            target=targets.LambdaFunction(
                lambda_handler,
                event=events.RuleTargetInput.from_object(
                    {"command_type": "FetchTransactValuationFeed"}
                ),
            )
        )
        fidelity_rule = events.Rule(
            self,
            "FidelitySchedule",
            schedule=events.Schedule.cron(minute="2", hour="8", week_day="2-6"),
        )
        fidelity_rule.add_target(
            target=targets.LambdaFunction(
                lambda_handler,
                event=events.RuleTargetInput.from_object(
                    {"command_type": "FetchFidelityValuationFeed"}
                ),
            )
        )

        # Schedule weekend forward fill for delayed pricing feeds
        # This should run every Monday, and it must come after the pricing feed fetch
        forward_fill_rule = events.Rule(
            self,
            "ForwardFillSchedule",
            schedule=events.Schedule.cron(minute="30", hour="9", week_day="2"),
        )
        forward_fill_rule.add_target(
            target=targets.LambdaFunction(
                lambda_handler,
                event=events.RuleTargetInput.from_object(
                    {"command_type": "ScheduleForwardFillFetchedValuations"}
                ),
            )
        )
