from aws_cdk import Duration
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_lambda as _lambda,
)
from constructs import Construct


class CognitoSignupLambda(Construct):
    def __init__(self, scope: Construct, id_: str, *, env_name="dev", vpc=None) -> None:
        super().__init__(scope, id_)

        # I don't like this approach - unfortunately trying to pass the in the userpool from outside causes a circular dependency in CDK
        pool_id = {
            "dev": "eu-west-2_5Htmn4k32",
            "stg": "eu-west-2_ATE6rsRA7",
            "prod": "eu-west-2_jQZYqWYS4",
        }

        lambda_role = iam.Role(
            self,
            "LambdaHandlerRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                ),
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaVPCAccessExecutionRole"
                ),
            ],
        )

        lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "cognito-idp:AdminAddUserToGroup",
                ],
                resources=[
                    f"arn:aws:cognito-idp:eu-west-2:919426555311:userpool/{pool_id.get(env_name)}"
                ],
            )
        )
        lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "ssm:GetParameter",
                ],
                resources=[
                    f"arn:aws:ssm:eu-west-2:919426555311:parameter/aventur-jarvis/{env_name}/PRIVATE_URL_URL"
                ],
            )
        )

        self.fn = _lambda.Function(
            self,
            id_,
            runtime=_lambda.Runtime.PYTHON_3_12,  # type: ignore
            handler="lambda_handler.lambda_handler",
            code=_lambda.Code.from_asset(
                "infrastructure/lambda_functions/cognito_signup"
            ),
            layers=[
                _lambda.LayerVersion.from_layer_version_arn(
                    self,
                    "AWSLambdaPowertoolsPythonV3",
                    layer_version_arn="arn:aws:lambda:eu-west-2:017000801446:layer:AWSLambdaPowertoolsPythonV3-python312-x86_64:5",
                )
            ],
            timeout=Duration.seconds(30),
            role=lambda_role,
            vpc=vpc,
            environment={
                "APP_ENV": env_name,
            },
        )
