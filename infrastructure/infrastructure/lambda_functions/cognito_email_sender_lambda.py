from aws_cdk import Duration
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_lambda as _lambda,
)
from aws_cdk import (
    aws_ssm as ssm,
)
from constructs import Construct


class CognitoEmailSenderLambda(Construct):
    def __init__(
        self,
        scope: Construct,
        id_: str,
        *,
        env_name="dev",
        kms_key_arn: str,
    ) -> None:
        super().__init__(scope, id_)

        lambda_role = iam.Role(
            self,
            "LambdaHandlerRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                ),
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaVPCAccessExecutionRole"
                ),
            ],
        )
        lambda_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "kms:Decrypt",
                ],
                resources=[kms_key_arn],
            )
        )

        self.fn = _lambda.Function(
            self,
            id_,
            runtime=_lambda.Runtime.PYTHON_3_12,  # type: ignore
            handler="lambda_handler.lambda_handler",
            code=_lambda.Code.from_asset(
                "infrastructure/lambda_functions/cognito_custom_email_sender"
            ),
            layers=[
                _lambda.LayerVersion.from_layer_version_arn(
                    self,
                    "AWSLambdaPowertoolsPythonV3",
                    layer_version_arn="arn:aws:lambda:eu-west-2:017000801446:layer:AWSLambdaPowertoolsPythonV3-python312-x86_64:5",
                )
            ],
            timeout=Duration.seconds(30),
            role=lambda_role,
            vpc=None,
            environment={
                "APP_ENV": env_name,
                "AWS_LAMBDA_KMS_KEY": kms_key_arn,
                "POSTMARK_BASE_URL": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/POSTMARK_BASE_URL"
                ),
                "POSTMARK_API_TOKEN": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/POSTMARK_API_TOKEN"
                ),
                "POSTMARK_FROM_ADDRESS": ssm.StringParameter.value_for_string_parameter(
                    self, f"/aventur-jarvis/{env_name}/POSTMARK_FROM_ADDRESS"
                ),
            },
        )
