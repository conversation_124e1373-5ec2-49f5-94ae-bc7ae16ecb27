import os

from aws_cdk import Duration
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_ecr as ecr
from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_lambda as _lambda,
)
from constructs import Construct
from infrastructure.auth_stack import AventurJarvisAuthStack
from infrastructure.constructs.queues import AventurQueuesConstruct
from infrastructure.s3_stack import AventurBucketStack


class ApiLambdaConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        auth_stack: AventurJarvisAuthStack,
        queues: AventurQueuesConstruct,
        bucket: AventurBucketStack,
        db_url: str,
        redis_host: str,
        vpc: ec2.Vpc,
        env_name: str = "dev",
    ) -> None:
        super().__init__(scope, construct_id)

        self.role = iam.Role(
            self,
            "InternalApiLambdaRole",
            assumed_by=iam.ServicePrincipal("lambda.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                ),
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaVPCAccessExecutionRole"
                ),
            ],
        )

        lambda_sg = ec2.SecurityGroup(
            self,
            "Internal ApiLambdaSecurityGroup",
            vpc=vpc,
            description="Security group for Internal API Lambda",
            allow_all_outbound=True,
        )
        self.security_group = lambda_sg

        repo = ecr.Repository.from_repository_name(
            self, "LambdaRepository", repository_name="jarvis-lambda"
        )
        self.lambda_function = _lambda.DockerImageFunction(
            self,
            "ApiHandler",
            code=_lambda.DockerImageCode.from_ecr(
                repository=repo,
                tag_or_digest=os.environ.get("BITBUCKET_BUILD_NUMBER"),
                cmd=["internal_api.main.handler"],
            ),
            memory_size=512,
            timeout=Duration.seconds(60),
            environment={
                "AWS_CLIENT_ID": auth_stack.user_pool_client_id,
                "AWS_BUCKET": bucket.document_bucket.bucket_name,
                "AWS_USER_POOL_ID": auth_stack.user_pool_id,
                "DB_URL": db_url,
                "APP_ENV": env_name,
                "LOCAL_S3": "false",
                "LOCAL_SES": "false",
                "LOCAL_SQS": "false",
                "SQS_QUEUE_NAME": queues.sqs_main_fifo_queue.queue_name,
                "SQS_DLQ_NAME": queues.sqs_dlq.queue_name,
                "LAMBDA_CONTEXT": "true",
                "LAMBDA_SENTRY_DSN": "https://<EMAIL>/4506281772449792",
                "REDIS_HOST": redis_host,
            },
            vpc=vpc,
            vpc_subnets=ec2.SubnetSelection(
                subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
            ),
            security_groups=[lambda_sg],
            role=self.role,
        )
