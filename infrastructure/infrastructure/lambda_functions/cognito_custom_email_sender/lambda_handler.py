"""
When 'aws_lambda_powertools' used a layer, its also contains 'aws_encryption_sdk'
a dependency for 'datamasking' module, so that it's available for import in the handler's code.
"""

import base64
import json
import os
import urllib.request as request
from enum import StrEnum
from typing import Any

from aws_encryption_sdk import (
    CommitmentPolicy,
    EncryptionSDKClient,
    StrictAwsKmsMasterKeyProvider,
)
from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.parser import event_parser
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import BaseModel, ConfigDict

logger = Logger()


class CognitoCustomEmailSenderTrigger(StrEnum):
    SignUp = "CustomEmailSender_SignUp"
    ResendCode = "CustomEmailSender_ResendCode"
    ForgotPassword = "CustomEmailSender_ForgotPassword"
    Authentication = "CustomEmailSender_Authentication"


class CognitoCustomEmailSenderRequest(BaseModel):
    code: str
    userAttributes: dict[str, str]

    model_config = ConfigDict(arbitrary_types_allowed=True)


class CognitoCustomEmailSenderEvent(BaseModel):
    triggerSource: CognitoCustomEmailSenderTrigger
    request: CognitoCustomEmailSenderRequest

    model_config = ConfigDict(arbitrary_types_allowed=True)


def _request(path: str, *, data: dict) -> Any:
    api_token = os.environ.get("POSTMARK_API_TOKEN")
    base_url = os.environ.get("POSTMARK_BASE_URL")

    req = request.Request(
        f"{base_url}{path}",
        headers={
            "X-Postmark-Server-Token": api_token,
            "Accept": "application/json",
            "Content-Type": "application/json",
        },
        method="POST",
        data=bytes(json.dumps(data), encoding="utf-8"),  # type: ignore
    )

    with request.urlopen(req) as r:
        return json.loads(r.read().decode("utf-8"))


def _send_email(*, to_email: str, **template_model_args):
    from_email = os.environ.get("POSTMARK_FROM_ADDRESS")
    app_env = os.environ.get("APP_ENV")

    template_model = {
        "product_name": "Aventur",
        "home_url": "https://aventur.co.uk",
        "login_url": f"https://jarvis-{app_env}.aventur.co.uk",
        "company_name": "Aventur Wealth Ltd",
        "company_address": "No 1. London Bridge, London SE1 9BG",
        "support_email": "<EMAIL>",
        "support_phone": "020 3740 1113",
        "username": to_email,
        **template_model_args,
    }

    data = {
        "From": from_email,
        "To": to_email,
        "TemplateAlias": "password-reset",
        "TemplateModel": template_model,
    }
    logger.info(f"Request data: {data}")

    response = _request("/email/withTemplate", data=data)
    logger.info(f"Response data: {response}")

    if response["ErrorCode"] == 0:
        logger.info("Message ID = %s" % response["MessageID"])
    else:
        logger.error(f"Error {response['ErrorCode']}: {response['Message']}")


def _decrypt(key_arn: str, encrypted_data: str) -> str:
    client = EncryptionSDKClient(
        commitment_policy=CommitmentPolicy.FORBID_ENCRYPT_ALLOW_DECRYPT
    )
    aws_kms_strict_master_key_provider = StrictAwsKmsMasterKeyProvider(
        key_ids=[key_arn]
    )

    decrypted, decrypt_header = client.decrypt(
        source=base64.b64decode(encrypted_data),
        key_provider=aws_kms_strict_master_key_provider,
    )
    return decrypted.decode("utf-8")


@event_parser(model=CognitoCustomEmailSenderEvent)
@logger.inject_lambda_context
def lambda_handler(event: CognitoCustomEmailSenderEvent, context: LambdaContext):
    logger.info(f"Cognito event received: {event}")

    if not (kms_key_arn := os.environ.get("AWS_LAMBDA_KMS_KEY")):
        raise RuntimeError(
            "Missing KMS key ARN. Please set the AWS_LAMBDA_KMS_KEY environment variable."
        )

    decrypted_code = _decrypt(kms_key_arn, event.request.code)
    logger.info(f"Decrypted code: {decrypted_code}")

    if to_ := event.request.userAttributes.get("email"):
        _send_email(to_email=to_, code=decrypted_code)
