import json
import os
from enum import StrEnum

import boto3
import urllib3
from botocore.exceptions import ClientError

from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.parser import event_parser
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import BaseModel, ConfigDict

logger = Logger()


class CognitoPostConfirmationTrigger(StrEnum):
    ConfirmSignUp = "PostConfirmation_ConfirmSignUp"
    ConfirmForgotPassword = "PostConfirmation_ConfirmForgotPassword"
    Authentication = "PostConfirmation_Authentication"


class CognitoCustomEmailSenderRequest(BaseModel):
    userAttributes: dict[str, str]

    model_config = ConfigDict(arbitrary_types_allowed=True)


class CognitoPostConfirmationEvent(BaseModel):
    version: str
    region: str
    userPoolId: str
    userName: str
    triggerSource: CognitoPostConfirmationTrigger
    request: CognitoCustomEmailSenderRequest
    callerContext: dict

    model_config = ConfigDict(arbitrary_types_allowed=True)


def add_user_to_group(user_pool_id: str, username: str, group_name: str):
    """
    Adds a user to a Cognito User Pool group.

    Args:
        user_pool_id (str): The ID of the Cognito User Pool.
        username (str): The username of the user to add to the group.
        group_name (str): The name of the group to add the user to.

    Returns:
        None
    """
    try:
        client = boto3.client("cognito-idp")
        client.admin_add_user_to_group(
            UserPoolId=user_pool_id, Username=username, GroupName=group_name
        )
        logger.info(f"Added user {username} to group {group_name}")
    except ClientError as e:
        logger.error(f"Error adding user to group: {e}")
        raise


def create_jarvis_client(cognito_id: str, first_name: str, last_name: str, email: str):
    http = urllib3.PoolManager()
    data = {
        "cognito_id": cognito_id,
        "first_name": first_name,
        "last_name": last_name,
        "email": email,
    }

    ssm_client = boto3.client("ssm", os.environ.get("AWS_REGION", "eu-west-2"))
    env_name = os.environ.get("APP_ENV")
    response = ssm_client.get_parameter(
        Name=f"/aventur-jarvis/{env_name}/PRIVATE_URL_URL",
    )
    api_url = response["Parameter"]["Value"]

    logger.info(f"Env name: {env_name} API URL: {api_url}")

    try:
        response = http.request(
            "POST",
            f"{api_url}/api/v1/clients/",
            body=json.dumps(data).encode("utf-8"),
            headers={"Content-Type": "application/json"},
            timeout=urllib3.util.Timeout(20),
        )

        if response.status != 200:
            logger.error(f"Error creating jarvis user: {response.data.decode('utf-8')}")
            raise Exception

        logger.info(f"User created {response.data.decode('utf-8')}")
        return {"statusCode": response.status, "body": response.data.decode("utf-8")}
    except Exception as e:
        logger.error(f"Error creating jarvis user: {e}")
        return {"statusCode": 500, "body": str(e)}


@event_parser(model=CognitoPostConfirmationEvent)
@logger.inject_lambda_context
def lambda_handler(event: CognitoPostConfirmationEvent, context: LambdaContext):
    """
    Handles Cognito Post Confirmation trigger.

    Args:
        event (CognitoPostConfirmationEvent): The event data from Cognito.
        context (LambdaContext): The Lambda context object.

    Returns:
        dict: The modified event data.
    """

    try:
        logger.info(f"Cognito event received: {event}")

        if event.triggerSource == CognitoPostConfirmationTrigger.ConfirmSignUp:
            # Extract user attributes from the event
            user_pool_id = event.userPoolId
            user_sub = event.userName

            user_attributes = event.request.userAttributes

            user_email = user_attributes.get("email")
            first_name = event.request.userAttributes.get("given_name")
            last_name = event.request.userAttributes.get("family_name")

            response = create_jarvis_client(
                user_sub,
                first_name,
                last_name,
                user_email,
            )

            if response["statusCode"] != 500:
                add_user_to_group(user_pool_id, user_email, "client")

        # Return the event to continue the Cognito flow
        return event.model_dump()

    except Exception as e:
        logger.error(f"Error in Lambda handler: {e}")
        # Optionally, you can modify the event to prevent further processing
        # event['response']['autoConfirmUser'] = False #example of preventing auto confirm.
        return event
