import os
from typing import Any

from aws_cdk import (
    Fn,
    Stack,
    Tags,
)
from aws_cdk import (
    aws_ec2 as ec2,
)
from aws_cdk import (
    aws_secretsmanager as secretmanager,
)
from aws_cdk import (
    aws_ssm as ssm,
)
from constructs import Construct

from .auth_stack import AventurJarvisAuthStack
from .constructs.api_gateway_construct import PrivateApiGatewayConstruct
from .constructs.cloudfront import AventurCloudFrontS3Construct
from .constructs.elasticache import AventurElastiCacheConstruct
from .constructs.fargate import AventurFargateConstruct
from .constructs.iam_user import AventurIAMUserConstruct
from .constructs.queues import AventurQueuesConstruct
from .lambda_functions.internal_api_lambda import ApiLambdaConstruct
from .lambda_functions.lambda_function import AventurLambdaConstruct
from .s3_stack import AventurBucketStack


def get_environment_variables(
    self,
    env_name=None,
    user_pool_client_id=None,
    user_pool_id=None,
    bucket_name=None,
    db_url=None,
    full_domain=None,
    sqs_queue_name=None,
    sqs_dlq_name=None,
    redis_host=None,
    access_key_id=None,
    secret_access_key=None,
    lambda_context: bool = False,
) -> dict[str, Any]:
    return {
        "AWS_ACCESS_KEY_ID": access_key_id,
        "AWS_SECRET_ACCESS_KEY": secret_access_key,
        "AWS_CLIENT_ID": user_pool_client_id,
        "AWS_USER_POOL_ID": user_pool_id,
        "AWS_REGION": "eu-west-2",
        "AWS_TIMESTREAM_REGION": "eu-west-1",
        "AWS_BEDROCK_REGION": "eu-west-1",
        "AWS_BUCKET": bucket_name,
        "AWS_ATHENA_DB": f"jarvis_{env_name}",
        "DB_URL": db_url,
        "APP_ENV": env_name,
        "SENTRY_DNS": "https://<EMAIL>/6214974",
        "URL": full_domain,
        "ZIGNSEC_ENV": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ZIGNSEC_ENV"
        ),
        "ZIGNSEC_ID": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ZIGNSEC_ID"
        ),
        "ZIGNSEC_GATEWAY_ENV": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ZIGNSEC_GATEWAY_ENV"
        ),
        "ZIGNSEC_GATEWAY_ID": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ZIGNSEC_GATEWAY_ID"
        ),
        "ZIGNSEC_VALIDATION_METHOD": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ZIGNSEC_VALIDATION_METHOD"
        ),
        "ZIGNSEC_WEBHOOK_SECRET": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ZIGNSEC_WEBHOOK_SECRET"
        ),
        "MS_TEAMS_URL": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/MS_TEAMS_URL"
        ),
        "POSTMARK_BASE_URL": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/POSTMARK_BASE_URL"
        ),
        "POSTMARK_API_TOKEN": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/POSTMARK_API_TOKEN"
        ),
        "POSTMARK_FROM_ADDRESS": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/POSTMARK_FROM_ADDRESS"
        ),
        "ADVISER_GROUP_EMAIL_ADDRESS": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ADVISER_GROUP_EMAIL_ADDRESS"
        ),
        "ACTIVECAMPAIGN_ACCOUNT": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ACTIVECAMPAIGN_ACCOUNT"
        ),
        "ACTIVECAMPAIGN_API_TOKEN": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/ACTIVECAMPAIGN_API_TOKEN"
        ),
        "TS_DATABASE_NAME": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/TS_DATABASE_NAME"
        ),
        "SQS_QUEUE_NAME": sqs_queue_name,
        "SQS_DLQ_NAME": sqs_dlq_name,
        "LOCAL_S3": "false",
        "LOCAL_SES": "false",
        "LOCAL_SQS": "false",
        "LOCAL_BEDROCK": "false",
        "LAMBDA_CONTEXT": str(lambda_context),
        "JARVIS_VERSION": os.environ.get("BITBUCKET_BUILD_NUMBER")
        if os.environ.get("BITBUCKET_BUILD_NUMBER")
        else "LATEST",
        "AWS_SEND_EMAIL_ON_USER_CREATION": "true",
        "REDIS_HOST": redis_host,
        "DATA_PROCESSING_BASE_URL": ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/DATA_PROCESSING_BASE_URL"
        ),
    }


def domain_data(subdomain):
    root_domain = "aventur.co.uk"
    full_domain = f"{subdomain}.{root_domain}"
    api_domain = f"{subdomain}-api.{root_domain}"

    return root_domain, full_domain, api_domain


class AventurBackendStack(Stack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        auth_stack: AventurJarvisAuthStack,
        env_name="dev",
        vpc_name="",
        subdomain="jarvis-dev",
        db_stack="",
        db_name="",
        bucket_stack: AventurBucketStack = None,
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)

        Tags.of(self).add("AppManagerCFNStackKey", construct_id)

        root_domain, full_domain, api_domain = domain_data(subdomain)

        vpc = ec2.Vpc.from_lookup(self, "VPC", vpc_name=vpc_name)

        db_proxy_endpoint = Fn.import_value(
            f"{self.region}:{self.account}:{db_stack}:db-proxy-endpoint"
        )
        db_secret_arn = Fn.import_value(
            f"{self.region}:{self.account}:{db_stack}:db-secret-arn"
        )

        db_secret = secretmanager.Secret.from_secret_complete_arn(
            self, f"{construct_id}DBSecrets", db_secret_arn
        )
        db_user = db_secret.secret_value_from_json("username").unsafe_unwrap()
        db_password = db_secret.secret_value_from_json("password").unsafe_unwrap()

        db_url = f"postgresql+asyncpg://{db_user}:{db_password}@{db_proxy_endpoint}:5432/{db_name}"

        queues = AventurQueuesConstruct(
            self,
            f"{construct_id}Queues",
            env_name=env_name,
        )

        elasticache = AventurElastiCacheConstruct(
            self,
            f"{construct_id}RedisCache",
            vpc=vpc,
        )

        lambda_function = AventurLambdaConstruct(
            self,
            f"{construct_id}Lambda",
            env_name=env_name,
            vpc=vpc,
            queues=queues,
            bucket=bucket_stack,
            db_url=db_url,
            auth_stack=auth_stack,
            redis_host=elasticache.redis_cluster.attr_redis_endpoint_address,
        )

        iam_user = AventurIAMUserConstruct(
            self,
            f"{construct_id}IAMUsers",
            env_name=env_name,
            user_pool=auth_stack.user_pool_id,
            fifo_queue=queues.sqs_main_fifo_queue.queue_name,
            document_bucket=bucket_stack,
            lambda_function=lambda_function,
        )

        env_vars = get_environment_variables(
            self,
            env_name=env_name,
            sqs_dlq_name=queues.sqs_dlq.queue_name,
            sqs_queue_name=queues.sqs_main_fifo_queue.queue_name,
            bucket_name=bucket_stack.document_bucket.bucket_name,
            db_url=db_url,
            full_domain=full_domain,
            user_pool_id=auth_stack.user_pool_id,
            user_pool_client_id=auth_stack.user_pool_client_id,
            redis_host=elasticache.redis_cluster.attr_redis_endpoint_address,
            access_key_id=iam_user.cfn_access_key.ref,
            secret_access_key=iam_user.cfn_access_key.attr_secret_access_key,
        )

        AventurFargateConstruct(
            self,
            f"{construct_id}Fargate",
            vpc=vpc,
            root_domain=root_domain,
            api_url=api_domain,
            env_vars=env_vars,
        )

        internal_api_lambda = ApiLambdaConstruct(
            self,
            f"{construct_id}InternalApiLambda",
            auth_stack=auth_stack,
            queues=queues,
            bucket=bucket_stack,
            db_url=db_url,
            redis_host=elasticache.redis_cluster.attr_redis_endpoint_address,
            vpc=vpc,
            env_name=env_name,
        )

        self.private_api = PrivateApiGatewayConstruct(
            self,
            f"{construct_id}PrivateApi",
            vpc=vpc,
            vpc_endpoint_id=ssm.StringParameter.value_for_string_parameter(
                self, f"/aventur-jarvis/{env_name}/VPC_ENDPOINT_ID"
            ),
            lambda_function=internal_api_lambda.lambda_function,
            env_name=env_name,
        )


class AventurFrontendStack(Stack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        subdomain="jarvis-dev",
        waf=None,
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)

        Tags.of(self).add("AppManagerCFNStackKey", construct_id)

        root_domain, full_domain, api_domain = domain_data(subdomain)

        AventurCloudFrontS3Construct(
            self,
            f"{construct_id}Cloudfront",
            root_domain=root_domain,
            full_domain=full_domain,
            full_api_domain=api_domain,
            waf=waf,
        )
