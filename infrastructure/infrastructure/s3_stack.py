from aws_cdk import Stack, RemovalPolicy, Duration
from constructs import Construct

from aws_cdk import (
    aws_s3 as s3,
    aws_iam as iam,
)


class AventurBucketStack(Stack):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        env_name: str,
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id, **kwargs)

        document_bucket_name = f"jarvis-documents-{env_name}"

        generated_docs_lifecycle_rule = s3.LifecycleRule(
            id="RemoveOldGeneratedDocs",
            enabled=True,
            prefix="document_generation",
            expiration=Duration.days(7),
        )

        uploaded_files_lifecycle_rule = s3.LifecycleRule(
            id="RemoveOldUploadedFiles",
            enabled=True,
            prefix="file_upload_temp",
            expiration=Duration.days(7),
        )

        self.document_bucket = s3.Bucket(
            self,
            f"{construct_id}DocumentBucket",
            bucket_name=document_bucket_name,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            encryption=s3.BucketEncryption.S3_MANAGED,
            versioned=True,
            removal_policy=RemovalPolicy.RETAIN,
            lifecycle_rules=[
                generated_docs_lifecycle_rule,
                uploaded_files_lifecycle_rule,
            ],
            cors=[
                s3.CorsRule(
                    allowed_methods=[s3.HttpMethods.PUT],
                    allowed_origins=["https://*.aventur.co.uk"],
                    allowed_headers=["*"],
                    max_age=3000,
                )
            ],
        )

        self.document_bucket_policy = iam.PolicyStatement(
            actions=["s3:ListBucket"],
            resources=[
                f"arn:aws:s3:::{document_bucket_name}",
            ],
        )

        self.document_policy = iam.PolicyStatement(
            actions=["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
            resources=[
                f"arn:aws:s3:::{document_bucket_name}/*",
            ],
        )
