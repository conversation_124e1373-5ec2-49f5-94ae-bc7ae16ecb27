import base64
import os
from unittest.mock import Mock

import pytest


@pytest.fixture(scope="session", autouse=True)
def aws_credentials():
    """Mocked AWS Credentials for moto."""
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "us-east-1"


@pytest.fixture
def set_env():
    os.environ["LAMBDA_KMS_KEY_ARN"] = "lambda_kms_key_arn"
    os.environ["POSTMARK_API_TOKEN"] = "postmark_api_token"
    os.environ["POSTMARK_BASE_URL"] = "https://api.postmarkapp.com"
    os.environ["POSTMARK_FROM_ADDRESS"] = "<EMAIL>"


@pytest.fixture
def lambda_context():
    return Mock()


@pytest.fixture
def cognito_request():
    return {
        "triggerSource": "CustomEmailSender_ForgotPassword",
        "request": {
            "code": base64.b64encode(b"123456"),
            "userAttributes": {
                "email": "<EMAIL>",
            },
        },
    }
