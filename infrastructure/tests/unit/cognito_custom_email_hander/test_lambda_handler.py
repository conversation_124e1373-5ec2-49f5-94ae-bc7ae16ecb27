from unittest.mock import patch

import pytest

from infrastructure.cognito_custom_email_sender import lambda_handler

general_template_vars = {
    "product_name": "Aventur",
    "home_url": "https://aventur.co.uk",
    "login_url": "https://jarvis.aventur.co.uk",
    "company_name": "Aventur Wealth Ltd",
    "company_address": "No 1. London Bridge, London SE1 9BG",
    "support_email": "<EMAIL>",
    "support_phone": "020 3740 1113",
}


class TestLambdaHandler:
    def test_raises_for_missing_kms_arn_key(self, cognito_request, lambda_context):
        with pytest.raises(Exception) as e:
            lambda_handler.lambda_handler(cognito_request, lambda_context)
        assert (
            str(e.value)
            == "Missing KMS key ARN. Please set the LAMBDA_KMS_KEY_ARN environment variable."
        )

    @patch("infrastructure.cognito_custom_email_sender.lambda_handler._decrypt")
    def test_lambda_handler(
        self, mock_decrypt, cognito_request, lambda_context, set_env
    ):
        mock_decrypt.return_value = "123456"

        with patch(
            "infrastructure.cognito_custom_email_sender.lambda_handler._request"
        ) as request_mock:
            lambda_handler.lambda_handler(cognito_request, lambda_context)
            request_mock.assert_called_with(
                "/email/withTemplate",
                data={
                    "From": "<EMAIL>",
                    "To": "<EMAIL>",
                    "TemplateAlias": "password-reset",
                    "TemplateModel": {
                        **general_template_vars,
                        "username": "<EMAIL>",
                        "code": "123456",
                    },
                },
            )
