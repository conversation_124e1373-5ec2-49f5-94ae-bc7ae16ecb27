# Developer onboarding - quickstart

1. Checkout the repo - the develop branch is the best place to start
2. Setup the .env files (details of where to put them are later in the README).  Please request these files if you don't have them.
3. Install these 3rd party tools:
- Just : https://github.com/casey/just - its useful tool for keep cli commands
- Docker desktop : https://www.docker.com/products/docker-desktop/

4. Run: "just run" from the root of the source directory (where the .justfile is).  This should build the front and backend in docker - it'll take a few minutes to install all the dependencies and build the system.
5. Run "just alembic-upgrade".  It'll build the DB schema in postgres and seed some initial data
6. Then run "just cognito-user-sync <EMAIL>" - it'll create an initial superadmin user in the DB.

Once that's all done - you should be able to hit "localhost" and login with "<EMAIL>" and the password (please ask for the current password)

# Build

## Prerequisites

- docker 20
- docker-compose 2
- node 22.17+
- npm 10+
- python 3.12
- uv

## Docker Compose

Full project build in docker (from the project root):

`docker-compose up –build –d`

This will bring up the full project including the following services:

1. **aventur-api**: the Fastapi instance (it will install all dependencies using poetry)
2. **aventur-nginx**: the web server for deploying the static site (it will install and build the application using npm)
3. **aventur-traefik**: application router
4. **aventur-db**: postgres
5. **aventur-pgadmin**: postgres pgadmin tool

## JS/NPM

(from the **frontend/webapp** or **frontend/mobileapp** directory)

1. `npm install`
2. `npm run dev` – vite development build
3. `npm run build` – vite production build
4. `npm run storybook` – run storybook
5. `npm run test` – run the vitest tests
6. `npm run tsc` – run vue-tsc check

#### Note:
Storybook requires a worker file to be generated and installed to the **public** folder:
```shell
npx msw init <PUBLIC>
```


## Python/Poetry

(from the backend directory)

1. `uv sync`
2. `uv run uvicorn api.main:app --host 0.0.0.0 --port 8181 –reload` - run a local instance of uvicorn

The local instance of uvicorn isn't required if you are running via docker.
However, it can be useful to run a local instance within an IDE for debugging.
This can be used in parallel with the docker version if a different port number is used.

# Database

## Alembic

From docker you'll need to run one or both of these commands:

- `alembic revision --autogenerate -m "short description"` - to generate new database schema
- `alembic upgrade head` - to apply the schema to the database

Alternatively:

- `docker-compose run api alembic revision --autogenerate -m "short description"`
- `docker-compose run api alembic upgrade head`

Locally you need to use `poetry run` to have the correct paths set

- `poetry run alembic revision --autogenerate -m "short description"` - to generate new database schema
- `poetry run alembic upgrade head` - to apply the schema to the database

## Seeding static data

Static data is seeded automatically while migrating the initial migration

# Environment variables

These files are not checked in, but you will require them to get the system working.

.env

```dotenv
BITBUCKET_TOKEN=<your_bitbucket_token>
```

The `BITBUCKET_TOKEN` env-var is required to install our financial-models package from its git repo.
To generate your own token navigate to the [financial-models repo](https://bitbucket.org/aventur_wealth/financial-models/src/main/),
then go to Repository Settings -> Access Tokens. From here you can create your own access token, the
value of which should be put in this .env file.

If you do not have the necessary repository permissions then please ask for a token to be
generated on your behalf.

backend/src/.env

```dotenv
# App
ENV=local
DEBUG=true
DB_URL=postgresql+asyncpg://aventur:Aventur1234@db:5432/aventur_db

# AWS
AWS_REGION=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_USER_POOL_ID=
AWS_CLIENT_ID=
```

frontend/.env

```dotenv
# App
VITE_URL=http://localhost

# AWS
VITE_AWS_REGION=
VITE_AWS_COGNITO_USER_POOL=
VITE_AWS_COGNITO_CLIENT_ID=
VITE_AWS_COGNITO_AUTHENTICATION_FLOW_TYPE=
VITE_AWS_COGNITO_ENDPOINT_URL=
```

Read further to learn more about the application configuration that might be suitable for your circumstances.

# Development

## Branching naming

All code checkins should be to a feature or bugfix branch with the following naming convention:

(feature/bugfix)/<Jira ticket ID>(optional other info)

For example, these are valid branch names:

- feature/AV-1
- bugfix/AV-2_addtional_documentation

(There currently is not anything stopping the use of other names)

## Checkin comment

Checkin comments should contain the Jira ticket ID somewhere.

## Pull requests

PRs should be made to the “develop” or "release/X.Y.Z" branch respectively.

# Just

https://github.com/casey/just

We use this tool to package up a number more complicated commands.

# Ignore large codebase formatting commits on `git-blame`

Run `git config blame.ignoreRevsFile .git-blame-ignore-revs`.
This allows to skip given commits from `git blame`

Set shell environment to be used by `just` if needed:

```shell
set windows-powershell := true
```

## Available commands

- `just [--list]` - List available recipes
- `just run` - Run the full platform in docker
- `just down` - Stop platform docker containers
- `just restart ARG` - Restart platform docker service container (e.g. `just restart api`)
- `just alembic-upgrade [ARG]` - Run Alembic `upgrade` command with the given argument (e.g. `just alembic-upgrade head`
  to generate DB schema using Alembic and deploy it)
- `just alembic-downgrade ARG` - Run Alembic `downgrade` command with the given argument
- `just test [ARG]` - Runs pytest in docker (provide `ARG` to specify folder or test file to run tests for)
- `just eval [ARG]` - Runs large language model evaluations using pytest in docker (provide `ARG` to specify folder or test file to run tests for). Tests will run locally, but using a cloud hosted LLM and will incur a cost per run. These are not run via the bitbucket pipeline, so run this locally when making LLM prompt iterations.
- `just lint` - Runs ruff and ruff format - fixes issues

## Cognito

You can run your local development stack with `cognito` service available locally. Use `just` commands to start/stop the
service and set the following environment variables:

### frontend

```dotenv
VITE_AWS_COGNITO_AUTHENTICATION_FLOW_TYPE=USER_PASSWORD_AUTH
VITE_AWS_COGNITO_ENDPOINT_URL=http://localhost:9229
```

### backend

```dotenv
AWS_REGION=local
AWS_USER_POOL_ID=cognito_local
AWS_CLIENT_ID=local
AWS_COGNITO_ENDPOINT_URL=http://localhost:9229
```

If you run the full stack using Docker, make sure you set `AWS_COGNITO_ENDPOINT_URL` backend environment variable
to `http://cognito:9229`
Your local user pool details are stored in the `cognito_local.json` file located in `dev-tools/cognito-local/db` folder.
There you can find an example file that you may use to reset the user pool.

Use `manage` to create new users:

```shell
$ manage create-user <EMAIL> Aventur User --type adviser
```

## Document Generation Lambda Handler

Document Generation runs in a lambda function. The lambda can also handle other commands outside
of document generation (see `lambda_handler.base.py` for details.).

To rebuild the docker image for the lambda:

```shell
$ docker-compose build lambda_handler
```

And then to run the container:

```shell
$ docker-compose up lambda_handler -d
```

### Running with Localstack or AWS

Document templating uses the AWS S3, SQS and SES services. For unit testing we need to run these
locally using Localstack. This is controlled by the following boolean environment variables:

- LOCAL_S3
- LOCAL_SQS
- LOCAL_SES

Setting any of these to `false` will use AWS rather than Localstack.

### Invoking the Lambda Function

When the 'lambda_handler' container is running the following URL can be used to invoke the
lambda:

http://localhost:9000/2015-03-31/functions/function/invocations

The script "commands/invoke_document_generation_lambda.py" has been setup to invoke the lambda as
follows:

The general syntax of the command is (note: requires "just run" to bring up all required services):

```shell
$ docker-compose -p local-aventur exec api python src/commands/invoke_document_generation_lambda.py {document_type} {email} {query_args}
```

NOTE: The `query_args` should be a JSON encoded string.

```shell
$ docker-compose -p local-aventur exec api python src/commands/invoke_document_generation_lambda.<NAME_EMAIL> '{"holding_id": 1}'
```

To generate a different document type you need to specify its name and adjust the `query_args` accordingly, e.g. for the
Headed Letter:

```shell
$ docker-compose -p local-aventur exec api python src/commands/invoke_document_generation_lambda.<NAME_EMAIL> '{"client_id": 2}'
```
