x-cognito-env: &cognito-env
  AWS_COGNITO_ENDPOINT_URL: http://cognito:9229

x-localstack-env: &localstack-env
  SQS_QUEUE_NAME: test-queue.fifo
  SQS_DLQ_NAME: test-dead-letter-queue
  LOCALSTACK_ENDPOINT_URL: http://localstack:4566
  LOCALSTACK_AWS_REGION: eu-west-2
  AWS_BUCKET: aventur-test
  JARVIS_SENDER_EMAIL_ADDRESS: <EMAIL>

services:
  api:
    image: aventur-backend:latest
    build:
      context: backend/
      dockerfile: Dockerfile
      args:
        - BITBUCKET_TOKEN=${BITBUCKET_TOKEN}
    environment:
      <<: [*cognito-env, *localstack-env]
      DB_HOST: db
      DB_PORT: 5432
      DB_USERNAME: test
      DB_PASSWORD: test123
      DB_DATABASE_NAME: test_db
      DB_URL: postgresql+asyncpg://test:test123@db:5432/test_db
      AWS_ACCESS_KEY_ID: local
      AWS_SECRET_ACCESS_KEY: local
      AWS_USER_POOL_ID: cognito_local
      AWS_REGION: local
      AWS_BEDROCK_REGION: test
      ZIGNSEC_ID: 12345
      ZIGNSEC_GATEWAY_ID: 12345
      ZIGNSEC_WEBHOOK_SECRET: 12345
      LOCAL_BEDROCK: "true"
      LOCAL_S3: "true"
      LOCAL_SQS: "true"
      LOCAL_SES: "true"
      ACTIVECAMPAIGN_ACCOUNT: test
      ACTIVECAMPAIGN_API_TOKEN: test
      POSTMARK_API_TOKEN: test
      POSTMARK_FROM_ADDRESS: test
      INTERNAL_API_BASE_URL: http://internal-api:8082
    volumes:
      - ${PWD}/backend/:/coverage/
      - ${PWD}/profiling/:/code/prof/
    depends_on:
      - internal-api
      - db
      - cognito
      - localstack
      - redis

  internal-api:
    image: aventur-internal-api:latest
    build:
      context: backend/
      dockerfile: Dockerfile
      args:
        - BITBUCKET_TOKEN=${BITBUCKET_TOKEN}
    command: uv run uvicorn internal_api.main:app --host 0.0.0.0 --port 8082 --reload
    environment:
      DB_HOST: db
      DB_PORT: 5432
      DB_USERNAME: test
      DB_PASSWORD: test123
      DB_DATABASE_NAME: test_db
      DB_URL: postgresql+asyncpg://test:test123@db:5432/test_db
    ports:
      - "8082:8082"
    volumes:
      - ${PWD}/backend/:/var/task
    depends_on:
      - db

  db:
    image: postgres:16-alpine3.20
    ports:
      - "5444:5432"
    environment:
      POSTGRES_USER: test
      POSTGRES_PASSWORD: test123
      POSTGRES_DB: test_db
      PGDATA: /var/lib/postgresql/data
    tmpfs:
      - /var/lib/postgresql/data
    cpus: 0.3
    mem_limit: 256M
    command: -c fsync=off -c full_page_writes=off

  cognito:
    image: aventur-cognito-local:latest
    build:
      context: dev-tools/cognito-local/
      dockerfile: Dockerfile
    ports:
      - "9230:9229"

  localstack:
    image: localstack/localstack
    ports:
      - "127.0.0.1:4666:4566" # LocalStack Gateway
      - "127.0.0.1:4610-4659:4510-4559" # external services port range
    environment:
      <<: *localstack-env
      DEBUG: ${DEBUG-}
      DOCKER_HOST: unix:///var/run/docker.sock
    volumes:
      - ${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack
      - /var/run/docker.sock:/var/run/docker.sock
      - ./dev-tools/cors-config.json:/opt/code/localstack/cors-config.json
      - ./dev-tools/start-localstack.sh:/etc/localstack/init/ready.d/start-localstack.sh

  redis:
    image: redis:7.4-alpine3.20
    restart: always
    ports:
      - 6380:6379
    environment:
      - REDIS_PASSWORD=my-password
      - REDIS_PORT=6379
