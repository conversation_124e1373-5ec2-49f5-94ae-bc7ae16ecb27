set dotenv-required

# List just commands
default:
    just --list

# Build Jarvis
build SERVICE='':
    docker-compose -p local-aventur -f docker-compose.yml build {{ SERVICE }}

# Run Jarvis
run:
    docker-compose -p local-aventur -f docker-compose.yml up --build -d

# Stop Jarvis
down:
    docker-compose -p local-aventur -f docker-compose.yml down

restart SERVICE:
    docker-compose -p local-aventur restart {{SERVICE}}

# Run ruff and ruff format
lint: build && down
    docker-compose -p local-aventur -f docker-compose.yml run --rm api uv run ruff check . --fix
    docker-compose -p local-aventur -f docker-compose.yml run --rm api uv run ruff format .

# Run test infrastructure (without api container)
test-run:
    docker-compose -p aventur_tests -f docker-compose.tests.yml up --build -d db cognito localstack

# Build test deployment
test-build:
    docker-compose -p aventur_tests -f docker-compose.tests.yml build

# Stop test deployment
test-down:
    docker-compose -p aventur_tests -f docker-compose.tests.yml down

# Run pytest - default: run all tests
test TEST='tests': test-build && test-down
    docker-compose -p aventur_tests -f docker-compose.tests.yml run --rm --remove-orphans api uv run pytest {{TEST}}

test-profile TEST: test-build && test-down
    docker-compose -p aventur_tests -f docker-compose.tests.yml run --rm api uv run pytest {{TEST}} --profile

# Build eval deployment
eval-build:
    COMPOSE_BAKE=true docker-compose -p aventur_evals -f docker-compose.evals.yml build

# Stop eval deployment
eval-down:
    docker-compose -p aventur_evals -f docker-compose.evals.yml down

# Run pytest - default: run all evals
eval EVAL='evals': eval-build && eval-down
    docker-compose -p aventur_evals -f docker-compose.evals.yml run --rm --remove-orphans api uv run pytest -s {{EVAL}}

# Alembic Autogenerate DB migration
autogen-revision MESSAGE:
    docker-compose -p local-aventur -f docker-compose.yml run api uv run alembic revision --autogenerate -m "{{MESSAGE}}"

# Alembic upgrade
alembic-upgrade TO='head':
    docker-compose -p local-aventur -f docker-compose.yml run --rm api uv run alembic upgrade {{TO}}

# Alembic downgrade
alembic-downgrade TO:
    docker-compose -p local-aventur -f docker-compose.yml run api uv run alembic downgrade {{TO}}

# Creates a local user based on the user setup in Cognito
cognito-user-sync EMAIL:
    docker-compose -p local-aventur -f docker-compose.yml run --rm api uv run manage create-user-from-cognito {{EMAIL}}

# Start Cognito
cognito:
    docker run --rm --name cognito-local -p 9229:9229 -v ./dev-tools/cognito-local:/app/.cognito -w /app jagregory/cognito-local:latest

# Stop Cognito
cognito-down:
    docker stop cognito-local && docker container rm cognito-local