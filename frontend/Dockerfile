FROM node:22.17.0-alpine3.22 AS build-stage

ENV NODE_OPTIONS=--max_old_space_size=2048

WORKDIR /app

COPY package*.json /app/
COPY ./webapp/package.json /app/webapp/
COPY ./shared/package.json /app/shared/
COPY tsconfig.base.json /app/

RUN npm install

COPY ./webapp /app/webapp/
COPY ./shared /app/shared/

ARG VITE_ENV
ARG VITE_URL
ARG VITE_SENTRY_DNS
ARG VITE_MARKER_IO_PROJECT
ARG VITE_AWS_REGION
ARG VITE_AWS_COGNITO_USER_POOL
ARG VITE_AWS_COGNITO_CLIENT_ID
ARG VITE_AMPLITUDE_KEY

ENV VITE_ENV=${VITE_ENV}
ENV VITE_URL=${VITE_URL}
ENV VITE_SENTRY_DNS=${VITE_SENTRY_DNS}
ENV VITE_MARKER_IO_PROJECT=${VITE_MARKER_IO_PROJECT}
ENV VITE_AWS_REGION=${VITE_AWS_REGION}
ENV VITE_AWS_COGNITO_USER_POOL=${VITE_AWS_COGNITO_USER_POOL}
ENV VITE_AWS_COGNITO_CLIENT_ID=${VITE_AWS_COGNITO_CLIENT_ID}
ENV VITE_AMPLITUDE_KEY=${VITE_AMPLITUDE_KEY}

RUN npm run build -w=webapp

FROM nginx:1.25.0-alpine

COPY webapp/nginx/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build-stage /app/webapp/dist/ /usr/share/nginx/html
