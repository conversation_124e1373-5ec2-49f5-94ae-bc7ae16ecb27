/// <reference types="@capacitor/splash-screen" />

import { CapacitorConfig } from '@capacitor/cli';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
const isProduction = process.env.NODE_ENV === 'production';

const config: CapacitorConfig = {
  appId: 'com.aventur.mobile',
  appName: 'aventur-mobileapp',
  webDir: 'dist',
  server: {
    cleartext: !isProduction,
  },
  zoomEnabled: false,
  loggingBehavior: isProduction ? 'none' : 'debug',
  android: {
    zoomEnabled: false,
  },
  ios: {
    zoomEnabled: false,
  },
  overrideUserAgent:
    'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
  plugins: {
    SplashScreen: {
      launchShowDuration: 1000,
      launchAutoHide: false,
      backgroundColor: '#14312c',
      androidSplashResourceName: 'splash',
      androidScaleType: 'CENTER_CROP',
      showSpinner: false,
      splashFullScreen: true,
      splashImmersive: true,
      useDialog: true,
    },
  },
};

export default config;
