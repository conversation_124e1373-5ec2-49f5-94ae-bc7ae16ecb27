import { faker } from '@faker-js/faker';
import { some } from 'lodash';

import { Money } from '@aventur-shared/utils/money';
import { UserRole } from '@aventur-shared/modules/auth';
import {
  DateTime,
  factory as dateTimeFactory,
} from '@aventur-shared/utils/dateTime';
import { User, UserId } from '@aventur-shared/modules/users';
import { Goal, GoalId } from '@aventur-shared/modules/goals';
import { Task, TaskStatus } from '@aventur-shared/modules/tasks';
import { AccountStatusEnum } from '@aventur-shared/modules/accounts';
import { Case, CaseId, CaseStatusEnum } from '@aventur-shared/modules/cases';
import { TaskTypeEnum } from '@aventur-shared/modules/tasks/models/task-type';
import { TaskStatusEnum } from '@aventur-shared/modules/tasks/models/task-status';
import { accountStatusToSelectOption } from '@aventur-shared/modules/accounts/models/account';
import { ClientAddress } from '@aventur-shared/modules/factfind/types/Address';
import { CaseType, SubTask } from '@aventur-shared/modules/cases/models';
import { GoalType } from '@aventur-shared/modules/goals/types/GoalType';
import { CaseGoal } from '@aventur-shared/modules/cases/models/case–goal';
import { CaseTypeEnum } from '@aventur-shared/modules/cases/models/case-type';
import { Client, ClientTypeEnum } from '@aventur-shared/modules/clients';
import { HealthScoreDTO } from '@aventur-shared/modules/clients/api';
import {
  AboutYou,
  Expenditure,
  Frequency,
  FrequencyEnum,
  Income,
} from '@aventur-shared/modules/factfind';
import {
  ClientGoal,
  ClientGoalTypeEnum,
} from '@aventur-shared/modules/clients/models';
import {
  PreviousInvestmentExperienceEnum,
  YesNoNeedsUpdatingEnum,
  creditHistoryEnum,
  employmentStatusEnum,
} from '@aventur-shared/constants';
import { RelationshipType } from '@aventur-shared/types/Relationship';
import { FamilyMember } from '@modules/clients/components/profile/personal-details/family/form/form-model';

export const taskTypeToSlug = (taskType: TaskTypeEnum) =>
  `${taskType}`.replace(/\B[A-Z]+/g, (match) => `_${match}`).toLowerCase();

export const goalTypeToName = (goalType: GoalType) =>
  `${ClientGoalTypeEnum[goalType]}`.replace(
    /\B[A-Z]+/g,
    (match) => ` ${match}`,
  );

export const taskTypeToDescription = (
  taskType: TaskTypeEnum,
  defaultDescription = 'Basic Task',
) =>
  taskType === TaskTypeEnum.Default
    ? defaultDescription
    : `${taskType}`.replace(/\B[A-Z]+/g, (match) => ` ${match}`);

let caseId = 0;
let goalId = 0;
let taskId = 0;
let clientId = 0;
let adviserId = 0;
let accountId = 0;
let clientGoalId = 0;
let clientIncomeId = 0;
let clientExpenditureId = 0;
const addressId = 0;
let familyMemberId = 0;

const _createAddress = (
  () =>
  (params: Partial<ClientAddress> = {}): ClientAddress =>
    Object.assign(
      {
        addressLineOne: null,
        addressLineTwo: null,
        addressLineThree: null,
        addressLineFour: null,
        city: null,
        countryId: null,
        postCode: null,
        isPrimary: true,
      },
      params,
    )
)();

const _createClient = (
  () =>
  (params: Partial<Client> = {}): Client =>
    Object.assign(
      {
        id: ++clientId,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        type: '',
        email: faker.internet.email(),
        title: null,
        genderId: null,
        dateOfBirth: null,
        nationalityId: null,
        maritalStatusId: null,
        phoneNumber: null,
        mobileNumber: null,
        addresses: [],
        linkedClients: [],
        advisor: _createAdviser(),
        clientType: ClientTypeEnum.Individual,
        clientStatus: 0,
        clientSource: 0,
        reviewFrequency: null,
        reviewMonth: null,
        nextReviewMonth: null,
      },
      params,
    )
)();

const _createAdviser = (() => () => ({
  id: ++adviserId,
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
}))();

const _createTask = (
  () =>
  (params: Partial<Task> = {}): Task => {
    const { subTasks, ...$params } = params;
    const taskType = $params.type ?? TaskTypeEnum.Default;

    return Object.assign(
      {
        slug: taskTypeToSlug(taskType),
        caseLevel: !some(subTasks, 'goalId'),
        description: taskTypeToDescription(taskType, $params.description),
        defaultGroup: UserRole.Adviser,
        type: TaskTypeEnum.Default,
        isModifiable: false,
        status: new TaskStatus(TaskStatusEnum.ToDo),
        dueDate: new DateTime(new Date()),
        advisor: _createAdviser(),
        assignedGroup: null,
        subTasks: subTasks ?? [_createSubTask()],
      },
      $params,
    );
  }
)();

const _createSubTask = (
  () =>
  (params: Partial<SubTask> = {}): SubTask =>
    Object.assign(
      {
        taskId: ++taskId,
        caseGoalId:
          params.caseGoalId === undefined ? ++goalId : params.caseGoalId, // case_goal_id
        goalId:
          params.goalId === undefined
            ? ClientGoalTypeEnum.ClientSetup
            : params.goalId,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        name: params.goalId ? goalTypeToName(params.goalId) : `Goal ${goalId}`,
        noDocumentsReason: null,
      },
      params,
    )
)();

const _createGoal = (
  () =>
  (params: Partial<Goal> = {}): Goal => ({
    id: ++goalId as GoalId,
    name: params.type ? goalTypeToName(params.type) : `Goal ${goalId}`,
    type: params?.type || ClientGoalTypeEnum.ClientSetup,
    accounts: params?.accounts || [],
    clients: params?.clients || [...createClients(1)],
    goalObjectives: [],
  })
)();

const _createLinkedHoling = () => ({
  id: 1,
  productTypeName: 'Type 1',
  providerName: 'Provider 1',
  accountNumber: '123-99-00',
});

const _createClientGoal = (
  () =>
  (params: Partial<ClientGoal> = {}): ClientGoal => ({
    id: ++clientGoalId as GoalId,
    goalTypeId: ClientGoalTypeEnum.BuildWealth,
    name: 'Goal name',
    cases: [],
    clientIds: [],
    attributes: {
      targetAmount: 100_000,
      targetDate: '01-01-2029',
    },
    linkedHoldings: [_createLinkedHoling()],
    riskProfile: null,
    cashForecast: null,
    objectives: 'Goal objectives',
    ...params,
  })
)();

const _createAccount = (
  () =>
  (params: Partial<Goal['accounts'][0]> = {}): Goal['accounts'][0] =>
    Object.assign(
      {
        id: ++accountId,
        accountNumber: faker.finance.accountNumber(),
        subAccountNumber: faker.finance.accountNumber(),
        providerName: 'Provider1',
        advices: params.advices || [],
        advisorId: createAdviser().id,
        clients: [...createClients(1)],
        expectedFees: [],
        feeSplitTemplate: 1,
        type: faker.finance.accountName(),
        typeGroupId: 1,
        originalStatus: accountStatusToSelectOption(AccountStatusEnum.Proposed),
        status: accountStatusToSelectOption(AccountStatusEnum.Proposed),
      },
      params,
    )
)();

const _createCase = (
  () =>
  (params: Partial<Case> = {}): Case => {
    const $advisor = params?.relatedAdvisor || _createAdviser();
    const $goals = params?.goals ?? [_createGoal()];
    const $tasks = params?.tasks ?? [
      _createTask({
        advisor: $advisor,
        status: new TaskStatus(TaskStatusEnum.InProgress),
        subTasks: $goals.map(({ id: goalId, name }) =>
          _createSubTask({
            goalId,
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            name,
          }),
        ),
      }),
    ];

    return {
      id: ++caseId as CaseId,
      type: params?.type || new CaseType(CaseTypeEnum.NewBusiness),
      name: `Case${goalId}`,
      status: params?.status || CaseStatusEnum.Open,
      createdOn: new Date(),
      completedOn: null,
      daysToComplete: 0,
      relatedAdvisor: $advisor,
      relatedManager: null,
      clients: params?.clients || [_createClient()],
      goals: $goals,
      tasks: params?.tasks || $tasks,
      reviewSlot: null,
    };
  }
)();

const _createClientIncome = (params: Partial<Income> = {}) => ({
  id: ++clientIncomeId,
  typeGroup: 1, //  "Income - Employment"
  type: 1, // "Annual Basic Salary"
  description: `Income ${clientIncomeId}`,
  frequency: new Frequency(FrequencyEnum.Yearly),
  amount: new Money(72000),
  ...params,
});

const _createClientExpenditure = (params: Partial<Expenditure> = {}) => ({
  id: ++clientExpenditureId,
  typeGroup: 7, //  "Household"
  type: 23, // "Mortgage"
  description: `Expenditure ${clientExpenditureId}`,
  frequency: new Frequency(FrequencyEnum.Monthly),
  amount: new Money(2340),
  isEssential: true,
  ...params,
});

export const createTasks = (num = 1): Task[] =>
  Array(num)
    .fill(undefined)
    .map(() => _createTask());

export const createClients = (num = 1): Client[] =>
  Array(num)
    .fill(undefined)
    .map(() => _createClient());

export const createGoals = (num = 1): CaseGoal[] =>
  Array(num)
    .fill(undefined)
    .map(() => _createGoal());

export const createHealthScore = (goals: ClientGoal[]) => {
  const healthScore: HealthScoreDTO = {
    overallScore: Math.floor(Math.random() * 1000),
    goalScores: goals.reduce(
      (acc, goal) => ({
        ...acc,
        [goal.id]: {
          goalId: goal.id,
          score: Math.floor(Math.random() * 100),
          auditResults: {},
        },
      }),
      {},
    ),
    errors: {},
    warnings: {},
  };
  return healthScore;
};

export const createFamilyMember = (): FamilyMember => ({
  id: ++familyMemberId,
  firstName: 'First Name',
  lastName: 'Last Name',
  dateOfBirth: dateTimeFactory('2000-01-01'),
  relationshipType: RelationshipType[2], // Child,
});

export const createUserStore = (
  params: Partial<User> = {},
): { user: User } => ({
  user: Object.assign(
    {
      id: 1 as UserId,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      type: '',
      permissions: [],
      groups: [UserRole.Client],
    },
    params,
  ),
});

export const createClientStore = (client: Client) => ({
  client_id: client.id,
  profile: {
    id: client.id,
  },
  goals: [],
  holdings: [],
});

export const createRefDataStore = () => ({
  goals: [
    {
      id: ClientGoalTypeEnum.Retirement,
      name: 'Retirement',
      code: 'RE',
      is_default: true,
      description: 'Retirement goal description',
      client_goal: true,
    },
    {
      id: ClientGoalTypeEnum.BuildWealth,
      name: 'Build Wealth',
      code: 'BW',
      is_default: false,
      description: 'BuildWealth goal description',
      client_goal: true,
    },
    {
      id: ClientGoalTypeEnum.EmergencyFund,
      name: 'Emergency Fund',
      code: 'EF',
      is_default: false,
      description: 'EmergencyFund goal description',
      client_goal: true,
    },
  ],
  nationalities: [
    {
      id: 1,
      name: 'British',
    },
  ],
  countries: [
    {
      id: 1,
      name: 'United Kingdom',
      iso_alpha2: 'GB',
      iso_alpha3: 'GBR',
    },
    {
      id: 2,
      name: 'Reunion',
      iso_alpha2: 'RE',
      iso_alpha3: 'REU',
    },
  ],
  titles: [{ id: 1, name: 'Mr' }],
  genders: [{ id: 1, name: 'Male' }],
  maritalStatuses: [{ id: 2, name: 'Married' }],
  relationshipTypes: [{ id: 0, name: 'Not Set' }],
});

export const createCashflowStore = () => ({
  incomes: [
    {
      id: 1,
      group_name: 'Employed',
      flow_type: 'Income',
      income_expenditure_group_id: 1,
      name: 'Annual Basic Salary',
      is_essential: null,
    },
    {
      id: 9,
      group_name: 'Company',
      flow_type: 'Income',
      income_expenditure_group_id: 3,
      name: 'Dividends',
      is_essential: null,
    },
  ],
  expenditures: [
    {
      id: 23,
      group_name: 'Household',
      flow_type: 'Expenditure',
      income_expenditure_group_id: 7,
      name: 'Mortgage',
      is_essential: true,
    },
    {
      id: 36,
      group_name: 'General',
      flow_type: 'Expenditure',
      income_expenditure_group_id: 12,
      name: 'Food - Essential',
      is_essential: true,
    },
  ],
  isLoaded: true,
});

export const createFactfindStore = (client: Client) => ({
  primaryDetails: {
    personalDetails: {
      firstName: client.firstName,
      lastName: client.lastName,
      dateOfBirth: client.dateOfBirth,
      genderId: 1, // Male
      maritalStatusId: 2, // Married
      nationalityId: 1, // British
      birthCountryId: 1, // United Kingdom
      primaryCountryId: 2, // Reunion
      secondaryCountryId: null,
      titleId: 1, // Mr
    },
    contactDetails: {
      email: client.email,
      phoneNumber: null,
      mobileNumber: null,
      addresses: [
        {
          id: 1,
          moveInDate: null,
          moveOutDate: null,
          ...createAddress({
            addressLineOne: '1 Test Street',
            countryId: 1,
            postCode: 'TE1 1ST',
            isPrimary: true,
          }),
        },
      ],
    },
    familyMembers: [createFamilyMember()],
    retirementDetails: {
      retirementAge: null,
      monthlyRetirementIncomeRequired: null,
      statePension: true,
      alreadyRetired: null,
    },
    furtherInformations: {
      previousFinancialAdvice: true,
      previousInvestmentExperience: PreviousInvestmentExperienceEnum.High,
      ethicalInvestments: false,
      religiousRestrictions: false,
      isVulnerablePerson: null,
      insuranceNumber: 'NI000000X',
      will: YesNoNeedsUpdatingEnum.NeedsUpdating,
      powerOfAttorney: null,
      creditHistory: creditHistoryEnum.Good,
      employmentStatus: employmentStatusEnum.Employed,
    },
  } as AboutYou,
  assets: [],
  debts: [],
  incomes: [],
  expenditures: [],
  goals: [],
});

export const createAdviser = _createAdviser;
export const createAccount = _createAccount;
export const createAddress = _createAddress;
export const createClient = _createClient;
export const createCase = _createCase;
export const createGoal = _createGoal;
export const createClientGoal = _createClientGoal;
export const createTask = _createTask;
export const createSubTask = _createSubTask;
export const createClientIncome = _createClientIncome;
export const createClientExpenditure = _createClientExpenditure;
