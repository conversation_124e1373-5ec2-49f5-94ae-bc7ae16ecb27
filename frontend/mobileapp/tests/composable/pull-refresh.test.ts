import { ref } from 'vue';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { apiClient } from '@aventur-shared/services/api';
import { GoalId } from '@aventur-shared/modules/goals';

import {
  createClient,
  createClientGoal,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import { default as AppLayout } from '@/layouts/AppLayout.vue';
//

const getSpy = vi.spyOn(apiClient, 'get');

const client = createClient();

const goalId = 1 as GoalId;
const clientGoal = createClientGoal({ id: goalId, clientIds: [client.id] });
const clientStore = { ...createClientStore(client), goals: [clientGoal] };
const refDataStore = createRefDataStore();

vi.mock('@/stores/appStore', () => ({
  useAppStore: () => ({
    isReady: ref(true),
    setIsReady: vi.fn(),
  }),
}));

vi.mock('@aventur-shared/utils/user', async () => ({
  isClient: vi.fn().mockReturnValue(Promise.resolve(true)),
}));

vi.mock('@aventur-shared/modules/auth', async () => ({
  UserRole: vi.fn(),
  useAuth: vi.fn().mockReturnValue({
    isUserDataReady: true,
  }),
}));

vi.mock('@/composables/useResetView.ts');
vi.mock('@/composables/usePullRefresh.ts', async (importOriginal) => {
  const original = await importOriginal();
  return {
    usePullRefresh: vi.fn().mockReturnValue({
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      ...original,
      handleTouchStart: vi.fn(),
      handleTouchMove: vi.fn(),
      handleTouchEnd: vi.fn(),
      registerRefreshHandler: vi.fn(),
    }),
  };
});

function makeWrapper() {
  return mount(AppLayout, {
    props: {
      user: {
        signInDetails: {},
        username: client.email as string,
        userId: String(client.id),
      },
    },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          // stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      stubs: {
        DashboardIndex: true,
      },
      components: {},
    },
  });
}

describe('PullRefresh', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('makes API calls on pull refresh', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    await router.push('/goals');
    await router.isReady();

    await wrapper.find('[data-v-app-layout]').trigger('touchstart');
    expect(wrapper.vm['handleTouchStart']).toHaveBeenCalled();

    await wrapper.find('[data-v-app-layout]').trigger('touchmove');
    expect(wrapper.vm['handleTouchMove']).toHaveBeenCalled();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    wrapper.vm.pullDistance = 40;

    await wrapper.find('[data-v-app-layout]').trigger('touchend');
    expect(wrapper.vm['handleTouchMove']).toHaveBeenCalled();

    expect(apiClient.get).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledWith(
      `/api/internal/v1/active-holdings?client_ids=${client.id}`,
    );
  });
});
