import { describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { apiClient } from '@aventur-shared/services/api';
import { useFactfindStore } from '@aventur-shared/modules/factfind';

import {
  createClient,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { profilePostStub } from '@tests/modules/clients/components/profile/stubs';
import { default as router } from '@/router';

import { default as Profile } from '@/pages/Profile.vue';
//

const client = createClient();

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();

function makeWrapper() {
  return mount(Profile, {
    props: {
      client: client,
      clientId: client.id,
    },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

const data = profilePostStub(client);
vi.spyOn(apiClient, 'get').mockResolvedValue({ client: data });

describe('Profile - Profile', () => {
  it('renders view and makes API calls', async () => {
    makeWrapper();
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
    );

    const { getClientPrimaryDetails, setClientPrimaryDetails } =
      useFactfindStore();

    expect(getClientPrimaryDetails).toHaveBeenCalledTimes(1);
    expect(setClientPrimaryDetails).toHaveBeenCalledTimes(1);
  });
});
