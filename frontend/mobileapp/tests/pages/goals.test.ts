import { describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { apiClient } from '@aventur-shared/services/api';
import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';

import {
  createClient,
  createClientGoal,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';
import { default as Goals } from '@/pages/Goals.vue';
import GoalsHeader from '@modules/clients/components/goals/headers/GoalsHeader.vue';
import GoalTypeGroup from '@modules/clients/components/goals/ui/GoalTypeGroup.vue';
import { GoalId } from '@aventur-shared/modules/goals';
//

const client = createClient();

const goalId = 1 as GoalId;
const clientGoal = createClientGoal({ id: goalId, clientIds: [client.id] });
const clientStore = { ...createClientStore(client), goals: [clientGoal] };
const refDataStore = createRefDataStore();

function makeWrapper() {
  return mount(Goals, {
    props: {
      client: client,
      clientId: client.id,
    },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

// Mock the API calls
const mockJarvisGetGoalsResponse = [
  {
    goal_id: 1,
    goal_name: 'Build Wealth',
    goal_objectives: 'BuildWealth goal objectives',
    clients: [client.id],
    open_cases: [],
    attributes: {
      target_amount: null,
      target_date: null,
      type_: ClientGoalTypeEnum.BuildWealth,
    },
    linked_holdings: [],
    risk_profile: null,
    cash_forecast: null,
  },
  {
    goal_id: 2,
    goal_name: 'Retirement',
    goal_objectives: 'Retirement goal objectives',
    clients: [client.id],
    open_cases: [1],
    attributes: {
      target_amount: 10_000,
      target_date: '01-01-2030',
      type_: ClientGoalTypeEnum.Retirement,
    },
    linked_holdings: [
      {
        holding_id: 1,
        account_number: '999999',
        provider_name: 'Abbey Life',
        product_type: 'ISA (stocks & shares)',
      },
    ],
    risk_profile: null,
    cash_forecast: null,
  },
];
vi.spyOn(apiClient, 'get').mockResolvedValue(mockJarvisGetGoalsResponse);
vi.mock('@aventur-shared/modules/clients/api', async (importOriginal) => ({
  ...(await importOriginal<
    typeof import('@aventur-shared/modules/clients/api')
  >()),
  getClientsActiveHoldings: vi.fn(() => Promise.resolve([])),
}));

describe('Profile - Goals', () => {
  it('renders view and makes API calls', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findComponent(GoalsHeader).text()).toContain(
      'Your financial score 0 out of 1000',
    );

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenNthCalledWith(
      1,
      `/api/v1/clients/${client.id}/factfind/goals`,
    );
    expect(apiClient.get).toHaveBeenNthCalledWith(
      2,
      `/api/v2/clients/${client.id}/health-score`,
    );

    await router.push('/goals');
    await router.isReady();

    expect(wrapper.findAllComponents(GoalTypeGroup).length).toEqual(3);
    wrapper.findAllComponents(GoalTypeGroup).forEach((component, index) => {
      const refGoal = refDataStore.goals[index];

      expect(component.vm.$props.groupId).to.eq(refGoal.id);
      expect(component.find('h3').text()).toContain(refGoal.name);
    });
  });
});
