import { find } from 'lodash';
import { describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { apiClient } from '@aventur-shared/services/api';
import { Goal } from '@aventur-shared/stores/refdataStore';
import { GoalId } from '@aventur-shared/modules/goals';
import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models/goal';
import { ClientGoalAttrsMapper } from '@aventur-shared/modules/clients/utils';

import {
  createClient,
  createClientGoal,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import GoalForm from '@modules/clients/components/goals/ui/form/form.vue';
import { FormValues } from '@modules/clients/components/goals/ui/form/form-model';
//

const mockToastSuccess = vi.fn();
vi.mock('@aventur-shared/composables/useToast', () => ({
  useToast: () => ({
    info: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    success: mockToastSuccess,
  }),
}));

// Mock ResizeObserver so the modal is able to be 'displayed' in the test
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

vi.spyOn(apiClient, 'get').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'patch').mockResolvedValue(undefined);

const client = createClient();

const goalId = 1 as GoalId;
const clientGoal = createClientGoal({
  id: goalId,
  clientIds: [client.id],
});
const clientStore = { ...createClientStore(client), goals: [clientGoal] };
const refDataStore = createRefDataStore();

function makeWrapper() {
  return mount(GoalForm, {
    props: {
      group: find(refDataStore.goals, {
        id: ClientGoalTypeEnum.BuildWealth,
      }) as Goal,
      client: client,
      goal: clientGoal,
    },
    global: {
      provide: {},
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            user: createUserStore({ id: client.id }),
            refdata: refDataStore,
            client: clientStore,
          },
        }),
      ],
    },
  });
}

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => {
    return {
      params: {
        id: goalId,
      },
    };
  }),
  useRouter: vi.fn(),
  onBeforeRouteLeave: vi.fn(),
}));

const mapAttrs = (goalTypeId: number, attrs: FormValues['attributes']) =>
  ClientGoalAttrsMapper.toDTO(goalTypeId, attrs);

describe('GoalForm', () => {
  /**
   * @TODO: Fix
   * This test fails even when debugging shows correct flow
   */
  it.todo('Makes correct API calls on goal update', async () => {
    const formWrapper = makeWrapper();
    await flushPromises();

    expect(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      formWrapper.find('button[type=submit]').element.disabled,
    ).toBeTruthy();

    await formWrapper.find('form').trigger('submit');
    await flushPromises();

    expect(mockToastSuccess).toHaveBeenCalledOnce();
    expect(apiClient.patch).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/goals/${clientGoal.id}`,
      {
        goal_type_id: clientGoal.goalTypeId,
        goal_name: clientGoal.name,
        client_ids: clientGoal.clientIds,
        goal_attributes: mapAttrs(clientGoal.goalTypeId, clientGoal.attributes),
      },
    );
  });
});
