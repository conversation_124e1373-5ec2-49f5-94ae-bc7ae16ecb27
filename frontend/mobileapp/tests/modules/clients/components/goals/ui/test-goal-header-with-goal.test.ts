import { ref } from 'vue';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import GoalsHeader from '@/modules/clients/components/goals/headers/GoalsHeader.vue';
import { GoalId } from '@aventur-shared/modules/goals';
import {
  createClient,
  createClientGoal,
  createClientStore,
  createHealthScore,
} from '@tests/helpers';

const client = createClient();
const goalId = 1 as GoalId;
const clientGoal = createClientGoal({ id: goalId, clientIds: [client.id] });
const healthScore = createHealthScore([clientGoal]);
const clientStore = {
  ...createClientStore(client),
  goals: [clientGoal],
  healthScore,
};

describe('GoalsHeader.vue with current goal', () => {
  let wrapper;

  beforeEach(() => {
    // Mock vue-router
    vi.mock('vue-router', () => ({
      useRoute: () => ({
        params: { goalId: '1' },
      }),
    }));

    // Set up mock for useCurrentGoal with a goal
    vi.mock(
      '@modules/clients/components/goals/composables/use-current-goal',
      () => ({
        useCurrentGoal: () => ({
          currentGoal: ref({
            goalId: clientGoal.id,
            goalName: clientGoal.name,
            typeId: clientGoal.goalTypeId,
            typeName: 'Some type',
          }),
        }),
      }),
    );

    wrapper = mount(GoalsHeader, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false,
            initialState: {
              client: clientStore,
            },
          }),
        ],
      },
    });
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('renders goal view when currentGoal exists', () => {
    expect(wrapper.text()).toContain(clientGoal.name);
    expect(wrapper.text()).toContain(
      healthScore.goalScores[clientGoal.id].score,
    );
    expect(wrapper.text()).toContain('out of 100');
  });

  it('displays warning count when warnings exist', () => {
    const warningHealthScore = {
      ...healthScore,
      warnings: {
        [goalId]: {
          1: 'Warning 1',
          2: 'Warning 2',
        },
      },
    };
    const warningStore = {
      ...clientStore,
      healthScore: warningHealthScore,
    };

    wrapper = mount(GoalsHeader, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false,
            initialState: {
              client: warningStore,
            },
          }),
        ],
      },
    });

    expect(wrapper.text()).toContain('2 warnings');
  });

  it('displays single warning text when one warning exists', () => {
    const warningHealthScore = {
      ...healthScore,
      warnings: {
        [goalId]: {
          1: 'Warning 1',
        },
      },
    };
    const warningStore = {
      ...clientStore,
      healthScore: warningHealthScore,
    };

    wrapper = mount(GoalsHeader, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false,
            initialState: {
              client: warningStore,
            },
          }),
        ],
      },
    });

    expect(wrapper.text()).toContain('1 warning');
  });

  it('displays error message when goal has errors', () => {
    const errorHealthScore = {
      ...healthScore,
      errors: {
        [goalId]: 'Critical error occurred',
      },
    };
    const errorStore = {
      ...clientStore,
      healthScore: errorHealthScore,
    };

    wrapper = mount(GoalsHeader, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false,
            initialState: {
              client: errorStore,
            },
          }),
        ],
      },
    });

    expect(wrapper.text()).toContain('Critical error occurred');
  });
});
