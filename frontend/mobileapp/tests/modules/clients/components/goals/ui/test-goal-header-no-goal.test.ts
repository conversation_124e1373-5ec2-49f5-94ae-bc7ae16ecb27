import { ref } from 'vue';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import GoalsHeader from '@/modules/clients/components/goals/headers/GoalsHeader.vue';
import { GoalId } from '@aventur-shared/modules/goals';
import {
  createClient,
  createClientGoal,
  createClientStore,
  createHealthScore,
} from '@tests/helpers';

const client = createClient();
const goalId = 1 as GoalId;
const clientGoal = createClientGoal({ id: goalId, clientIds: [client.id] });
const healthScore = createHealthScore([clientGoal]);
const clientStore = {
  ...createClientStore(client),
  goals: [clientGoal],
  healthScore,
};

describe('GoalsHeader.vue without current goal', () => {
  let wrapper;

  beforeEach(() => {
    // Mock vue-router
    vi.mock('vue-router', () => ({
      useRoute: () => ({
        params: { goalId: '1' },
      }),
    }));

    // Set up mock for useCurrentGoal with null
    vi.mock(
      '@modules/clients/components/goals/composables/use-current-goal',
      () => ({
        useCurrentGoal: () => ({
          currentGoal: ref(null),
        }),
      }),
    );
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('renders overall financial score', () => {
    const overallHealthScore = {
      overallScore: 800,
      goalScores: {},
      errors: {},
      warnings: {},
    };
    const noGoalStore = {
      ...clientStore,
      healthScore: overallHealthScore,
    };

    wrapper = mount(GoalsHeader, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false,
            initialState: {
              client: noGoalStore,
            },
          }),
        ],
      },
    });

    expect(wrapper.text()).toContain('Your financial score');
    expect(wrapper.text()).toContain('800');
    expect(wrapper.text()).toContain('out of 1000');
  });

  it('shows zero score when no health score is available', () => {
    const noScoreStore = {
      ...clientStore,
      healthScore: null,
    };

    wrapper = mount(GoalsHeader, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            stubActions: false,
            initialState: {
              client: noScoreStore,
            },
          }),
        ],
      },
    });

    expect(wrapper.text()).toContain('Your financial score');
    expect(wrapper.text()).toContain('0');
    expect(wrapper.text()).toContain('out of 1000');
  });
});
