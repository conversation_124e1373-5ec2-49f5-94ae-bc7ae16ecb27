import { ref } from 'vue';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { vMaska } from 'maska/vue';

import { apiClient } from '@aventur-shared/services/api';
import { CaseId } from '@aventur-shared/modules/cases';
import { GoalId } from '@aventur-shared/modules/goals';

import {
  createClient,
  createClientGoal,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import GoalItem from '@modules/clients/components/goals/ui/GoalItem.vue';
import { activeHoldingsInjectionKey } from '@modules/clients/components/goals/active-holdings';
//

// Mock ResizeObserver so the modal is able to be 'displayed' in the test
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

vi.spyOn(apiClient, 'get').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'patch').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'remove').mockResolvedValue(undefined);

const client = createClient();

const goalId = 1 as GoalId;
const clientGoal = createClientGoal({
  id: goalId,
  clientIds: [client.id],
  cases: [1 as CaseId],
});
const clientStore = { ...createClientStore(client), goals: [clientGoal] };

function makeWrapper() {
  return mount(GoalItem, {
    props: {
      client: client,
      clientId: client.id,
    },
    global: {
      directives: {
        maska: vMaska,
      },
      provide: {
        [activeHoldingsInjectionKey]: ref([
          ...clientGoal.linkedHoldings,
          {
            id: 2,
            productTypeName: 'Type 2',
            providerName: 'Provider 2',
            accountNumber: '999-99-99',
          },
        ]),
      },
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            user: createUserStore({ id: client.id }),
            refdata: createRefDataStore(),
            client: clientStore,
          },
        }),
      ],
    },
  });
}

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => {
    return {
      params: {
        id: goalId,
      },
    };
  }),
  useRouter: vi.fn(),
  onBeforeRouteLeave: vi.fn(),
}));

describe('GoalItem.vue', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Renders correct goal item markup', async () => {
    const wrapper = makeWrapper();

    // Form - name
    expect(wrapper.find('label[for=goalName]')).to.exist;
    expect(wrapper.find('label[for=goalName]').text()).to.eq('Goal name');
    expect(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      wrapper.find('input[name=goalName]').element.value,
    ).to.eq(clientGoal.name);

    // Form - clients
    expect(wrapper.find('label[for=clientIds]')).to.exist;
    expect(wrapper.find('label[for=clientIds]').text()).to.eq('Clients');

    // Form - attributes.targetAmount
    expect(wrapper.find('label[for="attributes.targetAmount"]')).to.exist;
    expect(wrapper.find('label[for="attributes.targetAmount"]').text()).to.eq(
      'Target amount, £',
    );
    expect(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      wrapper.find('input[name="attributes.targetAmount"]').element.value,
    ).to.eq(clientGoal.attributes.targetAmount?.toString());

    // Form - attributes.targetDate
    expect(wrapper.find('label[for="attributes.targetDate"]')).to.exist;
    expect(wrapper.find('label[for="attributes.targetDate"]').text()).to.eq(
      'Target date',
    );
    expect(
      wrapper
        .find('input[name="attributes.targetDate"]')
        .element.getAttribute('value'),
    ).to.eq(clientGoal.attributes.targetDate);

    // Objectives
    expect(wrapper.findElementByText('p', 'Goal objectives')).to.exist;
    expect(wrapper.findElementByText('p', clientGoal.objectives as string)).to
      .exist;

    // Link accounts
    expect(wrapper.findByTestId('goal-item-accounts')).to.exist;
    expect(
      wrapper.findByTestId('goal-item-accounts').findAll('ul > li').length,
    ).to.eq(2);
    expect(
      wrapper
        .findByTestId('goal-item-accounts')
        .findAll('ul > li')[0]
        .findAll('p')[0]
        .text(),
    ).to.eq(
      `${clientGoal.linkedHoldings[0].providerName} - ${clientGoal.linkedHoldings[0].productTypeName}`,
    );
    expect(
      wrapper
        .findByTestId('goal-item-accounts')
        .findAll('ul > li')[0]
        .findAll('p')[1]
        .text(),
    ).to.eq(clientGoal.linkedHoldings[0].accountNumber);
    expect(
      wrapper
        .findByTestId('goal-item-accounts')
        .findAll('ul > li')[0]
        .find('button')
        .text(),
    ).to.eq('Unlink');

    expect(
      wrapper
        .findByTestId('goal-item-accounts')
        .findAll('ul > li')[1]
        .findAll('p')[0]
        .text(),
    ).to.eq('Provider 2 - Type 2');
    expect(
      wrapper
        .findByTestId('goal-item-accounts')
        .findAll('ul > li')[1]
        .findAll('p')[1]
        .text(),
    ).to.eq('999-99-99');
    expect(
      wrapper
        .findByTestId('goal-item-accounts')
        .findAll('ul > li')[1]
        .find('button')
        .text(),
    ).to.eq('Link');

    // Footer
    expect(wrapper.findByTestId('goal-item-footer')).to.exist;
    expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
      /Clients:\s+(1)/,
    );
    expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
      /Open cases:\s+(1)/,
    );
    expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
      /Linked assets:\s+(1)/,
    );
    expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
      /Risk profile updated:\s+(N\/A)/,
    );
    expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
      /Cash forecast updated:\s+(N\/A)/,
    );

    expect(wrapper.findElementByText('button', 'Delete goal')).to.exist;
  });

  it('Makes correct API calls on delete goal', async () => {
    const wrapper = makeWrapper();

    await wrapper.findElementByText('button', 'Delete goal').trigger('click');
    await flushPromises();

    expect(apiClient.remove).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/goals/${clientGoal.id}`,
    );
    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenNthCalledWith(
      1,
      `/api/v1/clients/${client.id}/factfind/goals`,
    );
    expect(apiClient.get).toHaveBeenNthCalledWith(
      2,
      `/api/v2/clients/${client.id}/health-score`,
    );
  });

  it('Makes correct API calls on account link', async () => {
    const wrapper = makeWrapper();

    // Link accounts
    wrapper
      .findByTestId('goal-item-accounts')
      .findAll('ul > li')[1]
      .find('button')
      .trigger('click');
    await flushPromises();

    expect(apiClient.patch).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/goals/${clientGoal.id}/holdings`,
      {
        holding_ids: [1, 2],
      },
    );
    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenNthCalledWith(
      1,
      `/api/v1/clients/${client.id}/factfind/goals`,
    );
    expect(apiClient.get).toHaveBeenNthCalledWith(
      2,
      `/api/v2/clients/${client.id}/health-score`,
    );
  });

  it('Makes correct API calls on account unlink', async () => {
    const wrapper = makeWrapper();

    // Link accounts
    wrapper
      .findByTestId('goal-item-accounts')
      .findAll('ul > li')[0]
      .find('button')
      .trigger('click');
    await flushPromises();

    expect(apiClient.patch).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/goals/${clientGoal.id}/holdings`,
      {
        holding_ids: [],
      },
    );
    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenNthCalledWith(
      1,
      `/api/v1/clients/${client.id}/factfind/goals`,
    );
    expect(apiClient.get).toHaveBeenNthCalledWith(
      2,
      `/api/v2/clients/${client.id}/health-score`,
    );
  });
});
