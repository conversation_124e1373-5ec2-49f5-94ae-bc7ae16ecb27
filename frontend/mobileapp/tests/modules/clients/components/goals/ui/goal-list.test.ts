import { find } from 'lodash';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { VueWrapper, flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { GoalId } from '@aventur-shared/modules/goals';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';

import {
  createClient,
  createClientGoal,
  createClientStore,
  createHealthScore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';
import GoalTypeGroup from '@modules/clients/components/goals/ui/GoalTypeGroup.vue';
import GoalList from '@modules/clients/components/goals/ui/GoalList.vue';
import GoalListItem from '@modules/clients/components/goals/ui/GoalListItem.vue';
//

const client = createClient();

const goalId = 1 as GoalId;
const clientGoal = createClientGoal({ id: goalId, clientIds: [client.id] });
const healthScore = createHealthScore([clientGoal]);
const clientStore = {
  ...createClientStore(client),
  goals: [clientGoal],
  healthScore,
};
const refDataStore = createRefDataStore();

function makeWrapper() {
  return mount(GoalList, {
    props: {
      client: client,
      groups: refDataStore.goals,
      goals: clientStore.goals,
      activeHoldings: [],
    },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {
        GoalList,
      },
    },
  });
}

describe('GoalList', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders correct list UI', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findAllComponents(GoalTypeGroup).length).toEqual(3);
    wrapper.findAllComponents(GoalTypeGroup).forEach((component, index) => {
      const refGoal = refDataStore.goals[index];

      expect(component.vm.$props.groupId).to.eq(refGoal.id);
      expect(component.find('h3').text()).toContain(refGoal.name);

      // should see 1 goal in BuildWealth group, 0 for the other groups
      if (refGoal.id === ClientGoalTypeEnum.BuildWealth) {
        expect(component.find('[data-group-total-items]').text()).to.eq(
          '1 goal',
        );
      }
      if (
        [
          ClientGoalTypeEnum.EmergencyFund,
          ClientGoalTypeEnum.Retirement,
        ].includes(refGoal.id)
      ) {
        expect(component.find('[data-group-total-items]').text()).to.eq(
          '0 goals',
        );
      }
    });
  });

  it('renders correct list item UI', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    const group = find(
      wrapper.findAllComponents(GoalTypeGroup),
      (component) =>
        component.vm.$props.groupId === ClientGoalTypeEnum.BuildWealth,
    ) as VueWrapper;

    await group.find('h3').trigger('click');
    await flushPromises();

    expect(group.findComponent(GoalListItem)).to.exist;
    expect(group.findComponent(GoalListItem).find('h1').text()).to.eq(
      clientGoal.name,
    );
    expect(group.findComponent(GoalListItem).text()).to.match(
      new RegExp(
        `Amount:\\s+${formatWithCurrency(new Money(clientGoal.attributes.targetAmount as number))}\\s+Date:\\s+${new DateTime(clientGoal.attributes.targetDate).formatToView()}`,
      ),
    );

    const donut = group.find('[data-testid="score-donut"]');
    expect(donut.exists()).to.be.true;
    expect(donut.text()).toBe(healthScore.goalScores[1].score.toString());
  });

  it('navigates to goal details', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    const group = find(
      wrapper.findAllComponents(GoalTypeGroup),
      (component) =>
        component.vm.$props.groupId === ClientGoalTypeEnum.BuildWealth,
    ) as VueWrapper;

    await group.find('h3').trigger('click');
    await flushPromises();

    expect(
      group.findComponent(GoalListItem).find('a').element.getAttribute('href'),
    ).to.eq(`/goals/${clientGoal.id}`);
  });
});
