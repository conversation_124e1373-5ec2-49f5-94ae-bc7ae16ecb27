import { pick } from 'lodash';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { apiClient } from '@aventur-shared/services/api';

import {
  createClient,
  createClientStore,
  createFactfindStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import AboutYouForm from '@/modules/clients/components/profile/personal-details/aboutyou/AboutYouForm.vue';
import _form from '@/modules/clients/components/profile/personal-details/aboutyou/form/_form.vue';
import { profilePostStub } from '../stubs';
//

vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'get').mockResolvedValue(undefined);

const mockToastSuccess = vi.fn();
vi.mock('@aventur-shared/composables/useToast', () => ({
  useToast: () => ({
    info: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    success: mockToastSuccess,
  }),
}));

const client = createClient({
  dateOfBirth: new Date(1970, 0, 1),
});

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const factFindStore = createFactfindStore(client);

const data = profilePostStub(client);

function makeWrapper() {
  return mount(AboutYouForm, {
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            factfind: factFindStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

describe('Profile - AboutYou Form', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with the correct data', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findElementByText('label', 'Title')).to.exist;
    expect(wrapper.findElementByText('label', 'First name')).to.exist;
    expect(wrapper.findElementByText('label', 'Last name')).to.exist;
    expect(wrapper.findElementByText('label', 'Gender')).to.exist;
    expect(wrapper.findElementByText('label', 'Date of birth')).to.exist;
    expect(wrapper.findElementByText('label', 'Marital status')).to.exist;
    expect(wrapper.findElementByText('label', 'Nationality')).to.exist;
    expect(wrapper.findElementByText('label', 'Country of birth')).to.exist;
    expect(
      wrapper.findElementByText(
        'label',
        'Country of citizenship / first passport',
      ),
    ).to.exist;
    expect(wrapper.findElementByText('label', 'Dual citizenship (if any)')).to
      .exist;
    expect(
      wrapper.findElementByText(
        'label',
        'Will you qualify for full state pension at retirement age?',
      ),
    ).to.exist;
    expect(wrapper.findElementByText('label', 'Are you already retired?')).to
      .exist;
    expect(
      wrapper.findElementByText(
        'label',
        'Do you have previous investment experience?',
      ),
    ).to.exist;
    expect(
      wrapper.findElementByText(
        'label',
        'Have you experienced financial advice before?',
      ),
    ).to.exist;
    expect(
      wrapper.findElementByText(
        'label',
        'Do you wish to consider ethical investments?',
      ),
    ).to.exist;
    expect(
      wrapper.findElementByText(
        'label',
        'Does your religious views restrict your investment options?',
      ),
    ).to.exist;
    expect(wrapper.findElementByText('label', 'Are you a vulnerable person?'))
      .to.exist;
    expect(wrapper.findElementByText('label', 'Do you have a will in place?'))
      .to.exist;
    expect(wrapper.findElementByText('label', 'Power of attorney in place?')).to
      .exist;
    expect(wrapper.findElementByText('label', 'Credit history?')).to.exist;
    expect(wrapper.findElementByText('label', 'Employment status')).to.exist;
    expect(wrapper.findElementByText('label', 'National Insurance number')).to
      .exist;
  });

  it('makes correct API calls on submit', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findByTestId('submit-button').element.disabled).toBeFalsy();

    wrapper
      .findComponent(_form)
      .vm.$emit(
        'on-update',
        pick(factFindStore.primaryDetails, [
          'personalDetails',
          'furtherInformations',
          'retirementDetails',
        ]),
      );

    await flushPromises();

    expect(apiClient.post).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
      { ...data },
    );
    expect(mockToastSuccess).toHaveBeenCalledExactlyOnceWith(
      `Your details have been updated`,
    );
  });
});
