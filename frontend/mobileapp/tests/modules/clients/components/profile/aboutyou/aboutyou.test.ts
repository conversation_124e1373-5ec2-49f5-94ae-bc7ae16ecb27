import { pick } from 'lodash';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { DOMWrapper, flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import {
  createClient,
  createClientStore,
  createFactfindStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import AboutYouPreview from '@/modules/clients/components/profile/personal-details/AboutYouPreview.vue';
import { AboutYou } from '@aventur-shared/modules/factfind';
//

const client = createClient({
  dateOfBirth: new Date(1970, 0, 1),
});

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const factFindStore = createFactfindStore(client);

function makeWrapper(
  data: Pick<
    AboutYou,
    'personalDetails' | 'furtherInformations' | 'retirementDetails'
  >,
) {
  return mount(AboutYouPreview, {
    props: { data },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            factfind: factFindStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

const lastChild = (parentWrapper: DOMWrapper<Node>): Node | null =>
  parentWrapper?.element?.parentElement?.lastChild ?? null;

describe('Profile - AboutYou', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with the correct data', async () => {
    const wrapper = makeWrapper(
      pick(factFindStore.primaryDetails, [
        'personalDetails',
        'furtherInformations',
        'retirementDetails',
      ]),
    );
    await flushPromises();

    let element: DOMWrapper<Node> | null;

    // Personal Details
    expect(wrapper.findElementByText('h3', 'Personal Details')).to.exist;

    // first name
    expect(wrapper.findElementByText('dt', 'First name')).to.exist;
    expect(wrapper.findElementByText('dd', client.firstName)).to.exist;

    // last name
    expect(wrapper.findElementByText('dt', 'Last name')).to.exist;
    expect(wrapper.findElementByText('dd', client.lastName as string)).to.exist;

    // dob
    expect(wrapper.findElementByText('dt', 'Date of birth')).to.exist;
    expect(wrapper.findElementByText('dd', '1 Jan 1970')).to.exist;

    // title
    expect(wrapper.findElementByText('dt', 'Title')).to.exist;
    expect(wrapper.findElementByText('dd', 'Mr')).to.exist;

    // gender
    expect(wrapper.findElementByText('dt', 'Gender')).to.exist;
    expect(wrapper.findElementByText('dd', 'Male')).to.exist;

    // marital status
    expect(wrapper.findElementByText('dt', 'Marital status')).to.exist;
    expect(wrapper.findElementByText('dd', 'Married')).to.exist;

    // nationality
    expect(wrapper.findElementByText('dt', 'Nationality')).to.exist;
    expect(wrapper.findElementByText('dd', 'British')).to.exist;

    // country of birth
    expect(wrapper.findElementByText('dt', 'Country of birth')).to.exist;
    expect(wrapper.findElementByText('dd', 'United Kingdom')).to.exist;

    // country of citizenship
    expect(wrapper.findElementByText('dt', 'Country of citizenship')).to.exist;
    expect(wrapper.findElementByText('dd', 'Reunion')).to.exist;

    // dual citizenship
    expect(wrapper.findElementByText('dt', 'Dual citizenship')).to.exist;
    expect(wrapper.findElementByText('dd', 'N/A')).to.exist;

    // Retirement Details
    expect(wrapper.findElementByText('h3', 'Retirement Details')).to.exist;

    element = wrapper.findElementByText(
      'span',
      'Will you qualify for full state pension at retirement age?',
    );
    expect(element).to.exist;
    expect(element?.element.parentElement?.lastChild?.textContent).to.eq('Yes');

    element = wrapper.findElementByText('span', 'Are you already retired?');
    expect(element).to.exist;
    expect(element?.element.parentElement?.lastChild?.textContent).to.eq('-');

    // Further Information
    expect(wrapper.findElementByText('h3', 'Further Information')).to.exist;

    element = wrapper.findElementByText(
      'span',
      'Have you experienced financial advice before?',
    );
    expect(element).to.exist;
    expect(element?.element.parentElement?.lastChild?.textContent).to.eq('Yes');

    element = wrapper.findElementByText(
      'span',
      'Do you have previous investment experience?',
    );
    expect(element).to.exist;
    expect(lastChild(element)?.textContent).to.eq('High');

    element = wrapper.findElementByText(
      'span',
      'Do you wish to consider ethical investments?',
    );
    expect(element).to.exist;
    expect(lastChild(element)?.textContent).to.eq('No');

    element = wrapper.findElementByText(
      'span',
      'Does your religious views restrict your investment options?',
    );
    expect(element).to.exist;
    expect(lastChild(element)?.textContent).to.eq('No');

    element = wrapper.findElementByText('span', 'Are you a vulnerable person?');
    expect(element).to.exist;
    expect(lastChild(element)?.textContent).to.eq('-');

    element = wrapper.findElementByText('span', 'Do you have a will in place?');
    expect(element).to.exist;
    expect(lastChild(element)?.textContent).to.eq('Needs updating');

    element = wrapper.findElementByText('span', 'Credit history?');
    expect(element).to.exist;
    expect(lastChild(element)?.textContent).to.eq('Good');

    element = wrapper.findElementByText('span', 'Employment status');
    expect(element).to.exist;
    expect(lastChild(element)?.textContent).to.eq('Employed');

    element = wrapper.findElementByText('span', 'National Insurance number');
    expect(element).to.exist;
    expect(lastChild(element)?.textContent).to.eq('NI000000X');
  });
});
