import { filter, find, get } from 'lodash';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { Address } from '@aventur-shared/modules/factfind';

import {
  createClient,
  createClientStore,
  createFactfindStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import AddressDetailsPreview from '@/modules/clients/components/profile/personal-details/AddressDetailsPreview.vue';
//

const client = createClient({
  dateOfBirth: new Date(1970, 0, 1),
});

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const factFindStore = createFactfindStore(client);

const addresses = get(factFindStore, 'primaryDetails.contactDetails.addresses');

function makeWrapper(addresses: Address[]) {
  return mount(AddressDetailsPreview, {
    props: { addresses },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            factfind: factFindStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

const getCountryById = (id: number) => find(refDataStore.countries, { id });

describe('Profile - Addresses', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with the correct data', async () => {
    const wrapper = makeWrapper(addresses);
    await flushPromises();

    expect(wrapper.findElementByText('h3', 'Addresses')).to.exist;
    expect(wrapper.findAll('[data-testid=address-row]')).to.have.length(1);

    expect(wrapper.findElementByText('span', 'Primary')).to.exist;
    expect(
      wrapper.findElementByText(
        'span',
        filter([
          addresses[0].addressLineOne,
          addresses[0].addressLineTwo,
          addresses[0].addressLineThree,
          addresses[0].addressLineFour,
        ]).join(', '),
      ),
    ).to.exist;
    expect(
      wrapper.findElementByText(
        'p',
        [
          addresses[0].postCode,
          getCountryById(addresses[0].countryId)!.name,
        ].join(', '),
      ),
    ).to.exist;
  });
});
