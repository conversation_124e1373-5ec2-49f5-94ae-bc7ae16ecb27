import { get } from 'lodash';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { useFactfindStore } from '@aventur-shared/modules/factfind';
import { apiClient } from '@aventur-shared/services/api';
import { factory as dateTimeFactory } from '@aventur-shared/utils/dateTime';

import {
  createClient,
  createClientStore,
  createFactfindStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';

import AddressDetailsForm from '@/modules/clients/components/profile/personal-details/addresses/AddressForm.vue';
import _form from '@/modules/clients/components/profile/personal-details/addresses/form/_form.vue';
import { profilePostStub } from '../stubs';
//

vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'get');

const mockToastSuccess = vi.fn();
vi.mock('@aventur-shared/composables/useToast', () => ({
  useToast: () => ({
    info: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    success: mockToastSuccess,
  }),
}));

vi.mock('@capacitor/dialog', () => ({
  Dialog: {
    alert: vi.fn,
    prompt: vi.fn,
    confirm: () => Promise.resolve({ value: true }),
  },
}));

vi.mock('vue-router', () => ({
  useRouter: vi.fn,
  useRoute: () => ({
    params: { id: '1' },
  }),
}));

const client = createClient({
  dateOfBirth: new Date(1970, 0, 1),
});

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const factFindStore = createFactfindStore(client);

const data = profilePostStub(client);

function makeWrapper() {
  return mount(AddressDetailsForm, {
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            factfind: factFindStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

describe('Profile - Address Form', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with the correct data', async () => {
    const address = get(
      factFindStore,
      'primaryDetails.contactDetails.addresses[0]',
    );

    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findElementByText('label', 'Address Line 1')).to.exist;
    expect(
      (wrapper.find('input[name=addressLineOne]').element as HTMLInputElement)
        .value,
    ).to.eq(address.addressLineOne);

    expect(wrapper.findElementByText('label', 'Address Line 2')).to.exist;
    expect(
      (wrapper.find('input[name=addressLineTwo]').element as HTMLInputElement)
        .value,
    ).to.be.empty;

    expect(wrapper.findElementByText('label', 'Address Line 3')).to.exist;
    expect(
      (wrapper.find('input[name=addressLineThree]').element as HTMLInputElement)
        .value,
    ).to.be.empty;

    expect(wrapper.findElementByText('label', 'Address Line 4')).to.exist;
    expect(
      (wrapper.find('input[name=addressLineFour]').element as HTMLInputElement)
        .value,
    ).to.be.empty;

    expect(wrapper.findElementByText('label', 'Postcode')).to.exist;
    expect(
      (wrapper.find('input[name=postCode]').element as HTMLInputElement).value,
    ).to.eq(address.postCode);

    expect(wrapper.findElementByText('label', 'City')).to.exist;
    expect(
      (wrapper.find('input[name=city]').element as HTMLInputElement).value,
    ).to.eq('');

    expect(wrapper.findElementByText('label', 'Country')).to.exist;

    expect(wrapper.findElementByText('label', 'Move in date')).to.exist;
    expect(
      (wrapper.find('input[name=moveInDate]').element as HTMLInputElement)
        .value,
    ).to.eq('');

    expect(wrapper.findElementByText('label', 'Move out date')).to.exist;
    expect(
      (wrapper.find('input[name=moveOutDate]').element as HTMLInputElement)
        .value,
    ).to.eq('');
  });

  it('makes correct API call on update', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const address = wrapper.vm.address;
    wrapper.findComponent(_form).vm.$emit('on-update', {
      ...address,
      addressLineOne: 'Line 1',
      addressLineTwo: 'Line 2',
      postCode: 'postcode',
      city: 'city',
      countryId: 2,
      moveInDate: dateTimeFactory(new Date(2020, 5, 20)),
    });
    await flushPromises();

    expect(apiClient.post).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
      {
        ...data,
        addresses: [
          {
            id: 1,
            address_line_one: 'Line 1',
            address_line_two: 'Line 2',
            address_line_three: null,
            address_line_four: null,
            post_code: 'postcode',
            city: 'city',
            country_id: 2,
            is_primary: true,
            moved_in_date: '2020-06-20',
            moved_out_date: null,
          },
        ],
      },
    );
    expect(mockToastSuccess).toHaveBeenCalledExactlyOnceWith(
      `Your details have been updated`,
    );

    const { primaryDetails: primaryDetailsAfter, getClientPrimaryDetails } =
      useFactfindStore();

    expect(primaryDetailsAfter.contactDetails.addresses).to.have.length(1);
    expect(getClientPrimaryDetails).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
    );
  });

  it('makes correct API call on add', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    wrapper.findComponent(_form).vm.$emit('on-add', {
      addressLineOne: '1 High St',
      addressLineTwo: null,
      addressLineThree: null,
      addressLineFour: null,
      postCode: 'LN1 1XX',
      city: 'London',
      countryId: 1,
      isPrimary: false,
      moveInDate: dateTimeFactory(new Date(2025, 0, 1)),
      moveOutDate: null,
    });
    await flushPromises();

    expect(apiClient.post).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
      {
        ...data,
        addresses: [
          {
            id: 1,
            address_line_one: '1 Test Street',
            address_line_two: null,
            address_line_three: null,
            address_line_four: null,
            post_code: 'TE1 1ST',
            city: null,
            country_id: 1,
            is_primary: true,
            moved_in_date: null,
            moved_out_date: null,
          },
          {
            id: undefined,
            address_line_one: '1 High St',
            address_line_two: null,
            address_line_three: null,
            address_line_four: null,
            post_code: 'LN1 1XX',
            city: 'London',
            country_id: 1,
            is_primary: false,
            moved_in_date: '2025-01-01',
            moved_out_date: null,
          },
        ],
      },
    );
    expect(mockToastSuccess).toHaveBeenCalledExactlyOnceWith(
      `Your details have been updated`,
    );

    const { getClientPrimaryDetails } = useFactfindStore();

    expect(getClientPrimaryDetails).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
    );
  });

  it('makes correct API call on delete', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const address = wrapper.vm.address;

    wrapper.findComponent(_form).vm.$emit('on-delete', address);
    await flushPromises();

    expect(apiClient.post).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
      {
        ...data,
        addresses: [],
      },
    );
    expect(mockToastSuccess).toHaveBeenCalledExactlyOnceWith(
      `Your details have been updated`,
    );

    const { getClientPrimaryDetails } = useFactfindStore();

    expect(getClientPrimaryDetails).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
    );
  });
});
