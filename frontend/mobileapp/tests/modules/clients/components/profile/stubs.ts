import { Client } from '@aventur-shared/modules/clients';
//

export const profilePostStub = (client: Client) => ({
  first_name: client.firstName,
  last_name: client.lastName,
  gender_id: 1,
  marital_status_id: 2,
  nationality_id: 1,
  mobile_number: null,
  phone_number: null,
  primary_country_id: 2,
  relations: [
    {
      id: 1,
      date_of_birth: '2000-01-01',
      first_name: 'First Name',
      last_name: 'Last Name',
      relationship_type: 'Child',
    },
  ],
  secondary_country_id: null,
  title_id: 1,
  addresses: [
    {
      address_line_four: null,
      address_line_one: '1 Test Street',
      address_line_three: null,
      address_line_two: null,
      city: null,
      country_id: 1,
      id: 1,
      is_primary: true,
      moved_in_date: null,
      moved_out_date: null,
      post_code: 'TE1 1ST',
    },
  ],
  birth_country_id: 1,
  date_of_birth: '1970-01-01',
  email_address: client.email,
  factfind: {
    already_retired: null,
    credit_history: 'Good',
    employment_status: 'Employed',
    has_experienced_financial_advice_before: true,
    has_power_of_attorney_in_place: null,
    has_will_in_place: 'Needs updating',
    monthly_retirement_income_required: null,
    ni_number: 'NI000000X',
    previous_investment_experience: 'High',
    religious_restrictions: false,
    retirement_age: null,
    state_pension: true,
    vulnerable_person: null,
    wish_to_consider_ethical_investments: false,
  },
});
