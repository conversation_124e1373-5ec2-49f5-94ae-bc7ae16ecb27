import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import {
  createCashflowStore,
  createClient,
  createClientExpenditure,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
import { getMonthlyAmount } from '@aventur-shared/modules/factfind/services/frequency-amount';
import {
  Expenditure,
  Frequency,
  FrequencyEnum,
} from '@aventur-shared/modules/factfind';

import ExpendituresList from '@modules/clients/components/profile/expenditures/ExpendituresList.vue';
import ExpendituresGroup from '@modules/clients/components/profile/expenditures/ExpendituresGroup.vue';
import ExpendituresListItem from '@modules/clients/components/profile/expenditures/ExpendituresListItem.vue';
//

const client = createClient();
const expenditures = [
  createClientExpenditure({
    typeGroup: 12, // Expenditures - General
    type: 36, // Food - Essential
    amount: new Money(250),
    frequency: new Frequency(FrequencyEnum.Weekly),
  }),
];

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const cashflowStore = createCashflowStore();

function makeWrapper() {
  return mount(ExpendituresList, {
    props: {
      expenditures,
    },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            'cashflow-store': cashflowStore,
            client: clientStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {
        ExpendituresGroup,
        ExpendituresListItem,
      },
    },
  });
}

describe('ExpendituresList.vue', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders correct list item UI', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    const groups = wrapper.findAllComponents(ExpendituresGroup);

    expect(groups).to.have.length(2);

    expect(
      groups[0].find('[data-expenditures-group]').find('h1').text(),
    ).toEqual('Household');
    expect(
      groups[0].find('[data-expenditures-group]').find('p').text(),
    ).toEqual('0 items');

    const group2monthlyTotal = expenditures.reduce(
      (sum: number, expenditure: Expenditure) =>
        getMonthlyAmount(expenditure.amount, expenditure.frequency).getValue() +
        sum,
      0,
    );
    expect(
      groups[1].find('[data-expenditures-group]').find('h1').text(),
    ).toEqual('General');
    expect(
      groups[1].find('[data-expenditures-group]').find('p').text(),
    ).toEqual('1 item');

    groups[1].find('[data-expenditures-group]').trigger('click');
    await flushPromises();

    expect(groups[1].find('[data-expenditures-group-summary]')).to.exist;
    expect(
      groups[1].find('[data-expenditures-group-summary]').find('dt').text(),
    ).toEqual('per month');
    expect(
      groups[1].find('[data-expenditures-group-summary]').find('dd').text(),
    ).toEqual(formatWithCurrency(new Money(group2monthlyTotal)));

    const items = groups[1].findAllComponents(ExpendituresListItem);
    expect(items).to.have.length(1);
    expect(items[0].findElementByText('p', 'Food - Essential')).to.exist;
    expect(items[0].findElementByText('p', 'Expenditure 1')).to.exist;
    expect(
      items[0].findElementByText(
        'p',
        `lock ${formatWithCurrency(new Money(250))}`,
      ),
    ).to.exist;
    expect(items[0].findElementByText('p', 'Weekly')).to.exist;
  });
});
