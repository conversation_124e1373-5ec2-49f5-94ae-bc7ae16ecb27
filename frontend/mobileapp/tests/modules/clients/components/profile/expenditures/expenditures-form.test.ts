import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTesting<PERSON>inia } from '@pinia/testing';
import { vMaska } from 'maska/vue';

import {
  createCashflowStore,
  createClient,
  createClientExpenditure,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';

import { apiClient } from '@aventur-shared/services/api';
import { Money } from '@aventur-shared/utils/money';
import * as useConfirmationMod from '@aventur-shared/composables/useConfirmation';

import ExpenditureForm from '@modules/clients/components/profile/expenditures/ExpenditureForm.vue';
import form from '@modules/clients/components/profile/expenditures/form/form.vue';
//

const client = createClient();
const expenditures = [createClientExpenditure()];

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const cashflowStore = createCashflowStore();

vi.spyOn(apiClient, 'put').mockResolvedValue(undefined);

vi.spyOn(useConfirmationMod, 'useConfirmation').mockResolvedValue({
  isAccepted: () => true,
  onAccept: vi.fn(),
  onCancel: vi.fn(),
});

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => ({
    params: {
      id: expenditures[0].id,
    },
  })),
  useRouter: vi.fn(),
  onBeforeRouteLeave: vi.fn(),
}));

const directives = {
  maska: vMaska,
};

function makeWrapper() {
  return mount(ExpenditureForm, {
    global: {
      directives,
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            'cashflow-store': cashflowStore,
            client: clientStore,
            factfind: {
              expenditures,
            },
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
    },
  });
}

describe('ExpendituresForm', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders correct form UI', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.expenditure.id).toEqual(expenditures[0].id);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.expenditure.typeGroup).toEqual(expenditures[0].typeGroup);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.expenditure.type).toEqual(expenditures[0].type);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.expenditure.description).toEqual(
      expenditures[0].description,
    );
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.expenditure.frequency).toEqual(expenditures[0].frequency);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.expenditure.amount).toEqual(expenditures[0].amount);

    expect(wrapper.find('form').exists()).toBe(true);

    // group
    expect(wrapper.find('label[for="typeGroup"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="typeGroup"]').text()).toEqual('Type Group');

    // type
    expect(wrapper.find('label[for="type"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="type"]').text()).toEqual('Type');

    // frequency
    expect(wrapper.find('label[for="frequency"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="frequency"]').text()).toEqual('Frequency');

    // amount
    expect(wrapper.find('label[for="amount"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="amount"]').text()).toEqual('Amount, £');
    expect(wrapper.find('input#amount').element.getAttribute('value')).toEqual(
      '2340',
    );

    expect(wrapper.find('label[for="isEssential"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="isEssential"]').text()).toEqual(
      'Essential',
    );

    expect(wrapper.findElementByText('button', 'Delete Expenditure')).to.exist;
  });

  it('makes correct API calls on add', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    const expenditure = createClientExpenditure();
    wrapper.findComponent(form).vm.$emit('on-add', {
      ...expenditure,
    });
    await flushPromises();

    expect(apiClient.put).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/expenditure`,
      [
        {
          id: expenditures[0].id,
          type_id: expenditures[0].type,
          description: expenditures[0].description,
          amount: expenditures[0].amount.getValue(),
          frequency: expenditures[0].frequency.toValue(),
          is_essential: true,
        },
        {
          id: expenditure.id,
          type_id: expenditure.type,
          description: expenditure.description,
          amount: expenditure.amount.getValue(),
          frequency: expenditure.frequency.toValue(),
          is_essential: true,
        },
      ],
    );
  });

  it('makes correct API calls on update', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    wrapper.findComponent(form).vm.$emit('on-update', {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      ...wrapper.vm.expenditure,
      description: 'New description',
      amount: new Money(2500),
    });
    await flushPromises();

    expect(apiClient.put).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/expenditure`,
      [
        {
          id: expenditures[0].id,
          type_id: expenditures[0].type,
          description: 'New description',
          amount: 2500,
          frequency: 'Monthly',
          is_essential: true,
        },
      ],
    );
  });

  it('makes correct API calls on delete', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    wrapper.findComponent(form).vm.$emit(
      'on-delete',
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      wrapper.vm.expenditure,
    );
    await flushPromises();

    expect(apiClient.put).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/expenditure`,
      [],
    );
  });
});
