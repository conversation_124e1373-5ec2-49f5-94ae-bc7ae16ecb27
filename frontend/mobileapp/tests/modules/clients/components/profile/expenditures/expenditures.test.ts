import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import {
  createCashflowStore,
  createClient,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import { apiClient } from '@aventur-shared/services/api';
import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
import { getMonthlyAmount } from '@aventur-shared/modules/factfind/services/frequency-amount';
import { Expenditure } from '@aventur-shared/modules/factfind';

import ExpendituresView from '@modules/clients/components/profile/expenditures/Expenditures.vue';
import ExpendituresList from '@modules/clients/components/profile/expenditures/ExpendituresList.vue';
import ExpendituresListItem from '@modules/clients/components/profile/expenditures/ExpendituresListItem.vue';
//

const client = createClient();

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const cashflowStore = createCashflowStore();

function makeWrapper() {
  return mount(ExpendituresView, {
    props: {},
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            'cashflow-store': cashflowStore,
            client: clientStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {
        ExpendituresList,
        ExpendituresListItem,
      },
    },
  });
}

const mockJarvisGetExpenditureResponse = [
  {
    id: 1,
    amount: 1500,
    frequency: 'Monthly',
    group_id: 7,
    description: 'Mortgage',
    type_id: 23,
    is_essential: true,
  },
  {
    id: 2,
    amount: 230,
    frequency: 'Weekly',
    group_id: 12,
    description: 'Food',
    type_id: 36,
    is_essential: true,
  },
];
vi.spyOn(apiClient, 'get').mockResolvedValue(mockJarvisGetExpenditureResponse);

describe('Profile - Expenditures ', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders view and makes API calls', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const totalOverall = wrapper.vm.expenditures.reduce(
      (sum: number, expenditure: Expenditure) =>
        getMonthlyAmount(expenditure.amount, expenditure.frequency).getValue() +
        sum,
      0,
    );

    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/expenditure`,
    );

    expect(
      wrapper.find('[data-expenditures-summary]').find('dt').text(),
    ).toEqual('Monthly Total');
    expect(
      wrapper.find('[data-expenditures-summary]').find('dd').text(),
    ).toEqual(formatWithCurrency(new Money(totalOverall)));
  });
});
