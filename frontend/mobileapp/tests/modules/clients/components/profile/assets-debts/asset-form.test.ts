import { nextTick } from 'vue';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { vMaska } from 'maska/vue';

import form from '@/modules/clients/components/profile/assets-debts/assets/form/form.vue';
//

const directives = {
  maska: vMaska,
};

vi.mock('@aventur-shared/stores/refdataStore', () => ({
  useRefData: () => ({
    getProviders: [],
    getAssets: [{ name: 'Asset Group', id: 1 }],
    getProductsByGroupId: () => [{ name: 'Asset Type', id: 2 }],
    getCountries: [],
    getRiskLevels: [],
  }),
}));

vi.mock('@aventur-shared/composables/useAPIState', () => ({
  useAPIState: () => ({
    APIState: { isLoading: false },
  }),
}));

// Mock useAssetIdentity dynamically to allow test-specific overrides
const mockIsAccountType = vi.fn().mockReturnValue(false);
const mockIsPropertyType = vi.fn().mockReturnValue(false);
const mockIsCompanySharesType = vi.fn().mockReturnValue(false);
const mockIsCryptoCurrencyType = vi.fn().mockReturnValue(false);
const mockIsOtherAssetType = vi.fn().mockReturnValue(false);
const mockIsTermPolicyType = vi.fn().mockReturnValue(false);
const mockIsIndemnityPolicyType = vi.fn().mockReturnValue(false);
const mockIsWholeOfLifePolicyType = vi.fn().mockReturnValue(false);
const mockIsIncomeProtectionPolicyType = vi.fn().mockReturnValue(false);
const mockIsDefinedBenefitPensionType = vi.fn().mockReturnValue(false);

vi.mock(
  '@/modules/clients/components/profile/assets-debts/assets/form/use-asset-identity',
  () => ({
    useAssetIdentity: () => ({
      isAccountType: mockIsAccountType,
      isPropertyType: mockIsPropertyType,
      isCompanySharesType: mockIsCompanySharesType,
      isCryptoCurrencyType: mockIsCryptoCurrencyType,
      isOtherAssetType: mockIsOtherAssetType,
      isTermPolicyType: mockIsTermPolicyType,
      isIndemnityPolicyType: mockIsIndemnityPolicyType,
      isWholeOfLifePolicyType: mockIsWholeOfLifePolicyType,
      isIncomeProtectionPolicyType: mockIsIncomeProtectionPolicyType,
      isDefinedBenefitPensionType: mockIsDefinedBenefitPensionType,
    }),
  }),
);

vi.mock('@aventur-shared/composables/useValuation', () => ({
  useValuationProcess: () => ({
    addingValuationToExistingItem: vi.fn(),
    addingValuationToNewElement: vi.fn(),
    addValuation: vi.fn(),
  }),
}));

vi.mock('@aventur-shared/utils/valuation/factory', () => ({
  factory: vi.fn(),
}));

describe('form', () => {
  afterEach(() => {
    mockIsAccountType.mockReturnValue(false);
    mockIsPropertyType.mockReturnValue(false);
    mockIsCompanySharesType.mockReturnValue(false);
    mockIsCryptoCurrencyType.mockReturnValue(false);
    mockIsOtherAssetType.mockReturnValue(false);
    mockIsTermPolicyType.mockReturnValue(false);
    mockIsIndemnityPolicyType.mockReturnValue(false);
    mockIsWholeOfLifePolicyType.mockReturnValue(false);
    mockIsIncomeProtectionPolicyType.mockReturnValue(false);
    mockIsDefinedBenefitPensionType.mockReturnValue(false);
  });

  it('renders the form correctly', async () => {
    const wrapper = mount(form, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });

    expect(wrapper.find('[data-testid="asset-form"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="group-select"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="type-select"]').exists()).toBe(true);
  });

  it('type select has disabled attribute when needed', async () => {
    const wrapper = mount(form, {
      global: {
        directives,
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    wrapper.vm.values.groupId = undefined;
    await nextTick();

    const typeSelect = wrapper.find('[data-testid="type-select"]');
    expect(typeSelect.exists()).toBe(true);

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const condition = !wrapper.vm.values.groupId || wrapper.vm.isEditForm;
    expect(condition).toBe(true);
  });

  it('calls handleSubmit when form is submitted', async () => {
    const wrapper = mount(form, {
      global: {
        directives,
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });

    const handleSubmitSpy = vi.fn();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    wrapper.vm.onSubmit = handleSubmitSpy;

    await wrapper.find('[data-testid="asset-form"]').trigger('submit');

    expect(handleSubmitSpy).toHaveBeenCalled();
  });

  it('shows delete button in edit mode', async () => {
    const wrapper = mount(form, {
      global: {
        directives,
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
      props: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        asset: {
          key: '123',
          groupId: 1,
          typeId: 2,
          clientIds: [],
        },
      },
    });

    expect(wrapper.find('[data-testid="delete-button"]').exists()).toBe(true);
  });

  it('emits delete event when handleDelete is called', async () => {
    const wrapper = mount(form, {
      global: {
        directives,
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
      props: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        asset: {
          key: '123',
          groupId: 1,
          typeId: 2,
          clientIds: [],
        },
      },
    });

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleDelete();

    expect(wrapper.emitted('on-delete')).toBeDefined();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.emitted('on-delete').length).toBe(1);
  });

  it('emits on-cancel event when cancel button is clicked', async () => {
    const wrapper = mount(form, {
      global: {
        directives,
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });

    await wrapper.find('[data-testid="cancel-button"]').trigger('click');

    expect(wrapper.emitted('on-cancel')).toBeDefined();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.emitted('on-cancel').length).toBe(1);
  });

  describe('Asset Form Input Display Tests', () => {
    const testCases = [
      {
        description: 'account type',
        mockFn: mockIsAccountType,
        visibleComponents: ['provider-select', 'valuation-amount-input'],
      },
      {
        description: 'property type',
        mockFn: mockIsPropertyType,
        visibleComponents: ['address-line-one-input', 'valuation-amount-input'],
      },
      {
        description: 'company shares type',
        mockFn: mockIsCompanySharesType,
        visibleComponents: ['name-of-company-input', 'valuation-amount-input'],
      },
      {
        description: 'crypto type',
        mockFn: mockIsCryptoCurrencyType,
        visibleComponents: ['name-of-currency-input', 'valuation-amount-input'],
      },
      {
        description: 'other asset type',
        mockFn: mockIsOtherAssetType,
        visibleComponents: ['name-of-asset-input', 'valuation-amount-input'],
      },
      {
        description: 'term policy type',
        mockFn: mockIsTermPolicyType,
        visibleComponents: ['term-policy-provider-select'],
      },
      {
        description: 'indemnity policy type',
        mockFn: mockIsIndemnityPolicyType,
        visibleComponents: ['indemnity-policy-provider-select'],
      },
      {
        description: 'whole of life policy type',
        mockFn: mockIsWholeOfLifePolicyType,
        visibleComponents: ['wol-policy-provider-select'],
      },
      {
        description: 'income protection policy type',
        mockFn: mockIsIncomeProtectionPolicyType,
        visibleComponents: ['income-policy-provider-select'],
      },
      {
        description: 'defined benefit pension type',
        mockFn: mockIsDefinedBenefitPensionType,
        visibleComponents: ['defined-benefit-pension-provider-select'],
      },
    ];

    const allComponents = [
      'address-line-one-input',
      'provider-select',
      'name-of-company-input',
      'name-of-currency-input',
      'name-of-asset-input',
      'term-policy-provider-select',
      'indemnity-policy-provider-select',
      'wol-policy-provider-select',
      'income-policy-provider-select',
      'valuation-amount-input',
      'defined-benefit-pension-provider-select',
    ];

    testCases.forEach(({ description, mockFn, visibleComponents }) => {
      it(`displays the correct fields when ${description} is selected`, async () => {
        mockFn.mockReturnValue(true);

        const wrapper = mount(form, {
          global: {
            directives,
            plugins: [createTestingPinia({ createSpy: vi.fn })],
          },
          props: {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            asset: {
              key: '123',
              groupId: 1,
              typeId: 2,
              clientIds: [],
            },
          },
        });

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        wrapper.vm.setTypeValue(2222);
        await wrapper.vm.$nextTick();

        // Check visible components
        visibleComponents.forEach((componentId) => {
          const component = wrapper.find(`[data-testid="${componentId}"]`);
          expect(component.exists()).toBe(true);
        });

        // Check hidden components
        allComponents
          .filter((componentId) => !visibleComponents.includes(componentId))
          .forEach((componentId) => {
            const component = wrapper.find(`[data-testid="${componentId}"]`);
            expect(component.exists()).toBe(false);
          });
      });
    });
  });

  it('has an onSubmit method that processes new assets', async () => {
    const wrapper = mount(form, {
      global: {
        directives,
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(typeof wrapper.vm.onSubmit).toBe('function');

    // Ensure we can access the function without errors
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(() => wrapper.vm.onSubmit).not.toThrow();
  });

  it('has an onSubmit method that processes existing assets', async () => {
    const wrapper = mount(form, {
      global: {
        directives,
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
      props: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        asset: {
          key: '123',
          groupId: 1,
          typeId: 2,
          clientIds: [],
        },
      },
    });

    // Make sure onSubmit exists in edit mode
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(typeof wrapper.vm.onSubmit).toBe('function');

    // Ensure we can access the function without errors
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(() => wrapper.vm.onSubmit).not.toThrow();
  });

  it('emits on-load event with submitButtonRef on mount', async () => {
    const wrapper = mount(form, {
      global: {
        directives,
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });

    // The onMounted hook should have fired already during mount
    expect(wrapper.emitted('on-load')).toBeDefined();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.emitted('on-load')[0][0]).toHaveProperty('focusRef');
  });
});
