import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTesting<PERSON>inia } from '@pinia/testing';

import { AboutYou } from '@aventur-shared/modules/factfind';

import {
  createClient,
  createClientStore,
  createFactfindStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import ContactDetailsPreview from '@/modules/clients/components/profile/personal-details/ContactDetailsPreview.vue';
//

const client = createClient({
  dateOfBirth: new Date(1970, 0, 1),
});

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const factFindStore = createFactfindStore(client);

function makeWrapper(data: AboutYou['contactDetails']) {
  return mount(ContactDetailsPreview, {
    props: { data },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            factfind: factFindStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

const contactDetails = factFindStore.primaryDetails['contactDetails'];

describe('Profile - Contacts', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with the correct data', async () => {
    const wrapper = makeWrapper(contactDetails);
    await flushPromises();

    // Personal Details
    expect(wrapper.findElementByText('div', 'Contact details')).to.exist;

    // first name
    expect(wrapper.findElementByText('dt', 'Email')).to.exist;
    expect(wrapper.findElementByText('dd', client.email as string)).to.exist;

    // mobile number
    expect(wrapper.findElementByText('dt', 'Mobile number')).to.exist;
    expect(wrapper.findElementByText('dd', contactDetails.mobileNumber ?? '-'))
      .to.exist;

    // phone name
    expect(wrapper.findElementByText('dt', 'Phone number')).to.exist;
    expect(wrapper.findElementByText('dd', contactDetails.phoneNumber ?? '-'))
      .to.exist;
  });
});
