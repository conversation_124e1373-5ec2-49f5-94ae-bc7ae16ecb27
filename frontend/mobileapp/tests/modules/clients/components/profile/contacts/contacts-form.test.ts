import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { apiClient } from '@aventur-shared/services/api';

import {
  createClient,
  createClientStore,
  createFactfindStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import ContactDetailsForm from '@modules/clients/components/profile/personal-details/contacts/ContactDetailsForm.vue';
import _form from '@/modules/clients/components/profile/personal-details/contacts/form/_form.vue';
import { profilePostStub } from '../stubs';
//

vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'get').mockResolvedValue(undefined);

const mockToastSuccess = vi.fn();
vi.mock('@aventur-shared/composables/useToast', () => ({
  useToast: () => ({
    info: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    success: mockToastSuccess,
  }),
}));

const client = createClient({
  dateOfBirth: new Date(1970, 0, 1),
});

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const factFindStore = createFactfindStore(client);

const data = profilePostStub(client);

function makeWrapper() {
  return mount(ContactDetailsForm, {
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            factfind: factFindStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

describe('Profile - Contacts Form', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with the correct data', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findElementByText('label', 'Email address')).to.exist;
    expect(wrapper.find('input[name=email]')).to.exist;
    expect(
      wrapper.find('input[name=email]').element.getAttribute('readonly'),
    ).not.toBeNull();

    expect(wrapper.findElementByText('label', 'Mobile number')).to.exist;
    expect(wrapper.findElementByText('label', 'Phone number')).to.exist;
  });

  it('makes correct API calls on submit', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findByTestId('submit-button').element.disabled).toBeFalsy();

    wrapper.findComponent(_form).vm.$emit('on-update', {
      mobileNumber: '01234 99-88-77',
      phoneNumber: '09876 11-22-33',
    });
    await flushPromises();

    expect(apiClient.post).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
      {
        ...data,
        mobile_number: '01234 99-88-77',
        phone_number: '09876 11-22-33',
      },
    );
    expect(mockToastSuccess).toHaveBeenCalledExactlyOnceWith(
      `Your details have been updated`,
    );
  });
});
