import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { vMaska } from 'maska/vue';

import {
  createCashflowStore,
  createClient,
  createClientIncome,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';

import { apiClient } from '@aventur-shared/services/api';
import { Money } from '@aventur-shared/utils/money';
import * as useConfirmationMod from '@aventur-shared/composables/useConfirmation';

import IncomeForm from '@modules/clients/components/profile/income/IncomeForm.vue';
import form from '@modules/clients/components/profile/income/form/form.vue';
//

const client = createClient();
const incomes = [createClientIncome()];

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const cashflowStore = createCashflowStore();

vi.spyOn(apiClient, 'put').mockResolvedValue(undefined);

vi.spyOn(useConfirmationMod, 'useConfirmation').mockResolvedValue({
  isAccepted: () => true,
  onAccept: vi.fn(),
  onCancel: vi.fn(),
});

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => ({
    params: {
      id: incomes[0].id,
    },
  })),
  useRouter: vi.fn(),
  onBeforeRouteLeave: vi.fn(),
}));

const directives = {
  maska: vMaska,
};

function makeWrapper() {
  return mount(IncomeForm, {
    global: {
      directives,
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            'cashflow-store': cashflowStore,
            client: clientStore,
            factfind: {
              incomes,
            },
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
    },
  });
}

describe('IncomeForm', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders correct form UI', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.income.id).toEqual(incomes[0].id);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.income.typeGroup).toEqual(incomes[0].typeGroup);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.income.type).toEqual(incomes[0].type);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.income.description).toEqual(incomes[0].description);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.income.frequency).toEqual(incomes[0].frequency);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.income.amount).toEqual(incomes[0].amount);

    expect(wrapper.find('form').exists()).toBe(true);

    // group
    expect(wrapper.find('label[for="typeGroup"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="typeGroup"]').text()).toEqual('Type Group');

    // type
    expect(wrapper.find('label[for="type"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="type"]').text()).toEqual('Type');

    // frequency
    expect(wrapper.find('label[for="frequency"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="frequency"]').text()).toEqual('Frequency');

    // amount
    expect(wrapper.find('label[for="amount"]').exists()).toBeTruthy();
    expect(wrapper.find('label[for="amount"]').text()).toEqual('Amount, £');
    expect(wrapper.find('input#amount').element.getAttribute('value')).toEqual(
      '72000',
    );

    expect(wrapper.findElementByText('button', 'Delete Income')).to.exist;
  });

  it('makes correct API calls on update', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    wrapper.findComponent(form).vm.$emit('on-update', {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      ...wrapper.vm.income,
      description: 'New description',
      amount: new Money(100000),
    });
    await flushPromises();

    expect(apiClient.put).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/income`,
      [
        {
          id: incomes[0].id,
          type_id: incomes[0].type,
          description: 'New description',
          amount: 100000,
          frequency: 'Yearly',
        },
      ],
    );
  });

  it('makes correct API calls on add', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    const income = createClientIncome();
    wrapper.findComponent(form).vm.$emit('on-add', {
      ...income,
    });
    await flushPromises();

    expect(apiClient.put).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/income`,
      [
        {
          id: incomes[0].id,
          type_id: incomes[0].type,
          description: incomes[0].description,
          amount: incomes[0].amount.getValue(),
          frequency: incomes[0].frequency.toValue(),
        },
        {
          id: income.id,
          type_id: income.type,
          description: income.description,
          amount: income.amount.getValue(),
          frequency: income.frequency.toValue(),
        },
      ],
    );
  });

  it('makes correct API calls on delete', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    wrapper.findComponent(form).vm.$emit(
      'on-delete',
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      wrapper.vm.income,
    );
    await flushPromises();

    expect(apiClient.put).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/income`,
      [],
    );
  });
});
