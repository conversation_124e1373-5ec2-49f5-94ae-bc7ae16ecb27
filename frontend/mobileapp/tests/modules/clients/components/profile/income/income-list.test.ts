import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import {
  createCashflowStore,
  createClient,
  createClientIncome,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
import { Frequency, FrequencyEnum } from '@aventur-shared/modules/factfind';

import IncomeList from '@modules/clients/components/profile/income/IncomeList.vue';
import IncomeListItem from '@modules/clients/components/profile/income/IncomeListItem.vue';
//

const client = createClient();
const incomes = [
  createClientIncome(/* employment with annual salary */),
  createClientIncome({
    typeGroup: 3, // Income - Company
    type: 9, // Dividends
    description: 'Company Ltd.',
    amount: new Money(700),
    frequency: new Frequency(FrequencyEnum.Quarterly),
  }),
];

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const cashflowStore = createCashflowStore();

function makeWrapper() {
  return mount(IncomeList, {
    props: {
      incomes,
    },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            'cashflow-store': cashflowStore,
            client: clientStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {
        IncomeListItem,
      },
    },
  });
}

describe('IncomeList.vue', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders correct list item UI', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    const items = wrapper.findAllComponents(IncomeListItem);

    expect(items).to.have.length(2);
    expect(items[0].findElementByText('p', 'Annual Basic Salary')).to.exist;
    expect(items[0].findElementByText('p', 'Income 1')).to.exist;
    expect(
      items[0].findElementByText('p', formatWithCurrency(new Money(72000))),
    ).to.exist;
    expect(items[0].findElementByText('p', 'Yearly')).to.exist;

    expect(items[1].findElementByText('p', 'Dividends')).to.exist;
    expect(items[1].findElementByText('p', 'Company Ltd.')).to.exist;
    expect(items[1].findElementByText('p', formatWithCurrency(new Money(700))))
      .to.exist;
    expect(items[1].findElementByText('p', 'Quarterly')).to.exist;
  });
});
