import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import {
  createCashflowStore,
  createClient,
  createClientStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import { apiClient } from '@aventur-shared/services/api';
import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
import { getMonthlyAmount } from '@aventur-shared/modules/factfind/services/frequency-amount';
import { Income } from '@aventur-shared/modules/factfind';

import IncomeView from '@modules/clients/components/profile/income/Income.vue';
import IncomeList from '@modules/clients/components/profile/income/IncomeList.vue';
import IncomeListItem from '@modules/clients/components/profile/income/IncomeListItem.vue';
//

const client = createClient();

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const cashflowStore = createCashflowStore();

function makeWrapper() {
  return mount(IncomeView, {
    props: {},
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            'cashflow-store': cashflowStore,
            client: clientStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {
        IncomeList,
        IncomeListItem,
      },
    },
  });
}

const mockJarvisGetIncomeResponse = [
  {
    id: 1,
    amount: 75000,
    frequency: 'Yearly',
    group_id: 1,
    description: 'Contractor',
    type_id: 1,
    is_essential: null,
  },
  {
    id: 2,
    amount: 1230,
    frequency: 'Quarterly',
    group_id: 3,
    description: 'ABBVIE Inc.',
    type_id: 9,
    is_essential: null,
  },
];
vi.spyOn(apiClient, 'get').mockResolvedValue(mockJarvisGetIncomeResponse);

describe('Profile - Income', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders view and makes API calls', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const totalOverall = wrapper.vm.incomes.reduce(
      (sum: number, income: Income) =>
        getMonthlyAmount(income.amount, income.frequency).getValue() + sum,
      0,
    );

    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/income`,
    );

    expect(wrapper.find('[data-income-summary]').find('dt').text()).toEqual(
      'Monthly Total',
    );
    expect(wrapper.find('[data-income-summary]').find('dd').text()).toEqual(
      formatWithCurrency(new Money(totalOverall)),
    );

    expect(wrapper.findAllComponents(IncomeListItem).length).toBe(2);
  });
});
