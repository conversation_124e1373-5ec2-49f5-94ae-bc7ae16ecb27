import { filter, get } from 'lodash';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import {
  createClient,
  createClientStore,
  createFactfindStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';
import { default as router } from '@/router';

import FamilyDetailsPreview from '@/modules/clients/components/profile/personal-details/FamilyDetailsPreview.vue';
import { FamilyMember } from '@modules/clients/components/profile/personal-details/family/form/form-model';
//

const client = createClient();

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const factFindStore = createFactfindStore(client);

const members = get(factFindStore, 'primaryDetails.familyMembers');

function makeWrapper() {
  return mount(FamilyDetailsPreview, {
    props: { members },
    global: {
      plugins: [
        router,
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            factfind: factFindStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

describe('Profile - Family', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with the correct data', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findElementByText('h3', 'Family Members')).to.exist;
    expect(wrapper.findAll('[data-testid=family-member-row]')).to.have.length(
      1,
    );
    expect(
      wrapper.findElementByText(
        'span',
        filter([members[0].firstName, members[0].lastName]).join(' '),
      ),
    ).to.exist;
    expect(wrapper.findElementByText('p', 'Child')).to.exist;
  });
});
