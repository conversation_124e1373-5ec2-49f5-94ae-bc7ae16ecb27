import { get } from 'lodash';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { useFactfindStore } from '@aventur-shared/modules/factfind';
import { apiClient } from '@aventur-shared/services/api';
import { factory as dateTimeFactory } from '@aventur-shared/utils/dateTime';

import {
  createClient,
  createClientStore,
  createFactfindStore,
  createRefDataStore,
  createUserStore,
} from '@tests/helpers';

import FamilyMemberForm from '@/modules/clients/components/profile/personal-details/family/FamilyMemberForm.vue';
import _form from '@/modules/clients/components/profile/personal-details/family/form/_form.vue';
import { profilePostStub } from '../stubs';
//

vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'get').mockResolvedValue(undefined);

const mockToastSuccess = vi.fn();
vi.mock('@aventur-shared/composables/useToast', () => ({
  useToast: () => ({
    info: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    success: mockToastSuccess,
  }),
}));

vi.mock('@capacitor/dialog', () => ({
  Dialog: {
    alert: vi.fn,
    prompt: vi.fn,
    confirm: () => Promise.resolve({ value: true }),
  },
}));

vi.mock('vue-router', () => ({
  useRouter: vi.fn,
  useRoute: () => ({
    params: { id: '1' },
  }),
}));

const client = createClient({
  dateOfBirth: new Date(1970, 0, 1),
});

const clientStore = createClientStore(client);
const refDataStore = createRefDataStore();
const factFindStore = createFactfindStore(client);

const data = profilePostStub(client);

function makeWrapper() {
  return mount(FamilyMemberForm, {
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
            factfind: factFindStore,
            user: createUserStore({ id: client.id }),
          },
        }),
      ],
      components: {},
    },
  });
}

describe('Profile - Family Member Form', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with the correct data', async () => {
    const member = get(factFindStore, 'primaryDetails.familyMembers[0]');

    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findElementByText('label', 'First name')).to.exist;
    expect(
      (wrapper.find('input[name=firstName]').element as HTMLInputElement).value,
    ).to.eq(member.firstName);

    expect(wrapper.findElementByText('label', 'Last name')).to.exist;
    expect(
      (wrapper.find('input[name=lastName]').element as HTMLInputElement).value,
    ).to.eq(member.lastName);

    expect(wrapper.findElementByText('label', 'Date of birth')).to.exist;
    expect(
      (wrapper.find('input[name=dateOfBirth]').element as HTMLInputElement)
        .value,
    ).to.eq(member.dateOfBirth?.formatForForm());

    expect(wrapper.findElementByText('label', 'Relationship')).to.exist;
    expect(
      wrapper.find('ul#multiselect-options > li[data-selected="true"]').element
        ?.textContent,
    ).to.eq('Child');
  });

  it('makes correct API call on update', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const member = wrapper.vm.member;
    wrapper.findComponent(_form).vm.$emit('on-update', {
      ...member,
      firstName: 'New First Name',
      lastName: 'New Last Name',
      dateOfBirth: null,
      relationshipType: 'NotSet',
    });
    await flushPromises();

    expect(apiClient.post).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
      {
        ...data,
        relations: [
          {
            id: 1,
            first_name: 'New First Name',
            last_name: 'New Last Name',
            date_of_birth: null,
            relationship_type: 'NotSet',
          },
        ],
      },
    );
    expect(mockToastSuccess).toHaveBeenCalledExactlyOnceWith(
      `Your details have been updated`,
    );

    const { familyMembers, getClientPrimaryDetails } = useFactfindStore();

    expect(familyMembers).to.have.length(1);
    expect(getClientPrimaryDetails).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
    );
  });

  it('makes correct API call on add', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    wrapper.findComponent(_form).vm.$emit('on-add', {
      firstName: 'Family',
      lastName: 'Member',
      dateOfBirth: dateTimeFactory(new Date(1970, 0, 1)),
      relationshipType: 'NotSet', // Not set
    });
    await flushPromises();

    expect(apiClient.post).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
      {
        ...data,
        relations: [
          {
            id: 1,
            date_of_birth: '2000-01-01',
            first_name: 'First Name',
            last_name: 'Last Name',
            relationship_type: 'Child',
          },
          {
            id: undefined,
            date_of_birth: '1970-01-01',
            first_name: 'Family',
            last_name: 'Member',
            relationship_type: 'NotSet',
          },
        ],
      },
    );
    expect(mockToastSuccess).toHaveBeenCalledExactlyOnceWith(
      `Your details have been updated`,
    );

    const { getClientPrimaryDetails } = useFactfindStore();

    expect(getClientPrimaryDetails).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
    );
  });

  it('makes correct API call on delete', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    const member = wrapper.vm.member;

    wrapper.findComponent(_form).vm.$emit('on-delete', member);
    await flushPromises();

    expect(apiClient.post).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
      {
        ...data,
        relations: [],
      },
    );
    expect(mockToastSuccess).toHaveBeenCalledExactlyOnceWith(
      `Your details have been updated`,
    );

    const { getClientPrimaryDetails } = useFactfindStore();

    expect(getClientPrimaryDetails).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledExactlyOnceWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
    );
  });
});
