{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@modules/*": ["src/modules/*"],
      "@tests/*": ["tests/*"],
      "@aventur-shared/*": ["../shared/src/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.test.ts",
    "../shared/**/*.ts",
    "../shared/**/*.vue",
  ],
  "exclude": ["node_modules", "dist", "public"],
}
