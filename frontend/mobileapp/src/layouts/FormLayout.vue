<template>
  <div class="flex flex-col bg-white">
    <form class="flex flex-col gap-2" @submit.prevent="$emit('on-submit')">
      <header class="p-5">
        <slot name="header" />
      </header>
      <main class="px-5" :inert="readOnly">
        <slot name="body" />
      </main>
      <footer
        class="fixed bottom-0 z-40 w-full border-t border-black/10 bg-white px-7 py-5"
      >
        <slot name="footer">
          <div class="flex justify-between">
            <BaseButton theme="text-like" @click.stop="router.go(-1)">
              Back
            </BaseButton>
            <BaseButton type="submit" theme="primary" :disabled="readOnly">
              Save
            </BaseButton>
          </div>
        </slot>
      </footer>
    </form>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';

  const router = useRouter();

  withDefaults(
    defineProps<{
      readOnly?: boolean;
    }>(),
    {
      readOnly: false,
    },
  );
  defineEmits(['on-submit']);
</script>
