<template>
  <BaseLayoutContent>
    <template #content-wrapper>
      <div id="head">
        <Navbar>
          <div class="flex grow justify-center">
            <div class="text-sm uppercase text-white">
              {{ route.meta['title'] }}
            </div>
          </div>
          <div class="flex justify-end">
            <div class="flex items-center">
              <ProfileMenu :options="menuOptions" />
            </div>
          </div>
        </Navbar>
        <div v-if="$slots.head">
          <slot name="head" />
        </div>
      </div>
      <main>
        <div class="mx-auto">
          <slot />
        </div>
      </main>
    </template>
  </BaseLayoutContent>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import Navbar from '@/components/Navbar.vue';
  import BaseLayoutContent from '@/layouts/BaseLayoutContent.vue';
  import { ProfileMenu, menuOptions } from '@modules/users';

  const route = useRoute();
</script>

<style lang="scss" scoped>
  #head {
    background: top / cover no-repeat url('/assets/bg-aventur.png');
  }
</style>
