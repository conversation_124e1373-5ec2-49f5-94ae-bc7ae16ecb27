import { Capacitor } from '@capacitor/core';
import { API_NAME } from '@aventur-shared/services/api';
import { ResourcesConfig } from '@aws-amplify/core/src/singleton/types';

// Fix for working android locally, it uses ******** than
// localhost to connect to local machine.
const platform = Capacitor.getPlatform();
const isDev = import.meta.env.DEV;
const isAndroid = platform === 'android';

const defaultURL = import.meta.env.VITE_URL;
const androidURL = import.meta.env.VITE_URL_ANDROID;

const endpoint = isDev && isAndroid ? androidURL : defaultURL;

const awsconfig: ResourcesConfig = {
  Auth: {
    Cognito: {
      userPoolId: import.meta.env.VITE_AWS_COGNITO_USER_POOL || 'cognito_local',
      userPoolClientId: import.meta.env.VITE_AWS_COGNITO_CLIENT_ID || 'local',
      userPoolEndpoint: import.meta.env.VITE_AWS_COGNITO_ENDPOINT_URL,
      loginWith: {
        email: true,
      },
    },
  },
  API: {
    REST: {
      [API_NAME]: {
        region: import.meta.env.VITE_AWS_REGION || 'local',
        endpoint,
      },
      auth: {
        endpoint,
      },
    },
  },
};

export default awsconfig;
