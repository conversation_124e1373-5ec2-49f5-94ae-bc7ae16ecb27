<template>
  <Swiper
    :rewind="true"
    :autoplay="{
      pauseOnMouseEnter: true,
    }"
    :slidesPerView="1"
    :spaceBetween="30"
    :pagination="{
      el: swiperPagination,
    }"
    :modules="[Pagination, Autoplay]"
  >
    <SwiperSlide v-for="(tab, index) in tabs" :key="index">
      <Component :is="tab" />
    </SwiperSlide>
  </Swiper>
  <div ref="swiperPagination"></div>
</template>

<script setup lang="ts">
  import { useTemplateRef } from 'vue';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Autoplay, Pagination } from 'swiper/modules';

  import IntroGoals from '@modules/intro/IntroGoals.vue';
  import IntroGuidance from '@modules/intro/IntroGuidance.vue';
  import IntroSecurity from '@modules/intro/IntroSecurity.vue';
  import IntroFinances from '@modules/intro/IntroFinances.vue';
  import IntroSupport from '@modules/intro/IntroSupport.vue';
  //

  const tabs = [
    IntroGoals,
    IntroGuidance,
    IntroSecurity,
    IntroFinances,
    IntroSupport,
  ];

  const swiperPagination = useTemplateRef('swiperPagination');
</script>

<style lang="postcss">
  @import '../../assets/css/swiper.pcss';
</style>
