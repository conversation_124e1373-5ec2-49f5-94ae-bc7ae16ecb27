<template>
  <IntroWrapper>
    <h1 class="text-xl font-bold uppercase leading-tight">
      Financial Goals<br />Made Simple
    </h1>
    <p class="px-5 text-gray-500">
      Set and track your financial goals with personalized guidance. Whether
      it's saving for a home, planning retirement, or building an emergency fund
      - we're here to help you succeed.
    </p>
  </IntroWrapper>
</template>

<script setup lang="ts">
  import IntroWrapper from '@modules/intro/IntroWrapper.vue';
</script>

<style lang="postcss" scoped>
  [data-intro] :deep(> div:first-child) {
    background-image: url('/img/intro_goals.png');
  }
</style>
