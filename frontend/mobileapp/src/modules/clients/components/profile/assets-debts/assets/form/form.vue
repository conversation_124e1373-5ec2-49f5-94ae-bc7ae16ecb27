<template>
  <form @submit.prevent="onSubmit" data-testid="asset-form">
    <div class="px-7">
      <select-field
        data-testid="group-select"
        label="Group"
        name="groupId"
        :options="groups"
        :searchable="true"
        :disabled="isEditForm"
        @on-select="handleGroupSelect"
      />
      <select-field
        data-testid="type-select"
        label="Type"
        name="typeId"
        :options="productTypeSelectOptions"
        :searchable="true"
        :disabled="!values.groupId || isEditForm"
        @on-select="handleTypeSelect"
      />
      <template v-if="values.typeId">
        <multi-select-field
          data-testid="client-select"
          name="clientIds"
          label="Clients"
          :options="linkedClients"
          hint="Contact your advisor to link clients to your profile."
        />
      </template>
      <template v-if="values.typeId && isAccountType(values.typeId)">
        <select-field
          data-testid="provider-select"
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <text-field label="Sub Account Number" name="subAccountNumber" />
        <div class="mt-8 border-t border-dashed pt-5" v-if="isEditForm">
          <select-field
            label="Contribution/Withdrawal"
            name="monthlyPaymentDirection"
            :options="[
              { label: 'Contribution', value: 'contribution' },
              { label: 'Withdrawal', value: 'withdrawal' },
            ]"
          />
          <CurrencyField
            label="Amount, £"
            name="monthlyPaymentAmount"
            no-append
          />
          <select-field
            label="Estimated Risk"
            name="riskLevel"
            :options="
              getRiskLevels.map((risk_level) => ({
                label: risk_level.name,
                value: risk_level.id,
              }))
            "
          />
        </div>
      </template>
      <template v-if="values.typeId && isPropertyType(values.typeId)">
        <text-field
          data-testid="address-line-one-input"
          label="Address Line 1"
          name="addressLineOne"
        />
        <text-field label="Address Line 2" name="addressLineTwo" />
        <text-field label="City" name="city" />
        <text-field label="Postcode" name="postCode" />
        <select-field
          label="Country"
          name="countryId"
          :options="countries"
          :searchable="true"
        />
        <select-field
          label="Owner"
          name="owner"
          :options="owners"
          :can-clear="true"
        />
      </template>
      <template v-if="values.typeId && isCompanySharesType(values.typeId)">
        <text-field
          data-testid="name-of-company-input"
          label="Name of Company"
          name="nameOfCompany"
        />
      </template>
      <template v-if="values.typeId && isCryptoCurrencyType(values.typeId)">
        <text-field
          data-testid="name-of-currency-input"
          label="Name of Currency"
          name="nameOfCurrency"
        />
        <text-field label="Number of coins" name="numberOfCoins" />
      </template>
      <template v-if="values.typeId && isOtherAssetType(values.typeId)">
        <text-field
          data-testid="name-of-asset-input"
          label="Name of Asset"
          name="nameOfAsset"
        />
      </template>
      <template v-if="values.typeId && isTermPolicyType(values.typeId)">
        <select-field
          data-testid="term-policy-provider-select"
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <text-field label="Sub Account Number" name="subAccountNumber" />
        <text-field label="Cover Amount, £" name="coverAmount" />
        <date-picker label="Policy End Date" name="policyEndDate" />
      </template>
      <template v-if="values.typeId && isIndemnityPolicyType(values.typeId)">
        <select-field
          data-testid="indemnity-policy-provider-select"
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <text-field label="Sub Account Number" name="subAccountNumber" />
        <date-picker label="Policy End Date" name="policyEndDate" />
      </template>
      <template v-if="values.typeId && isWholeOfLifePolicyType(values.typeId)">
        <select-field
          data-testid="wol-policy-provider-select"
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <text-field label="Sub Account Number" name="subAccountNumber" />
        <CurrencyField label="Cover Amount, £" name="coverAmount" no-append />
      </template>
      <template
        v-if="values.typeId && isIncomeProtectionPolicyType(values.typeId)"
      >
        <select-field
          data-testid="income-policy-provider-select"
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <text-field label="Sub Account Number" name="subAccountNumber" />
        <text-field label="Monthly Benefit, £" name="monthlyBenefit" />
        <text-field label="Deferred Period, weeks" name="deferredWeeks" />
        <date-picker label="Policy End Date" name="policyEndDate" />
      </template>
      <template
        v-if="values.typeId && isDefinedBenefitPensionType(values.typeId)"
      >
        <select-field
          data-testid="defined-benefit-pension-provider-select"
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <text-field label="Sub Account Number" name="subAccountNumber" />
        <switch-field
          :value="true"
          label="Is the Pension Index Linked?"
          name="indexLinked"
        />
        <switch-field
          :value="true"
          label="Does the Pension Have Survivor Benefits?"
          name="survivorBenefits"
        />
        <switch-field
          :value="true"
          label="Are You Still Working In This Job?"
          name="isCurrentJob"
        />
        <template v-if="values.isCurrentJob">
          <CurrencyField
            label="Estimated Annual Income at Retirement, £"
            name="estimatedAnnualIncomeAtRetirement"
            no-append
          />
          <text-field
            label="Scheme Normal Retirement Age"
            name="schemeNormalRetirementAge"
          />
        </template>
        <template v-else>
          <text-field label="Accrual Rate" name="accrualRate" />
          <CurrencyField
            label="Predicted Final Salary, £"
            name="predictedFinalSalary"
            no-append
          />
          <text-field
            label="Predicted Years of Service at Retirement"
            name="predictedYearsOfServiceAtRetirement"
          />
        </template>
      </template>
      <template
        v-if="
          values.typeId &&
          isEditForm &&
          !isTermPolicyType(values.typeId) &&
          !isIndemnityPolicyType(values.typeId) &&
          !isWholeOfLifePolicyType(values.typeId) &&
          !isIncomeProtectionPolicyType(values.typeId) &&
          !isDefinedBenefitPensionType(values.typeId)
        "
      >
        <div class="mt-7 border-t border-dashed pt-4">
          <CurrencyField
            data-testid="valuation-amount-input"
            label="Valuation, £"
            name="valuationAmount"
            no-append
          />
          <date-picker
            data-testid="valuation-date-picker"
            label="Valuation date"
            name="valuationDate"
            :disabled="!hasValuation"
          />
          <multi-state-switch
            name="valuationType"
            :options="[
              {
                label: 'Estimate',
                value: 'estimate',
              },
              {
                label: 'Actual',
                value: 'actual',
              },
            ]"
            :disabled="!hasValuation"
          />
        </div>
      </template>
      <Alert
        v-if="values.typeId && !isEditForm"
        :icon="false"
        class="mt-6 text-[.75em]"
        type="secondary"
        message="You will be able to update asset valuations once it's successfully added to your profile."
      />
    </div>
    <div v-if="isEditForm" class="flex items-center justify-center py-3">
      <BaseButton
        data-testid="delete-button"
        theme="text-like"
        class="font-bold text-red-900 underline"
        :disabled="asset && !asset.hasQuantity"
        @click="handleDelete"
        >Delete Asset</BaseButton
      >
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton
          data-testid="cancel-button"
          theme="text-like"
          @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          data-testid="submit-button"
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="APIState.isLoading"
          >Save</BaseButton
        >
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { sortBy } from 'lodash';
  import { storeToRefs } from 'pinia';
  import { Ref, computed, onMounted, ref } from 'vue';
  import { useFieldError, useForm } from 'vee-validate';

  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import Alert from '@aventur-shared/components/Alert.vue';
  import {
    CurrencyField,
    DatePicker,
    MultiSelectField,
    MultiStateSwitch,
    SelectField,
    SwitchField,
    TextField,
  } from '@aventur-shared/components/form';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import {
    Asset,
    AssetListItem,
  } from '@aventur-shared/modules/factfind/types/Asset';
  import { useRefData } from '@aventur-shared/stores/refdataStore';
  import { useClientStore } from '@aventur-shared/modules/clients';
  import { useValuationProcess } from '@aventur-shared/composables/useValuation';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';
  import { factory as valuationFactory } from '@aventur-shared/utils/valuation/factory';
  import { ValuationType } from '@aventur-shared/modules/factfind/types/Valuation';

  import { type ValuationValues } from './form-model';
  import { useAssetForm } from './use-asset-form';
  import { useAssetIdentity } from './use-asset-identity';
  import { ownerValueLabelMap } from '../view-model';
  //

  const emit = defineEmits<{
    (e: 'on-add', asset: Asset): void;
    (e: 'on-update', asset: AssetListItem): void;
    (e: 'on-delete', asset: AssetListItem): void;
    (e: 'on-load', { focusRef }: { focusRef: Ref<HTMLElement | null> }): void;
    (e: 'on-cancel'): void;
  }>();

  const props = defineProps<{
    asset?: AssetListItem;
  }>();

  const submitButtonRef = ref<HTMLElement | null>(null);
  const isEditForm = !!props.asset;

  const { APIState } = useAPIState();
  const { getProfile: client } = storeToRefs(useClientStore());
  const {
    getProviders,
    getAssets,
    getProductsByGroupId,
    getCountries,
    getRiskLevels,
  } = useRefData();

  const {
    isAccountType,
    isPropertyType,
    isCompanySharesType,
    isCryptoCurrencyType,
    isOtherAssetType,
    isTermPolicyType,
    isIndemnityPolicyType,
    isWholeOfLifePolicyType,
    isIncomeProtectionPolicyType,
    isDefinedBenefitPensionType,
  } = useAssetIdentity();

  const { initialValues, validationSchema, handleGroupChange, setTypeValue } =
    useAssetForm(props.asset);

  type FormValues = (typeof initialValues)['value'] &
    ValuationValues & {
      isCurrentJob?: boolean;
    };

  const { handleSubmit, values, resetForm, meta } = useForm<FormValues>({
    validationSchema,
    initialValues: initialValues.value,
  });

  const groups = getAssets.map((product) => ({
    label: product.name,
    value: product.id,
  })) as Array<SelectOption>;

  const providers = getProviders.map((provider) => ({
    label: provider.name,
    value: provider.id,
  })) as Array<SelectOption>;

  const countries = sortBy(
    getCountries.map((item) => ({
      label: item.name,
      value: item.id,
    })),
    'label',
  ) as Array<SelectOption>;

  const owners = [...ownerValueLabelMap].map(([value, label]) => ({
    label,
    value,
  })) as Array<SelectOption>;

  const { linkedClients } = useLinkedClientList(client);

  onMounted(async () => {
    emit('on-load', { focusRef: submitButtonRef });
  });

  const productTypeSelectOptions = ref<SelectOption[]>(
    props.asset ? getProductTypeSelectOptions(props.asset.groupId) : [],
  );

  const handleGroupSelect = (selectedOptionValue: number) => {
    handleGroupChange(selectedOptionValue);
    resetForm({
      values: initialValues.value,
    });
    productTypeSelectOptions.value =
      getProductTypeSelectOptions(selectedOptionValue);
  };

  const handleTypeSelect = (selectedOptionValue: number) => {
    setTypeValue(selectedOptionValue);
    resetForm({
      values: initialValues.value,
    });
  };

  function getProductTypeSelectOptions(groupId: number): SelectOption[] {
    return getProductsByGroupId(groupId).map((type) => ({
      label: type.name,
      value: type.id,
    }));
  }

  const handleDelete = () => {
    props.asset && emit('on-delete', props.asset);
  };

  const isValuationValid = useFieldError('valuationAmount');
  const hasValuation = computed(
    () => !isValuationValid.value && values.valuationAmount,
  );
  const valuationProcess = useValuationProcess<AssetListItem>({
    onAddNew: (item) => {
      emit('on-add', item);
    },
    onAddToExisting: (item) => {
      emit('on-update', item);
    },
  });

  const onSubmit = handleSubmit((formValues) => {
    const castedValues = validationSchema.value.cast(formValues);

    // Extract valuation fields (they'll be undefined if they don't exist)
    const valuationAmount = (castedValues as any).valuationAmount ?? null;
    const valuationDate = (castedValues as any).valuationDate ?? null;
    const valuationType = (castedValues as any).valuationType ?? null;

    // Create the asset object by excluding valuation fields
    const asset = Object.fromEntries(
      Object.entries(castedValues).filter(
        ([key]) =>
          !['valuationAmount', 'valuationDate', 'valuationType'].includes(key),
      ),
    ) as unknown as Asset;

    const valuation = valuationFactory(
      valuationAmount as number,
      valuationDate as Date,
      valuationType as ValuationType,
    );

    if (isEditForm) {
      valuationProcess.addingValuationToExistingItem({
        key: props.asset.key,
        ...(asset as Asset),
      });
      valuationProcess.addValuation(valuation);
    } else {
      valuationProcess.addingValuationToNewElement({
        key: '', // typing
        ...(asset as Asset),
      });
      valuationProcess.addValuation(valuation);
    }
  });
</script>
