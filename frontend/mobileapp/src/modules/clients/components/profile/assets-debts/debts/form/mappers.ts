import { NonNullableFields } from '@aventur-shared/types/Common';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { Money } from '@aventur-shared/utils/money';
import { DebtTypeGuard } from '@aventur-shared/modules/factfind/utils/debtGuard';
import {
  CreditCard,
  Debt,
  Mortgage,
  OtherDebt,
  PersonalLoan,
} from '@aventur-shared/modules/factfind/types/Debt';
import {
  CreditCardFormValues,
  FormValues,
  MortgageFormValues,
  OtherDebtFormValues,
  PersonalLoanFormValues,
  ValuationValues,
} from './form-model';
import { useDebtIdentity } from './use-debt-identity';
import { Valuation } from '@aventur-shared/modules/factfind/models';

export const valuationValues = (
  valuation?: Valuation | null,
): ValuationValues => ({
  valuationAmount: valuation?.amount.getValue() ?? null,
  valuationDate: valuation?.date.formatForForm() ?? null,
  valuationType: valuation?.type || null,
});

export const formValuesToDomain = (formValues: FormValues): Debt => {
  const {
    isMortgageType,
    isPersonalLoanType,
    isCreditCardType,
    isOtherDebtType,
  } = useDebtIdentity();

  if (isMortgageType(formValues.typeId as number)) {
    return mortgageFormValuesToDomain(
      formValues as NonNullableFields<MortgageFormValues>,
    );
  }
  if (isPersonalLoanType(formValues.typeId as number)) {
    return personalLoanFormValuesToDomain(
      formValues as NonNullableFields<PersonalLoanFormValues>,
    );
  }
  if (isCreditCardType(formValues.typeId as number)) {
    return creditCardFormValuesToDomain(
      formValues as NonNullableFields<CreditCardFormValues>,
    );
  }
  if (isOtherDebtType(formValues.typeId as number)) {
    return otherDebtFormValuesToDomain(
      formValues as NonNullableFields<OtherDebtFormValues>,
    );
  }

  throw new Error("Couldn't match debt to any known type");
};

export const domainToFormValues = (debt: Debt): FormValues => {
  if (DebtTypeGuard.isMortgage(debt)) {
    return {
      ...domainToMortgageFormValues(debt),
      ...valuationValues(debt.valuation),
    };
  }
  if (DebtTypeGuard.isPersonalLoan(debt)) {
    return {
      ...domainToPersonalLoanFormValues(debt),
      ...valuationValues(debt.valuation),
    };
  }
  if (DebtTypeGuard.isCreditCard(debt)) {
    return {
      ...domainToCreditCardFormValues(debt),
      ...valuationValues(debt.valuation),
    };
  }
  if (DebtTypeGuard.isOtherDebt(debt)) {
    return {
      ...domainToOtherDebtFormValues(debt),
      ...valuationValues(),
    };
  }
  throw new Error("Couldn't match debt to any known type");
};

function mortgageFormValuesToDomain(
  fv: NonNullableFields<
    MortgageFormValues,
    'groupId' | 'typeId' | 'providerId' | 'accountNumber'
  >,
): Mortgage {
  return {
    id: null,
    clientIds: fv.clientIds,
    groupId: fv.groupId,
    typeId: fv.typeId,
    hasQuantity: fv.hasQuantity,
    securedAgainstAddressId: fv.securedAgainstAddressId,
    providerId: fv.providerId,
    accountNumber: fv.accountNumber,
    mortgageEndDate: fv.mortgageEndDate
      ? new DateTime(fv.mortgageEndDate)
      : null,
    interestRate: fv.interestRate,
    mortgageProductEndDate: fv.mortgageProductEndDate
      ? new DateTime(fv.mortgageProductEndDate)
      : null,
    monthlyPayment: fv.monthlyPayment ? new Money(fv.monthlyPayment) : null,
    advisorId: fv.advisorId,
    valuation: null,
  };
}

function personalLoanFormValuesToDomain(
  fv: NonNullableFields<
    PersonalLoanFormValues,
    'groupId' | 'typeId' | 'providerId' | 'accountNumber' | 'advisorId'
  >,
): PersonalLoan {
  return {
    id: null,
    clientIds: fv.clientIds,
    groupId: fv.groupId,
    typeId: fv.typeId,
    hasQuantity: fv.hasQuantity,
    securedAgainstAddressId: fv.securedAgainstAddressId,
    providerId: fv.providerId,
    accountNumber: fv.accountNumber,
    loanEndDate: fv.loanEndDate ? new DateTime(fv.loanEndDate) : null,
    interestRate: Number(fv.interestRate),
    loanProductEndDate: fv.loanProductEndDate
      ? new DateTime(fv.loanProductEndDate)
      : null,
    monthlyPayment: new Money(Number(fv.monthlyPayment)),
    advisorId: fv.advisorId,
    valuation: null,
  };
}

function creditCardFormValuesToDomain(
  fv: NonNullableFields<
    CreditCardFormValues,
    'groupId' | 'typeId' | 'providerId' | 'accountNumber' | 'advisorId'
  >,
): CreditCard {
  return {
    id: null,
    clientIds: fv.clientIds,
    groupId: fv.groupId,
    typeId: fv.typeId,
    hasQuantity: fv.hasQuantity,
    providerId: fv.providerId,
    accountNumber: fv.accountNumber,
    interestRate: fv.interestRate,
    interestRateEndDate: fv.interestRateEndDate
      ? new DateTime(fv.interestRateEndDate)
      : null,
    monthlyPayment: fv.monthlyPayment ? new Money(fv.monthlyPayment) : null,
    advisorId: fv.advisorId,
    valuation: null,
  };
}

function otherDebtFormValuesToDomain(
  fv: NonNullableFields<
    OtherDebtFormValues,
    'groupId' | 'typeId' | 'providerId' | 'accountNumber' | 'advisorId'
  >,
): OtherDebt {
  return {
    id: null,
    clientIds: fv.clientIds,
    groupId: fv.groupId,
    typeId: fv.typeId,
    hasQuantity: fv.hasQuantity,
    providerId: fv.providerId,
    accountNumber: fv.accountNumber,
    interestRate: fv.interestRate,
    interestRateEndDate: fv.interestRateEndDate
      ? new DateTime(fv.interestRateEndDate)
      : null,
    monthlyPayment: fv.monthlyPayment ? new Money(fv.monthlyPayment) : null,
    advisorId: fv.advisorId,
    valuation: null,
  };
}

function domainToMortgageFormValues(
  domain: Mortgage,
): NonNullableFields<
  MortgageFormValues,
  'groupId' | 'typeId' | 'providerId' | 'accountNumber'
> {
  return {
    id: domain.id,
    clientIds: domain.clientIds,
    groupId: domain.groupId,
    typeId: domain.typeId,
    hasQuantity: domain.hasQuantity,
    securedAgainstAddressId: domain.securedAgainstAddressId,
    providerId: domain.providerId,
    accountNumber: domain.accountNumber,
    mortgageEndDate: domain.mortgageEndDate
      ? domain.mortgageEndDate.formatForForm()
      : domain.mortgageEndDate,
    interestRate: domain.interestRate,
    mortgageProductEndDate: domain.mortgageProductEndDate
      ? domain.mortgageProductEndDate.formatForForm()
      : domain.mortgageProductEndDate,
    monthlyPayment: domain.monthlyPayment
      ? domain.monthlyPayment.getValue()
      : null,
    advisorId: domain.advisorId,
  };
}

function domainToPersonalLoanFormValues(
  domain: PersonalLoan,
): NonNullableFields<
  PersonalLoanFormValues,
  'groupId' | 'typeId' | 'providerId' | 'accountNumber'
> {
  return {
    id: domain.id,
    clientIds: domain.clientIds,
    groupId: domain.groupId,
    typeId: domain.typeId,
    hasQuantity: domain.hasQuantity,
    securedAgainstAddressId: domain.securedAgainstAddressId,
    providerId: domain.providerId,
    accountNumber: domain.accountNumber,
    loanEndDate: domain.loanEndDate
      ? domain.loanEndDate.formatForForm()
      : domain.loanEndDate,
    interestRate: domain.interestRate,
    loanProductEndDate: domain.loanProductEndDate
      ? domain.loanProductEndDate.formatForForm()
      : domain.loanProductEndDate,
    monthlyPayment: domain.monthlyPayment
      ? domain.monthlyPayment.getValue()
      : null,
    advisorId: domain.advisorId,
  };
}

function domainToCreditCardFormValues(
  domain: CreditCard,
): NonNullableFields<
  CreditCardFormValues,
  'groupId' | 'typeId' | 'providerId' | 'accountNumber'
> {
  return {
    id: domain.id,
    clientIds: domain.clientIds,
    groupId: domain.groupId,
    typeId: domain.typeId,
    hasQuantity: domain.hasQuantity,
    providerId: domain.providerId,
    accountNumber: domain.accountNumber,
    interestRate: domain.interestRate,
    interestRateEndDate: domain.interestRateEndDate
      ? domain.interestRateEndDate.formatForForm()
      : domain.interestRateEndDate,
    monthlyPayment: domain.monthlyPayment
      ? domain.monthlyPayment.getValue()
      : null,
    advisorId: domain.advisorId,
  };
}

function domainToOtherDebtFormValues(
  domain: OtherDebt,
): NonNullableFields<
  OtherDebtFormValues,
  'groupId' | 'typeId' | 'providerId' | 'accountNumber'
> {
  return {
    id: domain.id,
    clientIds: domain.clientIds,
    groupId: domain.groupId,
    typeId: domain.typeId,
    hasQuantity: domain.hasQuantity,
    providerId: domain.providerId,
    accountNumber: domain.accountNumber,
    interestRate: domain.interestRate,
    interestRateEndDate: domain.interestRateEndDate
      ? domain.interestRateEndDate.formatForForm()
      : domain.interestRateEndDate,
    monthlyPayment: domain.monthlyPayment
      ? domain.monthlyPayment.getValue()
      : null,
    advisorId: domain.advisorId,
  };
}
