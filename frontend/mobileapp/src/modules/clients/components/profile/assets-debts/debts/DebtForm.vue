<template>
  <FormWrapper>
    <DebtForm
      :debt="debt"
      :property-assets="propertyAssets"
      @on-add="onAdd"
      @on-update="onUpdate"
      @on-delete="onDelete"
      @on-cancel="back"
    />
  </FormWrapper>
</template>

<script setup lang="ts">
  import { filter } from 'lodash';
  import { computed, onBeforeMount, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  import { useListCrud } from '@aventur-shared/utils/list';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { Asset, Debt } from '@aventur-shared/modules/factfind/types';
  import { postAssetsAndDebts } from '@aventur-shared/modules/factfind/api';
  import { useRefData } from '@aventur-shared/stores';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';
  import { AssetTypeGuard } from '@aventur-shared/modules/factfind/utils/assetGuard';
  import { Property } from '@aventur-shared/modules/factfind/types/Asset';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { DebtListItem } from '@aventur-shared/modules/factfind/types/Debt';

  import {
    AventurEvents,
    type ClientCreatedDebtEvent,
    type ClientDeletedDebtEvent,
    type ClientUpdatedDebtEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import FormWrapper from '../FormWrapper.vue';
  import DebtForm from './form/form.vue';

  const route = useRoute();
  const router = useRouter();
  const toast = useToast();
  const { trackEvent } = useEventTracking();

  const { getProductById } = useRefData();

  const { userId } = useUserStore();
  const { assets, debts } = useFactfindStore();
  const { originalList: originalAssetList } = useListCrud<Asset>(assets);
  const {
    list: debtList,
    crud: debtCrud,
    reset: resetDebtList,
    isModified: _isDebtListModified,
  } = useListCrud<Debt>(debts);

  const debt = ref<DebtListItem>();
  const propertyAssets = computed(
    () =>
      (originalAssetList.value as Asset[]).filter(
        (asset) => AssetTypeGuard.isProperty(asset) && asset.id,
      ) as Property[],
  );
  const back = () => router.go(-1);

  onBeforeMount(() => {
    debt.value = debtList.value.find(
      (item) => item.id === Number(route.params.id),
    );
  });

  const action = async (items: Debt[]) => {
    try {
      const predicate = (item: Asset | Debt) => !item.id || item.hasQuantity;
      await postAssetsAndDebts(
        userId as ClientId,
        filter(assets, predicate),
        filter(items, predicate),
      );
      toast.success(`Your details have been updated`);
      back();
    } catch (e) {
      resetDebtList();
      toast.error(e as Error);
    }
  };

  const onAdd = async (item: Debt) => {
    debtCrud.add(item);
    await action(debtList.value);

    trackEvent<ClientCreatedDebtEvent>(AventurEvents.ClientCreatedDebt, {
      debt_type: getProductById(item.typeId)!.type,
    });
  };

  const onUpdate = async (item: DebtListItem) => {
    debtCrud.edit(item);
    await action(debtList.value);

    trackEvent<ClientUpdatedDebtEvent>(AventurEvents.ClientUpdatedDebt, {
      debt_id: item.id as number,
      debt_type: getProductById(item.typeId)!.type,
    });
  };

  const onDelete = async (item: DebtListItem) => {
    const { isAccepted } = await useConfirmation(
      `Are you sure you want to delete this item?`,
    );

    if (isAccepted()) {
      debtCrud.remove(item.key);
      await action(debtList.value);

      trackEvent<ClientDeletedDebtEvent>(AventurEvents.ClientDeletedDebt, {
        debt_id: item.id as number,
        debt_type: getProductById(item.typeId)!.type,
      });
    }
  };
</script>
