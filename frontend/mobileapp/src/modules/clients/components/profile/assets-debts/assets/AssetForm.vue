<template>
  <FormWrapper>
    <AssetForm
      :asset="asset"
      @on-add="onAdd"
      @on-update="onUpdate"
      @on-delete="onDelete"
      @on-cancel="back"
    />
  </FormWrapper>
</template>

<script setup lang="ts">
  import { filter } from 'lodash';
  import { onBeforeMount, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  import { useListCrud } from '@aventur-shared/utils/list';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { Asset, Debt } from '@aventur-shared/modules/factfind/types';
  import { useRefData } from '@aventur-shared/stores';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';
  import { AssetListItem } from '@aventur-shared/modules/factfind/types/Asset';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { postAssetsAndDebts } from '@aventur-shared/modules/factfind/api';

  import {
    AventurEvents,
    type ClientCreatedAssetEvent,
    type ClientDeletedAssetEvent,
    type ClientUpdatedAssetEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import FormWrapper from '../FormWrapper.vue';
  import AssetForm from './form/form.vue';
  //

  const route = useRoute();
  const router = useRouter();
  const toast = useToast();
  const { trackEvent } = useEventTracking();

  const { getProductById } = useRefData();

  const { userId } = useUserStore();
  const { assets, debts } = useFactfindStore();
  const {
    list: assetList,
    crud: assetCrud,
    reset: resetAssetList,
    isModified: _isAssetListModified,
  } = useListCrud<Asset>(assets);

  const asset = ref<AssetListItem>();
  const back = () => router.go(-1);

  onBeforeMount(() => {
    asset.value = assetList.value.find(
      (item) => item.id === Number(route.params.id),
    );
  });

  const action = async (items: Asset[]) => {
    try {
      const predicate = (item: Asset | Debt) => !item.id || item.hasQuantity;
      await postAssetsAndDebts(
        userId as ClientId,
        filter(items, predicate),
        filter(debts, predicate),
      );
      toast.success(`Your details have been updated`);
      back();
    } catch (e) {
      resetAssetList();
      toast.error(e as Error);
    }
  };

  const onAdd = async (item: Asset) => {
    assetCrud.add(item);
    await action(assetList.value);

    trackEvent<ClientCreatedAssetEvent>(AventurEvents.ClientCreatedAsset, {
      asset_type: getProductById(item.typeId)!.type,
    });
  };

  const onUpdate = async (item: AssetListItem) => {
    assetCrud.edit(item);
    await action(assetList.value);

    trackEvent<ClientUpdatedAssetEvent>(AventurEvents.ClientUpdatedAsset, {
      asset_id: item.id as number,
      asset_type: getProductById(item.typeId)!.type,
    });
  };

  const onDelete = async (item: AssetListItem) => {
    const { isAccepted } = await useConfirmation(
      `Are you sure you want to delete this item?`,
    );
    if (isAccepted()) {
      assetCrud.remove(item.key);
      await action(assetList.value);

      trackEvent<ClientDeletedAssetEvent>(AventurEvents.ClientDeletedAsset, {
        asset_id: item.id as number,
        asset_type: getProductById(item.typeId)!.type,
      });
    }
  };
</script>
