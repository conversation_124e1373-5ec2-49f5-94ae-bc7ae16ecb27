<template>
  <div class="flex flex-row place-items-center gap-5">
    <div class="flex flex-col">
      <p class="text-base font-semibold text-gray-900">
        {{ productGroupName(asset) }}
      </p>
      <p class="text-sm text-gray-500">
        {{ productTypeName(asset) }}
      </p>
      <p class="text-sm text-gray-500">
        {{ productDisplayText(asset) || '&nbsp;' }}
      </p>
    </div>
    <div class="ml-auto text-right text-base text-black">
      <template v-if="asset.valuation">
        {{ formatValuationToMoney(asset.valuation) }}
      </template>
      <template v-else>-</template>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Asset } from '@aventur-shared/modules/factfind';
  import { useProductData } from '@aventur-shared/modules/factfind/models/product';
  import { formatValuationToMoney } from '@aventur-shared/modules/factfind/models';

  const { productDisplayText, productGroupName, productTypeName } =
    useProductData();

  defineProps<{
    asset: Asset;
  }>();
</script>
