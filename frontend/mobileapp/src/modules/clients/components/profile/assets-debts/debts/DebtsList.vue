<template>
  <BaseSection no-padding-x class="p-5">
    <div class="flex items-center justify-between">
      <div class="flex flex-row place-items-center gap-3">
        <div class="h-10 w-1 bg-[#F18253]" />
        <div class="flex flex-col">
          <span class="text-xs">Total Debt Value</span>
          <span class="text-lg font-semibold text-red-900">{{
            totalValue
          }}</span>
        </div>
      </div>
      <toggle
        label="Include Inactive"
        :model-value="includeInactive"
        :label-class="['!text-gray-500']"
        :disabled="disableToggleInactive"
        @update:model-value="(value) => (includeInactive = value)"
      >
        <template #label="{ label }">
          <div class="flex flex-col items-start justify-center -space-y-1">
            <span>{{ label }}</span>
            <span class="text-xs text-gray-500"
              >Showing {{ items.length }} of {{ debts.length }}</span
            >
          </div>
        </template>
      </toggle>
    </div>
  </BaseSection>
  <div class="px-7">
    <BaseSection
      v-for="(debt, i) in items"
      :key="debt.id ?? i"
      divider="bottom"
      no-padding-x
      class="!border-gray-100 last-of-type:border-b-0"
    >
      <router-link :to="`/profile/debts/${debt.id}`">
        <DebtsListItem :debt="debt" />
      </router-link>
    </BaseSection>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  import Toggle from '@aventur-shared/components/Toggle.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import {
    formatWithCurrency,
    moneyFactory,
  } from '@aventur-shared/utils/money';
  import { Debt } from '@aventur-shared/modules/factfind/types';
  import { useAssetsDebtsFiltering } from '@aventur-shared/modules/factfind/composables';

  import DebtsListItem from '@modules/clients/components/profile/assets-debts/debts/DebtsListItem.vue';
  //

  const props = defineProps<{
    debts: Debt[];
  }>();

  const totalValue = computed(() => {
    return formatWithCurrency(
      moneyFactory(
        props.debts.reduce(
          (acc, item) => acc + (item.valuation?.amount.getValue() || 0),
          0,
        ),
      ),
      {
        maximumFractionDigits: 0,
      },
    );
  });

  const { items, includeInactive, disableToggleInactive } =
    useAssetsDebtsFiltering(
      () => props.debts,
      (item: Debt) => item.hasQuantity,
    );
</script>
