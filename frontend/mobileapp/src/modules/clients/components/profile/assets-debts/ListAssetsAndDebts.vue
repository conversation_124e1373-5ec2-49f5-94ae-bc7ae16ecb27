<template>
  <BoxAccordion is-open :is-busy="isLoading">
    <template #title="{ open }">
      <div class="font-medium">
        Assets
        <span v-if="!open" class="text-sm text-black/50"
          >({{ assets?.length }})</span
        >
      </div>
    </template>
    <template #panel>
      <AssetsList :assets="assets" />
      <hr class="mx-5 h-px border-0 bg-black/5" />
      <div class="flex items-center justify-center p-4">
        <router-link
          to="assets/add"
          class="text-primary text-sm font-medium underline"
        >
          + Add Asset
        </router-link>
      </div>
    </template>
  </BoxAccordion>

  <BoxAccordion is-open :is-busy="isLoading">
    <template #title="{ open }">
      <div class="font-medium">
        Debts
        <span v-if="!open" class="text-sm text-black/50"
          >({{ debts?.length }})</span
        >
      </div>
    </template>
    <template #panel>
      <DebtsList :debts="debts" />
      <hr class="mx-5 h-px border-0 bg-black/5" />
      <div class="flex items-center justify-center p-4">
        <router-link
          to="debts/add"
          class="text-primary text-sm font-medium underline"
        >
          + Add Debt
        </router-link>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { Asset, Debt } from '@aventur-shared/modules/factfind/types';
  import AssetsList from '@modules/clients/components/profile/assets-debts/assets/AssetsList.vue';
  import DebtsList from '@modules/clients/components/profile/assets-debts/debts/DebtsList.vue';
  import BoxAccordion from '@/components/BoxAccordion.vue';

  defineProps<{
    assets: Asset[];
    debts: Debt[];
    isLoading?: boolean;
  }>();
</script>
