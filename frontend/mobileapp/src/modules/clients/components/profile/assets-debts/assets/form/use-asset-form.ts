import { computed, ref } from 'vue';
import { ClientId } from '@aventur-shared/modules/clients';
import { useUserStore } from '@aventur-shared/modules/users';
import { Asset } from '@aventur-shared/modules/factfind/types';
import { AssetFormModelFactory } from './form-model';

export const useAssetForm = (asset?: Asset) => {
  const { userId } = useUserStore();
  const groupId = ref<Asset['groupId'] | undefined>(asset?.groupId);
  const typeId = ref<Asset['typeId'] | undefined>(asset?.typeId);
  const clientIds = asset?.clientIds.length
    ? asset.clientIds
    : [userId as ClientId];

  const formModel = computed(() =>
    AssetFormModelFactory.create(clientIds, groupId.value, typeId.value, asset),
  );
  const initialValues = computed(() => formModel.value.getInitialValues());
  const validationSchema = computed(() =>
    formModel.value.getValidationSchema(),
  );

  const handleGroupChange = (selectedGroup: Asset['groupId']) => {
    groupId.value = selectedGroup;
    typeId.value = undefined;
  };

  const setTypeValue = (selectedType: Asset['typeId']) =>
    (typeId.value = selectedType);

  return {
    validationSchema,
    initialValues,
    handleGroupChange,
    setTypeValue,
  };
};
