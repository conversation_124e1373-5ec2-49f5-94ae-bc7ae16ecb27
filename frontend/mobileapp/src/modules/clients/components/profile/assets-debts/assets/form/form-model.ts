import { DateTime } from 'luxon';
import { array, boolean, date, mixed, number, object, string } from 'yup';

import { useRefData } from '@aventur-shared/stores';
import { validationMessages } from '@aventur-shared/utils/form';
import {
  correctAccountNumber,
  invalidCharactersRegex,
} from '@aventur-shared/utils/form/fields';
import {
  fieldRequiredMessage,
  invalidNumberMessage,
  noFutureDateMessage,
} from '@aventur-shared/utils/form/validation-messages';
import { Asset } from '@aventur-shared/modules/factfind/types';
import { ClientId } from '@aventur-shared/modules/clients';
import { ValuationType } from '@aventur-shared/modules/factfind/types/Valuation';
import { PaymentDirectionType } from '@aventur-shared/modules/accounts/models/account';

import { useAssetIdentity } from './use-asset-identity';
import { valuationValues } from './mappers';
//

export type ValuationValues = {
  valuationAmount: number | null;
  valuationDate: string | null;
  valuationType: ValuationType | null;
};

const initialValues = (asset: Asset) => {
  const { valuation, ...values } = asset;
  return {
    ...values,
    ...valuationValues(valuation),
  };
};

const modeAddSchema = object({
  mode: string().optional().oneOf(['add', 'edit']).default('add'),
});

const modeEditSchema = object({
  mode: string().optional().oneOf(['add', 'edit']).default('edit'),
});

const baseSchema = object({
  clientIds: array().of(number().required()).required(),
  groupId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .required(validationMessages.fieldRequiredMessage),
  typeId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .required(validationMessages.fieldRequiredMessage),
});

const valuationSchema = object({
  valuationAmount: mixed()
    .nullable()
    .checkIsNumber(invalidNumberMessage)
    .when({
      is: (value: string) => value,
      then: () =>
        number()
          .positive('Value cannot be negative')
          .max(99000000, 'Value must be lower than 99.000.000'),
    })
    .when('mode', {
      is: 'edit',
      then: (schema) => schema.required(fieldRequiredMessage),
      otherwise: (schema) => schema.nullable(),
    })
    .transform((value) => value || null),
  valuationDate: date()
    .nullable()
    .when('valuationAmount', {
      is: (valuationAmount) => valuationAmount,
      then: (schema) =>
        schema.required(validationMessages.fieldRequiredMessage),
    })
    .transform((value) => (DateTime.isDateTime(value) ? value : undefined))
    .when({
      is: (value: string) => value,
      then: () => date().max(DateTime.now(), noFutureDateMessage),
    }),
  valuationType: string()
    .nullable()
    .when('valuationAmount', {
      is: (valuationAmount) => valuationAmount,
      then: (schema) =>
        schema.required(validationMessages.fieldRequiredMessage),
    }),
});

const baseAccountSchema = baseSchema.concat(
  object({
    providerId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value))
      .required(validationMessages.fieldRequiredMessage),
    accountNumber: correctAccountNumber
      .nullable()
      .required(validationMessages.fieldRequiredMessage),
    subAccountNumber: correctAccountNumber.default(''),
  }),
);

const accountSchema = baseAccountSchema
  .concat(
    object({
      monthlyPaymentDirection: mixed<PaymentDirectionType>()
        .defined()
        .nullable()
        .default(null),
      monthlyPaymentAmount: mixed()
        .nullable()
        .default(null)
        .checkIsNumber(validationMessages.invalidNumberMessage)
        .when({
          is: (value: string) => value,
          then: () =>
            number()
              .min(0, 'Value must be higher or equal to 0')
              .max(100000, 'Value must be lower than 100.000'),
        }),
      riskLevel: number().defined().nullable().default(null),
    }),
  )
  .concat(valuationSchema);

const termPolicySchema = baseAccountSchema.concat(
  object({
    coverAmount: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () => number().min(0, 'Value must be higher or equal to 0'),
      }),
    policyEndDate: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .test('future-date', validationMessages.futureDateMessage, (value) => {
        if (typeof value === 'string') {
          const date = new Date(value);
          return !isNaN(date.getTime()) && date > new Date();
        }
        return false;
      }),
  }),
);

const indemnityPolicySchema = baseAccountSchema.concat(
  object({
    policyEndDate: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .test('future-date', validationMessages.futureDateMessage, (value) => {
        if (typeof value === 'string') {
          const date = new Date(value);
          return !isNaN(date.getTime()) && date > new Date();
        }
        return false;
      }),
  }),
);

const wholeOfLifePolicySchema = baseAccountSchema.concat(
  object({
    coverAmount: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () => number().min(0, 'Value must be higher or equal to 0'),
      }),
  }),
);

const incomeProtectionPolicySchema = baseAccountSchema.concat(
  object({
    monthlyBenefit: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () => number().min(0, 'Value must be higher or equal to 0'),
      }),
    deferredWeeks: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () => number().min(0, 'Value must be higher or equal to 0'),
      }),
    policyEndDate: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .test('future-date', validationMessages.futureDateMessage, (value) => {
        if (typeof value === 'string') {
          const date = new Date(value);
          return !isNaN(date.getTime()) && date > new Date();
        }
        return false;
      }),
  }),
);

const definedBenefitPensionSchema = baseAccountSchema.concat(
  object({
    indexLinked: boolean().required(validationMessages.fieldRequiredMessage),
    survivorBenefits: boolean().required(
      validationMessages.fieldRequiredMessage,
    ),
    isCurrentJob: boolean().required(validationMessages.fieldRequiredMessage),
    estimatedAnnualIncomeAtRetirement: mixed().when('isCurrentJob', {
      is: true,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
    schemeNormalRetirementAge: mixed().when('isCurrentJob', {
      is: true,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
    accrualRate: mixed().when('isCurrentJob', {
      is: false,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
    predictedFinalSalary: mixed().when('isCurrentJob', {
      is: false,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
    predictedYearsOfServiceAtRetirement: mixed().when('isCurrentJob', {
      is: false,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
  }),
);

const propertySchema = baseSchema
  .concat(
    object({
      addressLineOne: string().matches(invalidCharactersRegex, {
        excludeEmptyString: true,
        message: validationMessages.invalidCharacterMessage,
      }),
      addressLineTwo: string().matches(invalidCharactersRegex, {
        excludeEmptyString: true,
        message: validationMessages.invalidCharacterMessage,
      }),
      city: string().matches(invalidCharactersRegex, {
        excludeEmptyString: true,
        message: validationMessages.invalidCharacterMessage,
      }),
      postCode: string().matches(invalidCharactersRegex, {
        excludeEmptyString: true,
        message: validationMessages.invalidCharacterMessage,
      }),
      countryId: number()
        .transform((value) => (isNaN(value) ? undefined : value))
        .required(validationMessages.fieldRequiredMessage),
      owner: string().transform(($n, $o) => $o ?? ''),
    }),
  )
  .concat(valuationSchema);

const companySharesSchema = baseSchema
  .concat(
    object({
      nameOfCompany: string().required(validationMessages.fieldRequiredMessage),
    }),
  )
  .concat(valuationSchema);

const cryptoCurrencySchema = baseSchema
  .concat(
    object({
      nameOfCurrency: string().required(
        validationMessages.fieldRequiredMessage,
      ),
      numberOfCoins: mixed()
        .nullable()
        .checkIsNumber(validationMessages.invalidNumberMessage)
        .when({
          is: (value: string) => value,
          then: () =>
            number()
              .min(0, 'Value must be higher or equal to 0')
              .max(1000000, 'Value must be lower than 1.000.000'),
        }),
    }),
  )
  .concat(valuationSchema);

const otherAssetSchema = baseSchema
  .concat(
    object({
      nameOfAsset: string().required(validationMessages.fieldRequiredMessage),
    }),
  )
  .concat(valuationSchema);

abstract class BaseFormModel {
  protected clientIds: ClientId[];
  protected groupId: number | null;
  protected typeId: number | null;
  protected asset?: Asset;

  constructor(
    clientIds: ClientId[],
    groupId: number | null,
    typeId: number | null,
    asset?: Asset,
  ) {
    this.clientIds = clientIds;
    this.groupId = groupId;
    this.typeId = typeId;
    this.asset = asset;
  }

  abstract getValidationSchema(): any;

  protected getBaseInitialValues() {
    return {
      id: null,
      clientIds: this.clientIds,
      groupId: this.groupId,
      typeId: this.typeId,
      ...valuationValues(),
    };
  }

  getInitialValues() {
    if (this.asset) return initialValues(this.asset);
    return this.getBaseInitialValues();
  }
}

class AccountFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return accountSchema.concat(modeEditSchema);
    return accountSchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      accountNumber: null,
      providerId: null,
      subAccountNumber: '',
    };
  }
}

class DefinedBenefitPensionFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return definedBenefitPensionSchema.concat(modeEditSchema);
    return definedBenefitPensionSchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      accountNumber: null,
      providerId: null,
      subAccountNumber: '',
      indexLinked: false,
      survivorBenefits: false,
      isCurrentJob: false,
      estimatedAnnualIncomeAtRetirement: undefined,
      schemeNormalRetirementAge: undefined,
      accrualRate: undefined,
      predictedFinalSalary: undefined,
      predictedYearsOfServiceAtRetirement: undefined,
    };
  }
}

class TermPolicyFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return termPolicySchema.concat(modeEditSchema);
    return termPolicySchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      accountNumber: null,
      providerId: null,
      subAccountNumber: '',
      coverAmount: undefined,
      policyEndDate: undefined,
    };
  }
}

class IndemnityPolicyFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return indemnityPolicySchema.concat(modeEditSchema);
    return indemnityPolicySchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      accountNumber: null,
      providerId: null,
      subAccountNumber: null,
      policyEndDate: undefined,
    };
  }
}

class WholeOfLifePolicyFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return wholeOfLifePolicySchema.concat(modeEditSchema);
    return wholeOfLifePolicySchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      accountNumber: null,
      providerId: null,
      subAccountNumber: '',
      coverAmount: undefined,
    };
  }
}

class IncomeProtectionPolicyFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return incomeProtectionPolicySchema.concat(modeEditSchema);
    return incomeProtectionPolicySchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      accountNumber: null,
      providerId: null,
      subAccountNumber: '',
      monthlyBenefit: undefined,
      deferredWeeks: undefined,
      policyEndDate: undefined,
    };
  }
}

class PropertyFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return propertySchema.concat(modeEditSchema);
    return propertySchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    const { getCountryByCode } = useRefData();
    return {
      ...this.getBaseInitialValues(),
      addressLineOne: '',
      addressLineTwo: '',
      city: '',
      countryId: getCountryByCode('GBR')?.id,
      postCode: '',
      owner: '',
    };
  }
}

class CompanySharesFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return companySharesSchema.concat(modeEditSchema);
    return companySharesSchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      nameOfCompany: '',
    };
  }
}

class CryptoCurrencyFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) return cryptoCurrencySchema.concat(modeEditSchema);
    return cryptoCurrencySchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      nameOfCurrency: '',
      numberOfCoins: null,
    };
  }
}

class OtherAssetFormModel extends BaseFormModel {
  getValidationSchema() {
    if (this.asset) {
      return otherAssetSchema.shape({
        mode: string().default('edit'),
      });
    }
    return otherAssetSchema;
  }

  getInitialValues() {
    if (this.asset) return super.getInitialValues();
    return {
      ...this.getBaseInitialValues(),
      nameOfAsset: '',
    };
  }
}

class InitialFormModel extends BaseFormModel {
  constructor(clientIds: ClientId[], groupId?: number, typeId?: number) {
    super(clientIds, groupId ?? null, typeId ?? null);
  }

  getValidationSchema() {
    return baseSchema.concat(modeAddSchema);
  }
}

export class AssetFormModelFactory {
  static create(
    clientIds: ClientId[],
    groupId?: Asset['groupId'],
    typeId?: Asset['typeId'],
    asset?: Asset,
  ) {
    const {
      isAccountType,
      isPropertyType,
      isCompanySharesType,
      isCryptoCurrencyType,
      isOtherAssetType,
      isTermPolicyType,
      isIndemnityPolicyType,
      isWholeOfLifePolicyType,
      isIncomeProtectionPolicyType,
      isDefinedBenefitPensionType,
    } = useAssetIdentity();

    if (groupId && typeId) {
      if (isAccountType(typeId))
        return new AccountFormModel(clientIds, groupId, typeId, asset);
      if (isPropertyType(typeId))
        return new PropertyFormModel(clientIds, groupId, typeId, asset);
      if (isCompanySharesType(typeId))
        return new CompanySharesFormModel(clientIds, groupId, typeId, asset);
      if (isCryptoCurrencyType(typeId))
        return new CryptoCurrencyFormModel(clientIds, groupId, typeId, asset);
      if (isOtherAssetType(typeId))
        return new OtherAssetFormModel(clientIds, groupId, typeId, asset);
      if (isTermPolicyType(typeId))
        return new TermPolicyFormModel(clientIds, groupId, typeId, asset);
      if (isIndemnityPolicyType(typeId))
        return new IndemnityPolicyFormModel(clientIds, groupId, typeId, asset);
      if (isWholeOfLifePolicyType(typeId))
        return new WholeOfLifePolicyFormModel(
          clientIds,
          groupId,
          typeId,
          asset,
        );
      if (isIncomeProtectionPolicyType(typeId))
        return new IncomeProtectionPolicyFormModel(
          clientIds,
          groupId,
          typeId,
          asset,
        );
      if (isDefinedBenefitPensionType(typeId))
        return new DefinedBenefitPensionFormModel(
          clientIds,
          groupId,
          typeId,
          asset,
        );
    }

    return new InitialFormModel(clientIds, groupId, typeId);
  }
}
