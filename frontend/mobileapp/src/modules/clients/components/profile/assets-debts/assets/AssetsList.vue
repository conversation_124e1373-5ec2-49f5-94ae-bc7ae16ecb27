<template>
  <BaseSection no-padding-x class="p-5">
    <div class="flex items-center justify-between">
      <div class="flex flex-row place-items-center gap-3">
        <div class="h-10 w-1 bg-[#C9E386]" />
        <div class="flex flex-col">
          <span class="text-xs">Total Assets Value</span>
          <span class="text-primary text-lg font-semibold">{{
            totalValue
          }}</span>
        </div>
      </div>
      <toggle
        label="Include Inactive"
        :model-value="includeInactive"
        :label-class="['!text-gray-500']"
        :disabled="disableToggleInactive"
        @update:model-value="(value) => (includeInactive = value)"
      >
        <template #label="{ label }">
          <div class="flex flex-col items-start justify-center -space-y-1">
            <span>{{ label }}</span>
            <span class="text-xs text-gray-500"
              >Showing {{ items.length }} of {{ assets.length }}</span
            >
          </div>
        </template>
      </toggle>
    </div>
  </BaseSection>
  <div class="px-7">
    <BaseSection
      v-for="(asset, i) in items"
      :key="asset.id ?? i"
      divider="bottom"
      no-padding-x
      class="!border-gray-100 last-of-type:border-b-0"
    >
      <router-link :to="`/profile/assets/${asset.id}`">
        <AssetsListItem :asset="asset" />
      </router-link>
    </BaseSection>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  import Toggle from '@aventur-shared/components/Toggle.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import {
    formatWithCurrency,
    moneyFactory,
  } from '@aventur-shared/utils/money';
  import { Asset } from '@aventur-shared/modules/factfind/types';
  import { useAssetsDebtsFiltering } from '@aventur-shared/modules/factfind/composables';

  import AssetsListItem from './AssetsListItem.vue';
  //

  const props = defineProps<{
    assets: Asset[];
  }>();

  const totalValue = computed(() => {
    return formatWithCurrency(
      moneyFactory(
        props.assets.reduce(
          (acc, item) => acc + (item.valuation?.amount.getValue() || 0),
          0,
        ),
      ),
      {
        maximumFractionDigits: 0,
      },
    );
  });

  const { items, includeInactive, disableToggleInactive } =
    useAssetsDebtsFiltering(
      () => props.assets,
      (item: Asset) => item.hasQuantity,
    );
</script>
