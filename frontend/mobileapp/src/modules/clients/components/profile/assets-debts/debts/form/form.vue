<template>
  <form @submit.prevent="onSubmit">
    <div class="px-7">
      <select-field
        label="Group"
        name="groupId"
        :options="groups"
        :searchable="true"
        :disabled="isEditForm"
        @on-select="handleGroupSelect"
      />
      <select-field
        label="Type"
        name="typeId"
        :options="productTypeSelectOptions"
        :searchable="true"
        :disabled="!values.groupId || isEditForm"
        @on-select="(val) => setTypeValue(val)"
      />
      <template v-if="values.typeId">
        <multi-select-field
          name="clientIds"
          label="Clients"
          :options="linkedClients"
          hint="Contact your advisor to link clients to your profile."
        />
      </template>
      <template v-if="values.typeId && isMortgageType(values.typeId)">
        <select-field
          label="Secured against address"
          name="securedAgainstAddressId"
          :options="securedAgainstAddressSelectOptions()"
          :searchable="true"
          :can-clear="true"
        />
        <select-field
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <date-picker label="Mortgage end date" name="mortgageEndDate" />
        <text-field label="Interest rate" name="interestRate" />
        <date-picker
          label="Mortgage product end date"
          name="mortgageProductEndDate"
        />
        <CurrencyField
          label="Monthly payment, £"
          name="monthlyPayment"
          no-append
        />
      </template>
      <template v-if="values.typeId && isPersonalLoanType(values.typeId)">
        <select-field
          label="Secured against address"
          name="securedAgainstAddressId"
          :options="securedAgainstAddressSelectOptions()"
          :searchable="true"
          :can-clear="true"
        />
        <select-field
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <date-picker label="Loan end date" name="loanEndDate" />
        <text-field label="Interest rate" name="interestRate" />
        <date-picker label="Loan product end date" name="loanProductEndDate" />
        <CurrencyField
          label="Monthly payment, £"
          name="monthlyPayment"
          no-append
        />
      </template>
      <template v-if="values.typeId && isCreditCardType(values.typeId)">
        <select-field
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <text-field label="Interest rate" name="interestRate" />
        <date-picker
          label="Interest rate end date"
          name="interestRateEndDate"
        />
        <CurrencyField
          label="Monthly payment, £"
          name="monthlyPayment"
          no-append
        />
      </template>
      <template v-if="values.typeId && isOtherDebtType(values.typeId)">
        <select-field
          label="Provider"
          name="providerId"
          :options="providers"
          :searchable="true"
        />
        <text-field label="Account Number" name="accountNumber" />
        <text-field label="Interest rate" name="interestRate" />
        <date-picker
          label="Interest rate end date"
          name="interestRateEndDate"
        />
        <CurrencyField
          label="Monthly payment, £"
          name="monthlyPayment"
          no-append
        />
      </template>
      <template v-if="values.typeId && isEditForm">
        <div class="mt-7 border-t border-dashed pt-4">
          <CurrencyField
            label="Valuation, £"
            name="valuationAmount"
            no-append
          />
          <date-picker
            label="Valuation date"
            name="valuationDate"
            :disabled="!hasValuation"
          />
          <multi-state-switch
            name="valuationType"
            :options="[
              {
                label: 'Estimate',
                value: 'estimate',
              },
              {
                label: 'Actual',
                value: 'actual',
              },
            ]"
            :disabled="!hasValuation"
          />
        </div>
      </template>
      <Alert
        v-if="values.typeId && !isEditForm"
        :icon="false"
        class="mt-6 text-[.75em]"
        type="secondary"
        message="You will be able to update debt valuations once it's successfully added to your profile."
      />
    </div>
    <div v-if="isEditForm" class="flex items-center justify-center py-3">
      <BaseButton
        theme="text-like"
        class="font-bold text-red-900 underline"
        :disabled="debt && !debt.hasQuantity"
        @click="handleDelete"
        >Delete Debt</BaseButton
      >
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton theme="text-like" @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="APIState.isLoading"
          >Save
        </BaseButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { Ref, computed, onMounted, ref, watch } from 'vue';
  import { useFieldError, useForm } from 'vee-validate';
  import {
    CurrencyField,
    DatePicker,
    MultiSelectField,
    MultiStateSwitch,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import Alert from '@aventur-shared/components/Alert.vue';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import {
    Debt,
    DebtListItem,
  } from '@aventur-shared/modules/factfind/types/Debt';
  import { Property } from '@aventur-shared/modules/factfind/types/Asset';
  import { useRefData } from '@aventur-shared/stores/refdataStore';
  import { useClientStore } from '@aventur-shared/modules/clients';
  import { useValuationProcess } from '@aventur-shared/composables/useValuation';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';
  import { formatPropertyAssetAddress } from '@aventur-shared/modules/factfind/utils/formatter';
  import { factory as valuationFactory } from '@aventur-shared/utils/valuation/factory';
  import { ValuationType } from '@aventur-shared/modules/factfind/types/Valuation';
  import { type ValuationValues } from './form-model';
  import { useDebtForm } from './use-debt-form';
  import { useDebtIdentity } from './use-debt-identity';
  import { FormValues, InitialFormValues } from './form-model';
  import { domainToFormValues, formValuesToDomain } from './mappers';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';

  const emit = defineEmits<{
    (e: 'on-add', debt: Debt): void;
    (e: 'on-update', debt: DebtListItem): void;
    (e: 'on-delete', asset: DebtListItem): void;
    (e: 'on-load', { focusRef }: { focusRef: Ref<HTMLElement | null> }): void;
    (e: 'on-cancel'): void;
  }>();

  const props = defineProps<{
    debt?: DebtListItem;
    propertyAssets: Property[];
  }>();

  const submitButtonRef = ref<HTMLElement | null>(null);
  const isEditForm = !!props.debt;

  const { APIState } = useAPIState();
  const { getProfile: client } = storeToRefs(useClientStore());
  const { getProviders, getDebts, getProductsByGroupId } = useRefData();

  const {
    isMortgageType,
    isPersonalLoanType,
    isCreditCardType,
    isOtherDebtType,
  } = useDebtIdentity();

  const { initialValues, validationSchema, handleGroupChange, setTypeValue } =
    useDebtForm(props.debt && domainToFormValues(props.debt));

  const { handleSubmit, values, resetForm, meta } = useForm<
    ValuationValues & (FormValues | InitialFormValues)
  >({
    validationSchema,
    initialValues: initialValues.value,
  });

  const groups = getDebts.map((product) => ({
    label: product.name,
    value: product.id,
  })) as Array<SelectOption>;

  const providers = getProviders.map((provider) => ({
    label: provider.name,
    value: provider.id,
  })) as Array<SelectOption>;

  const { linkedClients } = useLinkedClientList(client);

  onMounted(async () => {
    emit('on-load', { focusRef: submitButtonRef });
  });

  const productTypeSelectOptions = ref<SelectOption[]>(
    props.debt ? getProductTypeSelectOptions(props.debt.groupId) : [],
  );

  const securedAgainstAddressSelectOptions = (): SelectOption[] => {
    return props.propertyAssets.map((asset) => ({
      label: formatPropertyAssetAddress(asset),
      value: asset.id as number,
    }));
  };

  watch(validationSchema, () => {
    resetForm({
      values: initialValues.value,
    });
  });

  const handleGroupSelect = (selectedOptionValue: number) => {
    handleGroupChange(selectedOptionValue);
    productTypeSelectOptions.value =
      getProductTypeSelectOptions(selectedOptionValue);
  };

  function getProductTypeSelectOptions(groupId: number): SelectOption[] {
    return getProductsByGroupId(groupId).map((type) => ({
      label: type.name,
      value: type.id,
    }));
  }

  const handleDelete = () => {
    props.debt && emit('on-delete', props.debt);
  };

  const isValuationValid = useFieldError('valuationAmount');
  const hasValuation = computed(
    () => !isValuationValid.value && values.valuationAmount,
  );
  const valuationProcess = useValuationProcess<DebtListItem>({
    onAddNew: (item) => {
      emit('on-add', item);
    },
    onAddToExisting: (item) => {
      emit('on-update', item);
    },
  });

  const onSubmit = handleSubmit((formValues) => {
    const { valuationAmount, valuationDate, valuationType, ...debt } =
      validationSchema.value.cast(formValues);
    const valuation = valuationFactory(
      valuationAmount as number,
      valuationDate as Date,
      valuationType as ValuationType,
    );

    if (isEditForm) {
      valuationProcess.addingValuationToExistingItem({
        key: props.debt.key,
        ...formValuesToDomain(debt as FormValues),
      });
      valuationProcess.addValuation(valuation);
    } else {
      valuationProcess.addingValuationToNewElement({
        key: '', // typing
        ...formValuesToDomain(debt as FormValues),
      });
      valuationProcess.addValuation(valuation);
    }
  });
</script>
