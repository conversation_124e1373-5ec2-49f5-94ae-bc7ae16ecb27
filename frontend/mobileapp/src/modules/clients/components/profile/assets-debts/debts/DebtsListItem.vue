<template>
  <div class="flex flex-row place-items-center gap-5">
    <div class="flex flex-col">
      <p class="text-base font-semibold text-gray-900">
        {{ productGroupName(debt) }}
      </p>
      <p class="text-sm text-gray-500">
        {{ productTypeName(debt) }}
      </p>
      <p class="text-sm text-gray-500">
        {{ productDisplayText(debt) || '&nbsp;' }}
      </p>
    </div>
    <div class="ml-auto text-right text-base text-black">
      <template v-if="debt.valuation">
        {{ formatValuationToMoney(debt.valuation) }}
      </template>
      <template v-else>-</template>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { formatValuationToMoney } from '@aventur-shared/modules/factfind/models';
  import { useProductData } from '@aventur-shared/modules/factfind/models/product';
  import { Debt } from '@aventur-shared/modules/factfind';

  const { productDisplayText, productGroupName, productTypeName } =
    useProductData();

  defineProps<{
    debt: Debt;
  }>();
</script>
