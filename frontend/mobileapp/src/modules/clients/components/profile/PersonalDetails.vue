<template>
  <TabLayout>
    <template #body>
      <AboutYouPreview :data="data" />
    </template>
  </TabLayout>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { storeToRefs } from 'pinia';

  import { ClientId } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';

  import TabLayout from '@/layouts/TabLayout.vue';
  import { usePullRefresh } from '@/composables/usePullRefresh';
  import AboutYouPreview from '../profile/personal-details/AboutYouPreview.vue';
  //

  const { userId } = useUserStore();
  const { registerRefreshHandler } = usePullRefresh();
  const { getClientPrimaryDetails, setClientPrimaryDetails } =
    useFactfindStore();
  const { primaryDetails } = storeToRefs(useFactfindStore());

  const data = computed(() => ({
    personalDetails: primaryDetails.value.personalDetails,
    retirementDetails: primaryDetails.value.retirementDetails,
    furtherInformations: primaryDetails.value.furtherInformations,
  }));

  const action = async () => {
    setClientPrimaryDetails(await getClientPrimaryDetails(userId as ClientId));
  };

  registerRefreshHandler(action);
</script>
