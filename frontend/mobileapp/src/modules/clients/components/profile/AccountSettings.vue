<template>
  <TabLayout>
    <template #body>
      <AccountSettingsPreview :client="client" />
    </template>
  </TabLayout>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';

  import { useClientStore } from '@aventur-shared/modules/clients';

  import TabLayout from '@/layouts/TabLayout.vue';
  import AccountSettingsPreview from './settings/AccountSettingsPreview.vue';
  //

  const { getProfile: client } = storeToRefs(useClientStore());
</script>
