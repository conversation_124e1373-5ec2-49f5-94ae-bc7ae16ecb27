import { computed } from 'vue';
import { RouteLocationNormalizedGeneric } from 'vue-router';

import { useFactfindStore } from '@aventur-shared/modules/factfind';
import { useProductData } from '@aventur-shared/modules/factfind/models/product';
//

export default (route: RouteLocationNormalizedGeneric) => {
  const { getDebtById } = useFactfindStore();
  const { productDisplayText, productGroupName, productTypeName } =
    useProductData();

  const _item = computed(() => getDebtById(Number(route.params.id)));

  return {
    currentDebt: computed(() =>
      _item.value
        ? {
            id: _item.value.id,
            name: productDisplayText(_item.value),
            productGroupName: productGroupName(_item.value),
            productTypeName: productTypeName(_item.value),
          }
        : undefined,
    ),
  };
};
