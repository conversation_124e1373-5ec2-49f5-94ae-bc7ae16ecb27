import { ComputedRef, computed } from 'vue';
import { RouteLocationNormalizedGeneric } from 'vue-router';

import { Income, useFactfindStore } from '@aventur-shared/modules/factfind';
import { useCashFlow } from '@aventur-shared/modules/refdata';
//

export const useCurrentIncome = (route: RouteLocationNormalizedGeneric) => {
  const { getIncomeByTypeId } = useCashFlow();
  const { getIncomeById } = useFactfindStore();

  const _item: ComputedRef<Income | undefined> = computed(() =>
    getIncomeById(Number(route.params.id)),
  );

  return {
    currentIncome: computed(() =>
      _item.value
        ? {
            id: _item.value.id,
            description: _item.value.description,
            groupName: getIncomeByTypeId(_item.value.type)?.group_name,
            typeName: getIncomeByTypeId(_item.value.type)?.name,
          }
        : undefined,
    ),
  };
};
