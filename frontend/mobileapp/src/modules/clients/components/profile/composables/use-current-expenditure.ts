import { ComputedRef, computed } from 'vue';
import { RouteLocationNormalizedGeneric } from 'vue-router';

import {
  Expenditure,
  useFactfindStore,
} from '@aventur-shared/modules/factfind';
import { useCashFlow } from '@aventur-shared/modules/refdata';
//

export const useCurrentExpenditure = (
  route: RouteLocationNormalizedGeneric,
) => {
  const { getExpenditureByTypeId } = useCashFlow();
  const { getExpenditureById } = useFactfindStore();

  const _item: ComputedRef<Expenditure | undefined> = computed(() =>
    getExpenditureById(Number(route.params.id)),
  );

  return {
    currentExpenditure: computed(() =>
      _item.value
        ? {
            id: _item.value.id,
            description: _item.value.description,
            groupName: getExpenditureByTypeId(_item.value.type)?.group_name,
            typeName: getExpenditureByTypeId(_item.value.type)?.name,
          }
        : undefined,
    ),
  };
};
