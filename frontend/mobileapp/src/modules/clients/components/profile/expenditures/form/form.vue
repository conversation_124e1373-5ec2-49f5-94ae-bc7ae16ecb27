<template>
  <form @submit.prevent="onSubmit">
    <div class="px-7">
      <select-field
        label="Type Group"
        name="typeGroup"
        :searchable="true"
        :disabled="isEditForm"
        :options="cashFlow.getExpenditureGroupsOptions()"
        @on-select="handleGroupSelect"
      />
      <select-field
        label="Type"
        name="type"
        :searchable="true"
        :options="typeSelectOptions"
        :disabled="!values.typeGroup || isEditForm"
        @on-select="handleTypeSelect"
      />
      <text-field
        label="Description"
        name="description"
        hint="A short description of your expenditure"
      />
      <select-field
        label="Frequency"
        name="frequency"
        :searchable="false"
        :options="frequencySelectOptions"
      />
      <CurrencyField label="Amount, £" name="amount" no-append />
      <div class="pt-5">
        <switch-field
          :value="true"
          label="Essential"
          name="isEssential"
          :disabled="hasEssentialPropLocked"
          @change="setIsEssential"
        />
      </div>
    </div>
    <div v-if="isEditForm" class="flex items-center justify-center py-3">
      <BaseButton
        theme="text-like"
        class="font-bold text-red-900 underline"
        @click="handleDelete"
        >Delete Expenditure</BaseButton
      >
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton theme="text-like" @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="APIState.isLoading"
          >Save
        </BaseButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { find } from 'lodash';
  import { useForm } from 'vee-validate';
  import { Ref, computed, onMounted, ref } from 'vue';

  import {
    type Expenditure,
    ExpenditureListItem,
    Frequency,
    FrequencyEnum,
    frequencySelectOptions,
  } from '@aventur-shared/modules/factfind';
  import {
    CurrencyField,
    SelectField,
    SwitchField,
    TextField,
  } from '@aventur-shared/components/form';
  import { Money } from '@aventur-shared/utils/money';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

  import { type FormValues } from './form-model';
  import { useExpenditureForm } from './use-expenditure-form';
  //

  const { APIState } = useAPIState();
  const cashFlow = useCashFlow();
  const submitButtonRef = ref<HTMLElement | null>(null);

  onMounted(async () => {
    emit('on-load', { focusRef: submitButtonRef });
  });

  const props = defineProps<{
    expenditure?: ExpenditureListItem;
  }>();

  const isEditForm = !!props.expenditure;

  const emit = defineEmits<{
    (e: 'on-add', expenditure: Expenditure): void;
    (e: 'on-update', expenditure: ExpenditureListItem): void;
    (e: 'on-delete', expenditure: ExpenditureListItem): void;
    (e: 'on-load', { focusRef }: { focusRef: Ref<HTMLElement | null> }): void;
    (e: 'on-cancel'): void;
  }>();

  const { initialValues, validationSchema } = useExpenditureForm(
    props.expenditure,
  );

  const { handleSubmit, setFieldValue, values, meta } = useForm<FormValues>({
    initialValues: initialValues.value as FormValues,
    validationSchema,
  });

  const getTypeSelectOptions = (groupId: number) => {
    return groupId ? cashFlow.getExpenditureGroupTypesOptions(groupId) : [];
  };

  const typeSelectOptions = computed<SelectOption[]>(() => {
    return values.typeGroup ? getTypeSelectOptions(values.typeGroup) : [];
  });

  const hasEssentialPropLocked = computed(() => {
    const selectedType = find(typeSelectOptions.value, {
      value: values.type,
    }) as SelectOption;
    return selectedType?.label.toLowerCase().includes('essential');
  });

  const setIsEssential = (event: Event) => {
    setFieldValue('isEssential', (event.target as HTMLInputElement)?.checked);
  };

  const handleGroupSelect = () => {
    setFieldValue('type', null);
  };

  const handleTypeSelect = () => {
    const selectedType = find(typeSelectOptions.value, {
      value: values.type,
    }) as SelectOption<{ is_essential: boolean }>;
    setFieldValue('isEssential', selectedType?.ctx?.is_essential ?? false);
  };

  const handleDelete = () => {
    props.expenditure && emit('on-delete', props.expenditure);
  };

  const onSubmit = handleSubmit((formValues: FormValues) => {
    const expenditure = {
      key: props.expenditure?.key ?? undefined,
      id: formValues.id,
      typeGroup: formValues.typeGroup as number,
      type: formValues.type as number,
      description: formValues.description as string,
      frequency: new Frequency(formValues.frequency as FrequencyEnum),
      amount: new Money(Number(formValues.amount)),
      isEssential: formValues.isEssential as boolean,
    } as ExpenditureListItem;

    isEditForm ? emit('on-update', expenditure) : emit('on-add', expenditure);
  });
</script>
