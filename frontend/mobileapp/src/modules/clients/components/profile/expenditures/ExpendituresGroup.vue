<template>
  <BoxAccordion
    class="group !mt-0 !shadow-none"
    :is-disabled="!hasItems(group.value as number)"
    :classes="{
      title: 'border-none',
      panel: 'pt-0',
    }"
  >
    <template #title>
      <div class="flex flex-col items-start p-2" data-expenditures-group>
        <h1 class="text-base font-semibold text-gray-900">
          {{ group.label }}
        </h1>
        <p
          class="flex text-sm font-normal"
          :class="
            hasItems(group.value as number)
              ? 'text-primary-700'
              : 'text-gray-400'
          "
          data-group-total-items
        >
          {{ countText(items.length, 'item') }}
        </p>
      </div>
    </template>
    <template #default>
      <div class="flex flex-col gap-1">
        <div
          class="my-2 ml-2 flex flex-col border-l border-gray-900/10 pl-4"
          data-expenditures-group-summary
        >
          <dt class="text-sm/6 text-gray-600">per month</dt>
          <dd class="order-first font-semibold tracking-tight text-gray-900">
            {{ formatWithCurrency(new Money(monthlyTotal)) }}
          </dd>
        </div>
        <div>
          <slot :items="groupedExpenditures(groupId)" />
        </div>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { find, groupBy } from 'lodash';

  import { countText } from '@aventur-shared/utils/ui/count-text';
  import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import { Expenditure } from '@aventur-shared/modules/factfind';
  import { getMonthlyAmount } from '@aventur-shared/modules/factfind/services/frequency-amount';

  import BoxAccordion from '@/components/BoxAccordion.vue';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  //

  const props = defineProps<{
    groupId: number;
    expenditures: Expenditure[];
  }>();

  const { getExpenditureGroupsOptions } = useCashFlow();

  const group = computed(
    () =>
      find(getExpenditureGroupsOptions(), {
        value: props.groupId,
      }) as SelectOption,
  );

  const monthlyTotal = computed(() =>
    items.value.reduce(
      (sum: number, expenditure: Expenditure) =>
        getMonthlyAmount(expenditure.amount, expenditure.frequency).getValue() +
        sum,
      0,
    ),
  );

  const groupedExpenditures = (groupId: number) =>
    groupBy(props.expenditures, 'typeGroup')[groupId] ?? [];

  const items = computed(() => groupedExpenditures(props.groupId));
  const hasItems = (groupId: number) => items.value.length > 0;
</script>
