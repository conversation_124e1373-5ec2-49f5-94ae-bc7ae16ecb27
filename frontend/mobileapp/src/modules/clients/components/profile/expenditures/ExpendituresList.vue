<template>
  <ExpendituresGroup
    v-for="(expenditureGroup, index) in expenditureGroups"
    :key="index"
    :group-id="expenditureGroup.value as number"
    :expenditures="expenditures"
  >
    <template #default="{ items }">
      <router-link
        :to="`/profile/expenditures/${expenditure.id}`"
        v-for="expenditure in items"
      >
        <ExpendituresListItem :expenditure="expenditure" />
      </router-link>
    </template>
  </ExpendituresGroup>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import { Expenditure } from '@aventur-shared/modules/factfind';

  import ExpendituresGroup from './ExpendituresGroup.vue';
  import ExpendituresListItem from './ExpendituresListItem.vue';
  //

  const { getExpenditureGroupsOptions } = useCashFlow();

  defineProps<{
    expenditures: Expenditure[];
  }>();

  const expenditureGroups = computed(() => getExpenditureGroupsOptions());
</script>
