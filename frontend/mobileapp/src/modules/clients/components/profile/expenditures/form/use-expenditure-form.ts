import { computed } from 'vue';

import { Expenditure } from '@aventur-shared/modules/factfind';

import { validationSchema } from './form-model';
//

const defaultValues = () => ({
  id: null,
  type: null,
  typeGroup: null,
  description: null,
  frequency: null,
  amount: null,
  isEssential: false,
});

export const useExpenditureForm = (expenditure?: Expenditure) => {
  const initialValues = computed(() =>
    expenditure
      ? {
          id: expenditure.id,
          typeGroup: expenditure.typeGroup,
          type: expenditure.type,
          description: expenditure.description,
          frequency: expenditure.frequency.toValue(),
          amount: String(expenditure.amount.getValue()),
          isEssential: expenditure.isEssential,
        }
      : defaultValues(),
  );

  return {
    initialValues,
    validationSchema,
  };
};
