<template>
  <FormWrapper>
    <ExpenditureForm
      :expenditure="expenditure"
      @on-add="onAdd"
      @on-update="onUpdate"
      @on-delete="onDelete"
      @on-cancel="back"
    />
  </FormWrapper>
</template>

<script setup lang="ts">
  import { onBeforeMount, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  import { useListCrud } from '@aventur-shared/utils/list';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId } from '@aventur-shared/modules/clients';
  import {
    Expenditure,
    ExpenditureListItem,
    postExpenditures,
    useFactfindStore,
  } from '@aventur-shared/modules/factfind';

  import FormWrapper from './FormWrapper.vue';
  import ExpenditureForm from './form/form.vue';
  //

  const route = useRoute();
  const router = useRouter();
  const toast = useToast();

  const { userId } = useUserStore();
  const { getExpenditures } = useFactfindStore();

  const expenditure = ref<ExpenditureListItem>();
  const back = () => router.go(-1);

  const {
    list: expenditureList,
    crud: expenditureCrud,
    reset: resetExpenditureList,
    isModified: _isExpenditureListModified,
  } = useListCrud<Expenditure>(getExpenditures);

  onBeforeMount(() => {
    expenditure.value = expenditureList.value.find(
      (item) => item.id === Number(route.params.id),
    );
  });

  const action = async (items: Expenditure[]) => {
    try {
      await postExpenditures(userId as ClientId, items);
      toast.success(`Your details have been updated`);
      back();
    } catch (e) {
      toast.error(e as Error);
      resetExpenditureList();
    }
  };

  const onAdd = async (item: Expenditure) => {
    expenditureCrud.add(item);
    await action(expenditureList.value);
  };
  const onUpdate = async (item: ExpenditureListItem) => {
    expenditureCrud.edit(item);
    await action(expenditureList.value);
  };

  const onDelete = async (item: ExpenditureListItem) => {
    const { isAccepted } = await useConfirmation(
      `Are you sure you want to delete this item?`,
    );
    if (isAccepted()) {
      expenditureCrud.remove(item.key);
      await action(expenditureList.value);
    }
  };
</script>
