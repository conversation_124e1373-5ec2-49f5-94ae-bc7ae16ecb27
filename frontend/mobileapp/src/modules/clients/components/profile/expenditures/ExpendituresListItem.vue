<template>
  <div
    class="flex w-full items-start justify-stretch px-2 py-3"
    data-expenditure-item
  >
    <div class="grow">
      <p class="text-sm font-semibold text-gray-900">
        {{ expenditureTypeName }}
      </p>
      <p class="break-all text-gray-500">
        {{ expenditure.description || '-' }}
      </p>
    </div>
    <div>
      <div class="flex flex-col items-end">
        <p class="flex items-center justify-start break-all">
          <MaterialDesignIcon
            v-if="expenditure.isEssential"
            icon="lock"
            class="mr-1 text-sm text-gray-500"
          />
          {{ formatWithCurrency(expenditure.amount) }}
        </p>
        <p class="text-xs text-gray-500">
          {{ expenditure.frequency.toLabel() }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  import { formatWithCurrency } from '@aventur-shared/utils/money';
  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import { Expenditure } from '@aventur-shared/modules/factfind';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  //

  const { getExpenditureByTypeId } = useCashFlow();

  const props = defineProps<{
    expenditure: Expenditure;
  }>();

  const expenditureTypeName = computed(() => {
    return getExpenditureByTypeId(props.expenditure.type)?.name ?? 'N/A';
  });
</script>
