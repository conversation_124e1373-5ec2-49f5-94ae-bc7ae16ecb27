<template>
  <TabLayout>
    <template #body>
      <FamilyDetailsPreview :members="familyMembers" />
    </template>
  </TabLayout>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';

  import { ClientId } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';

  import TabLayout from '@/layouts/TabLayout.vue';
  import { usePullRefresh } from '@/composables/usePullRefresh';
  import FamilyDetailsPreview from './personal-details/FamilyDetailsPreview.vue';
  //

  const { familyMembers } = storeToRefs(useFactfindStore());

  const { userId } = useUserStore();
  const { registerRefreshHandler } = usePullRefresh();
  const { getClientPrimaryDetails, setClientPrimaryDetails } =
    useFactfindStore();

  const action = async () => {
    setClientPrimaryDetails(await getClientPrimaryDetails(userId as ClientId));
  };

  registerRefreshHandler(action);
</script>
