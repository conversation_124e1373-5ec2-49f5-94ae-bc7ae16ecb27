<template>
  <TabLayout>
    <template #body>
      <MarketingPreferences />
      <div class="mt-4 flex flex-col px-7 py-5 text-sm text-gray-600">
        <p>
          You can manually subscribe to or unsubscribe from any other available
          <b>optional</b> or <b>recommended</b> mailing lists listed below so
          you stay updated on the topics that are most important to your
          financial goals and objectives.
        </p>
      </div>
    </template>
  </TabLayout>
</template>

<script setup lang="ts">
  import TabLayout from '@/layouts/TabLayout.vue';
  import MarketingPreferences from './settings/MarketingPreferences.vue';
</script>
