<template>
  <TabLayout>
    <template #body>
      <ListAssetsAndDebts
        :debts="debts"
        :assets="assets"
        :is-loading="isLoading"
      />
    </template>
  </TabLayout>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { onMounted } from 'vue';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';

  import TabLayout from '@/layouts/TabLayout.vue';
  import { usePullRefresh } from '@/composables/usePullRefresh';
  import ListAssetsAndDebts from './assets-debts/ListAssetsAndDebts.vue';
  //

  const toast = useToast();
  const { userId } = useUserStore();
  const { isLoading } = useAPIState();
  const { registerRefreshHandler } = usePullRefresh();
  const { loadAssetsAndDebts } = useFactfindStore();
  const { assets, debts } = storeToRefs(useFactfindStore());

  const action = async () => {
    await loadAssetsAndDebts(userId as ClientId);
  };

  onMounted(async () => {
    registerRefreshHandler(action);

    try {
      await action();
    } catch (e: any) {
      toast.error(e);
    }
  });
</script>
