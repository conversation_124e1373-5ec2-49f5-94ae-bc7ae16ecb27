<template>
  <TabLayout>
    <template #body>
      <ContactDetailsPreview :data="contactDetails" />
    </template>
  </TabLayout>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';

  import { ClientId } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';

  import TabLayout from '@/layouts/TabLayout.vue';
  import { usePullRefresh } from '@/composables/usePullRefresh';
  import ContactDetailsPreview from '../profile/personal-details/ContactDetailsPreview.vue';
  //

  const { userId } = useUserStore();
  const { registerRefreshHandler } = usePullRefresh();
  const { getClientPrimaryDetails, setClientPrimaryDetails } =
    useFactfindStore();
  const { contactDetails } = storeToRefs(useFactfindStore());

  const action = async () => {
    setClientPrimaryDetails(await getClientPrimaryDetails(userId as ClientId));
  };

  registerRefreshHandler(action);
</script>
