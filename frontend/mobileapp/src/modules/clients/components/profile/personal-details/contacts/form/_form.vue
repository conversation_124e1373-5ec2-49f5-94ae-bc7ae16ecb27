<template>
  <form @submit.prevent="onSubmit" data-testid="contact-details">
    <div class="px-7">
      <text-field
        label="Email address"
        name="email"
        :is-readonly="true"
        hint="Use your account settings to change your email."
      />
      <text-field label="Mobile number" name="mobileNumber" />
      <text-field label="Phone number" name="phoneNumber" />
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton
          data-testid="cancel-button"
          theme="text-like"
          @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          data-testid="submit-button"
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="isLoading"
          >Save
        </BaseButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { useForm } from 'vee-validate';

  import { TextField } from '@aventur-shared/components/form';
  import Alert from '@aventur-shared/components/Alert.vue';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { useFactfindStore } from '@aventur-shared/modules/factfind';

  import { FormValues, validationSchema } from './form-model';
  //

  const { isLoading } = useAPIState();

  const emit = defineEmits<{
    (e: 'on-cancel'): void;
    (e: 'on-update', data: FormValues): void;
  }>();

  const { contactDetails } = useFactfindStore();

  const initialValues = {
    email: contactDetails.email as string,
    mobileNumber: contactDetails.mobileNumber,
    phoneNumber: contactDetails.phoneNumber,
  };

  const { handleSubmit, meta } = useForm<FormValues>({
    validationSchema,
    initialValues,
  });

  const onSubmit = handleSubmit((values: FormValues) => {
    emit('on-update', validationSchema.cast(values));
  });
</script>
