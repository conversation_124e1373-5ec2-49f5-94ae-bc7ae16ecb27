import { InferType, object, string } from 'yup';

import * as validationMessages from '@aventur-shared/utils/form/validation-messages';
//

export type FormValues = InferType<typeof validationSchema>;

export const validationSchema = object({
  email: string().required().email(validationMessages.invalidEmailMessage),
  phoneNumber: string()
    .nullable()
    .checkIsCorrectPhoneNumber('Please provide correct number')
    .transform((value) => (value === '' ? null : value)),
  mobileNumber: string()
    .nullable()
    .checkIsCorrectPhoneNumber('Please provide correct number')
    .transform((value) => (value === '' ? null : value)),
});
