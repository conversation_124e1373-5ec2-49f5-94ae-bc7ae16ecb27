import { InferType, boolean, mixed, number, object, string } from 'yup';

import * as validationMessages from '@aventur-shared/utils/form/validation-messages';
import {
  invalidCharactersRegex,
  nonAlphaNumericRegex,
} from '@aventur-shared/utils/form/fields';
import { factory as dateTimeFactory } from '@aventur-shared/utils/dateTime';
import { AboutYou } from '@aventur-shared/modules/factfind';
//

export type FormValues = InferType<typeof validationSchema>;

const personalDetailsValidationSchema = object({
  firstName: string()
    .nullable()
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: validationMessages.invalidCharacterMessage,
    })
    .required(validationMessages.fieldRequiredMessage),
  lastName: string()
    .nullable()
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: validationMessages.invalidCharacterMessage,
    })
    .required(validationMessages.fieldRequiredMessage),
  titleId: number()
    .nullable()
    .default(null)
    .transform((value) => (isNaN(value) ? null : value)),
  genderId: number()
    .nullable()
    .default(null)
    .transform((value) => (isNaN(value) ? null : value)),
  nationalityId: number()
    .nullable()
    .default(null)
    .transform((value) => (isNaN(value) ? null : value)),
  birthCountryId: number().required(validationMessages.fieldRequiredMessage),
  primaryCountryId: number().required(validationMessages.fieldRequiredMessage),
  secondaryCountryId: number()
    .nullable()
    .default(null)
    .transform((value) => (isNaN(value) ? null : value)),
  dateOfBirth: string()
    .nullable()
    .transform((value) => (value === '' ? null : value)),
  maritalStatusId: number()
    .nullable()
    .default(null)
    .transform((value) => (isNaN(value) ? null : value)),
});

const retirementDetailsValidationSchema = object({
  retirementAge: mixed()
    .nullable()
    .checkIsNumber(validationMessages.invalidNumberMessage)
    .when({
      is: (value: number) => value,
      then: () =>
        number()
          .min(0, 'Value must be higher than 0')
          .max(150, 'Value must be lower than 150'),
    })
    .transform((value) => value || null),
  monthlyRetirementIncomeRequired: mixed()
    .nullable()
    .checkIsNumber(validationMessages.invalidNumberMessage)
    .when({
      is: (value: number) => value,
      then: () =>
        number()
          .min(0, 'Value must be higher than 0')
          .max(20000, 'Value must be lower than 20000'),
    })
    .transform((value) => value || null),
  statePension: boolean().nullable(),
  alreadyRetired: boolean().nullable(),
});

const furtherInformationsValidationSchema = object({
  previousFinancialAdvice: boolean().nullable(),
  previousInvestmentExperience: string()
    .nullable()
    .default(null)
    .transform((value) => value || null),
  ethicalInvestments: boolean().nullable(),
  religiousRestrictions: boolean().nullable(),
  isVulnerablePerson: boolean().nullable(),
  insuranceNumber: string()
    .matches(nonAlphaNumericRegex, {
      excludeEmptyString: true,
      message: validationMessages.alphaNumericMessage,
    })
    .nullable()
    .transform((value) => value || null),
  will: string()
    .nullable()
    .default(null)
    .transform((value) => value || null),
  powerOfAttorney: string()
    .nullable()
    .default(null)
    .transform((value) => value || null),
  creditHistory: string()
    .nullable()
    .default(null)
    .transform((value) => value || null),
  employmentStatus: string()
    .nullable()
    .default(null)
    .transform((value) => value || null),
});

export const validationSchema = object({
  personalDetails: personalDetailsValidationSchema,
  furtherInformations: furtherInformationsValidationSchema,
  retirementDetails: retirementDetailsValidationSchema,
});

export const getInitialValues = (details: AboutYou) => ({
  personalDetails: {
    firstName: details.personalDetails.firstName!,
    lastName: details.personalDetails.lastName!,
    genderId: details.personalDetails.genderId,
    maritalStatusId: details.personalDetails.maritalStatusId,
    nationalityId: details.personalDetails.nationalityId,
    birthCountryId: details.personalDetails.birthCountryId!,
    primaryCountryId: details.personalDetails.primaryCountryId!,
    secondaryCountryId: details.personalDetails.secondaryCountryId,
    titleId: details.personalDetails.titleId,
    dateOfBirth: details.personalDetails.dateOfBirth
      ? dateTimeFactory(details.personalDetails.dateOfBirth).formatForForm()
      : null,
  },
  retirementDetails: details.retirementDetails,
  furtherInformations: details.furtherInformations,
});
