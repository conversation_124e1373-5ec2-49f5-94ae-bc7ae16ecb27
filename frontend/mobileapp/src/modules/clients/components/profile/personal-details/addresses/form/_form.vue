<template>
  <form @submit.prevent="onSubmit">
    <div class="px-7">
      <text-field label="Address Line 1" name="addressLineOne" />
      <text-field label="Address Line 2" name="addressLineTwo" />
      <text-field label="Address Line 3" name="addressLineThree" />
      <text-field label="Address Line 4" name="addressLineFour" />
      <text-field label="Postcode" name="postCode" />
      <text-field label="City" name="city" />
      <select-field
        label="Country"
        name="countryId"
        :value="address?.countryId || defaultCountryId"
        :options="countries"
        :searchable="true"
      />
      <date-picker label="Move in date" name="moveInDate" />
      <date-picker label="Move out date" name="moveOutDate" />
      <checkbox-field
        label="Is primary address"
        name="isPrimary"
        :checked-value="true"
      />
      <Alert
        v-if="showPrimaryAddressMessage"
        :icon="false"
        class="text-xs"
        type="secondary"
        message="You can only set one primary address. Your primary address will be changed to the current one."
      />
    </div>
    <div v-if="type === 'EDIT'" class="flex items-center justify-center py-3">
      <BaseButton
        theme="text-like"
        class="text-sm font-bold text-red-900 underline"
        @click="handleDelete"
        >Delete</BaseButton
      >
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton
          data-testid="cancel-button"
          theme="text-like"
          @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          data-testid="submit-button"
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="isLoading"
          >Save
        </BaseButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { sortBy } from 'lodash';
  import { useFieldValue, useForm } from 'vee-validate';

  import {
    CheckboxField,
    DatePicker,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { useRefData } from '@aventur-shared/stores';
  import Alert from '@aventur-shared/components/Alert.vue';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { Address } from '@aventur-shared/modules/factfind';
  import { factory as dateTimeFactory } from '@aventur-shared/utils/dateTime';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

  import {
    AddressListItem,
    FormValues,
    getInitialValues,
    validationSchema,
  } from './form-model';
  //

  const { isLoading } = useAPIState();
  const { getCountries, getCountryByCode } = useRefData();

  const { address, type } = defineProps<{
    address?: AddressListItem;
    type: 'EDIT' | 'ADD';
  }>();

  const emit = defineEmits<{
    (e: 'on-add', item: Address): void;
    (e: 'on-update', item: AddressListItem): void;
    (e: 'on-delete', item: AddressListItem): void;
    (e: 'on-cancel'): void;
  }>();

  const defaultCountryId: number | undefined =
    type == 'ADD' ? getCountryByCode('GBR')?.id : undefined;

  const countries = sortBy(
    getCountries.map((item) => ({
      label: item.name,
      value: item.id,
    })),
    'label',
  ) as Array<SelectOption>;

  const { handleSubmit, meta } = useForm<FormValues>({
    validationSchema,
    initialValues: getInitialValues(address),
  });

  const isPrimary = useFieldValue<boolean>('isPrimary');
  const showPrimaryAddressMessage = ref<boolean>(false);
  watch(isPrimary, (val: boolean) => {
    !address?.isPrimary && (showPrimaryAddressMessage.value = val);
  });

  const handleDelete = () => {
    address && emit('on-delete', address);
  };

  const onSubmit = handleSubmit((formValues: FormValues) => {
    const item = {
      key: address?.key ?? undefined,
      ...formValues,
      moveInDate: formValues?.moveInDate
        ? dateTimeFactory(formValues.moveInDate)
        : null,
      moveOutDate: formValues?.moveOutDate
        ? dateTimeFactory(formValues?.moveOutDate)
        : null,
    };
    type === 'EDIT'
      ? emit('on-update', item as AddressListItem)
      : emit('on-add', item as Address);
  });
</script>
