<template>
  <div
    class="flex flex-row items-center justify-between gap-5"
    data-testid="family-member-row"
  >
    <div class="flex flex-col">
      <p
        class="flex flex-row place-items-center gap-1 text-base font-semibold text-gray-900"
      >
        <span class="flex">{{ formattedName }}</span>
      </p>
      <p class="text-sm text-gray-500">
        {{ member.relationshipType }}
      </p>
    </div>
    <MaterialDesignIcon icon="chevron_right" class="text-sm text-gray-400" />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  import { ArrayElement } from '@aventur-shared/types/Common';
  import { formatName } from '@aventur-shared/utils/user';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';
  //

  const { member } = defineProps<{
    member: ArrayElement<AboutYou['familyMembers']>;
  }>();

  const formattedName = computed(() =>
    formatName({
      firstName: member.firstName as string,
      lastName: member.lastName,
    }),
  );
</script>
