<template>
  <form @submit.prevent="onSubmit" data-testid="personal-details">
    <div class="px-7">
      <BaseSection divider="bottom" no-padding class="pb-4">
        <select-field
          label="Title"
          name="personalDetails.titleId"
          :options="
            getTitles.map((i: RefDataDTO) => ({
              label: i.name,
              value: i.id,
            }))
          "
          :searchable="true"
          :can-clear="true"
        />
        <text-field label="First name" name="personalDetails.firstName" />
        <text-field label="Last name" name="personalDetails.lastName" />
        <select-field
          label="Gender"
          name="personalDetails.genderId"
          :options="
            getGenders.map((i: RefDataDTO) => ({
              label: i.name,
              value: i.id,
            }))
          "
          :searchable="true"
          :can-clear="true"
        />
        <date-picker label="Date of birth" name="personalDetails.dateOfBirth" />
        <select-field
          label="Marital status"
          name="personalDetails.maritalStatusId"
          :options="
            getMaritalStatuses.map((i: RefDataDTO) => ({
              label: i.name,
              value: i.id,
            }))
          "
          :searchable="true"
          :can-clear="true"
        />
      </BaseSection>
      <BaseSection divider="bottom" no-padding class="pb-4 pt-6">
        <select-field
          label="Nationality"
          name="personalDetails.nationalityId"
          :options="
            getNationalities.map((i: RefDataDTO) => ({
              label: i.name,
              value: i.id,
            }))
          "
          :searchable="true"
          :can-clear="true"
        />
        <select-field
          label="Country of birth"
          name="personalDetails.birthCountryId"
          :options="
            getCountries.map((i: RefDataDTO) => ({
              label: i.name,
              value: i.id,
            }))
          "
          :searchable="true"
        />
        <select-field
          label="Country of citizenship / first passport"
          name="personalDetails.primaryCountryId"
          :options="
            getCountries.map((i: RefDataDTO) => ({
              label: i.name,
              value: i.id,
            }))
          "
          :searchable="true"
        />
        <select-field
          label="Dual citizenship (if any)"
          name="personalDetails.secondaryCountryId"
          :options="
            getCountries.map((i: RefDataDTO) => ({
              label: i.name,
              value: i.id,
            }))
          "
          :searchable="true"
          :can-clear="true"
        />
      </BaseSection>
      <BaseSection divider="bottom" no-padding class="pb-4 pt-6">
        <checkbox-field
          label="Will you qualify for full state pension at retirement age?"
          name="retirementDetails.statePension"
          :checked-value="true"
          :justify-between="true"
        />
        <checkbox-field
          label="Are you already retired?"
          name="retirementDetails.alreadyRetired"
          :checked-value="true"
          :justify-between="true"
        />
      </BaseSection>
      <BaseSection divider="bottom" no-padding class="pb-4 pt-6">
        <select-field
          label="Do you have previous investment experience?"
          name="furtherInformations.previousInvestmentExperience"
          :options="previousInvestmentExperienceSelectOptions"
          :can-deselect="false"
          :can-clear="true"
        />
        <checkbox-field
          label="Have you experienced financial advice before?"
          name="furtherInformations.previousFinancialAdvice"
          :checked-value="true"
          :justify-between="true"
        />
        <checkbox-field
          label="Do you wish to consider ethical investments?"
          name="furtherInformations.ethicalInvestments"
          :checked-value="true"
          :justify-between="true"
        />
        <checkbox-field
          label="Does your religious views restrict your investment options?"
          name="furtherInformations.religiousRestrictions"
          :checked-value="true"
          :justify-between="true"
        />
        <checkbox-field
          label="Are you a vulnerable person?"
          name="furtherInformations.isVulnerablePerson"
          :checked-value="true"
          :justify-between="true"
        />
      </BaseSection>
      <BaseSection no-padding class="pb-4 pt-6">
        <select-field
          :options="yesNoNeedsUpdatingSelectOptions"
          label="Do you have a will in place?"
          name="furtherInformations.will"
          :can-deselect="false"
          :can-clear="true"
        />
        <select-field
          :options="yesNoNeedsUpdatingSelectOptions"
          label="Power of attorney in place?"
          name="furtherInformations.powerOfAttorney"
          :can-deselect="false"
          :can-clear="true"
        />
        <select-field
          :options="creditHistorySelectOptions"
          label="Credit history?"
          name="furtherInformations.creditHistory"
          :can-deselect="false"
          :can-clear="true"
        />
        <select-field
          label="Employment status"
          name="furtherInformations.employmentStatus"
          :options="employmentStatusSelectOptions"
          :searchable="true"
          :can-deselect="false"
          :can-clear="true"
        />
        <text-field
          label="National Insurance number"
          name="furtherInformations.insuranceNumber"
        />
      </BaseSection>
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton
          data-testid="cancel-button"
          theme="text-like"
          @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          data-testid="submit-button"
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="isLoading"
          >Save</BaseButton
        >
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { useForm } from 'vee-validate';

  import { useRefData } from '@aventur-shared/stores';
  import { RefDataDTO } from '@aventur-shared/types/store/Refdata';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import {
    CheckboxField,
    DatePicker,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { useFactfindStore } from '@aventur-shared/modules/factfind';
  import { factory as dateTimeFactory } from '@aventur-shared/utils/dateTime';
  import {
    creditHistorySelectOptions,
    employmentStatusSelectOptions,
    previousInvestmentExperienceSelectOptions,
    yesNoNeedsUpdatingSelectOptions,
  } from '@aventur-shared/constants';

  import { FormValues, getInitialValues, validationSchema } from './form-model';
  //

  const { isLoading } = useAPIState();

  const emit = defineEmits<{
    (e: 'on-cancel'): void;
    (e: 'on-update', data: FormValues): void;
  }>();

  const {
    getNationalities,
    getGenders,
    getMaritalStatuses,
    getTitles,
    getCountries,
  } = useRefData();

  const { primaryDetails } = useFactfindStore();

  const { handleSubmit, meta } = useForm<FormValues>({
    validationSchema,
    initialValues: getInitialValues(primaryDetails),
  });

  const onSubmit = handleSubmit((values: FormValues) => {
    values.personalDetails.dateOfBirth = values.personalDetails.dateOfBirth
      ? dateTimeFactory(values.personalDetails.dateOfBirth).formatForForm()
      : null;
    emit('on-update', values);
  });
</script>
