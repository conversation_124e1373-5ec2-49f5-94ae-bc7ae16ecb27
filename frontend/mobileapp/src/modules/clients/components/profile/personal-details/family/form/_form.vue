<template>
  <form @submit.prevent="onSubmit">
    <div class="px-7">
      <text-field label="First name" name="firstName" />
      <text-field label="Last name" name="lastName" />
      <date-picker label="Date of birth" name="dateOfBirth" />
      <select-field
        label="Relationship"
        name="relationshipType"
        :options="relationshipTypeSelectOptions"
      />
    </div>
    <div v-if="type === 'EDIT'" class="flex items-center justify-center py-3">
      <BaseButton
        theme="text-like"
        class="font-bold text-red-900 underline"
        @click="handleDelete"
        >Delete</BaseButton
      >
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton
          data-testid="cancel-button"
          theme="text-like"
          @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          data-testid="submit-button"
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="isLoading"
          >Save
        </BaseButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { useForm } from 'vee-validate';

  import {
    DatePicker,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { factory as dateTimeFactory } from '@aventur-shared/utils/dateTime';
  import { relationshipTypeSelectOptions } from '@aventur-shared/modules/factfind/models';

  import {
    FamilyMember,
    FamilyMembersListItem,
    FormValues,
    validationSchema,
  } from './form-model';
  //

  const { isLoading } = useAPIState();

  const { member, type } = defineProps<{
    member?: FamilyMembersListItem;
    type: 'EDIT' | 'ADD';
  }>();

  const emit = defineEmits<{
    (e: 'on-add', item: FamilyMember): void;
    (e: 'on-update', item: FamilyMembersListItem): void;
    (e: 'on-delete', item: FamilyMembersListItem): void;
    (e: 'on-cancel'): void;
  }>();

  const { handleSubmit, meta } = useForm<FormValues>({
    validationSchema,
    initialValues: {
      id: member?.id ?? null,
      firstName: member?.firstName ?? null,
      lastName: member?.lastName ?? null,
      dateOfBirth: member?.dateOfBirth
        ? dateTimeFactory(member.dateOfBirth).formatForForm()
        : null,
      relationshipType: member?.relationshipType ?? null,
    },
  });

  const handleDelete = () => {
    member && emit('on-delete', member);
  };

  const onSubmit = handleSubmit((values: FormValues) => {
    const item = {
      key: member?.key ?? undefined,
      ...values,
      dateOfBirth: values?.dateOfBirth
        ? dateTimeFactory(values.dateOfBirth)
        : null,
    };
    type === 'EDIT'
      ? emit('on-update', item as FamilyMembersListItem)
      : emit('on-add', item as FamilyMember);
  });
</script>
