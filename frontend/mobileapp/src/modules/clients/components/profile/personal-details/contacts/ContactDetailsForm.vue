<template>
  <FormWrapper>
    <ContactDetailsForm @on-cancel="back" @on-update="onUpdate" />
  </FormWrapper>
</template>

<script setup lang="ts">
  import { merge } from 'lodash';
  import { useRouter } from 'vue-router';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';
  import {
    postClientAboutYou,
    useFactfindStore,
  } from '@aventur-shared/modules/factfind';

  import FormWrapper from '../FormWrapper.vue';
  import ContactDetailsForm from './form/_form.vue';
  //

  const router = useRouter();
  const toast = useToast();

  const { userId } = useUserStore();
  const { primaryDetails } = useFactfindStore();

  const back = () => router.go(-1);

  const onUpdate = async (
    contactDetails: Partial<AboutYou['contactDetails']>,
  ) => {
    try {
      await postClientAboutYou(
        userId as ClientId,
        merge(primaryDetails, { contactDetails }),
      );
      toast.success(`Your details have been updated`);
      back();
    } catch (e) {
      toast.error(e as Error);
    }
  };
</script>
