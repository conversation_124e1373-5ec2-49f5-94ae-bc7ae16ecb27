<template>
  <BoxAccordion is-static>
    <template #title>
      <div class="font-medium">Addresses</div>
    </template>
    <template #panel>
      <AddressList :items="addresses" />
      <hr class="mx-5 h-px border-0 bg-black/5" />
      <div class="flex items-center justify-center p-4">
        <router-link
          to="/profile/address/add"
          class="text-primary text-sm font-medium underline"
        >
          + Add Address
        </router-link>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { Address } from '@aventur-shared/modules/factfind';

  import BoxAccordion from '@/components/BoxAccordion.vue';
  import AddressList from './addresses/ListAddresses.vue';
  //

  defineProps<{
    addresses: Address[];
  }>();
</script>
