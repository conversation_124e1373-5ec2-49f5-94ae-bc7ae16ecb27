<template>
  <div
    class="flex flex-row items-center justify-between gap-5"
    data-testid="address-row"
  >
    <div class="flex flex-col">
      <p
        class="flex flex-row place-items-center gap-1 text-base font-semibold text-gray-900"
      >
        <span class="flex">{{ addressCaption }}</span>
        <span v-if="address.isPrimary">
          <span class="text-xs text-gray-300">&middot;&nbsp;</span>
          <span class="text-xxs text-primary-700 uppercase">Primary</span>
        </span>
      </p>
      <p class="text-sm text-gray-500">
        {{ formattedAddress }}
      </p>
    </div>
    <MaterialDesignIcon icon="chevron_right" class="text-sm text-gray-400" />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { omit } from 'lodash';

  import { formatAddressForView } from '@aventur-shared/services/address';
  import { ClientAddress } from '@aventur-shared/modules/factfind/types/Address';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  //

  const { address } = defineProps<{
    address: ClientAddress;
  }>();

  const addressCaption = computed(() => address['addressLineOne']);
  const formattedAddress = computed(() =>
    formatAddressForView(omit(address, 'addressLineOne') as ClientAddress),
  );
</script>
