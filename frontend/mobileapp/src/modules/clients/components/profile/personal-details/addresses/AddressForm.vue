<template>
  <FormWrapper>
    <AddressForm
      :address="address"
      :type="address ? 'EDIT' : 'ADD'"
      @on-cancel="back"
      @on-add="onAdd"
      @on-update="onUpdate"
      @on-delete="onDelete"
    />
  </FormWrapper>
</template>

<script setup lang="ts">
  import { ValidationError } from 'yup';
  import { onBeforeMount, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Dialog } from '@capacitor/dialog';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { useListCrud } from '@aventur-shared/utils/list';
  import {
    Address,
    postClientAboutYou,
    useFactfindStore,
  } from '@aventur-shared/modules/factfind';

  import FormWrapper from '../FormWrapper.vue';
  import AddressForm from './form/_form.vue';
  import {
    AddressListItem,
    addressesValidationSchema,
  } from './form/form-model';
  //

  const route = useRoute();
  const router = useRouter();
  const toast = useToast();

  const { userId } = useUserStore();
  const {
    primaryDetails,
    contactDetails,
    getClientPrimaryDetails,
    setClientPrimaryDetails,
  } = useFactfindStore();

  const address = ref<AddressListItem>();
  const back = () => router.push('/profile/addresses');

  onBeforeMount(() => {
    address.value = list.value.find(
      (item) => item.id === Number(route.params.id),
    );
  });

  const { list, crud, reset } = useListCrud<Address>(contactDetails.addresses);

  const currentPrimaryAddressId = list.value.findIndex(
    (item) => item.isPrimary,
  );
  const checkForPrimaryAddressChange = async (items: Address[]) => {
    try {
      await addressesValidationSchema.validate(items);
    } catch (e) {
      if (
        e instanceof ValidationError &&
        currentPrimaryAddressId !== -1 &&
        e.type === 'has-primary' &&
        items[currentPrimaryAddressId]
      ) {
        items[currentPrimaryAddressId].isPrimary = false;
      }
    }
  };

  const action = async (items: Address[]) => {
    try {
      await checkForPrimaryAddressChange(items);

      const {
        personalDetails,
        familyMembers,
        furtherInformations,
        retirementDetails,
      } = primaryDetails;

      await postClientAboutYou(userId as ClientId, {
        personalDetails,
        familyMembers,
        furtherInformations,
        retirementDetails,
        contactDetails: {
          ...primaryDetails.contactDetails,
          addresses: items,
        },
      });
      toast.success(`Your details have been updated`);

      setClientPrimaryDetails(
        await getClientPrimaryDetails(userId as ClientId),
      );

      back().then();
    } catch (e) {
      reset();
      toast.error(e as Error);
    }
  };

  const onAdd = async (item: Address) => {
    crud.add(item);
    await action(list.value);
  };

  const onUpdate = async (item: AddressListItem) => {
    crud.edit(item);
    await action(list.value);
  };

  const onDelete = async (item: AddressListItem) => {
    const { value } = await Dialog.confirm({
      message: `Are you sure you want to delete this item?`,
    });

    if (value) {
      crud.remove(item.key);
      await action(list.value);
    }
  };
</script>
