<template>
  <BaseSection
    v-for="(item, index) in items"
    :key="index"
    class="my-4 flex flex-col gap-1"
  >
    <router-link :to="`/profile/family/${item.id}`">
      <FamilyMemberRow :member="item" />
    </router-link>
  </BaseSection>
</template>

<script setup lang="ts">
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';

  import FamilyMemberRow from './FamilyMemberRow.vue';
  //

  const { items } = defineProps<{
    items: AboutYou['familyMembers'];
  }>();
</script>
