<template>
  <BoxAccordion is-static>
    <template #title>
      <div class="font-medium">Family Members</div>
    </template>
    <template #panel>
      <ListFamilyMembers :items="members" />
      <hr class="mx-5 h-px border-0 bg-black/5" />
      <div class="flex items-center justify-center p-4">
        <router-link
          to="/profile/family/add"
          class="text-primary text-sm font-medium underline"
        >
          + Add Member
        </router-link>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';

  import BoxAccordion from '@/components/BoxAccordion.vue';
  import ListFamilyMembers from './family/ListFamilyMembers.vue';
  //

  defineProps<{
    members: AboutYou['familyMembers'];
  }>();
</script>
