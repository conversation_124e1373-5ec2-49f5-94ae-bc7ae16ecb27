<template>
  <BoxAccordion is-static>
    <template #title>
      <div class="font-medium">About you</div>
    </template>
    <template #action>
      <router-link to="/profile/aboutyou/edit" class="flex">
        <MaterialDesignIcon
          icon="edit"
          class="text-[1.15rem] text-gray-500/80"
        />
      </router-link>
    </template>
    <template #panel>
      <BaseSection divider="bottom">
        <h3 class="text-sm font-semibold text-gray-900">Personal Details</h3>
        <div class="grid grid-cols-2 gap-x-2 gap-y-6 py-4">
          <div
            v-for="detail in getDetails(props.data)['personalDetails']"
            :key="detail.title"
          >
            <ViewDetailItem :detail="detail" />
          </div>
        </div>
      </BaseSection>
      <BaseSection divider="bottom">
        <h3 class="text-sm font-semibold text-gray-900">Retirement Details</h3>
        <div class="grid grid-cols-2 gap-x-2 gap-y-6 py-4">
          <div
            v-for="detail in getDetails(props.data)['retirementDetails']"
            :key="detail.title"
            :class="{
              'col-span-2': ['boolean', 'option'].includes(detail.type),
            }"
          >
            <ViewDetailItem :detail="detail" />
          </div>
        </div>
      </BaseSection>
      <BaseSection>
        <h3 class="text-sm font-semibold text-gray-900">Further Information</h3>
        <div class="grid grid-cols-2 gap-x-2 gap-y-6 py-4">
          <div
            v-for="detail in getDetails(props.data)['furtherInformations']"
            :key="detail.title"
            :class="{
              'col-span-2': ['boolean', 'option'].includes(detail.type),
            }"
          >
            <ViewDetailItem :detail="detail" />
          </div>
        </div>
      </BaseSection>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { find, get } from 'lodash';

  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import { useRefData } from '@aventur-shared/stores/refdataStore';
  import { ViewDetail } from '@aventur-shared/types/ViewDetail';
  import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';
  import { Options } from '@aventur-shared/types/Common';
  import { factory as dateTimeFactory } from '@aventur-shared/utils/dateTime';

  import BoxAccordion from '@/components/BoxAccordion.vue';
  import ViewDetailItem from '@modules/clients/components/ViewDetailItem.vue';
  //

  const NA = 'N/A';

  const props = defineProps<{
    data: Pick<
      AboutYou,
      'personalDetails' | 'furtherInformations' | 'retirementDetails'
    >;
  }>();

  const {
    getTitles,
    getGenders,
    getMaritalStatuses,
    getNationalities,
    getCountries,
  } = useRefData();

  const getDetails = (
    data: typeof props.data,
  ): Record<string, ViewDetail[]> => {
    return {
      personalDetails: [
        {
          title: 'First name',
          value: data.personalDetails.firstName ?? NA,
          type: 'text',
        },
        {
          title: 'Last name',
          value: data.personalDetails.lastName ?? NA,
          type: 'text',
        },
        {
          title: 'Date of birth',
          value: data.personalDetails.dateOfBirth
            ? dateTimeFactory(
                data.personalDetails.dateOfBirth,
              ).formatWithShortName()
            : NA,
          type: 'text',
        },
        {
          title: 'Title',
          value: get(
            find(getTitles as Options[], { id: data.personalDetails.titleId }),
            'name',
            NA,
          ),
          type: 'text',
        },
        {
          title: 'Gender',
          value: get(
            find(getGenders as Options[], {
              id: data.personalDetails.genderId,
            }),
            'name',
            NA,
          ),
          type: 'text',
        },
        {
          title: 'Marital status',
          value: get(
            find(getMaritalStatuses as Options[], {
              id: data.personalDetails.maritalStatusId,
            }),
            'name',
            NA,
          ),
          type: 'text',
        },
        {
          title: 'Nationality',
          value: get(
            find(getNationalities as Options[], {
              id: data.personalDetails.nationalityId,
            }),
            'name',
            NA,
          ),
          type: 'text',
        },
        {
          title: 'Country of birth',
          value: get(
            find(getCountries as Options[], {
              id: data.personalDetails.birthCountryId,
            }),
            'name',
            NA,
          ),
          type: 'text',
        },
        {
          title: 'Country of citizenship',
          value: get(
            find(getCountries as Options[], {
              id: data.personalDetails.primaryCountryId,
            }),
            'name',
            NA,
          ),
          type: 'text',
        },
        {
          title: 'Dual citizenship',
          value: get(
            find(getCountries as Options[], {
              id: data.personalDetails.secondaryCountryId,
            }),
            'name',
            NA,
          ),
          type: 'text',
        },
      ],
      retirementDetails: [
        {
          title: 'Will you qualify for full state pension at retirement age?',
          value: data.retirementDetails.statePension,
          type: 'boolean',
        },
        {
          title: 'Are you already retired?',
          value: data.retirementDetails.alreadyRetired,
          type: 'boolean',
        },
      ],
      furtherInformations: [
        {
          title: 'Have you experienced financial advice before?',
          value: data.furtherInformations.previousFinancialAdvice,
          type: 'boolean',
        },
        {
          title: 'Do you have previous investment experience?',
          value: data.furtherInformations.previousInvestmentExperience,
          type: 'option',
        },
        {
          title: 'Do you wish to consider ethical investments?',
          value: data.furtherInformations.ethicalInvestments,
          type: 'boolean',
        },
        {
          title: 'Does your religious views restrict your investment options?',
          value: data.furtherInformations.religiousRestrictions,
          type: 'boolean',
        },
        {
          title: 'Are you a vulnerable person?',
          value: data.furtherInformations.isVulnerablePerson,
          type: 'boolean',
        },
        {
          title: 'Do you have a will in place?',
          value: data.furtherInformations.will,
          type: 'option',
        },
        {
          title: 'Credit history?',
          value: data.furtherInformations.creditHistory,
          type: 'option',
        },
        {
          title: 'Employment status',
          value: data.furtherInformations.employmentStatus,
          type: 'option',
        },
        {
          title: 'National Insurance number',
          value: data.furtherInformations.insuranceNumber,
          type: 'option',
        },
      ],
    };
  };
</script>
