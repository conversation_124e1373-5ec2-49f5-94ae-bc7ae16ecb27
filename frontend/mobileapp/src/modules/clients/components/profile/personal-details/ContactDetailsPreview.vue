<template>
  <BoxAccordion is-static>
    <template #title>
      <div class="font-medium">Contact details</div>
    </template>
    <template #action>
      <router-link to="/profile/contacts/edit" class="flex">
        <MaterialDesignIcon
          icon="edit"
          class="text-[1.15rem] text-gray-500/80"
        />
      </router-link>
    </template>
    <template #panel>
      <div>
        <BaseSection
          v-for="detail in getDetails(props.data)"
          :key="detail.title"
          class="py-4"
        >
          <dl>
            <dt class="text-sm text-gray-500">{{ detail.title }}</dt>
            <dd
              class="text-base font-medium text-gray-900 sm:col-span-2 sm:mt-0"
            >
              <template v-if="detail.type === 'text'">{{
                detail.value
              }}</template>
              <template v-if="detail.type === 'link'">
                <a :href="detail.href" class="text-secondary break-words"
                  >{{ detail.value
                  }}<MaterialDesignIcon
                    icon="open_in_new"
                    class="text-secondary"
                  />
                </a>
              </template>
              <template v-if="detail.type === 'list'">
                <ul>
                  <li
                    v-for="(item, itemIndex) in detail.items"
                    :key="`item-${itemIndex}`"
                  >
                    <a :href="item.href" class="text-secondary break-words">{{
                      item.value
                    }}</a>
                  </li>
                </ul>
              </template>
            </dd>
          </dl>
        </BaseSection>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { ViewDetail } from '@aventur-shared/types/ViewDetail';
  import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';

  import BoxAccordion from '@/components/BoxAccordion.vue';
  //

  const NONE = '-';

  const props = defineProps<{
    data: AboutYou['contactDetails'];
  }>();

  const getDetails = (data: typeof props.data): ViewDetail[] => [
    {
      title: 'Email',
      value: data.email ?? NONE,
      type: 'text',
    },
    {
      title: 'Mobile number',
      value: data.mobileNumber ?? NONE,
      type: 'text',
    },
    {
      title: 'Phone number',
      value: data.phoneNumber ?? NONE,
      type: 'text',
    },
  ];
</script>
