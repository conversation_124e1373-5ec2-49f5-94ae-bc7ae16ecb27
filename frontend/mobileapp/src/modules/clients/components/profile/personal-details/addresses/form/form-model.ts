import { InferType, array, boolean, number, object, string } from 'yup';

import {
  fieldRequiredMessage,
  invalidCharacterMessage,
} from '@aventur-shared/utils/form/validation-messages';
import { ListElement } from '@aventur-shared/utils/list';
import { factory as dateTimeFactory } from '@aventur-shared/utils/dateTime';
import { invalidCharactersRegex } from '@aventur-shared/utils/form/fields';
import { Address } from '@aventur-shared/modules/factfind';
//

export type AddressListItem = ListElement<Address>;

export type FormValues = InferType<typeof validationSchema>;

export const validationSchema = object({
  id: number().nullable().default(null),
  addressLineOne: string()
    .required(fieldRequiredMessage)
    .matches(invalidCharactersRegex, {
      message: invalidCharacterMessage,
    }),
  addressLineTwo: string()
    .nullable()
    .default(null)
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: invalidCharacterMessage,
    }),
  addressLineThree: string()
    .nullable()
    .default(null)
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: invalidCharacterMessage,
    }),
  addressLineFour: string()
    .nullable()
    .default(null)
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: invalidCharacterMessage,
    }),
  city: string().nullable().default(null).matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  countryId: number().required(fieldRequiredMessage),
  postCode: string()
    .required(fieldRequiredMessage)
    .matches(invalidCharactersRegex, {
      message: invalidCharacterMessage,
    }),
  moveInDate: string()
    .nullable()
    .default(null)
    .transform((value) => (value === '' ? null : value)),
  moveOutDate: string()
    .nullable()
    .default(null)
    .transform((value) => (value === '' ? null : value)),
  isPrimary: boolean().nullable(),
});

export const addressesValidationSchema = array()
  .of(validationSchema)
  .test(
    'has-primary',
    'Exactly one primary address must be specified.',
    (value) =>
      !value?.length ||
      value?.filter((address) => address.isPrimary).length === 1,
  );

export const getInitialValues = (addr?: Address) => ({
  ...addr,
  moveInDate: addr?.moveInDate
    ? dateTimeFactory(addr.moveInDate).formatForForm()
    : null,
  moveOutDate: addr?.moveOutDate
    ? dateTimeFactory(addr.moveOutDate).formatForForm()
    : null,
});
