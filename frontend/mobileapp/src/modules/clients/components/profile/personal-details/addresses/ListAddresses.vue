<template>
  <BaseSection
    v-for="(item, index) in sortedItems"
    :key="index"
    class="my-4 flex flex-col gap-1"
  >
    <router-link :to="`/profile/address/${item.id}`">
      <AddressRow :address="item" />
    </router-link>
  </BaseSection>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  import { sortAddresses } from '@aventur-shared/services/address';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import { Address } from '@aventur-shared/modules/factfind';

  import AddressRow from './AddressRow.vue';
  //

  const { items } = defineProps<{
    items: Address[];
  }>();

  const sortedItems = computed(() => sortAddresses(items));
</script>
