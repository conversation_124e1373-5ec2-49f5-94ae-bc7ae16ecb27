import { object, string } from 'yup';

import { ListElement } from '@aventur-shared/utils/list';
import { invalidCharactersRegex } from '@aventur-shared/utils/form/fields';
import { invalidCharacterMessage } from '@aventur-shared/utils/form/validation-messages';
import { ArrayElement, Nullable } from '@aventur-shared/types/Common';
import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';
import { Relation } from '@aventur-shared/types/Relationship';
//

export type FamilyMember = ArrayElement<AboutYou['familyMembers']>;

export type FormValues = {
  id: Nullable<number>;
  firstName: Nullable<Relation['firstName']>;
  lastName: Relation['lastName'];
  dateOfBirth: Nullable<string>;
  relationshipType: Nullable<string>;
};

export type FamilyMembersListItem = ListElement<FamilyMember>;

export const validationSchema = object({
  firstName: string().nullable().required().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  lastName: string().nullable().required().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  dateOfBirth: string()
    .nullable()
    .transform((value) => (value === '' ? null : value)),
  relationshipType: string()
    .nullable()
    .required()
    .transform((value) => (value === '' ? null : value)),
});
