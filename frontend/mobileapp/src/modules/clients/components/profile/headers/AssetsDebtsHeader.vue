<template>
  <BaseSection no-padding>
    <div class="p-6 text-gray-300">
      <div
        v-if="currentRouteItem"
        class="flex flex-col items-center justify-center"
      >
        <h1>{{ currentRouteItem.name }}</h1>
        <p class="text-sm text-white/60">
          {{ currentRouteItem.productGroupName }}
        </p>
      </div>
      <div v-else class="flex items-center justify-start">
        Your assets and debts are fundamental to our financial guidance and the
        way we determine your net worth.
      </div>
    </div>
  </BaseSection>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useRoute } from 'vue-router';

  import BaseSection from '@aventur-shared/components/BaseSection.vue';

  import {
    useCurrentAsset,
    useCurrentDebt,
  } from '@modules/clients/components/profile/composables';
  //

  const route = useRoute();
  const { currentAsset } = useCurrentAsset(route);
  const { currentDebt } = useCurrentDebt(route);

  const currentRouteItem = computed(
    () => currentAsset.value || currentDebt.value,
  );
</script>
