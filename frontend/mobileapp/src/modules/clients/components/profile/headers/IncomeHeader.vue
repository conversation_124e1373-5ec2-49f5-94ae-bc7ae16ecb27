<template>
  <div class="p-6 text-gray-300">
    <div v-if="currentIncome" class="flex flex-col items-center justify-center">
      <h1>{{ currentIncome.typeName }}</h1>
      <p class="text-sm text-white/60">{{ currentIncome.description }}</p>
    </div>
    <div v-else class="flex items-center justify-start">
      Breakdown of where your money comes from.
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { useCurrentIncome } from '@modules/clients/components/profile/composables/use-current-income';
  //

  const route = useRoute();
  const { currentIncome } = useCurrentIncome(route);
</script>
