<template>
  <div class="p-6 text-gray-300">
    <div
      v-if="currentExpenditure"
      class="flex flex-col items-center justify-center"
    >
      <h1>{{ currentExpenditure.typeName }}</h1>
      <p class="text-sm text-white/60">{{ currentExpenditure.description }}</p>
    </div>
    <div v-else class="flex items-center justify-start">
      Breakdown of how you spend you money.
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { useCurrentExpenditure } from '@modules/clients/components/profile/composables/use-current-expenditure';
  //

  const route = useRoute();
  const { currentExpenditure } = useCurrentExpenditure(route);
</script>
