<template>
  <form @submit.prevent="onSubmit" data-testid="contact-details">
    <div class="px-7">
      <text-field label="Email address" name="email" />
      <Alert :icon="false" class="text-xs" type="warning">
        <p>
          Changing your email will reset your sign-in details. You'll be logged
          off and need to login using your new email address and set a new
          password.
        </p>
      </Alert>
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton
          data-testid="cancel-button"
          theme="text-like"
          @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          data-testid="submit-button"
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="isLoading"
          >Save
        </BaseButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { useForm } from 'vee-validate';

  import { TextField } from '@aventur-shared/components/form';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { ClientId, useClientStore } from '@aventur-shared/modules/clients';

  import { FormValues, validationSchema } from './form-model';
  import Alert from '@aventur-shared/components/Alert.vue';
  //

  const { isLoading } = useAPIState();
  const { getProfile: client } = useClientStore();

  const emit = defineEmits<{
    (e: 'on-cancel'): void;
    (e: 'on-update', data: { clientId: ClientId; email: string }): void;
  }>();

  const { handleSubmit, meta } = useForm<FormValues>({
    validationSchema,
    initialValues: {
      email: client.email as string,
    },
  });

  const onSubmit = handleSubmit((values: FormValues) => {
    emit('on-update', { clientId: client.id, email: values.email });
  });
</script>
