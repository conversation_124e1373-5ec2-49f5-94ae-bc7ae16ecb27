<template>
  <BoxAccordion is-static :is-busy="isLoading">
    <template #title>
      <div class="font-medium">Marketing preferences</div>
    </template>
    <ul role="list" class="divide-y divide-gray-100">
      <li
        v-for="subscription in subscriptions"
        :key="subscription.id"
        class="flex items-center justify-between gap-x-6 py-5"
      >
        <div class="min-w-0">
          <div class="flex items-start gap-x-3">
            <p class="text-sm font-semibold leading-6 text-gray-900">
              {{ subscription.name }}
            </p>
          </div>
          <div
            class="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500"
          >
            <p
              :class="[
                statusCssMap[getSubscriptionStatusCaption(subscription.status)],
                'mt-0.5 whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium ring-1 ring-inset',
              ]"
            >
              {{ getSubscriptionStatusCaption(subscription.status) }}
            </p>
            <svg viewBox="0 0 2 2" class="size-0.5 fill-current text-gray-300">
              <circle cx="1" cy="1" r="1" />
            </svg>
            <p class="whitespace-nowrap">
              <span v-if="subscription.compulsory">Required</span>
              <span v-else>
                <template v-if="subscription.recommended">Recommended</template>
                <template v-else>Optional</template>
              </span>
            </p>
          </div>
        </div>
        <div class="-mb-3 flex flex-none items-center gap-x-4">
          <SubscriptionToggle
            :client-id="userId as ClientId"
            :subscription="subscription"
            @on-update="handleUpdate"
          />
        </div>
      </li>
    </ul>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { onMounted, ref } from 'vue';
  import { find, get, intersection, map, omit } from 'lodash';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import {
    getClientSubscriptions,
    getSubscriptionLists,
    subscribeMarketingContact,
    unsubscribeMarketingContact,
  } from '@aventur-shared/modules/factfind/api';
  import {
    ContactList,
    ContactSubscription,
  } from '@aventur-shared/modules/factfind/models/marketing';
  import { SubscriptionStatus } from '@aventur-shared/modules/factfind/types';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId, useClientStore } from '@aventur-shared/modules/clients';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';
  import { usePullRefresh } from '@/composables/usePullRefresh';

  import BoxAccordion from '@/components/BoxAccordion.vue';
  import SubscriptionToggle from './SubscriptionToggle.vue';
  //

  const toast = useToast();
  const { userId } = useUserStore();
  const { loadClientGoals } = useClientStore();
  const { getClientGoals } = storeToRefs(useClientStore());
  const { registerRefreshHandler } = usePullRefresh();

  const { isLoading } = useAPIState();
  const contactId = ref<string>();
  const contactLists = ref<ContactList[]>([]);
  const subscriptions = ref<ContactSubscription[]>([]);

  const getContactListNameById = (id: string) => {
    return get(find(contactLists.value, ['id', id]), 'name');
  };

  const getSubscriptionStatus = (subscriptionId: string) =>
    find(subscriptions.value, ['id', subscriptionId])?.status;

  const setSubscriptionStatus = (
    subscriptionId: string,
    status: SubscriptionStatus,
  ) => {
    subscriptions.value = subscriptions.value.map((subscription) =>
      subscription.id == subscriptionId
        ? { ...subscription, ...{ status } }
        : subscription,
    );
  };

  const getSubscriptionStatusCaption = (value: SubscriptionStatus) => {
    const indexOf = Object.values(SubscriptionStatus).indexOf(
      value as unknown as SubscriptionStatus,
    );
    return Object.keys(SubscriptionStatus)[indexOf];
  };

  const handleUpdate = (status: boolean, subscription: ContactSubscription) => {
    if (isLoading.value || subscription.compulsory) return;
    updateSubscription(status, subscription.id);
  };

  const updateSubscription = async (
    status: boolean,
    subscriptionId: string,
  ) => {
    if (!contactId.value) {
      return;
    }

    if (
      status &&
      getSubscriptionStatus(subscriptionId) != SubscriptionStatus.Active
    ) {
      try {
        const { status } = await subscribeMarketingContact(
          userId as ClientId,
          contactId.value,
          subscriptionId,
        );
        setSubscriptionStatus(subscriptionId, status);
        toast.success(
          `Successfully subscribed to '${getContactListNameById(
            subscriptionId,
          )}' list`,
        );
      } catch (e) {
        toast.error(e as Error);
      }
    }

    if (
      !status &&
      getSubscriptionStatus(subscriptionId) == SubscriptionStatus.Active
    ) {
      try {
        const { status } = await unsubscribeMarketingContact(
          userId as ClientId,
          contactId.value,
          subscriptionId,
        );
        setSubscriptionStatus(subscriptionId, status);
        toast.success(
          `Successfully unsubscribed from '${getContactListNameById(
            subscriptionId,
          )}' list`,
        );
      } catch (e) {
        toast.error(e as Error);
      }
    }
  };

  const action = async () => {
    const clientGoals = getClientGoals.value.map(
      (goal: ClientGoal) => goal.goalTypeId,
    );

    contactLists.value = await getSubscriptionLists();
    const { contact, contactLists: lists } = await getClientSubscriptions(
      userId as ClientId,
    );

    contactId.value = contact.id;
    subscriptions.value = map(contactLists.value, (list: ContactList) => ({
      ...omit(list, ['stringid']),
      status: find(lists, ['list', list.id])?.status || SubscriptionStatus.Any,
      recommended: !!intersection(clientGoals, list.required_goals).length,
    })).filter(
      (list: ContactSubscription) =>
        // hide required lists
        !list.compulsory,
    );
  };

  onMounted(async () => {
    try {
      registerRefreshHandler(async () => {
        await loadClientGoals(userId as ClientId);
        await action();
      });
      await action();
    } catch (e) {
      toast.error(e as Error);
    }
  });

  const statusCssMap = {
    Any: 'text-gray-600 bg-gray-50 ring-gray-500/10',
    Unconfirmed: 'text-yellow-800 bg-yellow-50 ring-yellow-600/20',
    Active: 'text-green-700 bg-green-50 ring-green-600/20',
    Unsubscribed: 'text-gray-100 bg-gray-500 ring-gray-500',
    Bounced: 'text-red-800 bg-red-50 ring-red-600/20',
  };
</script>
