<template>
  <BoxAccordion is-static>
    <template #title>
      <div class="font-medium">Authentication</div>
    </template>
    <template #action>
      <router-link to="/profile/account/edit" class="flex">
        <MaterialDesignIcon
          icon="edit"
          class="text-[1.15rem] text-gray-500/80"
        />
      </router-link>
    </template>
    <template #panel>
      <div>
        <BaseSection class="py-4">
          <dl>
            <dt class="text-sm text-gray-500">Email</dt>
            <dd
              class="text-base font-medium text-gray-900 sm:col-span-2 sm:mt-0"
            >
              {{ props.client.email }}
            </dd>
          </dl>
        </BaseSection>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import { Client } from '@aventur-shared/modules/clients';

  import BoxAccordion from '@/components/BoxAccordion.vue';
  //

  const props = defineProps<{
    client: Client;
  }>();
</script>
