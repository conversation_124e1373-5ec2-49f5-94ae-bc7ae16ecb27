import { computed } from 'vue';

import { Income } from '@aventur-shared/modules/factfind';

import { validationSchema } from './form-model';
//

const defaultValues = () => ({
  id: null,
  type: null,
  typeGroup: null,
  description: null,
  frequency: null,
  amount: null,
});

export const useIncomeForm = (income?: Income) => {
  const initialValues = computed(() =>
    income
      ? {
          id: income.id,
          typeGroup: income.typeGroup,
          type: income.type,
          description: income.description,
          frequency: income.frequency.toValue(),
          amount: String(income.amount.getValue()),
        }
      : defaultValues(),
  );

  return {
    initialValues,
    validationSchema,
  };
};
