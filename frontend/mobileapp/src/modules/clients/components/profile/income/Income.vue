<template>
  <BoxAccordion is-static :is-busy="isLoading">
    <template #title>
      <div class="font-medium">Your Income</div>
    </template>
    <template #panel>
      <BaseSection no-padding-x class="p-5">
        <div class="flex flex-row place-items-center gap-3" data-income-summary>
          <div class="h-10 w-1 bg-[#C9E386]" />
          <div class="flex flex-col">
            <dt class="text-xs">Monthly Total</dt>
            <dd class="text-primary text-lg font-semibold">
              {{ formatWithCurrency(new Money(totalOverall)) }}
            </dd>
          </div>
        </div>
      </BaseSection>
      <IncomeList :incomes="incomes" />
      <hr class="mx-5 h-px border-0 bg-black/5" />
      <div class="flex items-center justify-center p-4">
        <router-link
          to="income/add"
          class="text-primary text-sm font-medium underline"
        >
          + Add Income
        </router-link>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { computed, onMounted } from 'vue';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { Income, useFactfindStore } from '@aventur-shared/modules/factfind';
  import { getMonthlyAmount } from '@aventur-shared/modules/factfind/services/frequency-amount';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';

  import { usePullRefresh } from '@/composables/usePullRefresh';
  import BoxAccordion from '@/components/BoxAccordion.vue';
  import IncomeList from './IncomeList.vue';
  //

  const toast = useToast();
  const { userId } = useUserStore();
  const { isLoading } = useAPIState();
  const { loadClientIncomes } = useFactfindStore();
  const { getIncomes: incomes } = storeToRefs(useFactfindStore());
  const { registerRefreshHandler } = usePullRefresh();

  const totalOverall = computed<number>(() =>
    incomes.value.reduce(
      (sum: number, income: Income) =>
        getMonthlyAmount(income.amount, income.frequency).getValue() + sum,
      0,
    ),
  );

  const action = async () => {
    await loadClientIncomes(userId as ClientId);
  };

  onMounted(async () => {
    registerRefreshHandler(action);

    try {
      await action();
    } catch (e) {
      toast.error(e as Error);
    }
  });
</script>
