<template>
  <BaseSection v-for="(income, i) in incomes" :key="income.id ?? i">
    <router-link :to="`/profile/income/${income.id}`">
      <IncomeListItem :income="income" />
    </router-link>
  </BaseSection>
</template>

<script setup lang="ts">
  import { Income } from '@aventur-shared/modules/factfind';
  import { default as BaseSection } from '@aventur-shared/components/BaseSection.vue';

  import IncomeListItem from './IncomeListItem.vue';
  //

  defineProps<{
    incomes: Income[];
  }>();
</script>
