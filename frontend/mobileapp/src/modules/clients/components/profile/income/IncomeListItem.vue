<template>
  <div class="flex w-full items-start justify-stretch" data-income-item>
    <div class="grow">
      <p class="text-base font-semibold text-gray-900">
        {{ incomeTypeName }}
      </p>
      <p class="break-all">{{ props.income.description }}</p>
    </div>
    <div>
      <div class="flex flex-col items-end">
        <p class="break-all">
          {{ formatWithCurrency(props.income.amount) }}
        </p>
        <p class="text-xs text-gray-500">{{ income.frequency.toLabel() }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  import { Income } from '@aventur-shared/modules/factfind';
  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import { formatWithCurrency } from '@aventur-shared/utils/money';

  const { getIncomeByTypeId } = useCashFlow();

  const props = defineProps<{
    income: Income;
  }>();

  const incomeGroupName = computed(() => {
    return getIncomeByTypeId(props.income.type)?.group_name ?? 'N/A';
  });
  const incomeTypeName = computed(() => {
    return getIncomeByTypeId(props.income.type)?.name ?? 'N/A';
  });
</script>
