<template>
  <form @submit.prevent="onSubmit">
    <div class="px-7">
      <select-field
        label="Type Group"
        name="typeGroup"
        :searchable="true"
        :disabled="isEditForm"
        :options="cashFlow.getIncomeGroupsOptions()"
        @on-select="handleGroupSelect"
      />
      <select-field
        label="Type"
        name="type"
        :searchable="true"
        :options="typeSelectOptions"
        :disabled="!values.typeGroup || isEditForm"
      />
      <text-field
        label="Description"
        name="description"
        hint="A short description of your income source"
      />
      <select-field
        label="Frequency"
        name="frequency"
        :searchable="false"
        :options="frequencySelectOptions"
      />
      <CurrencyField label="Amount, £" name="amount" no-append />
    </div>
    <div v-if="isEditForm" class="flex items-center justify-center py-3">
      <BaseButton
        theme="text-like"
        class="font-bold text-red-900 underline"
        @click="handleDelete"
        >Delete Income</BaseButton
      >
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton theme="text-like" @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.valid"
          :is-busy="APIState.isLoading"
          >Save
        </BaseButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { useForm } from 'vee-validate';
  import { Ref, computed, onMounted, ref } from 'vue';

  import { Money } from '@aventur-shared/utils/money';
  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import {
    CurrencyField,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import {
    type Income,
    IncomeListItem,
  } from '@aventur-shared/modules/factfind/models/income';
  import {
    Frequency,
    FrequencyEnum,
    frequencySelectOptions,
  } from '@aventur-shared/modules/factfind/models/frequency';

  import { type FormValues } from './form-model';
  import { useIncomeForm } from './use-income-form';
  //

  const { APIState } = useAPIState();
  const cashFlow = useCashFlow();
  const submitButtonRef = ref<HTMLElement | null>(null);

  onMounted(async () => {
    emit('on-load', { focusRef: submitButtonRef });
  });

  const props = defineProps<{
    income?: IncomeListItem;
  }>();

  const isEditForm = !!props.income;

  const emit = defineEmits<{
    (e: 'on-add', income: Income): void;
    (e: 'on-update', income: IncomeListItem): void;
    (e: 'on-delete', income: IncomeListItem): void;
    (e: 'on-load', { focusRef }: { focusRef: Ref<HTMLElement | null> }): void;
    (e: 'on-cancel'): void;
  }>();

  const { initialValues, validationSchema } = useIncomeForm(props.income);

  const { handleSubmit, setFieldValue, values, meta } = useForm<FormValues>({
    initialValues: initialValues.value as FormValues,
    validationSchema,
  });

  const getTypeSelectOptions = (groupId: number) => {
    return groupId ? cashFlow.getIncomeGroupTypesOptions(groupId) : [];
  };

  const typeSelectOptions = computed<SelectOption[]>(() => {
    return values.typeGroup ? getTypeSelectOptions(values.typeGroup) : [];
  });

  const handleGroupSelect = () => {
    setFieldValue('type', null);
  };

  const handleDelete = () => {
    props.income && emit('on-delete', props.income);
  };

  const onSubmit = handleSubmit((formValues: FormValues) => {
    const income = {
      key: props.income?.key ?? undefined,
      id: formValues.id,
      typeGroup: formValues.typeGroup as number,
      type: formValues.type as number,
      description: formValues.description as string,
      frequency: new Frequency(formValues.frequency as FrequencyEnum),
      amount: new Money(Number(formValues.amount)),
    } as IncomeListItem;

    isEditForm ? emit('on-update', income) : emit('on-add', income);
  });
</script>
