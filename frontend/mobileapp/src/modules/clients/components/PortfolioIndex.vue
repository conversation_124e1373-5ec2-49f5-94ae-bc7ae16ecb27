<template>
  <BoxAccordion is-static :is-busy="isLoading">
    <template #title>
      <div class="font-medium">Assets</div>
      <toggle
        v-if="!isLoading"
        label="Include Inactive"
        :model-value="includeInactiveAssets"
        :label-class="['!text-gray-500']"
        :disabled="disableToggleInactiveAssets"
        @update:model-value="(value) => (includeInactiveAssets = value)"
      >
        <template #label="{ label }">
          <div class="flex flex-col items-start justify-center -space-y-1">
            <span>{{ label }}</span>
            <span class="text-xs text-gray-500"
              >Showing {{ assetsList.length }} of {{ assets.length }}</span
            >
          </div>
        </template>
      </toggle>
    </template>
    <template #panel>
      <div class="px-7">
        <BaseSection
          v-for="(asset, i) in assetsList"
          :key="asset.id ?? i"
          divider="bottom"
          no-padding-x
          class="!border-gray-100 last-of-type:border-b-0"
        >
          <router-link :to="`/profile/assets/${asset.id}`">
            <AssetsListItem :asset="asset" />
          </router-link>
        </BaseSection>
      </div>
    </template>
  </BoxAccordion>

  <BoxAccordion is-static :is-busy="isLoading">
    <template #title>
      <div class="font-medium">Debts</div>
      <toggle
        v-if="!isLoading"
        label="Include Inactive"
        :model-value="includeInactiveDebts"
        :label-class="['!text-gray-500']"
        :disabled="disableToggleInactiveDebts"
        @update:model-value="(value) => (includeInactiveDebts = value)"
      >
        <template #label="{ label }">
          <div class="flex flex-col items-start justify-center -space-y-1">
            <span>{{ label }}</span>
            <span class="text-xs text-gray-500"
              >Showing {{ debtsList.length }} of {{ debts.length }}</span
            >
          </div>
        </template>
      </toggle>
    </template>
    <template #panel>
      <div class="px-7">
        <BaseSection
          v-for="(debt, i) in debtsList"
          :key="debt.id ?? i"
          divider="bottom"
          no-padding-x
          class="!border-gray-100 last-of-type:border-b-0"
        >
          <router-link :to="`/profile/debts/${debt.id}`">
            <DebtsListItem :debt="debt" />
          </router-link>
        </BaseSection>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import Toggle from '@aventur-shared/components/Toggle.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import { Asset, Debt } from '@aventur-shared/modules/factfind/types';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { useAssetsDebtsFiltering } from '@aventur-shared/modules/factfind/composables';

  import AssetsListItem from '@modules/clients/components/profile/assets-debts/assets/AssetsListItem.vue';
  import DebtsListItem from '@modules/clients/components/profile/assets-debts/debts/DebtsListItem.vue';
  import BoxAccordion from '@/components/BoxAccordion.vue';
  //

  const { isLoading } = useAPIState();

  const props = defineProps<{
    assets: Asset[];
    debts: Debt[];
  }>();

  const {
    items: assetsList,
    includeInactive: includeInactiveAssets,
    disableToggleInactive: disableToggleInactiveAssets,
  } = useAssetsDebtsFiltering(
    () => props.assets,
    (item: Asset) => item.hasQuantity,
  );

  const {
    items: debtsList,
    includeInactive: includeInactiveDebts,
    disableToggleInactive: disableToggleInactiveDebts,
  } = useAssetsDebtsFiltering(
    () => props.debts,
    (item: Debt) => item.hasQuantity,
  );
</script>
