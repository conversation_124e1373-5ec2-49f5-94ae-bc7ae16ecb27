<template>
  <BaseBox
    v-for="section in profileSections"
    :key="section.slug"
    class="mt-4 px-7 py-5"
  >
    <BaseTitle
      class="my-2 flex flex-row justify-between font-semibold text-opacity-90"
    >
      {{ section.title }}
    </BaseTitle>
    <BaseSection no-padding>
      <ul role="list" class="divide-y divide-gray-100">
        <li v-for="item in section.items" :key="item.url" class="relative py-5">
          <template v-if="item.disabled">
            <div class="flex shrink-0 items-center gap-x-4">
              <div class="flex grow items-end">
                <p class="text-sm leading-6 text-gray-400">
                  {{ item.title }}
                </p>
              </div>
            </div>
          </template>
          <template v-else>
            <router-link :to="item.url">
              <div class="flex shrink-0 items-center gap-x-4">
                <div class="flex grow items-end">
                  <p class="text-sm leading-6 text-gray-900">
                    {{ item.title }}
                  </p>
                </div>
                <MaterialDesignIcon
                  icon="chevron_right"
                  class="flex-none text-lg text-gray-400"
                />
              </div>
            </router-link>
          </template>
        </li>
      </ul>
    </BaseSection>
  </BaseBox>
</template>

<script setup lang="ts">
  import BaseBox from '@aventur-shared/components/BaseBox.vue';
  import BaseTitle from '@aventur-shared/components/BaseTitle.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import { sections as profileSections } from '../profile-sections';
</script>
