<template>
  <DashboardSection title="Income" :loading="incomesCount === undefined">
    <div
      v-if="incomesCount === 0"
      class="flex flex-col items-center justify-center text-sm"
    >
      <h3 class="font-medium uppercase">complete your profile</h3>
      <router-link to="/profile/income" class="text-primary"
        >Add details &raquo;</router-link
      >
    </div>
    <div v-else-if="incomesCount" class="flex flex-col justify-between gap-6">
      <div class="whitespace-nowrap text-xl">
        <span class="font-medium">{{ formatWithCurrency(total) }}</span
        ><span class="block text-sm text-gray-500">per month</span>
      </div>
      <router-link to="/profile/income">
        <div
          class="flex h-16 items-center justify-start gap-5 rounded-lg border border-gray-200 px-4 py-2"
        >
          <div class="size-12" data-chart-container>
            <Doughnut :data="chartData" :options="chartOptions" />
          </div>
          <span class="grow">{{ incomesCount }} sources</span>
          <MaterialDesignIcon
            icon="chevron_right"
            class="flex-none text-lg text-gray-400"
          />
        </div>
      </router-link>
    </div>
  </DashboardSection>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { ArcElement, Chart } from 'chart.js';
  import { Doughnut } from 'vue-chartjs';

  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { formatWithCurrency } from '@aventur-shared/utils/money';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { getIncomes } from '@aventur-shared/modules/factfind/api';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { Income } from '@aventur-shared/modules/factfind/models';

  import { usePullRefresh } from '@/composables/usePullRefresh';
  import {
    getChartData,
    chartOptions as getChartOptions,
    getTotal as getIncomeTotal,
  } from '@modules/clients/components/dashboard/services';
  import DashboardSection from '@modules/clients/components/dashboard/DashboardSection.vue';
  //

  Chart.register(ArcElement);

  const toast = useToast();
  const { userId } = useUserStore();
  const { registerRefreshHandler } = usePullRefresh();

  const incomes = ref<Income[]>([]);
  const incomesCount = ref<number | undefined>();
  const total = ref<number>(0);

  const chartData = computed(() => getChartData(incomes.value));
  const chartOptions = computed(() => ({
    ...getChartOptions,
    borderWidth: 1 - (incomesCount.value as number) ? 1 : 0,
  }));

  const action = async () => {
    incomes.value = await getIncomes(userId as ClientId);
    incomesCount.value = incomes.value.length;
    total.value = getIncomeTotal(incomes.value);
  };

  registerRefreshHandler(async () => {
    incomesCount.value = undefined;
    await action();
  });

  onMounted(async () => {
    try {
      await action();
    } catch (e) {
      toast.error(e as Error);
    }
  });
</script>
