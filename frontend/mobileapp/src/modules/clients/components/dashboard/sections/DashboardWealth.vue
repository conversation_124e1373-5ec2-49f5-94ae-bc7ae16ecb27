<template>
  <DashboardSection title="Net Wealth" :is-busy="isLoading">
    <div class="flex flex-col items-start justify-between gap-5">
      <div class="flex gap-5">
        <h2 class="text-2xl font-medium">{{ formattedNetTotal }}</h2>
        <div class="flex flex-row place-items-center">
          <MaterialDesignIcon
            v-if="selectedRangePerformance > 0"
            icon="play_arrow"
            class="material-icons mt-1 -rotate-90 text-green-800"
          />
          <MaterialDesignIcon
            v-if="selectedRangePerformance < 0"
            icon="play_arrow"
            class="material-icons -mt-1 rotate-90 text-red-800"
          />
          <span class="text-sm text-gray-500"
            >{{
              formatPercentForView(selectedRangePerformance, {
                signDisplay: 'exceptZero',
              })
            }}
            over last {{ selectedRange }}</span
          >
        </div>
      </div>
      <div class="flex flex-row gap-6">
        <div class="flex flex-row place-items-center gap-3">
          <div class="h-10 w-1 bg-[#C9E386]" />
          <div class="flex flex-col">
            <span class="text-gray-500">Assets</span>
            <span class="text-sm font-medium">{{ formattedAssetsSum }}</span>
          </div>
        </div>
        <div class="flex flex-row place-items-center gap-3">
          <div class="h-10 w-1 bg-[#F18253]" />
          <div class="flex flex-col">
            <span class="text-gray-500">Debts</span>
            <span class="whitespace-nowrap text-sm font-medium"
              >-{{ formattedDebtsSum }}</span
            >
          </div>
        </div>
      </div>
    </div>
  </DashboardSection>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref } from 'vue';

  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';
  import { getClientPortfolioValuations } from '@aventur-shared/modules/clients/api';
  import { formatPercentForView } from '@aventur-shared/utils/form/formatters';
  import {
    PortfolioValuation,
    SelectedRange,
  } from '@aventur-shared/modules/clients/types';
  import {
    rangeToDtoFromDateMapper,
    rangeToDtoPeriodMapper,
  } from '@aventur-shared/modules/clients/utils/mappers/portfolioValuationMapper';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';

  import { usePullRefresh } from '@/composables/usePullRefresh';
  import DashboardSection from '@modules/clients/components/dashboard/DashboardSection.vue';
  //

  const { isLoading } = useAPIState();
  const { userId } = useUserStore();
  const { loadAssetsAndDebts } = useFactfindStore();
  const { registerRefreshHandler } = usePullRefresh();

  const selectedRange = ref<SelectedRange>('1y');
  const selectedRangePerformance = ref<number>(0);
  const lastItem = ref<PortfolioValuation>();

  const assetsSum = computed(
    () => new Money(lastItem.value?.assetValuation ?? 0),
  );
  const formattedAssetsSum = computed(() =>
    formatWithCurrency(assetsSum.value, { maximumFractionDigits: 0 }),
  );

  const debtsSum = computed(
    () => new Money(lastItem.value?.debtValuation ?? 0),
  );
  const formattedDebtsSum = computed(() =>
    formatWithCurrency(debtsSum.value, { maximumFractionDigits: 0 }),
  );

  const formattedNetTotal = computed(() =>
    formatWithCurrency(assetsSum.value.getValue() - debtsSum.value.getValue(), {
      maximumFractionDigits: 0,
    }),
  );

  const handleRangeChange = async (range: SelectedRange) => {
    selectedRange.value = range;
    const period = rangeToDtoPeriodMapper(range);
    const fromDate = rangeToDtoFromDateMapper(range);
    const valuations = await getClientPortfolioValuations(
      userId as ClientId,
      period,
      fromDate,
    );

    selectedRangePerformance.value =
      (valuations.slice(-1)[0].netValuation - valuations[0].netValuation) /
      Math.abs(valuations[0].netValuation);
    lastItem.value = valuations.pop();
  };

  registerRefreshHandler(async () => {
    await loadAssetsAndDebts(Number(userId) as ClientId);
    await handleRangeChange(selectedRange.value);
  });

  onMounted(async () => {
    if (userId) {
      await loadAssetsAndDebts(Number(userId) as ClientId);
    }
    await handleRangeChange(selectedRange.value);
  });
</script>
