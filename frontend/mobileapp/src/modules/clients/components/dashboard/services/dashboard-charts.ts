import { ChartData, ChartOptions } from 'chart.js';
import { getMonthlyAmount } from '@aventur-shared/modules/factfind/services/frequency-amount';
import { getChartColors } from '@aventur-shared/constants';
import { Expenditure, Income } from '@aventur-shared/modules/factfind/models';

export const chartOptions: Partial<ChartOptions<'doughnut'>> = {
  responsive: true,
  plugins: {
    legend: {
      display: false,
    },
  },
  animation: {
    delay: 100,
  },
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  borderWidth: 1,
};

export const getChartData = (
  data: Income[] | Expenditure[],
): ChartData<'doughnut', any> => ({
  datasets: [
    {
      label: 'Income',
      data: data.map((item: Income | Expenditure) =>
        getMonthlyAmount(item.amount, item.frequency).getValue(),
      ),
      backgroundColor: getChartColors(),
    },
  ],
});
