<template>
  <BoxAccordion is-open is-static>
    <template #title>
      <h1 class="font-normal">{{ title }}</h1>
    </template>
    <BaseSection no-padding class="p-1">
      <template v-if="loading">
        <div class="flex items-center justify-center">
          <LoadingSpinner class="size-4" />
        </div>
      </template>
      <template v-else>
        <slot />
      </template>
    </BaseSection>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import BoxAccordion from '@/components/BoxAccordion.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import LoadingSpinner from '@aventur-shared/components/LoadingSpinner.vue';

  defineProps<{
    title: string;
    loading?: boolean;
  }>();
</script>
