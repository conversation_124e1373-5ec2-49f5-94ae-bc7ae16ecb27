<template>
  <GoalList
    :groups="getGoals"
    :goals="clientGoals"
    :client="client"
    :active-holdings="activeHoldings"
    @goals-updated="onGoalsUpdated"
  />
</template>

<script setup lang="ts">
  import { inject } from 'vue';
  import { storeToRefs } from 'pinia';

  import { useRefData } from '@aventur-shared/stores';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId, useClientStore } from '@aventur-shared/modules/clients';
  import { GoalLinkedHolding } from '@aventur-shared/modules/clients/models';

  import { activeHoldingsInjectionKey } from './goals/active-holdings';
  import GoalList from './goals/ui/GoalList.vue';

  const { userId } = useUserStore();
  const { getProfile: client } = useClientStore();
  const { getGoals } = storeToRefs(useRefData());

  const { loadClientGoals, loadHealthScore } = useClientStore();
  const { getClientGoals: clientGoals } = storeToRefs(useClientStore());

  const activeHoldings = inject<GoalLinkedHolding[]>(
    activeHoldingsInjectionKey,
  ) as GoalLinkedHolding[];

  const onGoalsUpdated = async () => {
    await loadClientGoals(userId as ClientId);
    await loadHealthScore(userId as ClientId);
  };
</script>
