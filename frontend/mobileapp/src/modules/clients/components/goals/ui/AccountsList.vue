<template>
  <div data-testid="goal-item-accounts">
    <p class="font-medium text-gray-700 sm:text-sm lg:text-base">
      Link accounts
    </p>
    <ul role="list" class="divide-y divide-gray-100">
      <li
        v-for="account in activeHoldings"
        :key="account.id"
        class="flex items-center justify-between gap-x-6 py-3"
      >
        <div class="flex min-w-0 gap-x-4">
          <div class="min-w-0 flex-auto">
            <p class="truncate text-sm/6 font-semibold text-gray-900">
              {{ account.providerName }} - {{ account.productTypeName }}
            </p>
            <p class="text-xs text-gray-500">
              {{ account.accountNumber }}
            </p>
          </div>
        </div>
        <BaseButton
          theme="custom"
          class="w-20 rounded bg-white py-1 text-xs ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
          @click.stop="toggleAccountLink(account.id)"
          >{{ isLinked(account.id) ? 'Unlink' : 'Link' }}</BaseButton
        >
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
  import { map, without } from 'lodash';
  import { inject } from 'vue';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import { GoalLinkedHolding } from '@aventur-shared/modules/clients/models';
  import { activeHoldingsInjectionKey } from '@modules/clients/components/goals/active-holdings';

  const activeHoldings = inject<GoalLinkedHolding[]>(
    activeHoldingsInjectionKey,
  );

  const props = defineProps<{
    linkedAccounts?: GoalLinkedHolding[];
  }>();

  const emit = defineEmits<{
    (e: 'accounts-updated', selectedHoldingIds: number[]): void;
  }>();

  const isLinked = (accountId: number) =>
    new Set(map(props.linkedAccounts, 'id')).has(accountId);

  const toggleAccountLink = (accountId: number) => {
    const selectedHoldingIds: number[] = isLinked(accountId)
      ? without(map(props.linkedAccounts, 'id'), accountId)
      : [...map(props.linkedAccounts, 'id'), accountId];
    emit('accounts-updated', selectedHoldingIds);
  };
</script>

<style scoped></style>
