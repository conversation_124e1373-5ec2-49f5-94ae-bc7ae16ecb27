<template>
  <FormWrapper>
    <div class="flex flex-col gap-8">
      <GoalForm
        v-if="group"
        :group="group"
        :client="client"
        @on-submit="onSubmit"
        @on-cancel="back"
      />
    </div>
  </FormWrapper>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';

  import { useRefData } from '@aventur-shared/stores';
  import { Goal } from '@aventur-shared/stores/refdataStore';
  import { useClientStore } from '@aventur-shared/modules/clients';

  import FormWrapper from './FormWrapper.vue';
  import GoalForm from './form/form.vue';

  const router = useRouter();
  const { loadClientGoals } = useClientStore();

  const back = () => router.go(-1);

  const props = defineProps<{
    groupId: Goal['id'];
  }>();

  const { getGoalById } = useRefData();
  const { getProfile: client } = useClientStore();

  const group = computed(() => getGoalById(Number(props.groupId)) as Goal);

  const onSubmit = async () => {
    await loadClientGoals(client.id);
  };
</script>
