<template>
  <div data-testid="goal-item-footer" class="text-xs text-gray-400">
    <p>
      Clients:&nbsp;<span class="text-primary">{{
        goal.clientIds.length
      }}</span>
    </p>
    <p>
      Open cases:&nbsp;<span class="text-primary">{{ goal.cases.length }}</span>
    </p>
    <p>
      Linked assets:&nbsp;<span class="text-primary">{{
        goal.linkedHoldings?.length
      }}</span>
    </p>
    <p>
      Risk profile updated:&nbsp;<span class="text-primary">{{
        goal.riskProfile
          ? formatDatetimeStringForView(goal.riskProfile.updated_at)
          : 'N/A'
      }}</span>
    </p>
    <p>
      Cash forecast updated:&nbsp;<span class="text-primary">{{
        goal.cashForecast
          ? formatDatetimeStringForView(goal?.cashForecast.updated_at)
          : 'N/A'
      }}</span>
    </p>
  </div>
</template>

<script setup lang="ts">
  import { formatDatetimeStringForView } from '@aventur-shared/utils/dateTime';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';
  //

  defineProps<{
    goal: ClientGoal;
  }>();
</script>
