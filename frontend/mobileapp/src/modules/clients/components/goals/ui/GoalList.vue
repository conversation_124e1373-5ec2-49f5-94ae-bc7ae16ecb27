<template>
  <TabLayout>
    <template #body>
      <GoalTypeGroup
        v-for="group in groups"
        :group-id="group.id"
        :goals="goals"
        :client="client"
        :key="group.id"
        :is-loading="isLoading"
        @goal-created="$emit('goalsUpdated')"
      >
        <template #default="{ items }">
          <div class="flex flex-col justify-center">
            <template v-for="(goal, fieldIdx) in items" :key="goal.id">
              <GoalListItem :field-index="fieldIdx" :goal="goal" />
            </template>
          </div>
        </template>
      </GoalTypeGroup>
    </template>
  </TabLayout>
</template>

<script setup lang="ts">
  import { Client } from '@aventur-shared/modules/clients';
  import { Goal } from '@aventur-shared/types/store/Refdata';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import {
    ClientGoal,
    GoalLinkedHolding,
  } from '@aventur-shared/modules/clients/models';

  import TabLayout from '@/layouts/TabLayout.vue';
  import GoalListItem from './GoalListItem.vue';
  import GoalTypeGroup from './GoalTypeGroup.vue';

  const { isLoading } = useAPIState();

  defineProps<{
    groups: Goal[];
    client: Client;
    goals: ClientGoal[];
    activeHoldings: GoalLinkedHolding[];
  }>();

  defineEmits<{
    (e: 'goalsUpdated'): void;
  }>();
</script>
