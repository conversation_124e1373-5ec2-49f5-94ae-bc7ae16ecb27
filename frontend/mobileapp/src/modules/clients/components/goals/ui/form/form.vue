<template>
  <form @submit.prevent="onSubmit">
    <div class="px-7">
      <fieldset>
        <div class="flex flex-col gap-5">
          <SelectField
            v-if="mode === 'create'"
            label="Goal type"
            name="goalTypeId"
            :options="goalTypes"
            disabled
            @on-select="handleTypeChange"
          />
          <TextField
            label="Goal name"
            name="goalName"
            ref="goalNameField"
            :hint="fieldsHints.goalName"
          />
          <MultiSelectField
            label="Clients"
            name="clientIds"
            :options="linkedClients"
            :hint="fieldsHints.clientIds"
            @on-select="handleClientSelect"
          />
          <CurrencyField
            name="attributes.targetAmount"
            :label="fieldsLabels.targetAmount"
            :hint="fieldsHints.targetAmount"
            no-append
          />
          <DatePicker
            name="attributes.targetDate"
            :label="fieldsLabels.targetDate"
            :hint="fieldsHints.targetDate"
          />
          <template v-if="canUpdateObjectives">
            <TextAreaField label="Objectives" name="objectives" />
          </template>
          <template v-else>
            <div>
              <p class="font-medium text-gray-700">Goal objectives</p>
              <p class="text-sm text-black">
                {{ goal?.objectives ?? 'N/A' }}
              </p>
            </div>
          </template>
        </div>
      </fieldset>
    </div>
    <div
      class="fixed bottom-0 z-40 flex h-[90px] w-full items-center border-t border-black/10 bg-white px-7"
    >
      <div class="flex w-full justify-between">
        <BaseButton theme="text-like" @click="emit('on-cancel')"
          >Back</BaseButton
        >
        <BaseButton
          ref="submitButtonRef"
          type="submit"
          theme="primary"
          :disabled="!meta.dirty || !meta.valid"
          :is-busy="APIState.isLoading"
          >Save
        </BaseButton>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { merge } from 'lodash';
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useTimeoutFn } from '@vueuse/core';
  import { useField, useForm, useIsFieldDirty } from 'vee-validate';

  import { useRefData } from '@aventur-shared/stores';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { Goal } from '@aventur-shared/stores/refdataStore';
  import { Client, ClientGoal } from '@aventur-shared/modules/clients/models';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';
  import { ClientGoalAttrsMapper } from '@aventur-shared/modules/clients/utils';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';
  import {
    CurrencyField,
    DatePicker,
    MultiSelectField,
    SelectField,
    TextAreaField,
    TextField,
  } from '@aventur-shared/components/form';
  import {
    patchClientGoal,
    patchClientGoalObjectives,
    postClientGoals,
  } from '@aventur-shared/modules/factfind/api';

  import {
    AventurEvents,
    type ClientCreatedGoalEvent,
    type ClientUpdatedGoalEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import {
    ForbiddenError,
    PartialSubmissionError,
    action as saveGoalAction,
  } from './action';
  import { useGoalForm } from './use-form';
  import { FormValues } from './form-model';

  const toast = useToast();
  const router = useRouter();
  const { APIState } = useAPIState();
  const { getGoals } = useRefData();
  const { trackEvent } = useEventTracking();
  //

  const props = defineProps<{
    group: Goal;
    client: Client;
    goal?: ClientGoal;
  }>();

  const emit = defineEmits<{
    (e: 'on-submit', goal: ClientGoal | undefined): void;
    (e: 'on-cancel'): void;
  }>();

  const mode = computed<'edit' | 'create'>(() =>
    props.goal ? 'edit' : 'create',
  );

  const goalTypes = getGoals.map((goal: any) => ({
    label: goal.name,
    value: goal.id,
  }));

  const { linkedClients } = useLinkedClientList(props.client);

  const { initialValues, validationSchema, fieldsLabels, fieldsHints } =
    useGoalForm(props.group.id, props.goal);

  const { handleSubmit, resetForm, meta } = useForm({
    validationSchema,
    initialValues: merge(
      {
        clientIds: [props.client.id],
      },
      initialValues,
    ),
  });

  useField('objectives');
  const hasObjectivesUpdated = useIsFieldDirty('objectives');
  const canUpdateObjectives = computed(
    /**
     * Disabled by default. Alternatively, use `ability` to check for
     * user's permissions on the frontend.
     * @example:
     * import { useUserAbility } from '@aventur-shared/modules/users';
     * const ability = useUserAbility();
     * canUpdateObjectives = ability.can('update_client_goal_objectives_api_v1_clients', 'all');
     */
    () => false,
  );

  const handleTypeChange = () => {
    //
  };

  const handleClientSelect = () => {
    //
  };

  const mapAttrs = (goalTypeId: number, attrs: FormValues['attributes']) =>
    ClientGoalAttrsMapper.toDTO(goalTypeId, attrs);

  const onSubmit = handleSubmit(async (formValues) => {
    const actions: Promise<void | string>[] = [];

    if (mode.value === 'create') {
      actions.push(
        new Promise((resolve, reject) => {
          const createClientGoalDTO = {
            goal_type_id: formValues.goalTypeId,
            goal_name: formValues.goalName,
            client_ids: formValues.clientIds,
            goal_attributes: mapAttrs(
              formValues.goalTypeId,
              formValues.attributes,
            ),
          };
          postClientGoals(props.client.id, createClientGoalDTO)
            .then(() => {
              router.push('/goals');
              resolve();
            })
            .catch(reject);
        }),
      );
    }

    if (mode.value === 'edit') {
      if (canUpdateObjectives && hasObjectivesUpdated.value) {
        actions.push(
          new Promise((resolve, reject) => {
            // Delay execution to avoid concurrency issues as we update the same record
            useTimeoutFn(
              () =>
                patchClientGoalObjectives(props.client.id, props.goal!.id, {
                  goal_objectives: formValues.objectives as string,
                })
                  .then(resolve)
                  .catch(reject),
              250,
            );
          }),
        );
      }

      actions.push(
        new Promise((resolve, reject) => {
          const editClientGoalDTO = {
            id: props.goal!.id,
            goal_name: formValues.goalName,
            client_ids: formValues.clientIds,
            goal_attributes: mapAttrs(
              formValues.goalTypeId,
              formValues.attributes,
            ),
          };

          patchClientGoal(props.client.id, props.goal!.id, editClientGoalDTO)
            .then(resolve)
            .catch(reject);
        }),
      );
    }

    try {
      await saveGoalAction(actions);
      emit('on-submit', props.goal);
      toast.success('Goals updated');

      if (mode.value === 'create') {
        trackEvent<ClientCreatedGoalEvent>(AventurEvents.ClientCreatedGoal, {
          goal_type: props.group.code,
        });
      }
      if (mode.value === 'edit') {
        trackEvent<ClientUpdatedGoalEvent>(AventurEvents.ClientUpdatedGoal, {
          goal_id: props.goal!.id,
          goal_type: props.group.code,
        });
      }
    } catch (e) {
      if (e instanceof PartialSubmissionError) {
        return toast.warning(e.message);
      }
      if (e instanceof ForbiddenError) {
        return toast.error(e.message);
      }
      toast.error(e as Error);
    }

    resetForm({
      values: formValues,
    });
  });
</script>
