<template>
  <router-link :to="`/goals/${goal.id}`" :href="`/goals/${goal.id}`">
    <BaseSection no-padding-y class="py-3">
      <div class="flex w-full items-center justify-between gap-2">
        <div class="flex w-full flex-col items-start">
          <h1 class="flex text-base font-semibold text-gray-600">
            {{ goal.name }}
          </h1>
          <span class="sm:hidden lg:flex lg:grow"></span>
          <p class="flex text-xs font-normal text-gray-400">
            Amount:&nbsp;
            <span class="font-medium text-gray-600">{{
              goal.attributes.targetAmount
                ? formatWithCurrency(new Money(goal.attributes.targetAmount))
                : 'N/A'
            }}</span>
          </p>
          <p class="flex text-xs font-normal text-gray-400">
            Date:&nbsp;
            <span class="font-medium text-gray-600">{{
              goal.attributes.targetDate
                ? new DateTime(goal.attributes.targetDate).formatToView()
                : 'N/A'
            }}</span>
          </p>
        </div>
        <div class="w-16">
          <score-donut
            data-testid="score-donut"
            :score="goalsWithHealthScores[goal.id].healthScore.score"
          />
        </div>
        <MaterialDesignIcon
          icon="chevron_right"
          class="flex-none text-lg text-gray-400"
        />
      </div>
    </BaseSection>
  </router-link>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';

  import { DateTime } from '@aventur-shared/utils/dateTime';
  import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import { useClientStore } from '@aventur-shared/modules/clients';
  import ScoreDonut from '@aventur-shared/components/score-donut.vue';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';
  //

  defineProps<{
    goal: ClientGoal;
  }>();

  const { getGoalsWithHealthScores: goalsWithHealthScores } =
    storeToRefs(useClientStore());
</script>
