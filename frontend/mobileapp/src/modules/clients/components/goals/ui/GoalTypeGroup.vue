<template>
  <BoxAccordion :is-busy="isLoading">
    <template #title="{ open }">
      <div class="w-full items-center justify-start">
        <span class="flex font-medium">{{ group.name }}</span>
        <span
          data-group-total-items
          class="flex text-sm font-normal text-black/50"
          :class="hasGoals ? 'text-primary-700' : 'text-gray-400'"
          >{{ countText(totalItems, 'goal') }}</span
        >
      </div>
    </template>
    <template #panel>
      <slot :items="groupItems" :disabled="!totalItems" />
      <hr class="mx-5 h-px border-0 bg-black/5" />
      <div class="flex items-center justify-center p-4">
        <router-link
          :to="`/goals/add?group=${group.id}`"
          class="text-primary text-sm font-medium underline"
        >
          + Add {{ lowerCase(group.name) }} goal
        </router-link>
      </div>
    </template>
  </BoxAccordion>
</template>

<script setup lang="ts">
  import { computed, provide } from 'vue';
  import { lowerCase } from 'lodash';

  import { countText } from '@aventur-shared/utils/ui/count-text';
  import BoxAccordion from '@/components/BoxAccordion.vue';
  import { Goal, useRefData } from '@aventur-shared/stores/refdataStore';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';
  import { Client } from '@aventur-shared/modules/clients';

  const props = defineProps<{
    groupId: Goal['id'];
    goals: ClientGoal[];
    client: Client;
    isLoading?: boolean;
  }>();

  const { getGoalById } = useRefData();

  const group = computed<Goal>(() => getGoalById(props.groupId) as Goal);

  provide('group', group);

  const groupItems = computed<typeof props.goals>(() =>
    Object.values(props.goals).filter(
      (goal) => goal.goalTypeId === props.groupId,
    ),
  );

  const totalItems = computed<number>(() => groupItems.value.length);
  const hasGoals = computed<boolean>(() => totalItems.value > 0);
</script>
