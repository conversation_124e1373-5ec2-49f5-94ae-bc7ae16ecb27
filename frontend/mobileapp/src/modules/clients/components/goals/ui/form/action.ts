// TODO: Review
export class PartialSubmissionError extends Error {
  constructor(message: string) {
    super(message);

    this.constructor = PartialSubmissionError;
    Object.setPrototypeOf(this, PartialSubmissionError.prototype);
  }
}

export class ForbiddenError extends Error {
  constructor(message: string) {
    super(message);

    this.constructor = ForbiddenError;
    Object.setPrototypeOf(this, ForbiddenError.prototype);
  }
}

const extractErrorMessage = (error: any): string => {
  // Handle AWS Amplify API error structure
  if (error?.response?.status === 403 || error?.response?.statusCode === 403) {
    const responseData =
      error.response?.body || error.response?.data || error.response;

    if (responseData?.detail) {
      return responseData.detail;
    }
    if (typeof responseData === 'string') {
      try {
        const parsed = JSON.parse(responseData);
        if (parsed?.detail) {
          return parsed.detail;
        }
      } catch {
        // Fall through to default
      }
    }
    return 'This action is not allowed.';
  }

  // Handle direct error objects that might have status/statusCode
  if (error?.status === 403 || error?.statusCode === 403) {
    if (error?.detail) {
      return error.detail;
    }
    if (error?.body?.detail) {
      return error.body.detail;
    }
    if (error?.data?.detail) {
      return error.data.detail;
    }
    return 'This action is not allowed.';
  }

  return error?.message || 'Unknown error';
};

export const action = async (actions: Promise<string | void>[]) => {
  const results = await Promise.allSettled(actions);
  const rejected: PromiseRejectedResult[] = results.filter(
    (res) => res.status === 'rejected',
  );

  if (rejected.length == 1) {
    const error = rejected[0].reason;

    if (
      error?.response?.status === 403 ||
      error?.response?.statusCode === 403 ||
      error?.status === 403 ||
      error?.statusCode === 403
    ) {
      throw new ForbiddenError(extractErrorMessage(error));
    }
    throw new PartialSubmissionError(
      `Goal updated,\r\nbut ${error} update failed`,
    );
  }

  if (rejected.length > 1) {
    const hasForbiddenError = rejected.some(
      (result) =>
        result.reason?.response?.status === 403 ||
        result.reason?.response?.statusCode === 403 ||
        result.reason?.status === 403 ||
        result.reason?.statusCode === 403,
    );
    if (hasForbiddenError) {
      const forbiddenError = rejected.find(
        (result) =>
          result.reason?.response?.status === 403 ||
          result.reason?.response?.statusCode === 403 ||
          result.reason?.status === 403 ||
          result.reason?.statusCode === 403,
      );
      throw new ForbiddenError(extractErrorMessage(forbiddenError?.reason));
    }
    throw new Error('Unable to update goal');
  }
};
