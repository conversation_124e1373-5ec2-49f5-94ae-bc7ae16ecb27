<template>
  <FormWrapper>
    <div class="flex flex-col gap-8">
      <GoalForm
        :group="group"
        :client="client"
        :goal="goal"
        @on-submit="onSubmit"
        @on-cancel="back"
      />
      <div class="flex flex-col gap-8 px-7">
        <AccountsList
          :linked-accounts="goal.linkedHoldings"
          @accounts-updated="handleAccountsUpdated"
        />
        <GoalItemFooter :goal="goal" />
      </div>
      <div class="flex items-center justify-center">
        <BaseButton
          theme="text-like"
          class="font-bold text-red-900 underline"
          @click="handleGoalDeleted"
          >Delete goal</BaseButton
        >
      </div>
    </div>
  </FormWrapper>
</template>

<script setup lang="ts">
  import { find } from 'lodash';
  import { computed } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useRoute, useRouter } from 'vue-router';

  import { useRefData } from '@aventur-shared/stores';
  import { Goal } from '@aventur-shared/stores/refdataStore';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { GoalId } from '@aventur-shared/modules/goals';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId, useClientStore } from '@aventur-shared/modules/clients';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';
  import {
    deleteClientGoals,
    patchGoalHoldings,
  } from '@aventur-shared/modules/factfind/api';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';

  import {
    AventurEvents,
    type ClientDeletedGoalEvent,
    type ClientLinkedGoalAccountsEvent,
    type ClientUnlinkedGoalAccountsEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  import FormWrapper from './FormWrapper.vue';
  import AccountsList from './AccountsList.vue';
  import GoalItemFooter from './GoalItemFooter.vue';
  import GoalForm from './form/form.vue';
  //

  const toast = useToast();
  const route = useRoute();
  const router = useRouter();
  const { trackEvent } = useEventTracking();

  const back = () => router.go(-1);

  const { userId } = useUserStore();
  const { getGoalById } = useRefData();
  const {
    getProfile: client,
    loadClientGoals,
    loadHealthScore,
  } = useClientStore();
  const { getClientGoals, getGoalsWithHealthScores: goalsWithHealthScores } =
    storeToRefs(useClientStore());

  const goalId = Number(route.params.id) as GoalId;
  const goal = computed(
    () =>
      find(getClientGoals.value, {
        id: goalId,
      }) as ClientGoal,
  );

  const group = computed(() => getGoalById(goal.value.goalTypeId) as Goal);

  const handleGoalDeleted = async () => {
    try {
      await deleteClientGoals(userId as ClientId, goal.value.id);
      loadClientGoals(userId as ClientId).then();
      loadHealthScore(userId as ClientId).then();
      toast.success(`Goals updated`);

      trackEvent<ClientDeletedGoalEvent>(AventurEvents.ClientDeletedGoal, {
        goal_id: goal.value.id,
        goal_type: group.value.code,
      });

      await router.push('/goals');
    } catch (e) {
      toast.error(e as Error);
    }
  };

  const handleAccountsUpdated = async (selectedHoldingIds: number[]) => {
    try {
      await patchGoalHoldings(goalId, userId as ClientId, selectedHoldingIds);
      loadClientGoals(userId as ClientId).then();
      loadHealthScore(userId as ClientId).then();
      toast.success(`Goals updated`);

      if (selectedHoldingIds.length > goal.value.linkedHoldings.length) {
        trackEvent<ClientLinkedGoalAccountsEvent>(
          AventurEvents.ClientLinkedGoalAccounts,
          {
            goal_id: goal.value.id,
            goal_type: group.value.code,
          },
        );
      }
      if (selectedHoldingIds.length < goal.value.linkedHoldings.length) {
        trackEvent<ClientUnlinkedGoalAccountsEvent>(
          AventurEvents.ClientUnlinkedGoalAccounts,
          {
            goal_id: goal.value.id,
            goal_type: group.value.code,
          },
        );
      }
    } catch (e) {
      toast.error(e as Error);
    }
  };

  const onSubmit = () => {
    loadClientGoals(userId as ClientId).then();
    loadHealthScore(userId as ClientId).then();
  };
</script>
