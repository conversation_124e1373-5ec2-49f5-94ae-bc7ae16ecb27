<template>
  <div class="p-4 text-gray-300">
    <!-- Goal Item View -->
    <div
      v-if="currentGoal"
      class="flex flex-col items-center justify-center text-[#C9E386]"
    >
      <span>{{ currentGoal.goalName }}</span>
      <span class="text-5xl text-white">{{
        goalsWithHealthScores[currentGoal.goalId].healthScore.score
      }}</span>
      <span class="text-sm text-white/60">out of 100</span>
      <alert
        v-if="goalsWithHealthScores[currentGoal.goalId].errors"
        class="mt-2"
        type="error"
        :message="goalsWithHealthScores[currentGoal.goalId].errors"
      />
      <div
        v-if="
          goalsWithHealthScores[currentGoal.goalId].warnings &&
          Object.keys(goalsWithHealthScores[currentGoal.goalId].warnings)
            .length > 0
        "
        class="mt-2 w-full rounded border border-yellow-200 bg-yellow-100 px-0 py-2"
      >
        <Disclosure v-slot="{ open }">
          <DisclosureButton
            class="flex w-full items-center justify-between rounded px-4 text-left text-sm font-medium"
            data-testid="goal-disclosure-button"
          >
            <span class="text-yellow-700"
              >{{
                countText(
                  Object.keys(
                    goalsWithHealthScores[currentGoal.goalId].warnings,
                  ).length,
                  'warning',
                )
              }}
            </span>
            <ChevronDownIcon
              :class="open ? 'transform rotate-180' : ''"
              class="size-5 text-yellow-700"
            />
          </DisclosureButton>
          <DisclosurePanel data-testid="goal-panel" class="text-sm">
            <div
              v-for="(value, key) in goalsWithHealthScores[currentGoal.goalId]
                .warnings"
              :key="key"
            >
              <alert
                class="border-0"
                data-testid="goal-warning"
                type="warning"
                :message="
                  formatHoldingDisplay(
                    goalsWithHealthScores[currentGoal.goalId],
                    key,
                    value,
                  )
                "
              />
            </div>
          </DisclosurePanel>
        </Disclosure>
      </div>
    </div>
    <!-- Goal List View -->
    <div v-else class="flex flex-col items-center justify-center">
      <template v-if="healthScore">
        <span class="text-[#C9E386]">Your financial score</span>
        <span class="text-5xl text-white">
          {{ healthScore.overallScore }}
        </span>
        <span class="text-sm text-white/60">out of 1000</span>
      </template>
      <template v-else class="flex flex-col items-center justify-center">
        <span class="text-[#C9E386]">Your financial score</span>
        <span class="text-5xl text-white"> 0 </span>
        <span class="text-sm text-white/60">out of 1000</span>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { useRoute } from 'vue-router';
  import {
    Disclosure,
    DisclosureButton,
    DisclosurePanel,
  } from '@headlessui/vue';
  import { ChevronDownIcon } from '@heroicons/vue/20/solid';
  import { useCurrentGoal } from '@modules/clients/components/goals/composables/use-current-goal';
  import { useClientStore } from '@aventur-shared/modules/clients';
  import Alert from '@aventur-shared/components/Alert.vue';
  import { countText } from '@aventur-shared/utils/ui/count-text';

  const route = useRoute();
  const { currentGoal } = useCurrentGoal(route);
  const {
    getHealthScore: healthScore,
    getGoalsWithHealthScores: goalsWithHealthScores,
  } = storeToRefs(useClientStore());

  const formatHoldingDisplay = (goal, key, value) => {
    const holding = goal.linkedHoldings.find((h) => h.id === parseInt(key));
    return holding
      ? `${holding.providerName} - ${holding.accountNumber}: ${value}`
      : `Unknown holding: ${value}`;
  };
</script>
