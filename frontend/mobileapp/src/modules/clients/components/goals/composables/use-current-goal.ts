import { find } from 'lodash';
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { RouteLocationNormalizedGeneric } from 'vue-router';

import { useClientStore } from '@aventur-shared/modules/clients';
import { GoalId } from '@aventur-shared/modules/goals';
import { useRefData } from '@aventur-shared/stores';
//

export const useCurrentGoal = (route: RouteLocationNormalizedGeneric) => {
  const { getGoalById } = useRefData();
  const { getClientGoals } = storeToRefs(useClientStore());

  const _goal = computed(() =>
    find(getClientGoals.value, { id: Number(route.params.id) as GoalId }),
  );
  const _group = computed(() =>
    _goal.value ? getGoalById(_goal.value.goalTypeId) : undefined,
  );

  return {
    currentGoal: computed(() =>
      _goal.value && _group.value
        ? {
            goalId: _goal.value.id,
            goalName: _goal.value.name,
            typeId: _goal.value.goalTypeId,
            typeName: _group.value.name,
          }
        : undefined,
    ),
  };
};
