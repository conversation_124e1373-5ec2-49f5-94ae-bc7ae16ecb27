<template>
  <BaseButton
    :class="classObject"
    class="font-medium text-white focus:!outline-none focus:!outline-0"
    type="button"
    theme="custom"
  >
    <slot />
  </BaseButton>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import BaseButton from '@aventur-shared/components/BaseButton.vue';

  const props = defineProps<{
    isSelected: boolean;
  }>();

  const classObject = computed(() => ({
    'bg-primary/60 text-white': props.isSelected,
    'text-opacity-60': !props.isSelected,
  }));
</script>
