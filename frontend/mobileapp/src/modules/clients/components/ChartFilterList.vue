<template>
  <div
    class="flex flex-row justify-around overflow-x-scroll py-4"
    data-chart-filter
  >
    <ButtonChartFilter
      :is-selected="selectedRange === '1m'"
      @on-click="handleClick('1m')"
    >
      1m
    </ButtonChartFilter>
    <ButtonChartFilter
      :is-selected="selectedRange === '6m'"
      @on-click="handleClick('6m')"
    >
      6m
    </ButtonChartFilter>
    <ButtonChartFilter
      :is-selected="selectedRange === '1y'"
      @on-click="handleClick('1y')"
    >
      1y
    </ButtonChartFilter>
    <ButtonChartFilter
      :is-selected="selectedRange === '3y'"
      @on-click="handleClick('3y')"
    >
      3y
    </ButtonChartFilter>
    <ButtonChartFilter
      :is-selected="selectedRange === '5y'"
      @on-click="handleClick('5y')"
    >
      5y
    </ButtonChartFilter>
    <ButtonChartFilter
      :is-selected="selectedRange === 'All'"
      @on-click="handleClick('All')"
    >
      All
    </ButtonChartFilter>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { SelectedRange } from '@aventur-shared/modules/clients/types/SelectedRange';
  import ButtonChartFilter from './ButtonChartFilter.vue';

  const props = withDefaults(
    defineProps<{
      initialRange?: SelectedRange;
    }>(),
    {
      initialRange: '1m',
    },
  );

  const emit = defineEmits<{
    (e: 'on-range-pick', range: SelectedRange): void;
  }>();

  const selectedRange = ref<SelectedRange>(props.initialRange);

  const handleClick = (range: SelectedRange) => {
    selectedRange.value = range;
    emit('on-range-pick', range);
  };
</script>
