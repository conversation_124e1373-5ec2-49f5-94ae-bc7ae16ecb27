<template>
  <template v-if="detail.type === 'text'">
    <dl>
      <dt class="text-sm text-gray-500">{{ detail.title }}</dt>
      <dd class="text-base font-medium text-gray-900 sm:col-span-2 sm:mt-0">
        {{ detail.value }}
      </dd>
    </dl>
  </template>
  <template v-if="detail.type === 'link'">
    <router-link :to="detail.href" class="text-secondary break-words"
      >{{ detail.value }}
      <MaterialDesignIcon icon="open_in_new" class="text-secondary" />
    </router-link>
  </template>
  <template v-if="detail.type === 'list'">
    <ul>
      <li v-for="(item, itemIndex) in detail.items" :key="`item-${itemIndex}`">
        <router-link :to="item.href" class="text-secondary break-words"
          >{{ item.value }}
        </router-link>
      </li>
    </ul>
  </template>
  <template v-if="detail.type === 'boolean'">
    <div class="flex items-center justify-between gap-3">
      <span class="flex w-2/3 text-sm text-gray-500">{{ detail.title }}</span>
      <span
        v-if="detail.value === null"
        class="text-sm font-semibold text-gray-900"
        >-</span
      >
      <span
        v-else-if="detail.value === true"
        class="text-sm font-semibold text-gray-900"
        >Yes</span
      >
      <span
        v-else-if="detail.value === false"
        class="text-sm font-semibold text-gray-900"
        >No</span
      >
    </div>
  </template>
  <template v-if="detail.type === 'option'">
    <div class="flex items-center justify-between gap-3">
      <span class="flex w-2/3 text-sm text-gray-500">{{ detail.title }}</span>
      <span class="flex text-right text-sm font-semibold text-gray-900">{{
        detail.value ?? '-'
      }}</span>
    </div>
  </template>
</template>

<script setup lang="ts">
  import { ViewDetail } from '@aventur-shared/types/ViewDetail';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  //

  const { detail } = defineProps<{ detail: ViewDetail }>();
</script>
