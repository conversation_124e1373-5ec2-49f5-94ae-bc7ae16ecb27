type TSectionItem = {
  title: string;
  url: string;
  disabled?: boolean;
};
type TSection = {
  slug: string;
  title: string;
  items: Array<TSectionItem>;
};

export const sections: TSection[] = [
  {
    slug: 'personal-details',
    title: 'Personal details',
    items: [
      {
        title: 'About You',
        url: '/profile/aboutyou',
      },
      {
        title: 'Contact Details',
        url: '/profile/contacts',
      },
      {
        title: 'Your Addresses',
        url: '/profile/addresses',
      },
      {
        title: 'Your Family',
        url: '/profile/family',
      },
    ],
  },
  {
    slug: 'your-finance',
    title: 'Your finances',
    items: [
      {
        title: 'Assets & Debts',
        url: '/profile/assets-debts',
      },
      {
        title: 'Goals',
        url: '/profile/goals',
      },
      {
        title: 'Income',
        url: '/profile/income',
      },
      {
        title: 'Expenditures',
        url: '/profile/expenditures',
      },
    ],
  },
  {
    slug: 'settings',
    title: 'Settings',
    items: [
      {
        title: 'Account Settings',
        url: '/profile/account',
      },
      {
        title: 'Marketing Preferences',
        url: '/profile/marketing',
      },
    ],
  },
];
