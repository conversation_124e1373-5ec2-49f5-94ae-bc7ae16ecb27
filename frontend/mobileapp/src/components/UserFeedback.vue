<template>
  <div>
    <div data-feedback-button v-if="!isOpened" @click="open">
      <button>
        <svg
          width="20"
          height="20"
          xmlns="http://www.w3.org/2000/svg"
          style="transform: rotateZ(90deg); transform-origin: 50% 50%"
        >
          <defs>
            <linearGradient
              x1="23.656%"
              y1="50.828%"
              x2="57.117%"
              y2="52.047%"
              id="a"
            >
              <stop
                stop-color="currentColor"
                stop-opacity="0"
                offset="0%"
              ></stop>
              <stop stop-color="currentColor" offset="100%"></stop>
            </linearGradient>
          </defs>
          <g fill="none" fill-rule="evenodd">
            <path
              d="M0 12.8c3.833 1.333 7.166 2 10 2 2.315 0 4.518-.445 6.61-1.335a.997.997 0 011.39.918v.417c-2.167 1.333-4.834 2-8 2-3.166 0-6.5-.667-10-2v-2z"
              fill="url(#a)"
              transform="translate(1 2)"
            ></path>
            <path d="M2.636 11.6L4.273 10l3.272 3.2-1.636 1.6H2.636z"></path>
            <path
              d="M2.42 11.812l8.89-8.693a1.636 1.636 0 012.289 0l.932.911a1.636 1.636 0 010 2.34l-8.857 8.66a2 2 0 01-1.398.57H2.818a1 1 0 01-1-1v-1.358a2 2 0 01.602-1.43z"
              stroke="currentColor"
              stroke-width="1.75"
            ></path>
            <path
              d="M10.182 4.2c0 1.067.273 1.867.818 2.4.545.533 1.364.8 2.455.8"
              stroke="currentColor"
              stroke-width="1.636"
            ></path>
            <path d="M4.273 10.8l2.454 2.4 9-8.8L13.273 2z"></path>
          </g>
        </svg>
      </button>
    </div>
    <TransitionRoot as="template" :show="isOpened" class="inset-0">
      <Dialog class="relative z-50" as="div" @close="close">
        <div class="fixed inset-0 bg-black opacity-50" />
        <TransitionChild
          as="div"
          class="fixed inset-0 flex items-center justify-center p-4"
          enter="ease-out duration-100"
          enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enter-to="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-100"
          leave-from="opacity-100 translate-y-0 sm:scale-100"
          leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <div class="fixed inset-0 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4">
              <DialogPanel class="w-full max-w-lg">
                <div data-feedback-form>
                  <div class="aventur-logo py-3">
                    <img alt="Aventur logo" src="/assets/logo-white.svg" />
                  </div>
                  <form @submit.prevent="onSubmit">
                    <h1>Submit feedback</h1>
                    <TextField
                      label="Name"
                      name="name"
                      placeholder="Your name"
                    />
                    <TextField
                      label="Email"
                      name="email"
                      placeholder="Your email"
                    />
                    <TextAreaField
                      label="Message"
                      name="message"
                      placeholder="What's the issue?"
                      rows="4"
                    />
                    <div class="pb-4">
                      <template v-if="withAttachments">
                        <FileField
                          accept="image/*"
                          @on-file-upload="handleFileUpload"
                        >
                          <template #button="{ openFileInput }">
                            <div
                              class="flex w-full flex-row items-center justify-center gap-2 text-center"
                            >
                              <a
                                @click="openFileInput"
                                class="truncate text-sm font-normal text-white/90 underline"
                                >{{
                                  selectedFile
                                    ? selectedFile
                                    : 'Add attachments'
                                }}</a
                              >
                              <MaterialDesignIcon
                                v-if="selectedFile"
                                icon="close"
                                class="text-primary-400 text-sm font-bold"
                                @click="resetFile"
                              />
                            </div>
                          </template>
                        </FileField>
                      </template>
                    </div>
                    <div
                      class="flex flex-col items-center justify-center gap-3"
                    >
                      <FeedbackButton
                        theme="custom"
                        type="submit"
                        class="w-full"
                        :disabled="!meta.valid"
                        >Submit
                      </FeedbackButton>
                      <FeedbackButton
                        type="button"
                        @click="close"
                        theme="text-like"
                        class="w-fit text-sm text-white"
                        >Cancel
                      </FeedbackButton>
                    </div>
                  </form>
                </div>
              </DialogPanel>
            </div>
          </div>
        </TransitionChild>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
  import { filter } from 'lodash';
  import { ref } from 'vue';
  import { useTimeoutFn } from '@vueuse/core';
  import * as Sentry from '@sentry/vue';
  import { mixed, object, string } from 'yup';
  import { useForm } from 'vee-validate';
  import {
    Dialog,
    DialogPanel,
    TransitionChild,
    TransitionRoot,
  } from '@headlessui/vue';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { TextAreaField, TextField } from '@aventur-shared/components/form';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import { default as FeedbackButton } from '@aventur-shared/components/BaseButton.vue';
  import FileField from '@aventur-shared/components/form/fields/clean-fields/FileField.vue';

  import {
    AventurEvents,
    type ClientSubmittedFeedbackEvent,
    useEventTracking,
  } from '@/composables/useEventTracking';
  //

  const toast = useToast();
  const { trackEvent } = useEventTracking();

  const { withAttachments = false } = defineProps<{
    withAttachments?: boolean;
  }>();

  const isOpened = ref(false);
  const open = () => {
    isOpened.value = true;
  };
  const close = () => {
    isOpened.value = false;
  };

  type FormValues = {
    name: string;
    email: string;
    message: string;
    attachment?: File | null;
  };

  const validationSchema = object({
    name: string().required(),
    email: string().required().email(),
    message: string().required().max(1000),
    attachment: mixed().optional(),
  });

  const selectedFile = ref<string | null>(null);

  const { handleSubmit, setFieldValue, meta } = useForm<FormValues>({
    validationSchema,
  });

  const resetFile = () => {
    setFieldValue('attachment', null);
    selectedFile.value = null;
  };

  const handleFileUpload = async (files: FileList) => {
    setFieldValue('attachment', files[0]);
    selectedFile.value = files[0].name;
  };

  const encodeAttachment = async (attachmentField: File) => {
    const data = new Uint8Array(await attachmentField.arrayBuffer());
    return {
      data,
      filename: attachmentField.name,
    };
  };

  type FeedBackAttachment = {
    data: Uint8Array;
    filename: string;
  };

  const onSubmit = handleSubmit(async (values: FormValues) => {
    let attachment: FeedBackAttachment | undefined;
    if (values.attachment) {
      attachment = await encodeAttachment(values.attachment);
    }

    try {
      Sentry.captureFeedback(values, {
        attachments: filter([attachment as FeedBackAttachment]),
      });
      toast.success('Thank you for your feedback!');
      useTimeoutFn(close, 0);

      trackEvent<ClientSubmittedFeedbackEvent>(
        AventurEvents.ClientSubmittedFeedback,
        {
          current_page: window.location.pathname,
        },
      );
    } catch (e) {
      toast.error(e as Error);
    }
  });
</script>
