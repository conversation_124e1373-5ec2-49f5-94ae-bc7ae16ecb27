<template>
  <BaseBox class="mt-4">
    <Disclosure v-slot="{ open }" :default-open="isOpen && !isStatic">
      <DisclosureButton as="template" :disabled="isDisabled">
        <BaseTitle
          class="flex min-h-16 flex-row place-items-center justify-between border-b border-black/5 px-7 py-4"
          :class="classes?.title"
        >
          <slot name="title" :open="open" />
          <slot name="action">
            <template v-if="!isStatic && !isBusy && !isDisabled">
              <MaterialDesignIcon
                v-if="open"
                icon="expand_more"
                class="size-5"
              />
              <MaterialDesignIcon
                v-else-if="!open"
                icon="chevron_right"
                class="size-5"
              />
            </template>
            <span v-if="isBusy" class="text-primary flex justify-center">
              <LoadingSpinner class="size-4" />
            </span>
          </slot>
        </BaseTitle>
      </DisclosureButton>
      <DisclosurePanel :static="isStatic">
        <BaseSection v-if="!$slots.panel" :class="classes?.panel">
          <slot />
        </BaseSection>
        <slot name="panel" />
      </DisclosurePanel>
    </Disclosure>
  </BaseBox>
</template>

<script setup lang="ts">
  import {
    Disclosure,
    DisclosureButton,
    DisclosurePanel,
  } from '@headlessui/vue';
  import BaseBox from '@aventur-shared/components/BaseBox.vue';
  import BaseTitle from '@aventur-shared/components/BaseTitle.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import LoadingSpinner from '@aventur-shared/components/LoadingSpinner.vue';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';

  interface ClassList {
    title?: string;
    panel?: string;
  }

  withDefaults(
    defineProps<{
      isBusy?: boolean;
      isOpen?: boolean;
      isStatic?: boolean;
      isDisabled?: boolean;
      classes?: ClassList;
    }>(),
    {
      isBusy: false,
      isOpen: false,
      isStatic: false,
      isDisabled: false,
    },
  );
</script>
