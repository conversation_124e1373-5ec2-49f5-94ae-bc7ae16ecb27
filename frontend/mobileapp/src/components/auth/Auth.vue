<template>
  <Authenticator
    :login-mechanisms="['email']"
    :services="services"
    :sign-up-attributes="signUpAttributes"
    :form-fields="formFields"
  >
    <template #header>
      <div class="aventur-logo py-5">
        <img alt="Aventur logo" src="/assets/logo-white.svg" />
      </div>
    </template>
    <template #default>
      <AppLayout :user="user" />
    </template>

    <template v-slot:sign-in-footer v-if="usePasskey && !isPending">
      <div
        class="flex select-none flex-col items-center justify-center"
        data-amplify-footer
      >
        <button
          @click="signInWithPasskey"
          v-on-long-press="[
            resetBiometricAuth,
            { delay: 3000, modifiers: { stop: true, prevent: true } },
          ]"
          ref="btnBiometricAuth"
        >
          <MaterialDesignIcon
            icon="fingerprint"
            class="mb-5 text-5xl text-white"
          />
        </button>
        <AmplifyButton variation="link" size="small" @click="toForgotPassword"
          >Reset Password</AmplifyButton
        >
      </div>
    </template>

    <template v-slot:sign-up-fields>
      <AuthenticatorSignUpFormFields />
      <AmplifyCheckBox :errorMessage="validationErrors.acknowledgement" />
    </template>

    <template #footer>
      <div
        class="my-4 flex flex-col items-center justify-center space-y-3 text-sm"
      >
        <p
          class="flex flex-col items-center justify-center space-x-0 md:flex-row md:space-x-4"
        >
          <span>Aventur &copy; {{ currentYear }}</span>
        </p>
      </div>
    </template>
  </Authenticator>
  <FeedbackModal />
</template>

<script setup lang="ts">
  import { get, isEmpty } from 'lodash';
  import { computed, onMounted, onUnmounted, ref, toRefs, watch } from 'vue';
  import { useTimeoutFn } from '@vueuse/core';
  import { vOnLongPress } from '@vueuse/components';
  import { post } from 'aws-amplify/api';
  import { I18n } from 'aws-amplify/utils';
  import {
    AmplifyButton,
    AmplifyCheckBox,
    Authenticator,
    AuthenticatorSignUpFormFields,
    useAuthenticator,
  } from '@aws-amplify/ui-vue';
  import { type SignInInput, getCurrentUser, signIn } from '@aws-amplify/auth';
  import { captureException } from '@sentry/browser';
  import { Dialog } from '@capacitor/dialog';
  import {
    BiometricAuth,
    BiometryError,
    BiometryErrorType,
    BiometryType,
    CheckBiometryResult,
  } from '@aparajita/capacitor-biometric-auth';

  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import {
    SignupFormData,
    type SignupRequestData,
    formFields,
    signUpAttributes,
  } from '@aventur-shared/constants/auth';
  import { useToast } from '@aventur-shared/composables/useToast';

  import { useAppStore } from '@/stores';
  import AppLayout from '@/layouts/AppLayout.vue';
  import FeedbackModal from '@/components/UserFeedback.vue';
  import {
    BiometryTypesArray,
    type UserCredentials,
    useBiometric,
  } from '@/composables/useBiometric';
  //

  const toast = useToast();

  const updateBiometryInfo = async (info: CheckBiometryResult) => {
    selectedBiometryType.value = info.biometryType;

    // an attempt to get the current user without active session throws,
    // so we catch it to check for available biometry options and stored credentials
    // to offer passkey login if possible
    try {
      user.value = await getCurrentUser();
    } catch {
      if (
        props.initialState !== 'signUp' &&
        info.isAvailable &&
        usePasskey.value &&
        route.value === 'signIn'
      ) {
        await signInWithPasskey();
      }
    }
  };

  const { toForgotPassword, toSignUp } = useAuthenticator();
  const { user, isPending, route, validationErrors } =
    toRefs(useAuthenticator());
  const { setUsePasskey } = useAppStore();
  const { usePasskey } = toRefs(useAppStore());

  const {
    removeAppListener,
    authenticate,
    getUserCredentials,
    setUserCredentials,
  } = useBiometric(updateBiometryInfo);

  const props = defineProps<{
    initialState?: 'signUp' | 'signIn';
  }>();

  onMounted(async () => {
    if (props.initialState === 'signUp') {
      useTimeoutFn(toSignUp, 0);
    }
  });

  I18n.putVocabulariesForLanguage('en', {
    'Sign In': 'Login',
    'Sign in': 'Login',
    'Create Account': 'Register',
    'Forgot your password?': 'Reset password',
    'Back to Sign In': 'Back to login',
    'Enter your Password': 'Your password',
    'Please confirm your Password': 'Confirm your password',
    'We Texted You': 'Confirm your email',
  });

  const currentYear = new Date().getFullYear();
  const authFlowType =
    import.meta.env.VITE_AWS_COGNITO_AUTHENTICATION_FLOW_TYPE ?? undefined;

  const setupPasskey = ref(false);
  const selectedBiometryType = ref<BiometryType>(BiometryType.none);
  const selectedBiometryName = computed(() =>
    selectedBiometryType.value
      ? BiometryTypesArray[selectedBiometryType.value]
      : '',
  );

  const submitSignInForm = async (credentials: UserCredentials) => {
    if (credentials) {
      window.document
        .querySelector('[name=username]')
        ?.setAttribute('value', credentials.username);
      window.document
        .querySelector('[name=password]')
        ?.setAttribute('value', credentials.password);
      (
        window.document.querySelector(
          'form > div > button',
        ) as HTMLButtonElement
      ).click();
    }
  };

  const signInWithPasskey = async () => {
    try {
      await authenticate();
      await submitSignInForm(await getUserCredentials());
    } catch (e) {
      await removeAppListener();
      if (e instanceof BiometryError) {
        if (e.code !== BiometryErrorType.userCancel) {
          // capture the error and continue
          captureException(e);
        }
      }
    }
  };

  onMounted(async (): Promise<void> => {
    await updateBiometryInfo(await BiometricAuth.checkBiometry());
    try {
      // await setAppListener();
    } catch {
      //
    }
  });

  onUnmounted(async (): Promise<void> => {
    await removeAppListener();
  });

  const disableSubmitButton = () => {
    const button = document.querySelector(
      '[data-amplify-button]',
    ) as HTMLButtonElement;

    button.setAttribute('disabled', 'true');
    button.classList.add('amplify-button--disabled');
    button.innerText = 'Please, wait';
  };

  const enableSubmitButton = () => {
    const button = document.querySelector(
      '[data-amplify-button]',
    ) as HTMLButtonElement;

    button.setAttribute('disabled', 'false');
    button.classList.remove('amplify-button--disabled');
    button.innerText = 'Register';
  };

  const signUpInProgress = ref(false);
  watch(signUpInProgress, (value) => {
    value && disableSubmitButton();
    !value && enableSubmitButton();
  });

  const services = {
    async validateCustomSignUp(formData: SignupFormData) {
      if (!isEmpty(formData) && !formData.acknowledgement) {
        return {
          acknowledgement: 'You must agree to the Terms and Conditions',
        };
      }
    },
    async handleSignIn(input: SignInInput) {
      const { username, password } = input;

      const { nextStep } = await signIn({
        username,
        password,
        options: {
          authFlowType,
        },
      });

      if (nextStep.signInStep === 'DONE') {
        if (!usePasskey.value) {
          // bring up biometric set-up request dialog
          // except for iOS Face ID which shows its own prompt
          setupPasskey.value = [2].includes(selectedBiometryType.value);
          if (!setupPasskey.value) {
            const { value } = await Dialog.confirm({
              title: `Setup ${selectedBiometryName.value}`,
              message: `Enable ${selectedBiometryName.value} for Aventur?`,
            });
            setupPasskey.value = value;
          }

          if (setupPasskey.value) {
            try {
              // authenticate to access secure store,
              // store credentials if missing and set the biometry flag
              await authenticate();
              const cred = await getUserCredentials();
              if (cred === null) {
                await setUserCredentials({
                  username,
                  password: password as string,
                });
              }
              setUsePasskey(selectedBiometryType.value);
            } catch (e) {
              if (e instanceof BiometryError) {
                if (e.code !== BiometryErrorType.userCancel) {
                  // capture the error and continue
                  captureException(e);
                }
              }
            }
          }
        }
      }

      return { nextStep };
    },
    async handleSignUp(formData: SignupRequestData) {
      signUpInProgress.value = true;
      try {
        const {
          email,
          given_name: first_name,
          family_name: last_name,
        } = get(formData, 'options.userAttributes');

        const signUp = post({
          apiName: 'auth',
          path: '/auth/signup',
          options: {
            body: {
              data: { email, first_name, last_name },
            },
          },
        });

        await signUp.response;

        toast.success('Please, check your email to confirm your account.');
        return { nextStep: { signUpStep: 'DONE' } };
      } catch (e) {
        toast.error(e as Error);
        return { nextStep: 'signUp' };
      } finally {
        signUpInProgress.value = false;
      }
    },
  };

  const resetBiometricAuth = async () => {
    const { value } = await Dialog.confirm({
      title: 'Reset biometric',
      message: `Disable ${selectedBiometryName.value} for Aventur?`,
    });

    if (value) {
      try {
        await authenticate();
        await setUserCredentials(null);
        setUsePasskey(BiometryType.none);
      } catch (e) {
        if (e instanceof BiometryError) {
          if (e.code !== BiometryErrorType.userCancel) {
            // capture the error and continue
            captureException(e);
          }
        }
      }
    }
  };
</script>

<style lang="postcss">
  @import '../../assets/css/auth.pcss';
</style>
