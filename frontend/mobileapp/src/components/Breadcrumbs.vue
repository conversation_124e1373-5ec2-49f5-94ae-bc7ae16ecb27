<template>
  <nav class="flex" aria-label="Breadcrumb">
    <ol role="list" class="flex items-center space-x-2">
      <li>
        <div>
          <router-link to="/" class="text-gray-400 hover:text-gray-500">
            <MaterialDesignIcon icon="home" class="shrink-0" />
            <span class="sr-only">Home</span>
          </router-link>
        </div>
      </li>
      <li v-for="item in items" :key="item.label">
        <div class="flex items-center">
          <svg
            class="size-5 shrink-0 text-gray-300"
            fill="currentColor"
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
          </svg>
          <router-link
            v-if="item.to"
            :to="item.to"
            class="ml-2 text-sm font-medium text-gray-500 underline"
            >{{ item.label }}</router-link
          >
          <a v-else href="#" class="ml-2 text-sm font-medium text-gray-500">
            {{ item.label }}
          </a>
        </div>
      </li>
    </ol>
  </nav>
</template>

<script setup lang="ts">
  import { RouteLocationRaw } from 'vue-router';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';

  export type Items = Array<{
    label: string;
    to?: RouteLocationRaw;
  }>;

  defineProps<{
    items?: Items;
  }>();
</script>
