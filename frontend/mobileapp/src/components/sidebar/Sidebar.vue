<template>
  <TransitionRoot as="template" :show="isSidebarOpen">
    <Dialog
      as="div"
      class="fixed inset-0 z-40 flex md:hidden"
      @close="handleSidebarClose"
    >
      <TransitionChild
        as="template"
        enter="transition-opacity ease-linear duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="transition-opacity ease-linear duration-300"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <DialogOverlay class="fixed inset-0 bg-gray-600/75" />
      </TransitionChild>
      <TransitionChild
        as="template"
        enter="transition ease-in-out duration-300 transform"
        enter-from="-translate-x-full"
        enter-to="translate-x-0"
        leave="transition ease-in-out duration-300 transform"
        leave-from="translate-x-0"
        leave-to="-translate-x-full"
      >
        <div
          class="iphone-notch-safe bg-primary-950 relative flex w-full max-w-xs flex-1 flex-col"
        >
          <div
            class="border-primary/30 flex shrink-0 items-center border-b p-4"
          >
            <img src="/assets/logo-white.svg" alt="Aventur" />
          </div>
          <div class="mt-5 flex-1 overflow-y-auto">
            <SidebarItemList
              class="space-y-2 p-2"
              :item-list="sidebarOptions"
              @sidebar-item-clicked="emit('on-sidebar-close')"
            />
          </div>
          <p class="m-2 text-sm text-white">{{ version }} {{ mode }}</p>
        </div>
      </TransitionChild>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
  import { reject } from 'lodash';
  import { computed, toRef } from 'vue';
  import { useAppStore } from '@/stores/appStore';
  import {
    Dialog,
    DialogOverlay,
    TransitionChild,
    TransitionRoot,
  } from '@headlessui/vue';
  import SidebarItemList from './SidebarItemsList.vue';
  import { SidebarItem } from '@aventur-shared/types/Sidebar';

  const { mode, version } = useAppStore();

  const props = defineProps<{
    isClient: boolean;
    isSidebarOpen: boolean;
  }>();
  const emit = defineEmits<{
    (e: 'on-sidebar-close'): void;
  }>();

  const isSidebarOpen = toRef(props, 'isSidebarOpen');

  const sidebarItems: SidebarItem[] = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: 'home',
      current: true,
    },
    {
      name: 'Portfolio',
      path: '/portfolio',
      icon: 'business_center',
    },
    {
      name: 'Forecast',
      path: '/forecast',
      icon: 'monitoring',
      hidden: true,
    },
    {
      name: 'Goals',
      path: '/goals',
      icon: 'ads_click',
      hidden: true,
    },
    {
      name: 'Profile',
      path: '/profile',
      icon: 'account_circle',
    },
  ];
  const sidebarOptions = computed(() => reject(sidebarItems, 'hidden'));

  const handleSidebarClose = () => {
    emit('on-sidebar-close');
  };
</script>
