<template>
  <nav>
    <div
      v-for="item in itemList"
      :key="item.name"
      @click.stop="emit('sidebar-item-clicked')"
    >
      <a
        v-if="isExternal(item.path)"
        :href="item.path"
        target="_blank"
        :class="getItemClasses(item.path)"
      >
        <SidebarItemContent :item="item" :is-current="isCurrent(item.path)" />
      </a>
      <router-link v-else :to="item.path" :class="getItemClasses(item.path)">
        <SidebarItemContent :item="item" :is-current="isCurrent(item.path)" />
      </router-link>
    </div>
  </nav>
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router';
  import SidebarItemContent from './SidebarItem.vue';
  import { SidebarItem } from '@aventur-shared/types/Sidebar';

  type Props = {
    itemList: SidebarItem[];
  };

  defineProps<Props>();
  const emit = defineEmits<{
    (e: 'sidebar-item-clicked'): void;
  }>();

  const route = useRoute();

  const getItemClasses = (path: string) => `
  group flex items-center rounded-md p-2 text-sm
  font-medium
  ${isCurrent(path) ? 'bg-primary text-white' : 'text-gray-300'}`;

  const isExternal = (url: string) => url.startsWith('http');

  const isCurrent = (name: string) => {
    return name === '/' ? name === route.path : route.path.startsWith(name);
  };
</script>
