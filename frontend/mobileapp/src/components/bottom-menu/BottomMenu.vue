<template>
  <div
    v-if="!isChatbot"
    class="fixed bottom-0 w-full border-t border-gray-200 bg-white p-4"
  >
    <div class="flex items-center justify-between">
      <template v-for="(optionsChunk, index) in menuOptions" :key="index">
        <div v-for="item in optionsChunk" :key="item.name" class="w-16">
          <BottomMenuItem :item="item" :is-current="isCurrent(item)" />
        </div>
        <MiddleButton v-if="!index" class="w-24" />
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { chunk, reject } from 'lodash';
  import { useRoute } from 'vue-router';
  import { type MenuItem, menuItems } from '@/components/bottom-menu/Menu';
  import BottomMenuItem from '@/components/bottom-menu/BottomMenuItem.vue';
  import MiddleButton from '@/components/bottom-menu/MiddleButton.vue';

  const route = useRoute();

  const menuOptions = computed(() => chunk(reject(menuItems, 'hidden'), 2));

  const isChatbot = computed(() => route.path.startsWith('/avril'));

  const isCurrent = (item: MenuItem) => {
    return item.path === '/'
      ? item.path === route.path
      : route.path.startsWith(item.path);
  };
</script>
