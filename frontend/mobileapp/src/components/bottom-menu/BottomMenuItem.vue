<template>
  <router-link
    :to="item.path"
    class="flex flex-col items-center justify-center sm:mx-5"
  >
    <MaterialDesignIcon
      :icon="item.icon"
      class="text-3xl leading-6"
      :class="[isCurrent ? 'text-primary' : 'text-gray-400']"
    />
    <span class="text-xs font-medium leading-6">{{ item.name }}</span>
  </router-link>
</template>

<script setup lang="ts">
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';
  import type { MenuItem } from '@/components/bottom-menu/Menu';

  defineProps<{
    item: MenuItem;
    isCurrent: boolean;
  }>();
</script>
