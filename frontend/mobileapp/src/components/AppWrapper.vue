<template>
  <slot :show-intro="showIntro" :authInitialState="authInitialState">
    <div
      v-if="showIntro"
      class="flex size-full flex-col items-stretch justify-start p-5"
    >
      <Intro />
      <div class="flex flex-col items-center justify-center gap-2">
        <BaseButton
          type="button"
          theme="primary"
          @on-click="toAuth('signUp')"
          class="h-12 w-full"
          >Get started</BaseButton
        >
        <BaseButton
          @click="toAuth('signIn')"
          theme="text-like"
          class="text-primary text-sm font-semibold"
          >Log back in</BaseButton
        >
      </div>
    </div>
  </slot>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  import BaseButton from '@aventur-shared/components/BaseButton.vue';

  import Intro from '@modules/intro/Intro.vue';
  //

  const showIntro = ref(true);
  const authInitialState = ref<'signIn' | 'signUp'>('signIn');

  const toAuth = (initialState: 'signIn' | 'signUp') => {
    authInitialState.value = initialState;
    showIntro.value = false;
  };
</script>
