<template>
  <button type="button" class="text-white" @click="handleSidebarOpen">
    <span class="sr-only">Open sidebar</span>
    <MaterialDesignIcon icon="menu" />
  </button>
</template>

<script setup lang="ts">
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';

  const emit = defineEmits<{
    (e: 'on-sidebar-open'): void;
  }>();

  function handleSidebarOpen() {
    emit('on-sidebar-open');
  }
</script>
