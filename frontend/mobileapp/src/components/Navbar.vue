<template>
  <div
    id="app-navbar"
    class="bg-primary-950 fixed top-0 z-40 w-full shrink-0 !pb-0"
    :class="props.class"
  >
    <div class="mx-auto flex max-w-7xl items-center justify-between px-4 py-2">
      <div class="w-8">
        <template v-if="isSection">
          <a @click="router.go(-1)">
            <MaterialDesignIcon
              icon="chevron_left"
              class="flex text-left text-3xl leading-4 text-white"
            />
          </a>
        </template>
      </div>
      <slot />
    </div>
    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
      <slot name="bottom-bar" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import MaterialDesignIcon from '@aventur-shared/components/MaterialDesignIcon.vue';

  const props = defineProps<{
    class?: string | Record<string, boolean>;
  }>();

  const route = useRoute();
  const router = useRouter();

  const isSection = computed(() => route.path.match(/^\/\w+\/\w+/));
</script>
