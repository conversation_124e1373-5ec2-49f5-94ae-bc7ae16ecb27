import { signOut } from '@aws-amplify/auth';
import { PluginListenerHandle } from '@capacitor/core';
import { App } from '@capacitor/app';
import { SecureStorage } from '@aparajita/capacitor-secure-storage';
import {
  AndroidBiometryStrength,
  BiometricAuth,
  type ResumeListener,
} from '@aparajita/capacitor-biometric-auth';
//

export type UserCredentials = {
  username: string;
  password: string;
} | null;

const secureStorageKey = 'com.aventur.mobile';

const authenticationOptions = {
  reason: 'Please authenticate',
  cancelTitle: 'Cancel',
  allowDeviceCredential: true,
  iosFallbackTitle: 'Use passcode',
  androidTitle: 'Biometric login',
  androidSubtitle: 'Log in using biometric authentication',
  androidConfirmationRequired: false,
  androidBiometryStrength: AndroidBiometryStrength.weak,
};

enum BiometryType {
  None = '',
  TouchID = 'Touch ID',
  FaceID = 'Face ID',
  AndroidFingerprint = 'Fingerprint Unlock',
  AndroidFace = 'Face Unlock',
  AndroidIris = 'Iris Unlock',
}

export const BiometryTypesArray = Object.entries(BiometryType).map(
  ([_, name]) => name,
);

let appListener: PluginListenerHandle;

export const useBiometric = (listener: ResumeListener) => {
  const setAppListener = async () => {
    appListener = await BiometricAuth.addResumeListener(listener);
  };

  const removeAppListener = async () => {
    await appListener?.remove();
  };

  const getUserCredentials = async () =>
    (await SecureStorage.get(secureStorageKey)) as UserCredentials;

  const setUserCredentials = async (credentials: UserCredentials) =>
    credentials
      ? await SecureStorage.set(secureStorageKey, {
          ...credentials,
        })
      : await SecureStorage.remove(secureStorageKey);

  const authenticate = async (): Promise<void> =>
    await BiometricAuth.authenticate(authenticationOptions).then(
      async () => await removeAppListener(),
    );

  // App.addListener('pause', async () => {
  //   await removeAppListener();
  //   await signOut();
  // }).then();
  //
  // App.addListener('resume', async () => {
  //   await signOut();
  //   await setAppListener();
  // }).then();

  return {
    authenticate,
    setAppListener,
    removeAppListener,
    getUserCredentials,
    setUserCredentials,
  };
};
