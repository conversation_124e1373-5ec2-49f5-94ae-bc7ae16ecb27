import { onUnmounted, ref } from 'vue';
import { TemplateRef } from '@vue/runtime-core';
import { invoke, until, useScroll, useTimeoutFn } from '@vueuse/core';

import { useAPIState } from '@aventur-shared/composables/useAPIState';
import { useToast } from '@aventur-shared/composables/useToast';
//

const PULL_THRESHOLD = 40; // Distance needed to trigger refresh
const MAX_PULL_DISTANCE = 80; // Maximum pull distance

const initialized = ref(false);
const _handlers = ref<CallableFunction[]>([]);

export const usePullRefresh = (target?: TemplateRef<HTMLElement>) => {
  if (initialized.value === false) {
    if (!target) {
      // PullRefresh must be initialized with a target element
      return {
        handleTouchStart: undefined,
        handleTouchMove: undefined,
        handleTouchEnd: undefined,
        isPulling: undefined,
        pullOpacity: undefined,
        pullDistance: undefined,
        registerRefreshHandler: (handler: CallableFunction) => {},
      };
    }
  }

  const registerRefreshHandler = (handler: CallableFunction) => {
    _handlers.value.push(handler);
  };

  const unregisterAllRefreshHandlers = () => {
    _handlers.value = [];
  };

  onUnmounted(() => {
    unregisterAllRefreshHandlers();
  });

  if (initialized.value) {
    return {
      registerRefreshHandler,
    };
  }

  const toast = useToast();
  const { isLoading } = useAPIState();
  const { y } = useScroll(target);

  const touchStartY = ref(0);
  const pullDistance = ref(0);
  const isPulling = ref(false);
  const pullOpacity = ref(0);

  // Touch event handlers
  const handleTouchStart = (event: TouchEvent) => {
    // Only allow pull when at the top of the content
    // Alternatively: appLayoutTemplateRef.value?.scrollTop === 0
    if (y.value === 0) {
      touchStartY.value = event.touches[0].clientY;
    }
  };

  const { stop: stopPullReset, start: startPullReset } = useTimeoutFn(() => {
    invoke(async () => {
      await until(isLoading).toBe(false);
      // Reset values
      pullDistance.value = 0;
      touchStartY.value = 0;
      isPulling.value = false;
      pullOpacity.value = 0;
    });
  }, 300);

  const handleTouchMove = (event: TouchEvent) => {
    if (touchStartY.value === 0 || _handlers.value.length === 0) return;

    const touchY = event.touches[0].clientY;
    const distance = touchY - touchStartY.value;

    // Only allow pulling down, not up
    if (distance > 0) {
      stopPullReset();

      // Apply resistance to make pull feel natural
      pullDistance.value = Math.min(distance * 0.5, MAX_PULL_DISTANCE);
      pullOpacity.value = pullDistance.value / MAX_PULL_DISTANCE;
      isPulling.value = true;

      // Reset values
      startPullReset();

      // Prevent default scrolling behavior
      event.preventDefault();
    }
  };

  const handleTouchEnd = () => {
    if (pullDistance.value >= PULL_THRESHOLD) {
      pullDistance.value = MAX_PULL_DISTANCE;
      pullOpacity.value = 1;
      // Trigger refresh actions
      Promise.all(_handlers.value.map((handler) => handler())).catch(
        (e: Error) => {
          toast.error(e);
        },
      );
      startPullReset();
    }
  };

  return (function init() {
    initialized.value = true;
    return {
      handleTouchStart,
      handleTouchMove,
      handleTouchEnd,
      isPulling,
      pullOpacity,
      pullDistance,
      registerRefreshHandler,
    };
  })();
};
