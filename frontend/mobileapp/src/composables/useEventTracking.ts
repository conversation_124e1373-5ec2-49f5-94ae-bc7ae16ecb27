import { RouteParamsRawGeneric, Router } from 'vue-router';

import { track } from '@amplitude/analytics-browser';
import { User } from '@aventur-shared/modules/users';
import * as amplitude from '@amplitude/analytics-browser';
//

const AMPLITUDE_EVENT_TYPE = 'Aventur';

export enum AventurEvents {
  ClientSignedIn = 'client-signed-in',
  ClientSignedOut = 'client-signed-out',
  ClientViewedPage = 'client-viewed-page',
  ClientSubmittedFeedback = 'client-submitted-feedback',
  ClientClearedChat = 'client-cleared-chat',

  ClientCreatedGoal = 'client-created-goal',

  ClientUpdatedGoal = 'client-updated-goal',
  ClientDeletedGoal = 'client-deleted-goal',
  ClientLinkedGoalAccounts = 'client-linked-goal-accounts',
  ClientUnlinkedGoalAccounts = 'client-unlinked-goal-accounts',
  ClientCreatedAsset = 'client-created-asset',
  ClientUpdatedAsset = 'client-updated-asset',
  ClientDeletedAsset = 'client-deleted-asset',
  ClientCreatedDebt = 'client-created-debt',
  ClientUpdatedDebt = 'client-updated-debt',
  ClientDeletedDebt = 'client-deleted-debt',
  ClientCreatedIncome = 'client-created-income',
  ClientUpdatedIncome = 'client-updated-income',
  ClientDeletedIncome = 'client-deleted-income',
}

interface IAventurEvent {
  event: AventurEvents;
  display_name: string;
  options?: Record<string, unknown>;
}

export interface ClientSignedInEvent extends IAventurEvent {
  event: AventurEvents.ClientSignedIn;
}

export interface ClientSignedOutEvent extends IAventurEvent {
  event: AventurEvents.ClientSignedOut;
}

export interface ClientClearedChatEvent extends IAventurEvent {
  event: AventurEvents.ClientClearedChat;
}

export interface ClientSubmittedFeedbackEvent extends IAventurEvent {
  event: AventurEvents.ClientSubmittedFeedback;
  options: {
    current_page: string;
  };
}

export interface ClientViewedPageEvent extends IAventurEvent {
  event: AventurEvents.ClientViewedPage;
  options: {
    page_name: string | undefined;
    params: RouteParamsRawGeneric;
  };
}

export interface ClientCreatedGoalEvent extends IAventurEvent {
  event: AventurEvents.ClientCreatedGoal;
  options: {
    goal_type: string;
  };
}

export interface ClientUpdatedGoalEvent extends IAventurEvent {
  event: AventurEvents.ClientUpdatedGoal;
  options: {
    goal_id: number;
    goal_type: string;
  };
}

export interface ClientDeletedGoalEvent extends IAventurEvent {
  event: AventurEvents.ClientDeletedGoal;
  options: {
    goal_id: number;
    goal_type: string;
  };
}

export interface ClientLinkedGoalAccountsEvent extends IAventurEvent {
  event: AventurEvents.ClientLinkedGoalAccounts;
  options: {
    goal_id: number;
    goal_type: string;
  };
}

export interface ClientUnlinkedGoalAccountsEvent extends IAventurEvent {
  event: AventurEvents.ClientLinkedGoalAccounts;
  options: {
    goal_id: number;
    goal_type: string;
  };
}

export interface ClientCreatedAssetEvent extends IAventurEvent {
  event: AventurEvents.ClientCreatedAsset;
  options: {
    asset_type: string;
  };
}

export interface ClientUpdatedAssetEvent extends IAventurEvent {
  event: AventurEvents.ClientUpdatedAsset;
  options: {
    asset_id: number;
    asset_type: string;
  };
}

export interface ClientDeletedAssetEvent extends IAventurEvent {
  event: AventurEvents.ClientDeletedAsset;
  options: {
    asset_id: number;
    asset_type: string;
  };
}

export interface ClientCreatedDebtEvent extends IAventurEvent {
  event: AventurEvents.ClientCreatedDebt;
  options: {
    debt_type: string;
  };
}

export interface ClientUpdatedDebtEvent extends IAventurEvent {
  event: AventurEvents.ClientUpdatedDebt;
  options: {
    debt_id: number;
    debt_type: string;
  };
}

export interface ClientDeletedDebtEvent extends IAventurEvent {
  event: AventurEvents.ClientDeletedDebt;
  options: {
    debt_id: number;
    debt_type: string;
  };
}

export interface ClientCreatedIncomeEvent extends IAventurEvent {
  event: AventurEvents.ClientCreatedIncome;
  options: {
    income_type: string;
  };
}

export interface ClientUpdatedIncomeEvent extends IAventurEvent {
  event: AventurEvents.ClientUpdatedIncome;
  options: {
    income_id: number;
    income_type: string;
  };
}

export interface ClientDeletedIncomeEvent extends IAventurEvent {
  event: AventurEvents.ClientDeletedIncome;
  options: {
    income_id: number;
    income_type: string;
  };
}

type AventurEvent =
  | ClientSignedInEvent
  | ClientSignedOutEvent
  | ClientViewedPageEvent
  | ClientSubmittedFeedbackEvent
  | ClientClearedChatEvent
  | ClientCreatedGoalEvent
  | ClientUpdatedGoalEvent
  | ClientDeletedGoalEvent
  | ClientLinkedGoalAccountsEvent
  | ClientUnlinkedGoalAccountsEvent
  | ClientCreatedAssetEvent
  | ClientUpdatedAssetEvent
  | ClientDeletedAssetEvent
  | ClientCreatedDebtEvent
  | ClientUpdatedDebtEvent
  | ClientDeletedDebtEvent
  | ClientCreatedIncomeEvent
  | ClientUpdatedIncomeEvent
  | ClientDeletedIncomeEvent;

type EventOptions<T extends IAventurEvent> = {
  [P1 in keyof T['options']]: P1 extends keyof T['options']
    ? T['options'][P1]
    : never;
};

// write a mapper that accepts AventurEvents enum value and returns an object of an AventurEvent type
const eventMapper = (event: AventurEvents) =>
  ({
    [AventurEvents.ClientSignedIn]: {
      event: AventurEvents.ClientSignedIn,
      display_name: 'Client Signed In',
    },
    [AventurEvents.ClientSignedOut]: {
      event: AventurEvents.ClientSignedOut,
      display_name: 'Client Signed Out',
    },
    [AventurEvents.ClientViewedPage]: {
      event: AventurEvents.ClientViewedPage,
      display_name: 'Client Viewed Page',
    },
    [AventurEvents.ClientSubmittedFeedback]: {
      event: AventurEvents.ClientSubmittedFeedback,
      display_name: 'Client Submitted Feedback',
    },
    [AventurEvents.ClientClearedChat]: {
      event: AventurEvents.ClientClearedChat,
      display_name: 'Client Cleared AI Chat',
    },
    [AventurEvents.ClientCreatedGoal]: {
      event: AventurEvents.ClientCreatedGoal,
      display_name: 'Client Created Goal',
    },
    [AventurEvents.ClientUpdatedGoal]: {
      event: AventurEvents.ClientUpdatedGoal,
      display_name: 'Client Updated Goal',
    },
    [AventurEvents.ClientDeletedGoal]: {
      event: AventurEvents.ClientDeletedGoal,
      display_name: 'Client Deleted Goal',
    },
    [AventurEvents.ClientLinkedGoalAccounts]: {
      event: AventurEvents.ClientLinkedGoalAccounts,
      display_name: 'Client Linked Goal Accounts',
    },
    [AventurEvents.ClientUnlinkedGoalAccounts]: {
      event: AventurEvents.ClientUnlinkedGoalAccounts,
      display_name: 'Client Unlinked Goal Accounts',
    },
    [AventurEvents.ClientCreatedAsset]: {
      event: AventurEvents.ClientCreatedAsset,
      display_name: 'Client Created Asset',
    },
    [AventurEvents.ClientUpdatedAsset]: {
      event: AventurEvents.ClientUpdatedAsset,
      display_name: 'Client Updated Asset',
    },
    [AventurEvents.ClientDeletedAsset]: {
      event: AventurEvents.ClientDeletedAsset,
      display_name: 'Client Deleted Asset',
    },
    [AventurEvents.ClientCreatedDebt]: {
      event: AventurEvents.ClientCreatedDebt,
      display_name: 'Client Created Debt',
    },
    [AventurEvents.ClientUpdatedDebt]: {
      event: AventurEvents.ClientUpdatedDebt,
      display_name: 'Client Updated Debt',
    },
    [AventurEvents.ClientDeletedDebt]: {
      event: AventurEvents.ClientDeletedDebt,
      display_name: 'Client Deleted Debt',
    },
    [AventurEvents.ClientCreatedIncome]: {
      event: AventurEvents.ClientCreatedIncome,
      display_name: 'Client Created Income',
    },
    [AventurEvents.ClientUpdatedIncome]: {
      event: AventurEvents.ClientUpdatedIncome,
      display_name: 'Client Updated Income',
    },
    [AventurEvents.ClientDeletedIncome]: {
      event: AventurEvents.ClientDeletedIncome,
      display_name: 'Client Deleted Income',
    },
  })[event] as IAventurEvent;

const setAmplitudeUserDetails = (user: User) => {
  if (import.meta.env.VITE_AMPLITUDE_KEY) {
    amplitude.setUserId(user.email);
    const identify = new amplitude.Identify();
    identify
      .set('jarvis_id', user.id)
      .set('first_name', user.firstName)
      .set('last_name', user.lastName);
    amplitude.identify(identify);
    amplitude.setGroup('user-role', user.groups ?? '');
  }
};

const trackAventurEvent = <T extends AventurEvent>(
  event: AventurEvents,
  options?: EventOptions<T>,
) => {
  if (import.meta.env.VITE_AMPLITUDE_KEY) {
    const event_type = AMPLITUDE_EVENT_TYPE;
    const { display_name } = eventMapper(event);

    console.log('Tracking event: ', display_name, options);

    // track(display_name, {
    //   event_type,
    //   ...(options ?? {}),
    // });
  }
};

export const useEventTracking = (router?: Router) => {
  router?.beforeEach((to, from, next) => {
    console.log('beforeEach', to, from);

    // skip tracking initial sign-in navigation (dashboard > dashboard)
    // and logout action navigation (<> logout)
    if (
      to.path !== from.path &&
      to.name !== 'logout' &&
      from.name !== 'logout'
    ) {
      trackAventurEvent<ClientViewedPageEvent>(AventurEvents.ClientViewedPage, {
        page_name: to.meta?.title as string | undefined,
        params: to.params,
      });
    }

    next();
  });

  return {
    setTrackingIdentity: setAmplitudeUserDetails,
    trackEvent: trackAventurEvent,
  };
};
