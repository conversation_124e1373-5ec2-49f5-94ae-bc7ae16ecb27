import { computed } from 'vue';
import { defineStore } from 'pinia';

import { BiometryType } from '@aparajita/capacitor-biometric-auth';
//

interface AppState {
  mode: string;
  isReady: boolean;
  debug: boolean;
  version: string;
  usePasskey: BiometryType;
}

type Getters = {
  isAppReady: (state: AppState) => boolean;
};

type Actions = {
  setIsReady: (ready: boolean) => void;
  setUsePasskey: (biometryType: BiometryType) => void;
};

const mode = computed(() => {
  return import.meta.env.VITE_ENV ?? 'local';
});

export const useAppStore = defineStore<'app-store', AppState, Getters, Actions>(
  'app-store',
  {
    state: () => ({
      mode: mode.value,
      isReady: false,
      usePasskey: BiometryType.none,
      debug: mode.value === 'local',
      version: __APP_VERSION__,
    }),
    getters: {
      isAppReady: (state) => state.isReady,
    },
    actions: {
      setIsReady(ready) {
        this.isReady = ready;
      },
      setUsePasskey(biometryType: BiometryType) {
        this.usePasskey = biometryType;
      },
    },
    persist: {
      storage: localStorage,
      pick: ['usePasskey'],
    },
  },
);
