@import '@aws-amplify/ui-vue/styles.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn {
    @apply h-12 px-5 rounded-md transition-opacity focus:outline-secondary-300 focus:outline-offset-2 focus:outline;
    &:focus {
      outline-width: 1px;
    }
  }
}

.aventur-logo {
  @apply w-4/6 mx-auto;
}

[data-amplify-authenticator] {
  --amplify-colors-font-info: theme('colors.white');
  --amplify-components-field-gap: var(--amplify-space-xxxs);
  --amplify-components-authenticator-router-background-color: theme(
    'colors.primary.700/.3'
  );
  --amplify-components-button-primary-color: theme('colors.secondary.900');
  --amplify-components-button-primary-background-color: theme(
    'colors.secondary.DEFAULT'
  );
  --amplify-components-button-primary-active-border-color: theme(
    'colors.secondary.700'
  );
  --amplify-components-button-primary-active-background-color: theme(
    'colors.secondary.500'
  );
  --amplify-components-button-primary-focus-border-color: theme(
    'colors.secondary.700'
  );
  --amplify-components-button-primary-focus-background-color: theme(
    'colors.secondary.500'
  );
  --amplify-components-button-primary-hover-background-color: theme(
    'colors.secondary.700'
  );
  --amplify-components-button-primary-disabled-background-color: theme(
    'colors.secondary.300'
  );
  --amplify-components-button-link-color: theme('colors.white');
  --amplify-components-button-link-focus-color: theme('colors.white');
  --amplify-components-button-link-active-color: theme('colors.white');
  --amplify-components-button-border-color: theme('colors.white');
  --amplify-components-button-font-weight: var(--amplify-font-weights-medium);
  --amplify-components-tabs-item-active-border-color: theme('colors.white');
  --amplify-components-tabs-item-active-color: theme('colors.white');
  --amplify-components-tabs-item-border-color: theme('colors.primary.DEFAULT');
  --amplify-components-passwordfield-button-active-background-color: theme(
    'colors.primary.DEFAULT'
  );
  --amplify-components-passwordfield-button-hover-background-color: theme(
    'colors.primary.DEFAULT'
  );
  --amplify-components-tabs-item-color: theme('colors.white/.5');
  --amplify-components-tabs-item-hover-color: theme('colors.white');
  --amplify-components-field-label-color: theme('colors.white/.8');
  --amplify-components-text-error-color: theme('colors.red.300');

  background-color: #285049;
  background-image: url('/assets/bg-aventur.png');
  background-blend-mode: normal;
  background-position: top;
  background-size: cover;

  @apply size-full text-white/30 items-center justify-center px-8;

  [data-amplify-router] {
    padding-left: var(--amplify-space-medium);
    padding-right: var(--amplify-space-medium);
  }

  input {
    @apply h-12;
    background-color: theme('colors.white') !important;

    &:focus {
      @apply shadow-none;
    }
  }

  .amplify-button {
    &[role='switch']:focus {
      @apply shadow-none;
    }

    &:not([role='switch']) {
      @apply btn;
      border-radius: var(--amplify-components-button-border-radius) !important;
      margin-top: var(--amplify-space-medium);
    }

    &.amplify-button--disabled {
      color: theme('colors.secondary.400');
    }

    &.amplify-button--default {
      font-size: var(--amplify-font-sizes-small);
      @apply bg-transparent underline shadow-none text-white outline-none border-none;

      &:focus,
      &:active {
        @apply no-underline outline-none border-none shadow-none;
      }
    }

    &.amplify-button--link {
      @apply bg-transparent underline;

      &:focus,
      &:active {
        @apply no-underline outline-none border-none shadow-none;
      }
    }

    &.amplify-field__show-password {
      background: theme('colors.white') !important;
      border-color: rgb(33 82 73 / 0.3) !important;
      border-radius: var(--amplify-components-button-border-radius);
      border-start-start-radius: 0;
      border-end-start-radius: 0;
      border-left: 0 !important;
    }

    &.amplify-alert__dismiss {
      @apply hidden;
      background-color: var(--amplify-colors-red-10);
      border-color: transparent;
      &:hover,
      &:active,
      &:focus {
        background-color: var(--amplify-colors-red-20);
        border-color: var(--amplify-colors-red-40);
      }
      &:focus {
        box-shadow: 0 0 0 1px var(--amplify-colors-red-60);
      }
    }
  }

  .amplify-heading--3 {
    color: var(--amplify-colors-white);
    font-size: var(--amplify-font-sizes-medium);
    font-weight: var(--amplify-font-weights-bold);
  }

  .amplify-authenticator__subtitle {
    color: var(--amplify-colors-font-info);
    font-size: var(--amplify-font-sizes-small);
    font-weight: var(--amplify-font-weights-light);
  }

  #signIn-panel > [data-amplify-form] {
     padding: var(--amplify-space-zero);
  }
  #signUp-panel > [data-amplify-form] {
    padding: var(--amplify-space-zero);
    .amplify-field.amplify-passwordfield {
      @apply hidden;
    }
  }

  [data-amplify-form] {
    ::placeholder {
      color: #cfcfcf;
      font-size: 0.9rem;
    }
    ::-webkit-input-placeholder {
      /* WebKit browsers */
      color: #cfcfcf;
      font-size: 0.9rem;
    }
    :-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #cfcfcf;
      font-size: 0.9rem;
    }
    ::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #cfcfcf;
      font-size: 0.9rem;
    }
    :-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: #cfcfcf;
      font-size: 0.9rem;
    }

    padding: var(--amplify-space-xl) var(--amplify-space-zero)
      var(--amplify-space-zero);

    .amplify-label {
      font-size: var(--amplify-font-sizes-small);
    }

    .amplify-field-group {
      .amplify-input {
        @apply border-primary/30 !important;

        border-radius: var(--amplify-components-button-border-radius);
        border-start-start-radius: var(
          --amplify-components-button-border-radius
        ) !important;
        border-end-start-radius: var(
          --amplify-components-button-border-radius
        ) !important;

        [type='password'] {
          border-radius: var(--amplify-components-button-border-radius) 0 0
            var(--amplify-components-button-border-radius) !important;
        }
      }

      :not(:last-child) .amplify-input {
        border-end-end-radius: 0;
        border-start-end-radius: 0;
      }
    }

    .amplify-field {
      &.amplify-checkboxfield {
        .amplify-checkbox {
          @apply flex-row;
        }
        @apply min-h-12 justify-start;
        &.amplify-checkbox {
          flex-direction: row;
        }
      }
      .amplify-checkbox__button {
        @apply m-1 mr-3;
        --amplify-internal-checkbox_button-focused-before-border-color: theme(
          'colors.white'
        );
        &::before {
          @apply p-3;
          border-color: theme('colors.white');
        }
        .amplify-checkbox__icon {
          @apply bg-primary-700 size-5;
        }
      }
    }

    .amplify-text {
      @apply text-white;

      &.amplify-text--error,
      &.amplify-field__error-message {
        color: var(--amplify-components-text-error-color);
        font-size: var(--amplify-font-sizes-small);
      }

      &.amplify-checkbox__label {
        font-size: var(--amplify-font-sizes-small);
        color: var(--amplify-components-field-label-color);
      }
    }
  }

  [data-amplify-router] {
    @apply !bg-transparent !shadow-none !border-none;

    .amplify-tabs__list {
      @apply border-none;
      margin-bottom: var(--amplify-space-medium);

      &.amplify-tabs__list--top {
        --internal-item-border-width: 0 0
          var(--amplify-components-tabs-border-width) 0;
      }

      .amplify-tabs__item {
        font-weight: var(--amplify-font-weights-medium);
      }
    }
  }

  [data-amplify-container] {
    place-self: start;
  }

  /* iPhone Plus models, X, XS, XR, 11, Max, Pro models and similar */
  @media (max-height: 956px) {
    @apply pt-16;
  }

  /* up to iPhone 6 and similar */
  @media (max-height: 667px) {
    @apply pt-5;
  }
}
