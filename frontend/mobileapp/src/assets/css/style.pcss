@import 'vue-loading-overlay/dist/css/index.css';

@import 'toast.pcss';
@import 'feedback.pcss';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn {
    @apply py-2 px-5 rounded-md transition-opacity focus:outline-gray-100 focus:outline;

    &.btn-primary {
      @apply bg-primary text-white;
    }
  }

  .iphone-notch-safe {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

@font-face {
  font-family: 'Saira';
  font-style: normal;
  src: url('../fonts/Saira-VariableFont_wdth,wght.ttf') format('woff2');
}

body {
  @apply h-full;

  #app {
    @apply flex items-center justify-center bg-gray-100;
    height: 100dvh;

    &:has([data-v-app-splash]),
    &:has([data-amplify-authenticator]) {
      @apply bg-transparent;
    }
  }

  input:not([type='radio']),
  input:not([type='checkbox']),
  textarea {
    @apply border-gray-300 bg-gray-50/50;
  }
}

.toast-warning {
  @apply bg-amber-100 text-amber-700 !important;
}

[data-intro] {
  h1 {
    font-family: 'Saira', serif;
    font-optical-sizing: auto;
    font-weight: 500;
    font-variation-settings: 'wdth' 125;
  }

  > div:first-child {
    @apply size-48 mt-24;

    /* iPhone SE (and similar) fix */
    @media (max-width: 375px) {
      @apply size-40 mt-10;
    }
  }
  > div:last-child {
    /* iPhone SE (and similar) fix */
    @media (max-width: 375px) {
      @apply text-sm;
    }
  }
}
