@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn {
    @apply h-12 px-5 rounded-md transition-opacity focus:outline-secondary-300 focus:outline-offset-2 focus:outline;
    &:focus {
      outline-width: 1px;
    }
  }
}

[data-feedback-button] {
  @apply border-none bg-primary/80 fixed block shadow-none outline-none w-10 h-[42px] rounded-t-lg transition-all;

  inset: auto 0 15% auto !important;
  pointer-events: all !important;
  touch-action: auto !important;
  color-scheme: none !important;

  transform: rotateZ(-90deg) translateX(50%) translateY(3px);
  transform-origin: 100% 100% !important;

  z-index: 2147483647;

  &:hover {
    @apply bg-primary;
  }

  button {
    @apply text-white  flex flex-row-reverse w-full h-full border-none items-center outline-none font-semibold text-sm cursor-pointer py-[5px] px-2.5 rounded-t-lg;
    transition: background-color 200ms ease-out;
  }
}

[data-feedback-form] {
  @apply mt-3.5 p-4 rounded-xl bg-primary-950;
  @media (max-height: 667px) {
    @apply mt-4;
  }

  form {
    @apply font-medium text-base px-4;

    ::placeholder {
      color: #cfcfcf;
      font-size: 0.9rem;
    }
    ::-webkit-input-placeholder {
      /* WebKit browsers */
      color: #cfcfcf;
      font-size: 0.9rem;
    }
    :-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #cfcfcf;
      font-size: 0.9rem;
    }
    ::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #cfcfcf;
      font-size: 0.9rem;
    }
    :-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: #cfcfcf;
      font-size: 0.9rem;
    }

    h1 {
      @apply mb-7 pb-3 border-b-2 text-center text-white;
      padding-top: 1.2rem;
    }

    label {
      @apply mb-0 text-sm text-gray-50;
    }

    input {
      @apply h-12 text-black/90 !bg-white font-normal;

      &:focus {
        @apply shadow-none;
      }
    }

    textarea {
      @apply bg-white;
    }

    button {
      @apply btn;

      &[type='submit'] {
        @apply text-secondary-900 bg-secondary;
        &:hover {
          @apply text-white bg-secondary-700;
        }
        &:disabled {
          @apply opacity-100 text-secondary-400 bg-secondary-300;
        }
      }
    }
  }
}
