@import 'vue-toastification/dist/index.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

#app-navbar {
  padding: env(safe-area-inset-top) env(safe-area-inset-right)
    env(safe-area-inset-bottom) env(safe-area-inset-left);
}

.Vue-Toastification__toast.toast-success {
  @apply bg-green-100 border-green-400 text-green-700;
}

.Vue-Toastification__toast.toast-success .Vue-Toastification__progress-bar {
  @apply bg-green-700;
}

.Vue-Toastification__toast.toast-error {
  @apply bg-red-100 border-red-400 text-red-700;
}

.Vue-Toastification__toast.toast-error .Vue-Toastification__progress-bar {
  @apply bg-red-700;
}

.Vue-Toastification__toast.toast-info {
  @apply bg-yellow-100 border-yellow-400 text-yellow-700;
}

.Vue-Toastification__toast.toast-info .Vue-Toastification__progress-bar {
  @apply bg-yellow-700;
}

.Vue-Toastification__toast.toast-success .Vue-Toastification__close-button,
.Vue-Toastification__toast.toast-error .Vue-Toastification__close-button,
.Vue-Toastification__toast.toast-info .Vue-Toastification__close-button {
  @apply text-black;
}
