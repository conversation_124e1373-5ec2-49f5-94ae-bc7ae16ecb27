<template>
  <LayoutContent>
    <template #head>
      <BaseSection class="p-3" no-padding-x>
        <Line :data="data" :options="chartOptions" />
      </BaseSection>
      <hr class="-mt-2 h-px border-0 bg-black/20" />
      <ChartFilterList
        :initial-range="selectedRange"
        @on-range-pick="(range) => handleRangeChange(range)"
      />
      <BaseSection no-padding-x class="p-5">
        <div class="flex flex-row gap-6">
          <div class="flex flex-row place-items-center gap-3">
            <div class="h-10 w-1 bg-[#C9E386]" />
            <div class="flex flex-col">
              <span class="text-white/60">Assets</span>
              <span class="text-white">{{
                formatWithCurrency(assetsSum)
              }}</span>
            </div>
          </div>
          <div class="flex flex-row place-items-center gap-3">
            <div class="h-10 w-1 bg-[#F18253]" />
            <div class="flex flex-col">
              <span class="text-white/60">Debts</span>
              <span class="text-white"
                >-{{ formatWithCurrency(debtsSum) }}</span
              >
            </div>
          </div>
        </div>
      </BaseSection>
    </template>
    <div class="mb-24">
      <PortfolioIndex :assets="assets" :debts="debts" />
    </div>
  </LayoutContent>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { Line } from 'vue-chartjs';
  import {
    CategoryScale,
    ChartData,
    Chart as ChartJS,
    Legend,
    LineElement,
    LinearScale,
    PointElement,
    TimeScale,
    Title,
    Tooltip,
  } from 'chart.js';
  import 'chartjs-adapter-date-fns';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { ChartColours } from '@aventur-shared/constants';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';
  import { Asset, Debt } from '@aventur-shared/modules/factfind/types';
  import PortfolioIndex from '@modules/clients/components/PortfolioIndex.vue';
  import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import { getChartOptions } from '@aventur-shared/modules/clients/utils/chartOptions';
  import { SelectedRange } from '@aventur-shared/modules/clients/types/SelectedRange';
  import {
    rangeToDtoFromDateMapper,
    rangeToDtoPeriodMapper,
  } from '@aventur-shared/modules/clients/utils/mappers/portfolioValuationMapper';
  import { getClientPortfolioValuations } from '@aventur-shared/modules/clients/api';
  import { PortfolioValuation } from '@aventur-shared/modules/clients/types/PortfolioValuation';
  import { ClientId } from '@aventur-shared/modules/clients';

  import LayoutContent from '@/layouts/LayoutContent.vue';
  import ChartFilterList from '@modules/clients/components/ChartFilterList.vue';
  import { usePullRefresh } from '@/composables/usePullRefresh';
  //

  ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    TimeScale,
  );

  const toast = useToast();
  const { userId } = useUserStore();
  const { loadAssetsAndDebts } = useFactfindStore();
  const { registerRefreshHandler } = usePullRefresh();

  const assets = ref<Asset[]>([]);
  const debts = ref<Debt[]>([]);

  const selectedRange = ref<SelectedRange>('6m');
  const graphData = ref();
  const lastItem = ref<PortfolioValuation>();

  const action = async () => {
    const { assets: _assets, debts: _debts } = await loadAssetsAndDebts(
      Number(userId) as ClientId,
    );
    assets.value = _assets;
    debts.value = _debts;
    await handleRangeChange(selectedRange.value);
  };

  onMounted(async () => {
    registerRefreshHandler(async () => {
      assets.value = [];
      debts.value = [];
      await action();
    });

    try {
      await action();
    } catch (e) {
      toast.error(e as Error);
    }
  });

  const assetsSum = computed(() => {
    return new Money(lastItem.value?.assetValuation ?? 0);
  });

  const debtsSum = computed(() => {
    return new Money(lastItem.value?.debtValuation ?? 0);
  });

  const chartOptions = computed(() =>
    getChartOptions(selectedRange.value, {
      scales: {
        // eslint-disable-next-line id-length
        y: {
          ticks: {
            color: '#ffffff99',
          },
        },
        // eslint-disable-next-line id-length
        x: {
          ticks: {
            color: '#ffffff99',
          },
        },
      },
    }),
  );

  const data = computed<ChartData<'line', any>>(() => ({
    datasets: [
      {
        backgroundColor: ChartColours.LIGHT_GRAY,
        borderColor: ChartColours.LIGHT_GRAY,
        borderWidth: 2,
        pointBackgroundColor: ChartColours.DARK_GREEN,
        pointBorderWidth: 1.3,
        data: graphData.value,
      },
    ],
  }));

  const handleRangeChange = async (range: SelectedRange) => {
    selectedRange.value = range;
    const period = rangeToDtoPeriodMapper(range);
    const fromDate = rangeToDtoFromDateMapper(range);
    const res = await getClientPortfolioValuations(
      userId as ClientId,
      period,
      fromDate,
    );
    graphData.value = res.map((item) => ({
      // eslint-disable-next-line id-length
      x: item.timestamp.valueOf(),
      // eslint-disable-next-line id-length
      y: item.netValuation,
    }));
    lastItem.value = res.pop();
  };
</script>
