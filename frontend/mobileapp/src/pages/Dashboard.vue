<template>
  <LayoutContent>
    <div class="mb-24">
      <DashboardIndex :user="client" />
    </div>
  </LayoutContent>
</template>

<script setup lang="ts">
  import { onMounted } from 'vue';

  import { useRefData } from '@aventur-shared/stores';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useClientStore } from '@aventur-shared/modules/clients';

  import LayoutContent from '@/layouts/LayoutContent.vue';
  import { usePullRefresh } from '@/composables/usePullRefresh';
  import DashboardIndex from '@modules/clients/components/DashboardIndex.vue';
  //

  const { getRefData } = useRefData();
  const { getUserData } = useUserStore();
  const { getProfile: client } = useClientStore();
  const { loadClientData, loadClientGoals } = useClientStore();
  const { registerRefreshHandler } = usePullRefresh();

  onMounted(async () => {
    registerRefreshHandler(async () => {
      await getRefData();
      await getUserData();
      await loadClientData(client.id);
      await loadClientGoals(client.id);
    });
  });
</script>
