<template>
  <LayoutContent>
    <template #head>
      <BaseSection no-padding>
        <GoalsHeader />
      </BaseSection>
    </template>
    <template v-if="!APIState.error">
      <div class="mb-24">
        <router-view />
      </div>
    </template>
    <template v-else>
      <Alert class="m-4 text-sm" type="error">
        We were unable to load some important data. Please, contact Aventur
        support team at <b><EMAIL></b> or call our office at
        <b>020 3740 1113</b>. Our team is here to help.
      </Alert>
    </template>
  </LayoutContent>
</template>

<script setup lang="ts">
  import { onMounted, provide, ref } from 'vue';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId, useClientStore } from '@aventur-shared/modules/clients';
  import { getClientsActiveHoldings } from '@aventur-shared/modules/clients/api';
  import Alert from '@aventur-shared/components/Alert.vue';
  import BaseSection from '@aventur-shared/components/BaseSection.vue';
  import {
    GoalLinkedHolding,
    Holding,
  } from '@aventur-shared/modules/clients/models';

  import LayoutContent from '@/layouts/LayoutContent.vue';
  import GoalsHeader from '@modules/clients/components/goals/headers/GoalsHeader.vue';
  import { activeHoldingsInjectionKey } from '@modules/clients/components/goals/active-holdings';
  import { usePullRefresh } from '@/composables/usePullRefresh';
  //

  const toast = useToast();
  const { userId } = useUserStore();
  const { APIState, setError: setAPIError } = useAPIState();
  const { loadClientGoals, loadHealthScore } = useClientStore();
  const { registerRefreshHandler } = usePullRefresh();

  const activeHoldings = ref<GoalLinkedHolding[]>([]);
  provide(activeHoldingsInjectionKey, activeHoldings);

  const action = async () => {
    await loadClientGoals(userId as ClientId);
    await loadHealthScore(userId as ClientId);

    const holdings = await getClientsActiveHoldings([userId as number]);
    activeHoldings.value = holdings.map((holding: Holding) => ({
      id: holding.id,
      accountNumber: holding.accountNumber,
      providerName: holding.provider.name,
      productTypeName: holding.product
        ? holding.product?.product_type.name
        : '',
    }));
  };

  onMounted(async () => {
    registerRefreshHandler(action);

    try {
      await action();
    } catch (e: any) {
      setAPIError(e);
      toast.error(e);
    }
  });
</script>
