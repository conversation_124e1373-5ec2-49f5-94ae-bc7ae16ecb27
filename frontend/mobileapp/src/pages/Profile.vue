<template>
  <LayoutContent>
    <template #head>
      <router-view name="header" />
    </template>
    <template v-if="!APIState.error">
      <div class="mb-24">
        <router-view />
      </div>
    </template>
    <template v-else>
      <Alert class="m-4 text-sm" type="error">
        We were unable to load some important data. Please, contact Aventur
        support team at <b><EMAIL></b> or call our office at
        <b>020 3740 1113</b>. Our team is here to help.
      </Alert>
    </template>
  </LayoutContent>
</template>

<script setup lang="ts">
  import { onMounted } from 'vue';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { useFactfindStore } from '@aventur-shared/modules/factfind/stores';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import Alert from '@aventur-shared/components/Alert.vue';

  import LayoutContent from '@/layouts/LayoutContent.vue';
  //

  const toast = useToast();
  const { userId } = useUserStore();
  const { APIState, setError: setAPIError } = useAPIState();
  const { getClientPrimaryDetails, setClientPrimaryDetails } =
    useFactfindStore();

  onMounted(async () => {
    try {
      setClientPrimaryDetails(
        await getClientPrimaryDetails(userId as ClientId),
      );
    } catch (e: any) {
      setAPIError(e);
      toast.error(e);
    }
  });
</script>
