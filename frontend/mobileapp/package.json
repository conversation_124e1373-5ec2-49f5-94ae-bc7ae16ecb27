{"name": "aventur-mobileapp", "version": "1.2", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "test": "vitest run", "coverage": "vitest run --coverage", "tsc": "vue-tsc --noEmit", "lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "lint:inspect-config": "eslint --inspect-config", "prettier": "prettier -w -u ./src", "tailwind-config-viewer": "tailwind-config-viewer -o", "sync": "npx cap sync", "dev:ios": "npx cap run ios", "dev:android": "NODE_ENV=development npx cap run android", "generate-assets-ios": "npx capacitor-assets generate --ios --logoSplashScale '1.0' --iconBackgroundColor '#14312C' --iconBackgroundColorDark '#14312C' --splashBackgroundColor '#14312C' --splashBackgroundColorDark '#14312C'", "generate-assets-android": "npx capacitor-assets generate --android --logoSplashScale '0.8' --iconBackgroundColor '#14312C' --iconBackgroundColorDark '#14312C' --splashBackgroundColor '#14312C' --splashBackgroundColorDark '#14312C'"}, "dependencies": {"@aparajita/capacitor-biometric-auth": "^8.0.2", "@aparajita/capacitor-secure-storage": "^6.0.1", "@capacitor/android": "^6.0.0", "@capacitor/app": "^6.0.2", "@capacitor/core": "^6.0.0", "@capacitor/dialog": "^6.0.2", "@capacitor/ios": "^6.0.0", "@capacitor/preferences": "^6.0.2", "@capacitor/splash-screen": "^6.0.3"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "^6.0.0"}, "engines": {"node": ">=22.17"}}