### SETUP

#### iOS:
1. Install cocoapods
2. Run `npm run sync:mobileapp` in _frontend_ dir
3. Run `npm run dev:ios` in _frontend_ dir or open `mobileapp/ios` project in Xcode to continue


#### Android
1. Run `npm run sync:mobileapp` in _frontend_ dir
2. Add **ANDROID_HOME** and **JAVA_HOME** to env variables
3. Generate **upload-keystore.jks** file and save it to _modileapp/android_ folder: https://developer.android.com/studio/publish/app-signing

### TESTING

#### IOS:
For basic and rough testing (general functionality, UI, device sizes, etc.) 
just run `npm run dev:mobileap` in the _frontend_ directory and navigate to the Vite URL as normal.

For iOS simulator tests follow the steps 2-3 from the _SETUP_ section above.

For local physical device testing:
1. Pair your device in Xcode: https://developer.apple.com/documentation/xcode/running-your-app-in-simulator-or-on-a-device#
2. Register the device in your Apple Developer Program account: https://developer.apple.com/help/account/devices/register-a-single-device
3. Install `ngrok`: https://ngrok.com/docs/getting-started/
4. Configure `ngrok` tunnels (see example configuration YAML file in `dev-tools/ngrok` folder)
5. Start `ngrok` tunnels (free plan offers 1 agent and 3 tunnels per agent): `ngrok start --all`
6. Update **VITE_URL** and **VITE_AWS_COGNITO_ENDPOINT_URL** in `mobileapp/.env` file with new generated URLs
7. If not using paid `ngrok` account, add `ngrok-skip-browser-warning` header to Amplify API configuration in `mobileapp/src/main.ts` file (https://ngrok.com/docs/errors/err_ngrok_6024/)
8. Run `npm run sync:mobileapp` in _frontend_ folder
9. Open `mobileapp/ios` project in Xcode and point the app build to your connected device

#### Android:

1. Download and install Android Studio: https://developer.android.com/studio
2. Download and install Java / OpenJDK if needed
3. Open Android project in _mobileapp/android_ folder
4. Select a virtual device or pair a physical device: https://developer.android.com/studio/run/device 
