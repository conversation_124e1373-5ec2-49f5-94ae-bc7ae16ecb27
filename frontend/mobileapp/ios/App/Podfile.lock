PODS:
  - AparajitaCapacitorBiometricAuth (8.0.2):
    - Capacitor
  - AparajitaCapacitorSecureStorage (6.0.1):
    - Capacitor
    - KeychainSwift (~> 21.0)
  - Capacitor (6.2.1):
    - Capacitor<PERSON>ordova
  - CapacitorApp (6.0.2):
    - Capacitor
  - CapacitorCordova (6.2.1)
  - CapacitorDialog (6.0.2):
    - Capacitor
  - CapacitorPreferences (6.0.3):
    - Capacitor
  - CapacitorSplashScreen (6.0.3):
    - Capacitor
  - KeychainSwift (21.0.0)

DEPENDENCIES:
  - "AparajitaCapacitorBiometricAuth (from `../../../node_modules/@aparajita/capacitor-biometric-auth`)"
  - "AparajitaCapacitorSecureStorage (from `../../../node_modules/@aparajita/capacitor-secure-storage`)"
  - "Capacitor (from `../../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../../node_modules/@capacitor/app`)"
  - "CapacitorCordova (from `../../../node_modules/@capacitor/ios`)"
  - "CapacitorDialog (from `../../../node_modules/@capacitor/dialog`)"
  - "CapacitorPreferences (from `../../../node_modules/@capacitor/preferences`)"
  - "CapacitorSplashScreen (from `../../../node_modules/@capacitor/splash-screen`)"

SPEC REPOS:
  trunk:
    - KeychainSwift

EXTERNAL SOURCES:
  AparajitaCapacitorBiometricAuth:
    :path: "../../../node_modules/@aparajita/capacitor-biometric-auth"
  AparajitaCapacitorSecureStorage:
    :path: "../../../node_modules/@aparajita/capacitor-secure-storage"
  Capacitor:
    :path: "../../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../../node_modules/@capacitor/app"
  CapacitorCordova:
    :path: "../../../node_modules/@capacitor/ios"
  CapacitorDialog:
    :path: "../../../node_modules/@capacitor/dialog"
  CapacitorPreferences:
    :path: "../../../node_modules/@capacitor/preferences"
  CapacitorSplashScreen:
    :path: "../../../node_modules/@capacitor/splash-screen"

SPEC CHECKSUMS:
  AparajitaCapacitorBiometricAuth: c058563fe00160e12b022341e11b3181357ea93a
  AparajitaCapacitorSecureStorage: 18190f233b0531422af8c1caa51804686d940ee0
  Capacitor: c95400d761e376be9da6be5a05f226c0e865cebf
  CapacitorApp: e1e6b7d05e444d593ca16fd6d76f2b7c48b5aea7
  CapacitorCordova: 8d93e14982f440181be7304aa9559ca631d77fff
  CapacitorDialog: 25878ac91f5ffaca62ce78884b6e1473e75596e1
  CapacitorPreferences: f3eadae2369ac3ab8e21743a2959145b0d1286a3
  CapacitorSplashScreen: fd8bf1bf9081d9aa8817b7cd37d740d1bdaf2fb2
  KeychainSwift: 4a71a45c802fd9e73906457c2dcbdbdc06c9419d

PODFILE CHECKSUM: 828c6566207f432f5d75c096a0266af731a4ae02

COCOAPODS: 1.16.2
