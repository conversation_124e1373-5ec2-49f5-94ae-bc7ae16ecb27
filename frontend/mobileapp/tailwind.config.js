const defaultTheme = require('tailwindcss/defaultTheme');
const colors = require('tailwindcss/colors');

module.exports = {
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}',
    '../shared/**/*.{js,ts,jsx,tsx,vue}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Manrope', ...defaultTheme.fontFamily.sans],
      },
      fontSize: {
        xxs: '0.60rem',
      },
      colors: {
        transparent: 'transparent',
        current: 'currentColor',
        // Aventur theme
        primary: {
          DEFAULT: '#215249',
          50: '#f3faf8',
          100: '#d5f2e9',
          200: '#abe4d2',
          300: '#79cfb7',
          400: '#4db49b',
          500: '#349881',
          600: '#277a69',
          700: '#236256',
          800: '#215249',
          900: '#1e433c',
          950: '#14312c',
        },
        secondary: {
          DEFAULT: colors.violet['400'],
          ...colors.violet,
        },
      },
      keyframes: {
        gradient: {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
      },
      animation: {
        gradient: 'gradient 4s ease infinite',
      },
      backgroundSize: {
        200: '200% 200%',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
};
