// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':aparajita-capacitor-biometric-auth')
    implementation project(':aparajita-capacitor-secure-storage')
    implementation project(':capacitor-app')
    implementation project(':capacitor-dialog')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-splash-screen')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
