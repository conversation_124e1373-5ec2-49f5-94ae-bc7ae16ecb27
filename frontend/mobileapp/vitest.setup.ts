import { DOMWrapper, RouterLinkStub, config } from '@vue/test-utils';
import { vi } from 'vitest';
import {
  addCheckIsNumberValidation,
  addCheckIsValidPhoneNumber,
} from 'aventur-shared/src/plugins/yup-validation-methods';

addCheckIsNumberValidation();
addCheckIsValidPhoneNumber();

const _plugins = () => {
  return {
    findByTestId(selector: string) {
      const element = this.element.querySelector(`[data-testid='${selector}']`);
      return element ? new DOMWrapper(element) : null;
    },
    findElementByText(cssSelector: string, text: string) {
      return this.findAll(cssSelector)
        .filter((el: DOMWrapper<HTMLElement>) => {
          return el.text() === text;
        })
        .at(0);
    },
  };
};

// add plugins globally if needed
config.global.plugins = [];
config.plugins.VueWrapper.install(_plugins);
config.global.components = {
  'router-link': RouterLinkStub,
};
config.global.provide = {
  toggleSidebar: vi.fn(),
};
