{"name": "aventur", "private": true, "workspaces": ["webapp", "mobileapp", "shared"], "scripts": {"dev:webapp": "npm run dev -w=webapp", "dev:mobileapp": "npm run dev -w=mobileapp", "dev:ios": "npm run dev:ios -w=mobileapp", "dev:android": "npm run dev:android -w=mobileapp", "sync:mobileapp": "npm run build -w=mobileapp && npm run sync -w=mobileapp"}, "dependencies": {"@amplitude/analytics-browser": "^2.13.2", "@aws-amplify/ui-vue": "^4.2.26", "@casl/ability": "^6.7.1", "@casl/vue": "^2.2.2", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.1.5", "@sentry/tracing": "^7.114.0", "@vee-validate/rules": "^4.11.3", "@vueform/multiselect": "^2.6.2", "@vueuse/components": "^13.1.0", "@vueuse/core": "^13.3.0", "@vueuse/router": "^13.0.0", "aventur-shared": "*", "aws-amplify": "^6.10.3", "change-case-all": "^2.1.0", "chart.js": "^4.4.4", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "luxon": "^3.4.4", "maska": "^3.0.2", "material-icons": "^1.13.13", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.0.2", "qs": "^6.14.0", "sortablejs": "^1.15.2", "sortablejs-vue3": "^1.2.11", "swiper": "^11.2.6", "ts-deep-pick": "^0.2.2", "uuid": "^11.0.5", "vee-validate": "^4.15.0", "vue": "^3.5.17", "vue-chartjs": "^5.3.1", "vue-loading-overlay": "^6.0.6", "vue-router": "^4.4.5", "vue-slider-component": "^4.1.0-beta.7", "vue-tippy": "^6.4.4", "vue-toastification": "^2.0.0-rc.5", "vuejs-confirm-dialog": "^0.5.1", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/compat": "^1.2.6", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.20.0", "@faker-js/faker": "^9.0.1", "@pinia/testing": "^1.0.0", "@sentry/vite-plugin": "^3.1.2", "@sentry/vue": "^9.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.15", "@types/lodash": "^4.17.7", "@types/luxon": "^3.4.2", "@types/node": "^22.15.14", "@types/uuid": "^10.0.0", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/coverage-v8": "^3.0.5", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.4.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.20", "babel-loader": "^10.0.0", "c8": "^10.1.3", "eslint": "^9.20.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-vue": "^10.1.0", "globals": "^16.2.0", "jsdom": "^26.0.0", "postcss": "^8.4.47", "postcss-import": "^16.1.0", "prettier": "^3.2.5", "sass": "^1.79.2", "sass-embedded": "^1.79.4", "sinon": "^21.0.0", "tailwind-config-viewer": "^2.0.4", "tailwindcss": "^3.4.12", "typescript": "^5.7.3", "typescript-eslint": "^8.24.0", "vite": "^7.0.4", "vite-plugin-pwa": "^1.0.0", "vitest": "^3.0.5", "vue-eslint-parser": "^10.1.3", "vue-loader": "^17.2.2", "vue-tsc": "^3.0.1"}}