{"name": "aventur-webapp", "version": "1.14.3", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "test": "vitest run", "coverage": "vitest run --coverage", "tsc": "vue-tsc --noEmit", "lint": "eslint ./src", "lint:fix": "eslint ./src --fix", "lint:inspect-config": "eslint --inspect-config", "prettier": "prettier -w -u ./src", "tailwind-config-viewer": "tailwind-config-viewer -o", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@storybook/addon-essentials": "^8.2.5", "@storybook/addon-interactions": "^8.2.5", "@storybook/addon-links": "^8.2.5", "@storybook/blocks": "^8.2.5", "@storybook/test": "^8.2.5", "@storybook/vue3": "^8.2.5", "@storybook/vue3-vite": "^8.2.5", "eslint-plugin-storybook": "^0.11.2", "msw": "^2.4.9", "msw-storybook-addon": "^2.0.3", "storybook": "^8.3.2", "storybook-vue3-router": "^5.0.0"}, "engines": {"node": ">=22.17"}, "msw": {"workerDirectory": ["public"]}, "dependencies": {"@uppy/aws-s3": "^4.1.0", "@uppy/core": "^4.2.1", "@uppy/drag-drop": "^4.0.2", "@uppy/progress-bar": "^4.0.0", "@uppy/xhr-upload": "^4.2.0", "filesize": "^11.0.1"}}