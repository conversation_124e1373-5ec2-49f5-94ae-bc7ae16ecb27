import { create<PERSON><PERSON> } from 'pinia';

import { type Preview, setup } from '@storybook/vue3';
import { vueRouter } from 'storybook-vue3-router';

import { initialize as initMSW, mswLoader } from 'msw-storybook-addon';
import { http } from 'msw';

import { Amplify } from 'aws-amplify';
import awsConfig from '../src/aws-exports';

import { fetchJwts } from './helpers';

// register `yup` methods
import {
  addCheckIsNumberValidation,
  addCheckIsValidPhoneNumber,
} from 'aventur-shared/src/plugins/yup-validation-methods';

addCheckIsNumberValidation();
addCheckIsValidPhoneNumber();

import 'tailwindcss/tailwind.css';
import { fetchAuthSession } from 'aws-amplify/auth';

/*
 * Initializes MSW
 * See https://github.com/mswjs/msw-storybook-addon#configuring-msw
 * to learn how to customize it
 */
initMSW(
  {
    serviceWorker: { url: '/mockServiceWorker.js' },
  },
  [
    http.get('http://localhost:8000/api/*', async ({ request }) => {
      console.log('[MSW]', request.url);
    }),
  ],
);

// Configure Amplify
Amplify.configure(awsConfig, {
  API: {
    REST: {
      headers: async () => {
        const { idToken } = (await fetchAuthSession()).tokens ?? {};
        return {
          Authorization: `Bearer ${idToken}`,
        };
      },
    },
  },
});

setup((app) => {
  app.use(createPinia());
});

export const beforeAll = async () => {
  // IDE Vue plugin may not like using import meta here, but it works
  const username = import.meta.env.STORYBOOK_USERNAME;
  const password = import.meta.env.STORYBOOK_PASSWORD;

  // sign-in to Cognito to allow API calls to be intercepted by MSW
  await fetchJwts(username, password).then(() => {
    console.log('Signed in to Cognito');
  });
};

const preview: Preview = {
  loaders: [mswLoader],

  decorators: [vueRouter()],

  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },

  tags: ['autodocs'],
};

export default preview;
