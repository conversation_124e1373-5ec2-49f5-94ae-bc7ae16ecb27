import { some } from 'lodash';
import { fetchAuthSession, getCurrentUser, signIn } from 'aws-amplify/auth';

import {
  Task,
  TaskStatus,
  TaskStatusEnum,
  TaskTypeEnum,
} from 'aventur-shared/src/modules/tasks';
import {
  AccountStatusEnum,
  accountStatusToSelectOption,
} from 'aventur-shared/src/modules/accounts/models/account';

import * as helpers from '../tests/helpers';
import { SubTask } from '@aventur-shared/modules/cases/models';

let taskId = 0;

export const fetchJwts = async (username: string, password: string) => {
  const { loginId } = (await getCurrentUser()).signInDetails ?? {};

  if (!loginId) {
    await signIn({
      username,
      password,
      options: {
        authFlowType: 'USER_PASSWORD_AUTH',
      },
    });
  }

  const { idToken, accessToken } = (await fetchAuthSession()).tokens ?? {};
  const accessTokenPayload = accessToken?.payload ?? {};

  return {
    idToken: idToken?.toString(),
    accessToken: accessToken?.toString(),
    clientId: accessTokenPayload.client_id as string,
    accessTokenSub: accessTokenPayload.sub,
  };
};

export const createClientCaseForTask = (params: Partial<Task>) => {
  const advisor = helpers.createAdviser();
  const clients = helpers.createClients();
  const goals = [
    helpers.createGoal({
      type: 5,
      name: 'Retirement',
      accounts: [
        helpers.createAccount(), // proposed
        helpers.createAccount({
          originalStatus: accountStatusToSelectOption(
            AccountStatusEnum.ActiveNonAdvised,
          ),
          status: accountStatusToSelectOption(
            AccountStatusEnum.ActiveNonAdvised,
          ),
        }),
      ],
    }),
    helpers.createGoal({ type: 10, name: 'Build Wealth' }),
  ];
  const subTasks = (params?.subTasks ?? [{ taskId: ++taskId } as SubTask]).map(
    ({ taskId, caseGoalId, goalId }) =>
      helpers.createSubTask({
        taskId,
        caseGoalId,
        goalId,
      }),
  );
  const tasks = [
    helpers.createTask({
      type: params?.type ?? TaskTypeEnum.Default,
      caseLevel: params?.caseLevel ?? !some(subTasks, 'goalId'),
      status: params?.status ?? new TaskStatus(TaskStatusEnum.ToDo),
      isModifiable: params?.isModifiable ?? true,
      advisor,
      subTasks,
    }),
  ];

  return helpers.createCase({
    relatedAdvisor: advisor,
    clients,
    goals,
    tasks,
  });
};
