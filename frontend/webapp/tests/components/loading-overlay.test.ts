import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import LoadingOverlay from '@aventur-shared/components/LoadingOverlay.vue';
import LoadingSpinner from '@aventur-shared/components/LoadingSpinner.vue';

describe('LoadingOverlay', () => {
  it('displays the loading spinner when isLoading is true', () => {
    const wrapper = mount(LoadingOverlay, {
      props: {
        isLoading: true,
      },
    });
    const spinner = wrapper.findComponent(LoadingSpinner);
    expect(spinner.exists()).toBe(true);
  });

  it('hides the loading spinner when isLoading is false', () => {
    const wrapper = mount(LoadingOverlay, {
      props: {
        isLoading: false,
      },
    });
    const spinner = wrapper.findComponent(LoadingSpinner);
    expect(spinner.exists()).toBe(false);
  });
});
