/* eslint-disable @typescript-eslint/ban-ts-comment */
import { ref } from 'vue';
import { mount, flushPromises } from '@vue/test-utils';
import { afterEach, describe, it, expect, vi } from 'vitest';
import { createTesting<PERSON>inia } from '@pinia/testing';
import { apiClient } from '@aventur-shared/services/api';
import ClientList from '@/modules/clients/ui/client-list.vue';
import { UserRole } from '@aventur-shared/modules/auth';

vi.mock('vue-router', async (importOriginal) => {
  const $router = await importOriginal();

  return {
    // @ts-ignore
    ...$router,
    useRouter: vi.fn,
    useRoute: vi.fn(() => {
      return {
        name: 'route',
      };
    }),
    onBeforeRouteUpdate: vi.fn,
    onBeforeRouteLeave: vi.fn,
  };
});

const initialUserId = 1;
vi.mock('@aventur-shared/utils/user', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    isAdvisor: vi.fn().mockReturnValue(Promise.resolve(true)),
  };
});

// Need to mock the table service
const filters = ref({ advisor_id: initialUserId });
vi.mock(
  '@aventur-shared/utils/table/use-table-service',
  async (importOriginal) => {
    const mod = await importOriginal();
    return {
      // @ts-ignore
      ...mod,
      useTableService: vi.fn().mockImplementation(() => {
        const apply = vi.fn().mockReturnValue(Promise.resolve());
        const addFilter = vi.fn().mockImplementation((newFilters) => {
          filters.value = {
            ...filters.value,
            ...newFilters,
          };
          return { apply };
        });
        const changePage = vi.fn((page) => {
          return { addFilter, apply };
        });
        const applyFilterRules = vi.fn();
        const resetFilters = vi.fn((resetFilters) => {
          filters.value = {
            advisor_id: initialUserId,
          };
          return { changePage, addFilter, apply };
        });

        return {
          changePage,
          addFilter,
          applyFilterRules,
          resetFilters,
          page: ref(1),
          filters: filters,
          setTotalItems: vi.fn(),
          config: { totalItems: 999 },
        };
      }),
    };
  },
);

function makeWrapper() {
  return mount(ClientList, {
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            user: {
              user: { id: initialUserId, groups: [UserRole.SuperAdmin] },
            },
            'filters-store': {
              filters: {
                route: [{ advisor_id: 2, type_id: 2 }],
              },
            },
          },
        }),
      ],
    },
  });
}

const mockJarvisGetTasksResponse = { clients: [], total_client_count: 0 };

vi.spyOn(apiClient, 'get').mockResolvedValue(mockJarvisGetTasksResponse);

afterEach(() => {
  filters.value = { advisor_id: initialUserId };
  vi.clearAllMocks();
});

describe('Filters panel', () => {
  it('Uses last saved filters params', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(3);
    expect(apiClient.get).toHaveBeenNthCalledWith(
      2,
      '/api/internal/v1/clients/',
      {
        advisor_id: 2,
        type_id: 2,
        page: 1,
        results_per_page: 10,
      },
    );

    vi.clearAllMocks();

    // Emit an event to simulate the filters reset
    //@ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:reset');
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenLastCalledWith(
      '/api/internal/v1/clients/',
      {
        page: 1,
        results_per_page: 10,
      },
    );
  });
});
