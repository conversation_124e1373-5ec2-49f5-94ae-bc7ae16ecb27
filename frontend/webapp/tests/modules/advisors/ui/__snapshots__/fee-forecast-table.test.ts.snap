// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Renders the expected columns and rows 1`] = `
"<div class="relative overflow-x-scroll" data-testid="fee-forecast-table">
  <table class="w-full text-left text-sm text-gray-900">
    <thead class="text-xs text-gray-500">
      <tr>
        <th scope="col" class="whitespace-nowrap px-8 py-3 text-sm font-normal text-gray-400 text-left">First</th>
        <th scope="col" class="whitespace-nowrap px-8 py-3 text-sm font-normal text-gray-400 text-right">Second</th>
        <th scope="col" class="whitespace-nowrap px-8 py-3 text-sm font-normal text-gray-400 text-right">Third</th>
      </tr>
    </thead>
    <tbody>
      <tr class="border-b last:border-none hover:bg-gray-50">
        <td class="px-8 py-4 text-left">1</td>
        <td class="px-8 py-4 text-right">2</td>
        <td class="px-8 py-4 text-right">3</td>
      </tr>
      <tr class="border-b last:border-none hover:bg-gray-50">
        <td class="px-8 py-4 text-left">4</td>
        <td class="px-8 py-4 text-right">5</td>
        <td class="px-8 py-4 text-right">6</td>
      </tr>
    </tbody>
  </table>
</div>"
`;
