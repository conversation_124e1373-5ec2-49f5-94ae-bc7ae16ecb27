import { expect, it, vi } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { apiClient } from '@aventur-shared/services/api';
import FeeForecastTable from '@modules/advisors/ui/fee-forecast-table.vue';

it('Renders the expected columns and rows', async () => {
  vi.spyOn(apiClient, 'get').mockResolvedValue({
    columns: ['First', 'Second', 'Third'],
    data: [
      [1, 2, 3],
      [4, 5, 6],
    ],
  });

  const wrapper = mount(FeeForecastTable);
  await flushPromises();

  const tableDiv = wrapper.find('[data-testid="fee-forecast-table"]');
  expect(tableDiv.html()).toMatchSnapshot();
  expect(apiClient.get).toHaveBeenCalledTimes(1);
});
