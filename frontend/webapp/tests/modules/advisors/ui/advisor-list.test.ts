/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { PageTitle } from '@modules/ui/text';
import AdvisorList from '@modules/advisors/ui/advisor-list.vue';
import { apiClient } from '@aventur-shared/services/api';
import { TableWithPagination } from '@modules/ui';
import { Button as CustomButton } from '@modules/ui';
import * as createActionMod from '@modules/advisors/use-cases/user-creates-advisor';
import * as updateActionMod from '@modules/advisors/use-cases/user-updates-advisor';
import { AdvisorRoleEnum } from '@aventur-shared/modules/advisors';

vi.spyOn(apiClient, 'get').mockResolvedValue({
  administrators: [],
  total_count: 0,
});
vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'put').mockResolvedValue(undefined);
const mockCreateAction = vi.spyOn(createActionMod, 'action');
const mockUpdateAction = vi.spyOn(updateActionMod, 'action');

vi.mock('vue-router', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    useRoute: vi.fn(() => {
      return {
        query: {
          page: 1,
        },
      };
    }),
    useRouter: vi.fn(() => {
      return {
        replace: vi.fn(),
      };
    }),
    onBeforeRouteLeave: vi.fn(),
    onBeforeRouteUpdate: vi.fn(),
  };
});

function makeWrapper() {
  return mount(AdvisorList, {
    stubs: ['router-link'],
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            advisors: {
              advisorsRoles: [
                {
                  value: 'adviser',
                  label: 'Adviser',
                },
              ],
            },
          },
        }),
      ],
    },
  });
}

describe('Advisor List', () => {
  it('displays proper children', async () => {
    const wrapper = makeWrapper();
    const title = wrapper.findComponent(PageTitle);
    const tableWithPaginationComp =
      wrapper.findAllComponents(TableWithPagination);
    const btn = wrapper.findComponent(CustomButton);

    expect(title.exists()).toBe(true);
    expect(title.text()).toBe('Administrators');
    expect(tableWithPaginationComp.length).toEqual(1);
    expect(btn.text()).toBe('Add new');
  });

  it('calls proper action/request on creating new advisor', async () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    await wrapper.vm.handleSaveAndClose('add', {
      email: '<EMAIL>',
      status: 'Active',
      firstName: 'test',
      lastName: 'test',
      reachedCompetentAdviserStatus: true,
      reachedCompetentAdviserStatusDate: '12/12/2022',
      roles: [AdvisorRoleEnum.SuperAdmin],
    });
    expect(mockCreateAction).toHaveBeenCalledTimes(1);
    expect(apiClient.post).toHaveBeenCalledTimes(1);
    expect(apiClient.post).toHaveBeenCalledWith('/api/v1/advisor', {
      email: '<EMAIL>',
      status: 'Active',
      first_name: 'test',
      last_name: 'test',
      reached_competent_adviser_status: true,
      reached_competent_adviser_date: '2022-12-12',
      roles: [AdvisorRoleEnum.SuperAdmin],
    });
  });

  it('calls proper action/request on updating existing advisor', async () => {
    const wrapper = makeWrapper();
    const fv = {
      id: 1,
      email: '<EMAIL>',
      status: 'Active',
      firstName: 'test',
      lastName: 'test',
      role: 'superadmin',
      reachedCompetentAdviserStatus: true,
      reachedCompetentAdviserStatusDate: '12/12/2022',
      roles: [AdvisorRoleEnum.SuperAdmin],
    };
    // @ts-ignore
    await wrapper.vm.handleSaveAndClose('edit', fv);
    expect(mockUpdateAction).toHaveBeenCalledTimes(1);
    expect(apiClient.put).toHaveBeenCalledTimes(1);
    expect(apiClient.put).toHaveBeenCalledWith(`/api/v1/advisor/${fv.id}`, {
      status: 'Active',
      first_name: 'test',
      last_name: 'test',
      reached_competent_adviser_status: true,
      reached_competent_adviser_date: '2022-12-12',
      roles: [AdvisorRoleEnum.SuperAdmin],
    });
  });
});
