/* eslint-disable @typescript-eslint/ban-ts-comment */
import { ref } from 'vue';
import { createTestingPinia } from '@pinia/testing';
import { flushPromises, mount } from '@vue/test-utils';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { directive as tippyDirective } from 'vue-tippy';

import { apiClient } from '@aventur-shared/services/api';
import { UserRole } from '@aventur-shared/modules/auth';

import TaskList from '@modules/tasks/ui/task-list.vue';
import FiltersPanel from '@modules/ui/filters-panel/filters-panel.vue';

vi.mock('vue-router', async (importOriginal) => {
  const $router = await importOriginal();

  return {
    // @ts-ignore
    ...$router,
    useRouter: vi.fn,
    useRoute: vi.fn(() => {
      return {
        name: undefined,
      };
    }),
    onBeforeRouteUpdate: vi.fn,
    onBeforeRouteLeave: vi.fn,
  };
});

const initialUserId = 1;
vi.mock('@aventur-shared/utils/user', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    isAdvisor: vi.fn().mockReturnValue(Promise.resolve(true)),
  };
});

// Need to mock the table service
const filters = ref({ assignee_id: initialUserId, include_undated: 'false' });
vi.mock(
  '@aventur-shared/utils/table/use-table-service',
  async (importOriginal) => {
    const mod = await importOriginal();
    return {
      // @ts-ignore
      ...mod,
      useTableService: vi.fn().mockImplementation(() => {
        const apply = vi.fn().mockReturnValue(Promise.resolve());
        const addFilter = vi.fn().mockImplementation((newFilters) => {
          filters.value = {
            ...filters.value,
            ...newFilters,
          };
          return { apply };
        });
        const changePage = vi.fn((page) => {
          return { addFilter, apply };
        });
        const applyFilterRules = vi.fn();
        const resetFilters = vi.fn((resetFilters) => {
          filters.value = {
            assignee_id: initialUserId,
            include_undated: 'false',
          };
          return { changePage, addFilter, apply };
        });

        return {
          changePage,
          addFilter,
          applyFilterRules,
          resetFilters,
          page: ref(1),
          filters: filters,
          setTotalItems: vi.fn(),
          config: { totalItems: 999 },
        };
      }),
    };
  },
);

function makeWrapper() {
  return mount(TaskList, {
    global: {
      directives: {
        tippy: tippyDirective,
      },
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            users: {
              user: { id: initialUserId, groups: [UserRole.Adviser] },
            },
          },
        }),
      ],
    },
  });
}

const mockJarvisGetTasksResponse = { data: [], count: 0 };

vi.spyOn(apiClient, 'get').mockResolvedValue(mockJarvisGetTasksResponse);

afterEach(() => {
  filters.value = { assignee_id: initialUserId, include_undated: 'false' };
  vi.clearAllMocks();
});

describe('Structure', () => {
  it('Renders the filters panel', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    const filtersPanelWrapper = wrapper.findComponent(FiltersPanel);
    expect(filtersPanelWrapper.exists()).toBe(true);

    await filtersPanelWrapper.findByTestId('filters-toggle')?.trigger('click');
    expect(filtersPanelWrapper.findByTestId('filters-apply')).not.toBeNull();
    expect(filtersPanelWrapper.findByTestId('filters-reset')).not.toBeNull();
    await filtersPanelWrapper.findByTestId('filters-apply').trigger('click');

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith('/api/v1/task/', {
      assignee_id: initialUserId,
      include_undated: 'false',
      page: '1',
    });
  });
});

describe('API call tests', () => {
  it('Makes the expected call on page load', async () => {
    makeWrapper();
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledWith('/api/v1/task/', {
      assignee_id: initialUserId,
      include_undated: 'false',
      page: '1',
    });
  });
  // This one behaves weirdly so skipping for now
  it.skip('Makes the expected call when an assignee group is selected', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // Emit an event to simulate the assignee group being selected
    //@ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:apply', {
      assignee_group: 'Some group name...',
      assignee_id: null, // fake filterRules
    });
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith('/api/v1/task/', {
      assignee_group: 'Some group name...',
      include_undated: 'false',
      page: '1',
    });
  });
  // This one behaves weirdly so skipping for now
  it.skip('Makes the expected call when an assignee is selected', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // Emit an event to simulate the assignee being selected
    //@ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:apply', {
      assignee_id: 999,
      assignee_group: null, // fake filterRules
    });
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith('/api/v1/task/', {
      assignee_id: 999,
      include_undated: 'false',
      page: '1',
    });
  });
  // This one behaves weirdly so skipping for now
  it.skip('Makes the expected call when task slugs are selected', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // Emit an event to simulate the assignee being selected
    //@ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:apply', {
      'slugs[]': ['slug1', 'slug2'],
    });
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith('/api/v1/task/', {
      assignee_id: 1,
      include_undated: 'false',
      'slugs[]': 'slug1,slug2',
      page: '1',
    });
  });
  it('Makes the expected call when the filters are reset', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // @ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:reset');
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith('/api/v1/task/', {
      assignee_id: initialUserId,
      include_undated: 'false',
      page: '1',
    });
  });
});
