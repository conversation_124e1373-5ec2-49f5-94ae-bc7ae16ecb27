import { expect, it, vi } from 'vitest';
import { VueWrapper, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { formatName } from '@aventur-shared/utils/user';
import { GoalId } from '@aventur-shared/modules/goals';
import { CaseClient } from '@aventur-shared/modules/cases';
import { ClientGoal } from '@aventur-shared/modules/clients/models';
import { ClientGoalAttributes } from '@aventur-shared/modules/clients/models/goal';

import { createClient } from '@tests/helpers';
import GoalItemObjectivesModal from '@modules/factfind/ui/goals/modals/goal-objectives-modal.vue';
//

vi.mock('@modules/factfind/use-cases/link-holdings');

// Mock ResizeObserver so the modal is able to be 'displayed' in the test
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

const client = createClient();

const getClientGoal = (params = {}): ClientGoal => ({
  id: 1 as GoalId,
  goalTypeId: 10,
  name: 'Goal 1',
  cases: [],
  clientIds: [],
  attributes: {} as ClientGoalAttributes,
  linkedHoldings: [],
  riskProfile: null,
  cashForecast: null,
  objectives: null,
  ...params,
});

const makeWrapper = (props: {
  readonly goal: ClientGoal;
  readonly client: CaseClient;
  readonly onGoalUpdated?:
    | ((id: GoalId, objectives: string) => any)
    | undefined;
}) =>
  mount(GoalItemObjectivesModal, {
    plugins: [
      createTestingPinia({
        createSpy: vi.fn,
      }),
    ],
    props,
  });

async function openModal(wrapper: VueWrapper) {
  await wrapper
    .getComponent('[data-testid="goal-item-objectives"]')
    .trigger('click');
}

it("can open modal and displays goal's name and client's name in the title", async () => {
  const wrapper = makeWrapper({
    goal: getClientGoal({
      name: '10K Emergency fund',
      clientIds: [client.id],
    }),
    client: client as CaseClient,
  });

  await openModal(wrapper);

  const modalWrapper = wrapper.getComponent(
    '[data-testid="goal-detail-modal"]',
  ) as VueWrapper;

  expect(modalWrapper.findElementByText('h1', '10K Emergency fund')).to.exist;
  expect(modalWrapper.findElementByText('dt', 'Goal Objectives')).to.exist;
  expect(modalWrapper.findElementByText('dd', formatName(client))).to.exist;
});
