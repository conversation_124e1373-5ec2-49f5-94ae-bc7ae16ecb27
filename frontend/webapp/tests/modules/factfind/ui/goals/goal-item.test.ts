import { expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { CaseId } from '@aventur-shared/modules/cases';
import { ClientId } from '@aventur-shared/modules/clients';
import { GoalId, WithdrawalType } from '@aventur-shared/modules/goals';
import {
  ClientGoal,
  ClientGoalAttributes,
} from '@aventur-shared/modules/clients/models/goal';

import { createClient } from '@tests/helpers';
import GoalItem from '@modules/factfind/ui/goals/GoalItem.vue';
//

// Mock ResizeObserver so the modal is able to be 'displayed' in the test
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

const getClientGoal = (params = {}): ClientGoal => ({
  id: 1 as GoalId,
  goalTypeId: 10,
  name: 'Goal 1',
  // description: 'Goal Description',
  cases: [1 as CaseId],
  clientIds: [1 as ClientId],
  attributes: {} as ClientGoalAttributes,
  linkedHoldings: [],
  riskProfile: null,
  cashForecast: null,
  objectives: null,
  ...params,
});

const makeWrapper = (goal) =>
  mount(GoalItem, {
    props: {
      client: createClient(),
      fieldIndex: 1,
      clientId: 1 as ClientId,
      goal: getClientGoal(goal),
      activeHoldings: [],
    },
    global: {
      provide: {
        group: {
          id: 1,
        },
      },
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });

it('Renders correct goal item markup', async () => {
  const goal = getClientGoal();
  const wrapper = makeWrapper(goal);

  expect(wrapper.findByTestId('goal-item-menu')).to.exist;
  expect(wrapper.findByTestId('goal-item-header')).to.exist;
  expect(wrapper.findByTestId('goal-item-header').text()).to.match(
    new RegExp(`${goal.name}`),
  );
  expect(wrapper.findByTestId('goal-item-header').text()).to.match(
    /Target amount:\s+N\/A/,
  );
  expect(wrapper.findByTestId('goal-item-header').text()).to.match(
    /Target date:\s+N\/A/,
  );
  expect(wrapper.findByTestId('goal-item-footer')).to.exist;
  expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
    /Clients:\s+(1)/,
  );
  expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
    /Open cases:\s+(1)/,
  );
  expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
    /Linked assets:\s+(0)/,
  );
  expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
    /Risk profile:\s+(-)/,
  );
  expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
    /Cash forecast:\s+(-)/,
  );
});

it('Renders correct enabled goal item markup - without objectives', async () => {
  const goal = getClientGoal();
  const wrapper = makeWrapper(goal);

  expect(wrapper.get('textarea').element.value).toBe('');
});

it('Renders correct enabled goal item markup - with objectives', async () => {
  const goal = getClientGoal({ objectives: 'Goal objectives' });
  const wrapper = makeWrapper(goal);

  expect(wrapper.get('textarea').element.value).toBe('Goal objectives');
});

it('Renders correct goal markup for goal with linked assets', async () => {
  const goal = getClientGoal({
    linkedHoldings: [
      {
        id: 1,
        productTypeName: 'Type 1',
        providerName: 'Provider 1',
        accountNumber: '123',
      },
      {
        id: 2,
        productTypeName: 'Type 2',
        providerName: 'Provider 2',
        accountNumber: '456',
      },
    ],
  });
  const wrapper = makeWrapper(goal);

  expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
    /Linked assets:\s+(2)/,
  );
});

it('Renders correct goal markup for goal with risk profile', async () => {
  const goal = getClientGoal({
    riskProfile: {
      recommended_risk: 5,
      risk_attitude: 5,
      loss_tolerance: 5,
      updated_at: new Date().toUTCString(),
      answers: [],
    },
  });
  const wrapper = makeWrapper(goal);

  expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
    /Risk profile:\s+(✓)/,
  );
});

it('Renders correct goal markup for goal with cash forecast', async () => {
  const goal = getClientGoal({
    cashForecast: {
      cashflows: [
        {
          type: WithdrawalType.FullWithdrawal,
          start: 70,
          end: null,
        },
      ],
      options: {},
      updated_at: new Date().toUTCString(),
    },
  });
  const wrapper = makeWrapper(goal);

  expect(wrapper.findByTestId('goal-item-footer').text()).to.match(
    /Cash forecast:\s+(✓)/,
  );
});

it('Opens goal item menu', async () => {
  const wrapper = makeWrapper(getClientGoal());

  await wrapper.findByTestId('goal-item-menu').trigger('click');
  expect(wrapper.findElementByText('p', 'Configure Goal')).to.exist;
  expect(wrapper.findElementByText('span[role=menuitem]', 'Edit goal')).to
    .exist;
  expect(wrapper.findElementByText('span[role=menuitem]', 'Link assets')).to
    .exist;
  expect(wrapper.findElementByText('span[role=menuitem]', 'Update objectives'))
    .to.exist;
  expect(wrapper.findElementByText('span[role=menuitem]', 'View risk profile'))
    .to.exist;
  expect(wrapper.findElementByText('span[role=menuitem]', 'View cash forecast'))
    .to.exist;
});
