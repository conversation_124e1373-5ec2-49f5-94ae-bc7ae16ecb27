/* eslint-disable @typescript-eslint/ban-ts-comment */
import { expect, it } from 'vitest';
import { mount } from '@vue/test-utils';
import { GoalLinkedHolding } from '@aventur-shared/modules/clients/models';
import AccountSelector from '@modules/factfind/ui/goals/account-selector.vue';

const holdings: GoalLinkedHolding[] = [
  {
    id: 1,
    productTypeName: 'ISA',
    providerName: 'Provider A',
    accountNumber: '123',
  },
  {
    id: 2,
    productTypeName: 'Fund',
    providerName: 'Provider B',
    accountNumber: '456',
  },
];

function makeWrapper() {
  return mount(AccountSelector, {
    props: { holdings },
  });
}

it('initializes with expected default data', () => {
  const wrapper = makeWrapper();

  // @ts-ignore
  expect(wrapper.vm.searchValue).toBe('');
  // @ts-ignore
  expect([...wrapper.vm.selectedHoldings]).toEqual([]);
});

it('filters options based on search input', async () => {
  const wrapper = makeWrapper();

  // @ts-ignore
  expect(wrapper.vm.getAssetOptions(holdings).length).toBe(2);
  // @ts-ignore
  wrapper.vm.searchValue = 'ISA';
  // @ts-ignore
  expect(wrapper.vm.getAssetOptions(holdings).length).toBe(1);
});

it('updates selectedHoldings on selection', async () => {
  const wrapper = makeWrapper();
  const holdingId = 1;
  // Simulate selection
  // @ts-ignore
  await wrapper.vm.handleSelection({ target: { id: holdingId.toString() } });
  // @ts-ignore
  expect(wrapper.vm.selectedHoldings.has(holdingId)).toBeTruthy();

  // Simulate deselection
  // @ts-ignore
  await wrapper.vm.handleSelection({ target: { id: holdingId.toString() } });
  // @ts-ignore
  expect(wrapper.vm.selectedHoldings.has(holdingId)).toBeFalsy();
});

it('emits holdingsSelected with correct payload on linking', async () => {
  const wrapper = makeWrapper();
  // @ts-ignore
  wrapper.vm.selectedHoldings.add(1);
  // @ts-ignore
  wrapper.vm.selectedHoldings.add(2);

  // @ts-ignore
  await wrapper.vm.handleLinkHoldings();
  expect(wrapper.emitted('holdingsSelected')).toBeTruthy();
  // @ts-ignore
  expect(wrapper.emitted('holdingsSelected')[0]).toEqual([[1, 2]]);
});

it('clears selections on cancel', async () => {
  const wrapper = makeWrapper();
  // @ts-ignore
  wrapper.vm.selectedHoldings.add(1);

  // @ts-ignore
  await wrapper.vm.cancelAddingAccounts();
  // @ts-ignore
  expect([...wrapper.vm.selectedHoldings]).toEqual([]);
});
