/* eslint-disable @typescript-eslint/ban-ts-comment */
import { expect, it, vi } from 'vitest';
import { VueWrapper, mount } from '@vue/test-utils';

import { GoalId } from '@aventur-shared/modules/goals';
import { formatName } from '@aventur-shared/utils/user';
import { ClientGoal } from '@aventur-shared/modules/clients/models';
import { ClientGoalAttributes } from '@aventur-shared/modules/clients/models/goal';

import { createClient } from '@tests/helpers';
import GoalItemLinkAssetsModal from '@modules/factfind/ui/goals/modals/goal-linked-assets-modal.vue';
//

vi.mock('@modules/factfind/use-cases/link-holdings');

// Mock ResizeObserver so the modal is able to be 'displayed' in the test
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

const client = createClient();

const getClientGoal = (params = {}): ClientGoal => ({
  id: 1 as GoalId,
  goalTypeId: 10,
  name: 'Goal 1',
  cases: [],
  clientIds: [],
  attributes: {} as ClientGoalAttributes,
  linkedHoldings: [],
  riskProfile: null,
  cashForecast: null,
  objectives: null,
  ...params,
});

async function openModal(wrapper: VueWrapper) {
  await wrapper
    .getComponent('[data-testid="goal-item-link-assets"]')
    .trigger('click');
}

it("can open modal and displays goal's name and client's name in the title", async () => {
  const wrapper = mount(GoalItemLinkAssetsModal, {
    props: {
      goal: getClientGoal({
        name: 'Save for a Vacation',
        clientIds: [client.id],
      }),
      holdings: [],
      client,
    },
  });

  await openModal(wrapper);

  const modalWrapper = wrapper.getComponent(
    '[data-testid="goal-detail-modal"]',
  ) as VueWrapper;

  expect(modalWrapper.findElementByText('h1', 'Save for a Vacation')).to.exist;
  expect(modalWrapper.findElementByText('dt', 'Related Assets')).to.exist;
  expect(modalWrapper.findElementByText('dd', formatName(client))).to.exist;
});

it('correctly separates available and selected holdings based on preSelectedHoldings', async () => {
  const holdings = [
    {
      id: 1,
      productTypeName: 'Type 1',
      providerName: 'Provider 1',
      accountNumber: '123',
    },
    {
      id: 2,
      productTypeName: 'Type 2',
      providerName: 'Provider 2',
      accountNumber: '456',
    },
  ];

  const wrapper = mount(GoalItemLinkAssetsModal, {
    props: {
      goal: getClientGoal({
        clientIds: [client.id],
      }),
      holdings: holdings,
      preSelectedHoldings: new Set([holdings[0].id]),
      client,
    },
  });

  // @ts-ignore
  expect(wrapper.vm.selectedHoldings).toEqual([holdings[0]]);
  // @ts-ignore
  expect(wrapper.vm.availableHoldings).toEqual([holdings[1]]);
  // @ts-ignore
  expect(wrapper.vm.allHoldings).toEqual({
    1: holdings[0],
    2: holdings[1],
  });
});

it('moves a holding from selected to available on "Unlink" click', async () => {
  const holdings = [
    {
      id: 1,
      productTypeName: 'Type 1',
      providerName: 'Provider 1',
      accountNumber: '123',
    },
  ];

  const wrapper = mount(GoalItemLinkAssetsModal, {
    props: {
      goal: getClientGoal({
        clientIds: [client.id],
      }),
      holdings: holdings,
      preSelectedHoldings: new Set([1]),
      client,
    },
  });

  await openModal(wrapper);

  await wrapper.getComponent('[data-testid="unlink-button"]').trigger('click');

  // @ts-ignore
  expect(wrapper.vm.selectedHoldings.length).toBe(0);
  // @ts-ignore
  expect(wrapper.vm.availableHoldings).toEqual(holdings);
});

it('moves a holding from available to selected on receipt of "holdings-selected" event', async () => {
  const holdings = [
    {
      id: 1,
      productTypeName: 'Type 1',
      providerName: 'Provider 1',
      accountNumber: '123',
    },
  ];

  const wrapper = mount(GoalItemLinkAssetsModal, {
    props: {
      goal: getClientGoal({
        clientIds: [client.id],
      }),
      holdings: holdings,
      client,
    },
  });

  await openModal(wrapper);

  // Emit the holdings selected event from the account-selector component
  await wrapper
    .findComponent({ name: 'AccountSelector' })
    .vm.$emit('holdingsSelected', [1]);

  // @ts-ignore
  expect(wrapper.vm.selectedHoldings).toEqual(holdings);
  // @ts-ignore
  expect(wrapper.vm.availableHoldings.length).toBe(0);
});

it('emits "goalUpdated" with the expected payload when the "Done" button is clicked', async () => {
  const holdings = [
    {
      id: 1,
      productTypeName: 'Type 1',
      providerName: 'Provider 1',
      accountNumber: '123',
    },
  ];

  const wrapper = mount(GoalItemLinkAssetsModal, {
    props: {
      goal: getClientGoal({
        clientIds: [client.id],
      }),
      holdings: holdings,
      preSelectedHoldings: new Set([1]),
      client,
    },
  });

  await openModal(wrapper);

  await wrapper.getComponent('[data-testid="done-button"]').trigger('click');

  const emitted = wrapper.emitted();
  expect(emitted).toHaveProperty('goalUpdated');
  expect(emitted['goalUpdated'][0]).toEqual([1, [1]]);
});
