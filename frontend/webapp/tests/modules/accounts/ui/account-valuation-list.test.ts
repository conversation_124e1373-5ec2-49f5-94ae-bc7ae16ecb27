/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { apiClient } from '@aventur-shared/services/api';
import AccountValuationList from '@/modules/accounts/ui/account-valuation-list.vue';
import { Button as CustomButton, TTable } from '@modules/ui';
import * as createActionMod from '@modules/accounts/use-cases/user-adds-valuation-to-an-account';
import { AccountValuationModal } from '@modules/accounts/ui/account-valuation';

vi.spyOn(apiClient, 'get').mockResolvedValue({
  valuations: [],
});
vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'put').mockResolvedValue(undefined);
const mockCreateAction = vi.spyOn(createActionMod, 'action');

vi.mock('vue-router', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    useRoute: vi.fn(() => {
      return {
        params: {
          accountId: 1,
        },
      };
    }),
  };
});

function makeWrapper() {
  return mount(AccountValuationList, {
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Valuation List', () => {
  it('displays proper children', async () => {
    const wrapper = makeWrapper();
    const modal = wrapper.findAllComponents(AccountValuationModal);
    const table = wrapper.findAllComponents(TTable);
    const btn = wrapper.findComponent(CustomButton);

    expect(modal.length).toEqual(1);
    expect(table.length).toEqual(1);
    expect(btn.text()).toBe('Add valuation');
  });

  it('calls proper action/request on creating new valuation', async () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    await wrapper.vm.handleCreateOrEditValuation({
      amount: 1,
      date: '2022-12-12',
      type: 'actual',
    });
    expect(mockCreateAction).toHaveBeenCalledTimes(1);
    expect(apiClient.post).toHaveBeenCalledTimes(1);
    expect(apiClient.post).toHaveBeenCalledWith(
      `/api/v1/holdings/1/valuation`,
      {
        amount: 1,
        date: '2022-12-12',
        is_actual: true,
      },
    );
  });
});
