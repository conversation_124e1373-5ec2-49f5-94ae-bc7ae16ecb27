export const mockPlanInfo = [
  {
    id: 1,
    dateOfInformation: '2022-12-21',
    notes: 'asdfasdf',
    productLayout: 'pension_layout',
    fundValue: '1000',
    transferValue: null,
    chargesFigures: {
      chargesPlanAMC: null,
      chargesFundAMC: '1',
      chargesDFMfee: null,
      additionalfeePAAmount: null,
      annualOverallChargePAPercentageFigures: null,
    },
    chargesProjections: {
      commencementDate: null,
      retirementDate: null,
      dateOfProjection: null,
      retirementDateOnProjection: null,
      rateOfProjection: null,
      projectionFigure: null,
      annualOverallChargePAPercentageProjections: null,
    },
    safeguardingBenefits: {
      guaranteedAnnuityRateGAR: true,
      guaranteedMinPensionGMP: true,
      isTFCGreaterThan25Percentage: true,
      anyLoyaltyBonus: true,
      protectedRetirementAge: true,
    },
    otherPensionInfo: {
      adviserRemunerationAllowable: true,
      availableFundsForSwitchOnFile: true,
      contributingHistoryOnFile: true,
      deathBenefitsOnFile: true,
      dischargeFormsOnFile: true,
      drawdownAvailable: true,
      lifeCover: true,
      nomineeFlexiAccessDrawdownAvailable: true,
      switchingStrategyOnFile: true,
      UFPLSAvailable: true,
      withProfits: true,
    },
    constituents: [
      {
        value: 600.0,
        fund: 'Aviva Inv Monthly Income Plus 2 Inc',
        fundCharge: 2,
        ISIN: 'GB00B7RBPR66',
        investedPercentage: 60,
        weightedCharge: 1.2,
        id: 2478,
      },
      {
        value: 400.0,
        fund: 'Cash',
        fundCharge: 1,
        ISIN: 'Cash',
        investedPercentage: 40,
        weightedCharge: 0.4,
        id: 2477,
      },
    ],
    portfolioModel: 1,
  },
];
