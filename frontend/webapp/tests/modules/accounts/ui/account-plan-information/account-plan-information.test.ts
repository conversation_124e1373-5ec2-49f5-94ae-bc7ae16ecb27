/* eslint-disable @typescript-eslint/ban-ts-comment */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import AccountPlanInformation from '@modules/accounts/ui/account-plan-information/account-plan-information.vue';
import AccountPlanInfoForm from '@modules/accounts/ui/account-plan-information/account-plan-info-form.vue';
import AccountPlanInfoPension from '@modules/accounts/ui/account-plan-information/account-plan-info-pension.vue';
import { mockPlanInfo } from './plan-info-mock';
import { SingleAccount } from '@aventur-shared/modules/accounts';
import * as apiCalls from '@aventur-shared/modules/accounts/api';

const mockGetPlanInformationList = vi
  .spyOn(apiCalls, 'getPlanInformationList')
  .mockResolvedValue(mockPlanInfo);

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => {
    return {
      params: {
        accountId: 1,
      },
    };
  }),
  onBeforeRouteLeave: vi.fn(),
}));

vi.mock('lodash', () => ({
  // Use require to avoid hoisting issues
  ...require('lodash'),
  debounce: (fn) => {
    return fn;
  },
  isFunction: vi.fn(),
}));

function makeWrapper() {
  return mount(AccountPlanInformation, {
    props: {
      account: { id: 1, productLayout: 'pension_layout' } as SingleAccount,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Account plan information', () => {
  // enableAutoUnmount(afterEach);
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('displays proper ui', async () => {
    const wrapper = makeWrapper();
    await flushPromises();
    // @ts-ignore
    expect(wrapper.vm.selectedPlanId, 'SelectPlanId is 1').toBe(1);
    expect(
      mockGetPlanInformationList,
      'getPlanInformationListCalled',
    ).toBeCalledTimes(1);
    expect(
      // @ts-ignore
      wrapper.vm.planInformation,
      'planInformation length check',
    ).toHaveLength(1);
    expect(
      wrapper.findAllComponents(AccountPlanInfoForm).length,
      'AccountPlanInfoForm',
    ).toBe(1);
    expect(
      wrapper.findAllComponents(AccountPlanInfoPension).length,
      'AccountPlanInfoPension',
    ).toBe(1);
    // @ts-ignore
  });

  it('handles plan change', async () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    expect(wrapper.vm.planInformationSelectOptions).toStrictEqual([]);
    await flushPromises();
    // @ts-ignore
    expect(wrapper.vm.planInformationSelectOptions).toStrictEqual([
      { value: 'NEW', label: 'New plan' },
      { value: 1, label: '21 December 2022' },
    ]);
    // @ts-ignore
    expect(wrapper.vm.selectedPlanId).toBe(1);
    const planSelectInput = wrapper.findByTestId('plan-select');
    planSelectInput.findAll('li').at(0)?.trigger('click');
    await flushPromises();
    // @ts-ignore
    expect(wrapper.vm.selectedPlanId).toBe('NEW');
  });
});
