/* eslint-disable @typescript-eslint/ban-ts-comment */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import * as apiCalls from '@aventur-shared/modules/accounts/api';
import AccountPlanInformation from '@modules/accounts/ui/account-plan-information/account-plan-information.vue';
import { mockPlanInfo } from './plan-info-mock';
import { SingleAccount } from '@aventur-shared/modules/accounts';

const mockGetPlanInformationList = vi
  .spyOn(apiCalls, 'getPlanInformationList')
  .mockResolvedValue(mockPlanInfo);

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => {
    return {
      params: {
        accountId: 1,
      },
    };
  }),
  onBeforeRouteLeave: vi.fn(),
}));

vi.mock('lodash', () => ({
  // Use require to avoid hoisting issues
  ...require('lodash'),
  debounce: (fn) => {
    return fn;
  },
}));

function makeWrapper() {
  return mount(AccountPlanInformation, {
    props: {
      account: { id: 1, productLayout: 'pension_layout' } as SingleAccount,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Investment Constituents', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('displays constituents correctly', async () => {
    const wrapper = makeWrapper();
    await flushPromises();
    expect(
      mockGetPlanInformationList,
      'getPlanInformationListCalled',
    ).toBeCalledTimes(1);
    expect(
      // @ts-ignore
      wrapper.vm.currentPlanInformation.constituents,
      'planInformation length check',
    ).toHaveLength(2);
    const constituentsValue = wrapper.find('[name="constituents[0].value"]');

    expect((constituentsValue.element as HTMLInputElement).value).toBe('600');

    const constituentsInvestedPercentage = wrapper.find(
      '[name="constituents[0].investedPercentage"]',
    );
    expect(
      (constituentsInvestedPercentage.element as HTMLInputElement).value,
    ).toBe('60');

    const fundValue = wrapper.find('[name="fundValue"]');

    expect((fundValue.element as HTMLInputElement).value).toBe('1000');

    await constituentsValue.setValue('400');

    expect((fundValue.element as HTMLInputElement).value).toBe('800');
  });
});

describe('Handle symbol pick tests', () => {
  const mockGetIsinFundCharge = vi.spyOn(apiCalls, 'getIsinFundCharge');

  afterEach(() => {
    mockGetIsinFundCharge.mockReset();
  });

  it('updates ISIN and fund name when symbol is picked', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // @ts-ignore
    wrapper.findByTestId('add-investment-constituents-row').trigger('click');
    await flushPromises();

    const symbol = {
      code: 'TEST123',
      name: 'Test Fund',
    };

    const constituentComponent = wrapper.findComponent({
      name: 'AccountPlanInfoInvestmentConstituents',
    });
    // @ts-ignore
    await constituentComponent.vm.handleSymbolPick(symbol, 0);

    // @ts-ignore
    expect(constituentComponent.vm.fields[0].value.ISIN).toBe(symbol.code);
    // @ts-ignore
    expect(constituentComponent.vm.fields[0].value.fund).toBe(symbol.name);
  });

  it('fetches and updates fund charge when symbol is picked', async () => {
    mockGetIsinFundCharge.mockResolvedValue('0.06');

    const wrapper = makeWrapper();
    await flushPromises();

    // @ts-ignore
    wrapper.findByTestId('add-investment-constituents-row').trigger('click');
    await flushPromises();

    const symbol = {
      code: 'TEST123',
      name: 'Test Fund',
    };
    const constituentComponent = wrapper.findComponent({
      name: 'AccountPlanInfoInvestmentConstituents',
    });
    // @ts-ignore
    await constituentComponent.vm.handleSymbolPick(symbol, 0);

    expect(mockGetIsinFundCharge).toHaveBeenCalledWith(symbol.code);
    expect(
      constituentComponent.find('[name="constituents[0].fundCharge"]').element // @ts-ignore
        .value,
    ).toBe('0.06');
  });
});
