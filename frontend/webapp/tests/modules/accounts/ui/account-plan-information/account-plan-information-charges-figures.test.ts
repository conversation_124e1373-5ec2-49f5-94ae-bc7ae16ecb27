/* eslint-disable @typescript-eslint/ban-ts-comment */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import * as apiCalls from '@aventur-shared/modules/accounts/api';
import { nextTick } from 'vue';
import AccountPlanInformation from '@modules/accounts/ui/account-plan-information/account-plan-information.vue';
import { mockPlanInfo } from './plan-info-mock';
import { SingleAccount } from '@aventur-shared/modules/accounts';

vi.spyOn(apiCalls, 'getPlanInformationList').mockResolvedValue(mockPlanInfo);

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => {
    return {
      params: {
        accountId: 1,
      },
    };
  }),
  onBeforeRouteLeave: vi.fn(),
}));

vi.mock('lodash', () => ({
  // Use require to avoid hoisting issues
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  ...require('lodash'),
  debounce: (fn) => {
    return fn;
  },
}));

function makeWrapper() {
  return mount(AccountPlanInformation, {
    props: {
      account: { id: 1, productLayout: 'pension_layout' } as SingleAccount,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Charges Figures', () => {
  // enableAutoUnmount(afterEach);
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('calculates annualOverallChargePAPercentageFigures', async () => {
    const mockGetAnnualOverallChargeUsingFigures = vi
      .spyOn(apiCalls, 'getAnnualOverallChargeUsingFigures')
      .mockResolvedValue('102.00');

    const wrapper = makeWrapper();
    await flushPromises();
    const annualOverallChargePAPercentageFigures = wrapper.find(
      '[name="chargesFigures.annualOverallChargePAPercentageFigures"]',
    );
    const chargesPlanAMCInput = wrapper.find(
      '[name="chargesFigures.chargesPlanAMC"]',
    );
    const chargesFundAMCInput = wrapper.find(
      '[name="chargesFigures.chargesFundAMC"]',
    );
    const chargesDFMfeeInput = wrapper.find(
      '[name="chargesFigures.chargesDFMfee"]',
    );
    const additionalfeePAAmountInput = wrapper.find(
      '[name="chargesFigures.additionalfeePAAmount"]',
    );
    const fundValueInput = wrapper.find('[name="fundValue"]');

    await fundValueInput.setValue('1');
    await chargesPlanAMCInput.setValue('1');
    await chargesFundAMCInput.setValue('1');
    await chargesDFMfeeInput.setValue('1');
    await additionalfeePAAmountInput.setValue('1');

    await nextTick();

    await flushPromises();

    expect(
      mockGetAnnualOverallChargeUsingFigures,
      'getAnnualOverallChargeUsingFiguresCalled',
    ).toBeCalledTimes(1);

    // @ts-ignore
    expect(annualOverallChargePAPercentageFigures.element.value).toBe('102.00');
  });
});
