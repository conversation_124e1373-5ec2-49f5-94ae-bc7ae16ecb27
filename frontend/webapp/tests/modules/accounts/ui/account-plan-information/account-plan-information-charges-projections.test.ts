/* eslint-disable @typescript-eslint/ban-ts-comment */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import * as apiCalls from '@aventur-shared/modules/accounts/api';
import { nextTick } from 'vue';
import AccountPlanInformation from '@modules/accounts/ui/account-plan-information/account-plan-information.vue';
import { mockPlanInfo } from './plan-info-mock';
import { SingleAccount } from '@aventur-shared/modules/accounts';

vi.spyOn(apiCalls, 'getPlanInformationList').mockResolvedValue(mockPlanInfo);

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => {
    return {
      params: {
        accountId: 1,
      },
    };
  }),
  onBeforeRouteLeave: vi.fn(),
}));

vi.mock('lodash', () => ({
  // Use require to avoid hoisting issues
  ...require('lodash'),
  debounce: (fn) => {
    return fn;
  },
}));

function makeWrapper() {
  return mount(AccountPlanInformation, {
    props: {
      account: { id: 1, productLayout: 'pension_layout' } as SingleAccount,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Charges Projections', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('calculates annualOverallChargePAPercentageProjections', async () => {
    const mockGetAnnualOverallChargeUsingProjections = vi
      .spyOn(apiCalls, 'getAnnualOverallChargeUsingProjections')
      .mockResolvedValue('1.00');
    const wrapper = makeWrapper();
    await flushPromises();
    const annualOverallChargePAPercentageProjections = wrapper.find(
      '[name="chargesProjections.annualOverallChargePAPercentageProjections"]',
    );
    const retirementDateOnProjectionInput = wrapper.find(
      '[name="chargesProjections.retirementDateOnProjection"]',
    );
    const dateOfProjectionInput = wrapper.find(
      '[name="chargesProjections.dateOfProjection"]',
    );
    const projectionFigureInput = wrapper.find(
      '[name="chargesProjections.projectionFigure"]',
    );
    const fundValueInput = wrapper.find('[name="fundValue"]');
    const rateOfProjectionInput = wrapper.find(
      '[name="chargesProjections.rateOfProjection"]',
    );

    await fundValueInput.setValue('1');
    await projectionFigureInput.setValue('1');
    await retirementDateOnProjectionInput.setValue('2023-12-12');
    await dateOfProjectionInput.setValue('2023-12-01');
    await rateOfProjectionInput.setValue('1');
    await nextTick();
    await flushPromises();
    expect(
      mockGetAnnualOverallChargeUsingProjections,
      'getAnnualOverallChargeUsingProjectionsCalled',
    ).toBeCalledTimes(1);

    expect(
      (<HTMLInputElement>annualOverallChargePAPercentageProjections.element)
        .value,
    ).toBe('1.00');
  });
});
