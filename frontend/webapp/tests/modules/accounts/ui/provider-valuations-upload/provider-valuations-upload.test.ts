import { shallowMount } from '@vue/test-utils';
import { describe, afterEach, it, expect, vi } from 'vitest';
import { ProviderValuationsUpload } from '@/modules/accounts/ui/provider-valuations-upload';
import { apiClient } from '@aventur-shared/services/api';

const mockPost = vi.spyOn(apiClient, 'post');

const mockToastError = vi.fn();
const mockToastSuccess = vi.fn();
vi.mock('@aventur-shared/composables/useToast', () => ({
  useToast: () => ({
    info: vi.fn(),
    error: mockToastError,
    success: mockToastSuccess,
  }),
}));

describe('ProviderValuationsUpload.vue', () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  it('calls handleFileUpload correctly', async () => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    mockPost.mockReturnValue(10);
    const wrapper = shallowMount(ProviderValuationsUpload);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    await wrapper.vm.handleFileUpload({
      providerId: 1,
      valuationDate: new Date(),
      file: new File([], 'test.xlsx'),
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.unmatchedRows).toBeNull();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.missingValuations).toBeNull();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.rowCount).toBe(10);
    expect(mockToastSuccess).toHaveBeenCalledOnce();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.getComponent('[data-testid="success-alert"]').exists()).toBe(
      true,
    );
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.isLoading).toBe(false);
    expect(mockToastSuccess).toHaveBeenCalledOnce();
  });

  it('shows error alerts when upload fails with validation errors', async () => {
    const errorResponse = {
      response: {
        status: 400,
        data: {
          holding_match_errors: { columns: ['A', 'B'], data: [['1', '2']] },
          active_account_check_errors: {
            columns: ['X', 'Y'],
            data: [['3', '4']],
          },
          number_validated: 5,
        },
      },
    };
    mockPost.mockRejectedValueOnce(errorResponse);
    const wrapper = shallowMount(ProviderValuationsUpload);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    await wrapper.vm.action(1, new Date(), new File([], 'test.xlsx'));
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.unmatchedRows).toEqual(
      errorResponse.response.data.holding_match_errors,
    );
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.missingValuations).toEqual(
      errorResponse.response.data.active_account_check_errors,
    );
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.rowCount).toBe(5);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.getComponent('[data-testid="warning-alert"]').exists()).toBe(
      true,
    );
    expect(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      wrapper.getComponent('[data-testid="unmatched-holdings-alert"]').exists(),
    ).toBe(true);
    expect(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      wrapper.getComponent('[data-testid="missing-valuations-alert"]').exists(),
    ).toBe(true);
    expect(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      wrapper.getComponent('[data-testid="row-count-alert"]').exists(),
    ).toBe(true);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.isLoading).toBe(false);
    expect(mockToastError).toHaveBeenCalledOnce();
  });

  it('shows toast error when upload fails due to formatting validation error', async () => {
    const errorResponse = {
      response: {
        status: 422,
        data: {
          detail: 'Spreadsheet has missing values',
        },
      },
    };
    mockPost.mockRejectedValueOnce(errorResponse);
    const wrapper = shallowMount(ProviderValuationsUpload);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    await wrapper.vm.action(1, new Date(), new File([], 'test.xlsx'));
    expect(mockToastError).toHaveBeenCalledWith(errorResponse);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.isLoading).toBe(false);
  });
});
