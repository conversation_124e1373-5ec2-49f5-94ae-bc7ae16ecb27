import { mount, shallowMount } from '@vue/test-utils';
import { describe, afterEach, beforeEach, it, expect, vi } from 'vitest';
import { UploadForm } from '@/modules/accounts/ui/provider-valuations-upload';

const mocks = vi.hoisted(() => {
  return {
    useFormMock: vi.fn(),
    useIsFormValidMock: vi.fn(),
    storeToRefsMock: vi.fn(),
  };
});

vi.mock('vee-validate', async () => {
  const actualVeeValidate =
    await vi.importActual<typeof import('vee-validate')>('vee-validate');
  return {
    ...actualVeeValidate,
    useForm: mocks.useFormMock,
    useIsFormValid: mocks.useIsFormValidMock,
  };
});

vi.mock('pinia', async () => {
  const actualPinia = await vi.importActual<typeof import('pinia')>('pinia');
  return {
    ...actualPinia,
    storeToRefs: mocks.storeToRefsMock,
  };
});

vi.mock('@aventur-shared/stores', () => ({
  useRefData: vi.fn(),
}));

describe('UploadForm.vue', () => {
  const mockSubmit = vi.fn();
  const mockSetFieldValue = vi.fn();

  const providersMock = [
    { id: 1, name: 'Provider One' },
    { id: 2, name: 'Provider Two' },
  ];

  const formValues = {
    providerId: null,
    valuationDate: null,
    file: null,
  };

  beforeEach(() => {
    mocks.storeToRefsMock.mockReturnValue({ getProviders: providersMock });

    mocks.useFormMock.mockReturnValue({
      handleSubmit: (fn: any) => fn(formValues),
      setFieldValue: mockSetFieldValue,
    });

    mocks.useIsFormValidMock.mockReturnValue({ value: true });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('disables inputs when isLoading is true', () => {
    const wrapper = mount(UploadForm, {
      propsData: { isLoading: true },
    });

    // Check if the select field and date picker are disabled
    expect(
      wrapper.findComponent({ name: 'SelectField' }).props('disabled'),
    ).toBe(true);
    expect(
      wrapper.findComponent({ name: 'DatePicker' }).props('disabled'),
    ).toBe(true);

    // Check if the submit button is disabled
    const submitButton = wrapper.getComponent('[data-testid="submit-button"]');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(submitButton.props('disabled')).toBe(true);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(submitButton.props('isBusy')).toBe(true);
  });

  it('handles file upload correctly', async () => {
    const wrapper = shallowMount(UploadForm);
    const file = new File(['test'], 'test-file.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const fileInput = wrapper.findComponent({ name: 'FileField' });

    await fileInput.vm.$emit('on-file-upload', [file]);

    expect(mockSetFieldValue).toHaveBeenCalledWith('file', file);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    expect(wrapper.vm.selectedFile).toBe('test-file.xlsx');
  });

  it('validates and submits the form', async () => {
    const wrapper = shallowMount(UploadForm, {
      propsData: { isLoading: false },
      attrs: { onFileSubmitted: mockSubmit },
    });

    const submitButton = wrapper.find('form');
    await submitButton.trigger('submit');

    expect(mockSubmit).toHaveBeenCalledWith(formValues);
  });

  it('does not submit if form is invalid', async () => {
    mocks.useIsFormValidMock.mockReturnValue({ value: false });

    const wrapper = shallowMount(UploadForm, {
      propsData: { isLoading: false },
    });

    const submitButton = wrapper.find('form');
    await submitButton.trigger('submit');

    expect(mockSubmit).not.toHaveBeenCalled();
  });
});
