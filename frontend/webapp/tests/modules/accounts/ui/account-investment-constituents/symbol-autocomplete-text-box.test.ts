import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import SymbolAutocompleteTextBox from '@/modules/accounts/ui/account-plan-information/form/symbol-autocomplete-text-box.vue';
import { nextTick } from 'vue';
import {
  getSymbolCodeCompletions,
  getSymbolNameCompletions,
} from '@aventur-shared/modules/accounts';

// Mock the API calls
vi.mock('@aventur-shared/modules/accounts', () => ({
  getSymbolCodeCompletions: vi.fn(),
  getSymbolNameCompletions: vi.fn(),
}));

describe('SymbolAutocompleteTextBox', () => {
  const defaultProps = {
    name: 'symbol',
    label: 'Symbol',
    modelValue: '',
    isIsinField: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('input').exists()).toBe(true);
  });

  it('emits update:modelValue when input changes', async () => {
    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });

    const input = wrapper.find('input');
    await input.setValue('TEST');

    expect(wrapper.emitted('update:modelValue')).toBeTruthy();
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['TEST']);
  });

  it('shows dropdown with filtered options when input length >= 3', async () => {
    const mockSymbols = [
      { code: 'TEST1', name: 'Test Stock 1' },
      { code: 'TEST2', name: 'Test Stock 2' },
    ];

    (getSymbolNameCompletions as any).mockResolvedValue(mockSymbols);

    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });

    const input = wrapper.find('input');
    await input.setValue('TEST');

    // Wait for debounce
    await new Promise((resolve) => setTimeout(resolve, 500));
    await nextTick();

    expect(wrapper.find('#autocomplete-dropdown').exists()).toBe(true);
    expect(wrapper.findAll('li')).toHaveLength(2);
  });

  it('does not show dropdown when input length < 3', async () => {
    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });

    const input = wrapper.find('input');
    await input.setValue('TE');

    await nextTick();

    expect(wrapper.find('#autocomplete-dropdown').exists()).toBe(false);
  });

  it('calls correct API based on isIsinField prop', async () => {
    const mockSymbols = [
      { code: 'TEST1', name: 'Test Stock 1' },
      { code: 'TEST2', name: 'Test Stock 2' },
    ];

    (getSymbolCodeCompletions as any).mockResolvedValue(mockSymbols);

    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: {
        ...defaultProps,
        isIsinField: true,
      },
    });

    const input = wrapper.find('input');
    await input.setValue('TEST');

    // Wait for debounce
    await new Promise((resolve) => setTimeout(resolve, 500));

    expect(getSymbolCodeCompletions).toHaveBeenCalled();
    expect(getSymbolNameCompletions).not.toHaveBeenCalled();
  });

  it('emits on-symbol-pick when option is selected', async () => {
    const mockSymbols = [
      { code: 'TEST1', name: 'Test Stock 1' },
      { code: 'TEST2', name: 'Test Stock 2' },
    ];

    (getSymbolNameCompletions as any).mockResolvedValue(mockSymbols);

    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });

    const input = wrapper.find('input');
    await input.setValue('TEST');

    // Wait for debounce
    await new Promise((resolve) => setTimeout(resolve, 500));
    await nextTick();

    const firstOption = wrapper.findAll('li')[0];
    await firstOption.trigger('click');

    expect(wrapper.emitted('on-symbol-pick')).toBeTruthy();
    expect(wrapper.emitted('on-symbol-pick')?.[0]).toEqual([
      { code: 'TEST1', name: 'Test Stock 1' },
    ]);
  });

  it('handles keyboard navigation correctly', async () => {
    const mockSymbols = [
      { code: 'TEST1', name: 'Test Stock 1' },
      { code: 'TEST2', name: 'Test Stock 2' },
    ];

    (getSymbolNameCompletions as any).mockResolvedValue(mockSymbols);

    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });

    const input = wrapper.find('input');
    await input.setValue('TEST');

    // Wait for debounce
    await new Promise((resolve) => setTimeout(resolve, 500));
    await nextTick();

    // Press down arrow
    await input.trigger('keydown', { key: 'ArrowDown' });
    await nextTick();

    // Check first item is highlighted
    expect(wrapper.findAll('li')[0].classes()).toContain('bg-gray-100');

    // Press enter to select
    await input.trigger('keydown', { key: 'Enter' });
    await nextTick();

    expect(wrapper.emitted('on-symbol-pick')).toBeTruthy();
    expect(wrapper.emitted('on-symbol-pick')?.[0]).toEqual([
      { code: 'TEST1', name: 'Test Stock 1' },
    ]);
  });

  it('closes dropdown when clicking outside', async () => {
    const mockSymbols = [{ code: 'TEST1', name: 'Test Stock 1' }];

    (getSymbolNameCompletions as any).mockResolvedValue(mockSymbols);

    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });

    const input = wrapper.find('input');
    await input.setValue('TEST');

    // Wait for debounce
    await new Promise((resolve) => setTimeout(resolve, 500));
    await nextTick();

    expect(wrapper.find('#autocomplete-dropdown').exists()).toBe(true);

    // Simulate click outside
    const clickEvent = new Event('click');
    document.dispatchEvent(clickEvent);
    await nextTick();

    expect(wrapper.find('#autocomplete-dropdown').exists()).toBe(false);
  });

  it('closes dropdown when pressing ESC key', async () => {
    const mockSymbols = [
      { code: 'TEST1', name: 'Test Stock 1' },
      { code: 'TEST2', name: 'Test Stock 2' },
    ];

    (getSymbolNameCompletions as any).mockResolvedValue(mockSymbols);

    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });

    // Open the dropdown
    const input = wrapper.find('input');
    await input.setValue('TEST');

    // Wait for debounce
    await new Promise((resolve) => setTimeout(resolve, 500));
    await nextTick();

    // Verify dropdown is open
    expect(wrapper.find('#autocomplete-dropdown').exists()).toBe(true);

    // Press ESC key
    await input.trigger('keydown', { key: 'Escape' });
    await nextTick();

    // Verify dropdown is closed
    expect(wrapper.find('#autocomplete-dropdown').exists()).toBe(false);
  });

  it('handles zero additional options correctly', async () => {
    const mockSymbols = [
      { code: 'TEST1', name: 'Test Stock 1' },
      { code: 'TEST2', name: 'Test Stock 2' },
    ];

    (getSymbolNameCompletions as any).mockResolvedValue(mockSymbols);

    const wrapper = mount(SymbolAutocompleteTextBox, {
      props: defaultProps,
    });

    const input = wrapper.find('input');
    await input.setValue('TEST');

    // Wait for debounce
    await new Promise((resolve) => setTimeout(resolve, 500));
    await nextTick();

    expect(
      wrapper.find('[data-testid="additional-options-count"]').exists(),
    ).toBe(false);
  });
});
