/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { directive as tippyDirective } from 'vue-tippy';

import {
  createAdviser,
  createCase,
  createClients,
  createGoal,
  createTask,
} from '@tests/helpers';

import { apiClient } from '@aventur-shared/services/api';

import { Case, CaseId, CaseStatusEnum } from '@aventur-shared/modules/cases';
import { Task, TaskStatus } from '@aventur-shared/modules/tasks';
import { TaskStatusEnum } from '@aventur-shared/modules/tasks/models/task-status';

import { CaseType } from '@aventur-shared/modules/cases/models';
import { SelectField } from '@aventur-shared/components/form';

import { canBeClosed } from '@aventur-shared/modules/cases/models/case';
import { default as CaseOverview } from '@modules/clients-cases/ui/case-overview/case-overview.vue';
import { default as CaseOverviewReadonly } from '@modules/clients-cases/ui/case-overview/case-overview-readonly.vue';
import { ChangeReviewSlotForm } from '@modules/clients-cases/use-cases/user-changes-review-slot';
import * as getClientAvailableReviewSlotsApiCalls from '@aventur-shared/modules/upcoming-reviews/api';
import * as updateReviewSlotAction from '@modules/clients-cases/use-cases/user-changes-review-slot/action';
import { ChangeAdvisorForm } from '@/modules/clients-cases/use-cases/user-changes-advisor-for-case';
import * as getAdvisorsApiCalls from '@aventur-shared/modules/advisors/api';
import { AdvisorRoleEnum } from '@aventur-shared/modules/advisors';
import * as updateAdviserApiCalls from '@modules/clients-cases/use-cases/user-changes-advisor-for-case';
import * as updateCaseManagerApiCalls from '@modules/clients-cases/use-cases/user-changes-manager-for-case';

const updateReviewSlotActionMock = vi
  .spyOn(updateReviewSlotAction, 'changeReviewSlotForCaseAction')
  .mockResolvedValue();

vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'get').mockResolvedValue([]);

vi.spyOn(
  getClientAvailableReviewSlotsApiCalls,
  'getCaseAvailableReviewSlots',
).mockResolvedValue({
  availableReviewSlots: [{ id: 1, description: 'December 2024' }],
  hasReviewSlotsOutsideOfMaxAssignablePeriod: false,
});

vi.spyOn(getAdvisorsApiCalls, 'getAdvisors').mockResolvedValue({
  totalItems: 1,
  items: [
    {
      ...createAdviser(),
      email: '<EMAIL>',
      roles: [AdvisorRoleEnum.Adviser],
      status: 'Active',
      reachedCompetentAdviserStatus: false,
      reachedCompetentAdviserStatusDate: null,
    },
  ],
});

const updateCaseAdvisorActionMock = vi
  .spyOn(updateAdviserApiCalls, 'changeAdvisorForCaseAction')
  .mockResolvedValue(undefined);

const updateCaseManagerActionMock = vi
  .spyOn(updateCaseManagerApiCalls, 'changeManagerForCaseAction')
  .mockResolvedValue(undefined);

type propsType = {
  caseId: CaseId;
  clientCase: Case;
};

const caseId = 1 as CaseId;
const goals = [createGoal()];

vi.mock('vue-router', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    useRoute: vi.fn(() => {
      return {
        params: {
          id: caseId,
        },
      };
    }),
  };
});

function makeWrapper(component: any, propsData: propsType) {
  return mount(component, {
    props: propsData,
    global: {
      directives: {
        tippy: tippyDirective,
      },
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            client: {},
          },
        }),
      ],
    },
  });
}

describe('Client Case Overview', () => {
  describe('Structure', () => {
    it('should render correct markup for open cases', async () => {
      let tasks: Task[];
      let data: propsType;

      tasks = [
        createTask({
          status: new TaskStatus(TaskStatusEnum.NotApplicable),
        }),
        createTask(),
      ];
      data = {
        caseId,
        clientCase: {
          id: caseId,
          name: 'Case1',
          status: CaseStatusEnum.Open,
          relatedAdvisor: createAdviser(),
          relatedManager: null,
          clients: createClients(),
          goals,
          tasks,
          type: new CaseType(1),
          createdOn: new Date(),
          completedOn: new Date(),
          daysToComplete: 0,
          reviewSlot: null,
        },
      };

      const wrapper = makeWrapper(CaseOverview, data);

      let btnCompleteCase;

      // @ts-ignore
      expect(canBeClosed(wrapper.props('clientCase').goals)).toBeFalsy();

      btnCompleteCase = wrapper.findByTestId('complete-case');
      expect(btnCompleteCase).not.toBeUndefined();
      expect(btnCompleteCase.element.disabled).toBe(true);

      tasks = [
        createTask({
          status: new TaskStatus(TaskStatusEnum.NotApplicable),
        }),
        createTask({
          status: new TaskStatus(TaskStatusEnum.Canceled),
        }),
      ];
      data = {
        caseId,
        clientCase: createCase({ goals, tasks }),
      };
      await wrapper.setProps(data);
      await flushPromises();

      // @ts-ignore
      expect(canBeClosed(wrapper.props('clientCase').tasks)).toBeTruthy();
      btnCompleteCase = wrapper.findByTestId('complete-case');
      expect(btnCompleteCase).not.toBeUndefined();
      expect(btnCompleteCase.element.disabled).toBe(false);
    });

    it('should render correct markup for completed cases', async () => {
      const tasks = [
        createTask({
          status: new TaskStatus(TaskStatusEnum.Completed),
        }),
        createTask({
          status: new TaskStatus(TaskStatusEnum.NotApplicable),
        }),
      ];
      const data: propsType = {
        caseId,
        clientCase: {
          id: caseId,
          name: 'Case1',
          status: CaseStatusEnum.Completed,
          relatedAdvisor: createAdviser(),
          relatedManager: null,
          clients: createClients(),
          goals,
          tasks,
          type: new CaseType(1),
          createdOn: new Date(),
          completedOn: new Date(),
          daysToComplete: 0,
          reviewSlot: null,
        },
      };

      const wrapper = makeWrapper(CaseOverviewReadonly, data);
      expect(wrapper.findByTestId('complete-case')).toBeNull();
    });
  });
  describe('Behaviour', () => {
    it('should open review slot update form', async () => {
      const data = {
        caseId,
        clientCase: createCase(),
      };

      const wrapper = makeWrapper(CaseOverview, data);
      expect(wrapper.findComponent(ChangeReviewSlotForm).exists()).toBe(false);
      wrapper.findByTestId('openReviewSlotFormHandler').trigger('click');
      await flushPromises();
      expect(wrapper.findComponent(ChangeReviewSlotForm).exists()).toBe(true);
    });

    it('should display available review slots in dropdown', async () => {
      const tasks = [createTask()];
      const data = {
        caseId,
        clientCase: {
          id: caseId,
          name: 'Case1',
          status: CaseStatusEnum.Open,
          relatedAdvisor: createAdviser(),
          relatedManager: null,
          clients: createClients(),
          goals,
          tasks,
          type: new CaseType(1),
          createdOn: new Date(),
          completedOn: new Date(),
          daysToComplete: 0,
          reviewSlot: null,
        },
      };

      const wrapper = makeWrapper(CaseOverview, data);
      wrapper.findByTestId('openReviewSlotFormHandler').trigger('click');
      await flushPromises();
      const reviewSlotSelect = wrapper
        .findComponent(ChangeReviewSlotForm)
        .findComponent(SelectField);
      expect(reviewSlotSelect.findAll('li').length).toBe(1);
    });

    it('should update review slot', async () => {
      const tasks = [createTask()];
      const data = {
        caseId,
        clientCase: {
          id: caseId,
          name: 'Case1',
          status: CaseStatusEnum.Open,
          relatedAdvisor: createAdviser(),
          relatedManager: null,
          clients: createClients(),
          goals,
          tasks,
          type: new CaseType(1),
          createdOn: new Date(),
          completedOn: new Date(),
          daysToComplete: 0,
          reviewSlot: null,
        },
      };

      const wrapper = makeWrapper(CaseOverview, data);
      expect(wrapper.findByTestId('openReviewSlotFormHandler').text()).toBe(
        'Not set',
      );
      wrapper.findByTestId('openReviewSlotFormHandler').trigger('click');
      await flushPromises();
      const changeReviewSlotForm = wrapper.findComponent(ChangeReviewSlotForm);
      changeReviewSlotForm.vm.$emit('on-review-slot-change', {
        id: 1,
        description: 'December 2024',
      });
      await flushPromises();
      expect(updateReviewSlotActionMock).toHaveBeenCalledTimes(1);
      expect(updateReviewSlotActionMock).toHaveBeenCalledWith({
        reviewSlotId: 1,
        caseId: data.caseId,
      });
      expect(wrapper.findByTestId('openReviewSlotFormHandler').text()).toBe(
        'December 2024',
      );
    });

    it('should display warning message for Annual Review case when there are no available slots', async () => {
      vi.clearAllMocks();
      vi.spyOn(
        getClientAvailableReviewSlotsApiCalls,
        'getCaseAvailableReviewSlots',
      ).mockResolvedValue({
        availableReviewSlots: [],
        hasReviewSlotsOutsideOfMaxAssignablePeriod: true,
      });
      const tasks = [createTask()];
      const data = {
        caseId,
        clientCase: {
          id: caseId,
          name: 'Case1',
          status: CaseStatusEnum.Open,
          relatedAdvisor: createAdviser(),
          relatedManager: null,
          clients: createClients(),
          goals,
          tasks,
          type: new CaseType(2),
          createdOn: new Date(),
          completedOn: new Date(),
          daysToComplete: 0,
          reviewSlot: null,
        },
      };
      const wrapper = makeWrapper(CaseOverview, data);
      await flushPromises();
      expect(wrapper.findByTestId('reviewSlotWarningMessage').text()).toBe(
        'No available review slots',
      );
    });

    it('should not display warning message for non Annual Review case when there are no available slots', async () => {
      vi.clearAllMocks();
      vi.spyOn(
        getClientAvailableReviewSlotsApiCalls,
        'getCaseAvailableReviewSlots',
      ).mockResolvedValue({
        availableReviewSlots: [],
        hasReviewSlotsOutsideOfMaxAssignablePeriod: true,
      });
      const tasks = [createTask()];
      const data = {
        caseId,
        clientCase: {
          id: caseId,
          name: 'Case1',
          status: CaseStatusEnum.Open,
          relatedAdvisor: createAdviser(),
          relatedManager: null,
          clients: createClients(),
          goals,
          tasks,
          type: new CaseType(1),
          createdOn: new Date(),
          completedOn: new Date(),
          daysToComplete: 0,
          reviewSlot: null,
        },
      };
      const wrapper = makeWrapper(CaseOverview, data);
      await flushPromises();
      expect(wrapper.findByTestId('reviewSlotWarningMessage')).toBe(null);
    });

    it('should open case adviser update form', async () => {
      const data = {
        caseId,
        clientCase: createCase(),
      };

      const wrapper = makeWrapper(CaseOverview, data);
      expect(wrapper.findComponent(ChangeAdvisorForm).exists()).toBe(false);
      wrapper.findByTestId('openChangeAdviserFormHandler').trigger('click');
      await flushPromises();
      expect(wrapper.findComponent(ChangeAdvisorForm).exists()).toBe(true);
    });

    it('should make the api call to update the case adviser', async () => {
      const data = {
        caseId,
        clientCase: createCase(),
      };

      const wrapper = makeWrapper(CaseOverview, data);
      wrapper.findByTestId('openChangeAdviserFormHandler').trigger('click');
      await flushPromises();
      const changeAdvisorForm = wrapper.findComponent(ChangeAdvisorForm);
      changeAdvisorForm.vm.$emit('on-advisor-change', { advisorId: 1 });
      await flushPromises();
      expect(updateCaseAdvisorActionMock).toHaveBeenCalledTimes(1);
      expect(updateCaseAdvisorActionMock).toHaveBeenCalledWith({
        caseId: data.caseId,
        advisorId: 1,
      });
    });

    it('should open case manager update form', async () => {
      const data = {
        caseId,
        clientCase: createCase(),
      };

      const wrapper = makeWrapper(CaseOverview, data);
      expect(wrapper.findComponent(ChangeAdvisorForm).exists()).toBe(false);
      wrapper.findByTestId('openChangeManagerFormHandler').trigger('click');
      await flushPromises();
      expect(wrapper.findComponent(ChangeAdvisorForm).exists()).toBe(true);
    });
  });

  it('should make the api call to update the case manager', async () => {
    const data = {
      caseId,
      clientCase: createCase(),
    };

    const wrapper = makeWrapper(CaseOverview, data);
    wrapper.findByTestId('openChangeManagerFormHandler').trigger('click');
    await flushPromises();
    const changeManagerForm = wrapper.findComponent(ChangeAdvisorForm);
    changeManagerForm.vm.$emit('on-advisor-change', { advisorId: 1 });
    await flushPromises();
    expect(updateCaseManagerActionMock).toHaveBeenCalledTimes(1);
    expect(updateCaseManagerActionMock).toHaveBeenCalledWith({
      caseId: data.caseId,
      managerId: 1,
    });
  });
});
