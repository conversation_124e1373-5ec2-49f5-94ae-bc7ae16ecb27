import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import {
  createCase,
  createAccount,
  createTask,
  createGoal,
} from '@tests/helpers';

import { Case, CaseId } from '@aventur-shared/modules/cases';
import { Goal } from '@aventur-shared/modules/goals';
import { TaskGoal, TaskTypeEnum } from '@aventur-shared/modules/tasks';
import { AccountStatusEnum } from '@aventur-shared/modules/accounts';
import { Modal } from '@/modules/ui';
import TaskItemAcceptanceFromClient from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item/task-item-acceptance-from-client.vue';

type Props = {
  caseId: CaseId;
  goal: TaskGoal;
  accounts: Goal['accounts'];
  isModifiable: boolean;
  caseAdvisorId: Case['relatedAdvisor']['id'];
  type: 'Existing' | 'Proposed';
};

const account = createAccount({
  status: {
    id: AccountStatusEnum.Proposed,
    name: 'Proposed',
  },
  advices: [
    {
      id: 1,
      description: 'test',
      isAccepted: false,
      isImplemented: false,
      type: 1,
    },
    {
      id: 2,
      description: 'test2',
      isAccepted: false,
      isImplemented: false,
      type: 1,
    },
  ],
});
const goal = createGoal({
  accounts: [account],
});
const task = createTask({
  type: TaskTypeEnum.CreateAdvice,
});
const clientCase = createCase({
  goals: [goal],
  tasks: [task],
});

const props: Props = {
  caseId: clientCase.id,
  goal: {
    taskSlug: task.slug,
    ...goal,
    ...task['subTasks'][0],
  },
  accounts: [account],
  isModifiable: true,
  caseAdvisorId: clientCase.relatedAdvisor.id,
  type: 'Existing',
};

function makeWrapper(props: Props) {
  return mount(TaskItemAcceptanceFromClient, {
    props,
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Task item acceptance from client', () => {
  it('displays proper account type', async () => {
    const wrapper = makeWrapper(props);
    const h1 = wrapper.find('h1');
    expect(h1.exists()).toBe(true);
    expect(h1.text()).toBe(`${props.type} accounts`);
  });

  it('displays proper button text for unaccepted advice', () => {
    const wrapper = makeWrapper({
      ...props,
      accounts: [
        createAccount({
          advices: [
            {
              id: 1,
              description: 'test',
              isAccepted: false,
              isImplemented: false,
              type: 1,
            },
          ],
        }),
      ],
    });
    const button = wrapper.findComponent(Modal).find('button');
    expect(button.text()).toBe('Accepted 0/1');
  });

  it('displays proper button text for accepted advice', () => {
    const wrapper = makeWrapper({
      ...props,
      accounts: [
        createAccount({
          advices: [
            {
              id: 1,
              description: 'test',
              isAccepted: true,
              isImplemented: false,
              type: 1,
            },
          ],
        }),
      ],
    });
    const button = wrapper.findComponent(Modal).find('button');
    expect(button.text()).toBe('Accepted 1/1');
  });

  it('renders a modal', async () => {
    const wrapper = makeWrapper({
      ...props,
      accounts: [
        createAccount({
          advices: [
            {
              id: 1,
              description: 'test',
              isAccepted: false,
              isImplemented: false,
              type: 1,
            },
          ],
        }),
      ],
    });
    expect(wrapper.findComponent(Modal).exists()).toBe(true);
  });
});
