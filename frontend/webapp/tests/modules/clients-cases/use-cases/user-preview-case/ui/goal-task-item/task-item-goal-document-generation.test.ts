import { mount } from '@vue/test-utils';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { createTesting<PERSON>inia } from '@pinia/testing';

import {
  createCase,
  createClient,
  createGoal,
  createSubTask,
  createTask,
} from '@tests/helpers';

import { apiClient } from '@aventur-shared/services/api';
import { Case } from '@aventur-shared/modules/cases';
import { Task } from '@aventur-shared/modules/tasks';
import { TaskTypeEnum } from '@aventur-shared/modules/tasks/models/task-type';

import { DocumentType } from '@modules/clients-cases/models/document_generation';
import TaskItemGoal from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item-goal.vue';
import { ArrayElement } from '@aventur-shared/types/Common';
import TaskItemDocumentGeneration from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item/task-item-document-generation.vue';
import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';
import { GoalId } from '@aventur-shared/modules/goals';

vi.mock('vue-router');

interface ComponentProps {
  clientCase: Case;
  task: Task;
  subTask: ArrayElement<Task['subTasks']>;
  isReadonly: boolean;
  defaultOpen?: boolean;
}

function makeWrapper(propsData: ComponentProps, goalAccounts: object[] = []) {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  propsData.clientCase.goals[0].accounts = goalAccounts;
  return mount(TaskItemGoal, {
    props: propsData,
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            'client-case-store': { clientCase: propsData.clientCase },
          },
        }),
      ],
    },
  });
}

vi.spyOn(apiClient, 'get').mockResolvedValue([]);
vi.spyOn(apiClient, 'patch').mockResolvedValue({});

afterEach(() => {
  vi.clearAllMocks();
});

const clients = [createClient()];

const preparePropsData = (
  goalType: ClientGoalTypeEnum = ClientGoalTypeEnum.Retirement,
  taskType: TaskTypeEnum = TaskTypeEnum.PlanningReportGenerated,
) => {
  const goals = [
    createGoal({
      type: goalType,
      clients,
    }),
  ];
  const tasks = [
    createTask({
      type: taskType,
      subTasks: goals.map(({ id, type }) =>
        createSubTask({
          caseGoalId: id,
          goalId: type as GoalId,
        }),
      ),
    }),
  ];
  return {
    clientCase: createCase({ tasks, goals, clients }),
    task: tasks[0],
    subTask: tasks[0].subTasks[0],
    isReadonly: false,
    defaultOpen: true,
  };
};

describe('handleDocumentGenerationRequest Tests', () => {
  it('Makes the expected calls for the Suitability Report document', async () => {
    const propsData = preparePropsData();
    const wrapper = makeWrapper(propsData);

    expect(
      wrapper.getComponent(TaskItemDocumentGeneration).props('documentType'),
    ).toEqual(DocumentType.SuitabilityReport);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleDocumentGenerationRequest(
      DocumentType.SuitabilityReport,
    );

    expect(apiClient.patch).toHaveBeenCalledTimes(1);
    expect(apiClient.patch).toHaveBeenCalledWith(
      `/api/v2/case/${propsData.clientCase['id']}/${propsData.task['slug']}/${propsData.subTask['taskId']}`,
      {
        command: 'requestDocument',
        payload: {
          document_args: {
            case_goal_id: propsData.subTask.caseGoalId,
          },
          document_type: 'SuitabilityReport',
        },
      },
    );
  });

  it('Makes the expected calls for the Suitability Report Mortgage document', async () => {
    const propsData = preparePropsData(ClientGoalTypeEnum.PropertyOwnership);
    const wrapper = makeWrapper(propsData);

    expect(
      wrapper.getComponent(TaskItemDocumentGeneration).props('documentType'),
    ).toEqual(DocumentType.SuitabilityReportMortgage);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleDocumentGenerationRequest(
      DocumentType.SuitabilityReportMortgage,
    );

    expect(apiClient.patch).toHaveBeenCalledTimes(1);
    expect(apiClient.patch).toHaveBeenCalledWith(
      `/api/v2/case/${propsData.clientCase['id']}/${propsData.task['slug']}/${propsData.subTask['taskId']}`,
      {
        command: 'requestDocument',
        payload: {
          document_args: {
            case_goal_id: propsData.subTask.caseGoalId,
          },
          document_type: 'SuitabilityReportMortgage',
        },
      },
    );
  });

  it('Makes the expected calls for the Headed Letter document', async () => {
    const propsData = preparePropsData(
      ClientGoalTypeEnum.BuildWealth,
      TaskTypeEnum.HeadedLetterGenerated,
    );
    const wrapper = makeWrapper(propsData);

    expect(
      wrapper.getComponent(TaskItemDocumentGeneration).props('documentType'),
    ).toEqual(DocumentType.HeadedLetter);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleDocumentGenerationRequest(DocumentType.HeadedLetter);

    expect(apiClient.patch).toHaveBeenCalledTimes(1);
    expect(apiClient.patch).toHaveBeenCalledWith(
      `/api/v2/case/${propsData.clientCase['id']}/${propsData.task['slug']}/${propsData.subTask['taskId']}`,
      {
        command: 'requestDocument',
        payload: {
          document_args: {
            client_id: propsData.clientCase.clients[0].id,
          },
          document_type: 'HeadedLetter',
        },
      },
    );
  });
});
