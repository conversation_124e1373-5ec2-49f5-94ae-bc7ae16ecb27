import { mount } from '@vue/test-utils';
import { afterEach, describe, it, vi, assert, expect } from 'vitest';
import { createTestingPinia } from '@pinia/testing';
import {
  createClient,
  createAdviser,
  createTask,
  createCase,
} from '@tests/helpers';

import { apiClient } from '@aventur-shared/services/api';
import { Case } from '@aventur-shared/modules/cases';
import { Task, TaskTypeEnum } from '@aventur-shared/modules/tasks';
import { SubTask } from '@aventur-shared/modules/cases/models';

import TaskItemCase from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item-case.vue';

vi.mock('vue-router');

interface Props {
  clientCase: Case;
  task: Task;
  subTask: SubTask;
  isReadonly: boolean;
}

function makeWrapper(propsData: Props) {
  return mount(TaskItemCase, {
    props: propsData,
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {},
        }),
      ],
    },
  });
}

vi.spyOn(apiClient, 'patch').mockResolvedValue({});

afterEach(() => {
  vi.clearAllMocks();
});

describe('Mailing task: Client Onboarding', () => {
  const task = createTask({
    isModifiable: true,
    caseLevel: true,
    type: TaskTypeEnum.ClientOnboardingMessage,
  });

  const propsData = {
    clientCase: createCase({
      tasks: [task],
      clients: [createClient({ email: null })],
    }),
    task: task,
    subTask: task['subTasks'][0],
    caseAdvisor: createAdviser(),
    isReadonly: false,
  };

  it('Renders correct markup if goal has client(s) without valid emails', async () => {
    const wrapper = makeWrapper(propsData);
    assert.exists(
      wrapper.findElementByText(
        'p',
        'Click the button below to send the Aventur branded and tracked email to the case client(s). Using this option is highly recommended.',
      ),
    );
    assert.exists(
      wrapper.findElementByText(
        'span',
        'No clients with a valid email address found. Please, update client details to enable automated email option.',
      ),
    );
    assert.notExists(wrapper.findElementByText('button', 'Send Message'));
    assert.notExists(
      wrapper.findElementByText(
        'label',
        'Check to receive a copy to your mailbox',
      ),
    );
  });

  it('Renders correct markup if goal has at least one client with valid email', async () => {
    propsData.clientCase.clients = [createClient()];
    const wrapper = makeWrapper(propsData);

    assert.notExists(
      wrapper.findElementByText(
        'span',
        'No clients with a valid email address found. Please, update client details to enable automated email option.',
      ),
    );
    assert.exists(wrapper.findElementByText('button', 'Send Message'));
    assert.exists(
      wrapper.findElementByText(
        'label',
        'Check to receive a copy to your mailbox',
      ),
    );
  });

  it('Makes correct API calls', async () => {
    const client = createClient();
    propsData.clientCase.clients = [client];
    const wrapper = makeWrapper(propsData);

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleMessage(/* copySelf */ true);

    expect(apiClient.patch).toHaveBeenCalledOnce();
    expect(apiClient.patch).toHaveBeenCalledWith(
      `/api/v2/case/${propsData.clientCase['id']}/${propsData.task['slug']}/${propsData.subTask['taskId']}`,
      {
        command: 'sendClientEmail',
        payload: {
          copy_self: true,
        },
      },
    );
  });
});

describe('Mailing task: Client Annual Review', () => {
  const task = createTask({
    isModifiable: true,
    caseLevel: true,
    type: TaskTypeEnum.ClientAnnualReviewMessage,
  });

  const propsData = {
    clientCase: createCase({
      tasks: [task],
      clients: [createClient({ email: null })],
    }),
    task: task,
    subTask: task['subTasks'][0],
    caseAdvisor: createAdviser(),
    isReadonly: false,
  };

  it('Renders correct markup if goal has client(s) without valid emails', async () => {
    const wrapper = makeWrapper(propsData);
    assert.exists(
      wrapper.findElementByText(
        'p',
        'Click the button below to send the Aventur branded and tracked email to the case client(s). Using this option is highly recommended.',
      ),
    );
    assert.exists(
      wrapper.findElementByText(
        'span',
        'No clients with a valid email address found. Please, update client details to enable automated email option.',
      ),
    );
    assert.notExists(wrapper.findElementByText('button', 'Send Message'));
    assert.notExists(
      wrapper.findElementByText(
        'label',
        'Check to receive a copy to your mailbox',
      ),
    );
  });

  it('Renders correct markup if goal has at least one client with valid email', async () => {
    propsData.clientCase.clients = [createClient()];
    const wrapper = makeWrapper(propsData);

    assert.notExists(
      wrapper.findElementByText(
        'span',
        'No clients with a valid email address found. Please, update client details to enable automated email option.',
      ),
    );
    assert.exists(wrapper.findElementByText('button', 'Send Message'));
    assert.exists(
      wrapper.findElementByText(
        'label',
        'Check to receive a copy to your mailbox',
      ),
    );
  });

  it('Makes correct API calls', async () => {
    propsData.clientCase.clients = [createClient(), createClient()];
    const wrapper = makeWrapper(propsData);

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleMessage(/* copySelf */ true);

    expect(apiClient.patch).toHaveBeenCalledOnce();
    expect(apiClient.patch).toHaveBeenCalledWith(
      `/api/v2/case/${propsData.clientCase['id']}/${propsData.task['slug']}/${propsData.subTask['taskId']}`,
      {
        command: 'sendClientEmail',
        payload: {
          copy_self: true,
        },
      },
    );
  });
});
