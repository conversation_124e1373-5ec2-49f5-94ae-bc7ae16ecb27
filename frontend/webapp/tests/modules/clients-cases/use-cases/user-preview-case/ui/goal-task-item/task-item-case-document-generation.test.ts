import { mount } from '@vue/test-utils';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { createTestingPinia } from '@pinia/testing';

import {
  createAccount,
  createCase,
  createTask,
  createGoal,
  createSubTask,
} from '@tests/helpers';

import { apiClient } from '@aventur-shared/services/api';
import { Case } from '@aventur-shared/modules/cases';
import { Task, TaskStatus } from '@aventur-shared/modules/tasks';
import {
  account,
  AccountStatusEnum,
} from '@aventur-shared/modules/accounts/models';
import { TaskTypeEnum } from '@aventur-shared/modules/tasks/models/task-type';
import { TaskStatusEnum } from '@aventur-shared/modules/tasks/models/task-status';

import { DocumentType } from '@modules/clients-cases/models/document_generation';
import TaskItemCase from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item-case.vue';
import { accountStatusToSelectOption } from '@aventur-shared/modules/accounts/models/account';
import { SubTask } from '@aventur-shared/modules/cases/models';
import TaskItemDocumentGeneration from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item/task-item-document-generation.vue';
import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';

vi.mock('vue-router');

interface ComponentProps {
  clientCase: Case;
  task: Task;
  subTask: SubTask;
  isReadonly: boolean;
}

const accounts = [
  createAccount({ providerName: 'Keith' }),
  createAccount({ providerName: 'Keith' }),
  createAccount({ providerName: 'Principality' }),
];
const goals = [
  createGoal({
    accounts,
  }),
];

const preparePropsData = (taskType: TaskTypeEnum) => {
  const tasks = [
    createTask({
      type: taskType,
      subTasks: [createSubTask({ caseGoalId: null, goalId: null })],
    }),
  ];
  return {
    clientCase: createCase({ tasks, goals }),
    task: tasks[0],
    subTask: tasks[0].subTasks[0],
    isReadonly: false,
  };
};

function makeWrapper(propsData: ComponentProps) {
  return mount(TaskItemCase, {
    props: propsData,
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            'client-case-store': { clientCase: propsData.clientCase },
          },
        }),
      ],
    },
  });
}

vi.spyOn(apiClient, 'patch').mockResolvedValue({});

afterEach(() => {
  vi.clearAllMocks();
});

describe('handleDocumentGenerationRequest Tests', () => {
  it('Makes the expected calls for the Letter of Authority document', async () => {
    vi.spyOn(account, 'isAccountToReview').mockReturnValue(true);

    const propsData = preparePropsData(TaskTypeEnum.LetterOfAuthorityGenerated);
    const wrapper = makeWrapper(propsData);

    expect(
      wrapper.getComponent(TaskItemDocumentGeneration).props('documentType'),
    ).toEqual(DocumentType.LetterOfAuthority);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleDocumentGenerationRequest(
      DocumentType.LetterOfAuthority,
    );

    expect(apiClient.patch).toHaveBeenCalledTimes(accounts.length);
    for (let index = 0; index < accounts.length; index++) {
      expect(apiClient.patch).toHaveBeenNthCalledWith(
        index + 1,
        `/api/v2/case/${propsData.clientCase['id']}/${propsData.task['slug']}/${propsData.subTask['taskId']}`,
        {
          command: 'requestDocument',
          payload: {
            document_args: {
              holding_id: accounts[index].id,
            },
            document_type: 'LetterOfAuthority',
          },
        },
      );
    }
  });

  it('Makes the expected calls for the Letter of Authority Cover Letter document', async () => {
    vi.spyOn(account, 'isAccountToReview').mockReturnValue(true);

    const propsData = preparePropsData(TaskTypeEnum.LoaCoverLetterGenerated);
    const wrapper = makeWrapper(propsData);

    expect(
      wrapper.getComponent(TaskItemDocumentGeneration).props('documentType'),
    ).toEqual(DocumentType.LetterOfAuthorityCoverLetter);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleDocumentGenerationRequest(
      DocumentType.LetterOfAuthorityCoverLetter,
    );

    const expectedJsonArgs = [
      {
        holding_ids: [1, 2],
        adviser_id: wrapper.vm.$props.clientCase.relatedAdvisor.id,
      }, // Keith provider
      {
        holding_ids: [3],
        adviser_id: wrapper.vm.$props.clientCase.relatedAdvisor.id,
      }, // Principality provider
    ];

    expect(apiClient.patch).toHaveBeenCalledTimes(expectedJsonArgs.length);
    for (let index = 0; index < expectedJsonArgs.length; index++) {
      expect(apiClient.patch).toHaveBeenNthCalledWith(
        index + 1,
        `/api/v2/case/${propsData.clientCase['id']}/${propsData.task['slug']}/${propsData.subTask['taskId']}`,
        {
          command: 'requestDocument',
          payload: {
            document_args: expectedJsonArgs[index],
            document_type: 'LetterOfAuthorityCoverLetter',
          },
        },
      );
    }
  });

  it('Makes the expected calls for the Financial Summary Document', async () => {
    const propsData = preparePropsData(
      TaskTypeEnum.FinancialSummaryDocumentGenerated,
    );
    const wrapper = makeWrapper(propsData);

    expect(
      wrapper.getComponent(TaskItemDocumentGeneration).props('documentType'),
    ).toEqual(DocumentType.FinancialSummaryDocument);

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.handleDocumentGenerationRequest(
      DocumentType.FinancialSummaryDocument,
    );

    expect(apiClient.patch).toHaveBeenCalledTimes(1);
    expect(apiClient.patch).toHaveBeenCalledWith(
      `/api/v2/case/${propsData.clientCase['id']}/${propsData.task['slug']}/${propsData.subTask['taskId']}`,
      {
        command: 'requestDocument',
        payload: {
          document_args: {
            case_id: propsData.clientCase['id'],
          },
          document_type: 'FinancialSummaryDocument',
        },
      },
    );
  });
});

describe('LOA Generated button enabled tests', () => {
  // Component to be initialised where the LOA Generated task is In Progress
  // This ensures that isActionable is true isolating whether the LOA component is enabled to just
  // if there are accounts to review
  const tasks = [
    createTask({
      isModifiable: true,
      type: TaskTypeEnum.LetterOfAuthorityGenerated,
      status: new TaskStatus(TaskStatusEnum.InProgress),
    }),
    createTask(),
  ];

  it('Ensures that the Generate LOA component is not modifiable when there are no accounts to review', async () => {
    const goals = [
      createGoal({
        accounts: [],
      }),
    ];
    const propsData = {
      clientCase: createCase({ tasks, goals }),
      task: tasks[0],
      subTask: tasks[0].subTasks[0],
      isReadonly: false,
    };
    const wrapper = makeWrapper(propsData);
    // The task should be actionable, but there should be no accounts to review
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.isActionable).toBe(true);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.accountsToReview.length).toEqual(0);

    // Therefore the generate document component should be disabled
    const loaTaskComponent = wrapper
      .getComponent('[data-testid="loa-task-item"]')
      .findComponent({ name: 'task-item-document-generation' });
    expect(loaTaskComponent.vm.isModifiable).toBe(false);
    expect(loaTaskComponent.vm.messageToUser).toBeDefined();
  });

  it('Ensures that the Generate LOA component is modifiable when there are accounts to review', async () => {
    const goals = [
      createGoal({
        accounts: [
          createAccount({
            status: accountStatusToSelectOption(
              AccountStatusEnum.ActiveNonAdvised,
            ),
          }),
        ],
      }),
    ];
    const propsData = {
      clientCase: createCase({ tasks, goals }),
      task: tasks[0],
      subTask: tasks[0].subTasks[0],
      isReadonly: false,
    };
    // Ensure that we have an account to review
    const wrapper = makeWrapper(propsData);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.isActionable).toBe(true);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.accountsToReview.length).toEqual(1);

    // There should be no user message and the component should be enabled
    const loaTaskComponent = wrapper
      .getComponent('[data-testid="loa-task-item"]')
      .findComponent({ name: 'task-item-document-generation' });
    expect(loaTaskComponent.vm.isModifiable).toBe(true);
    expect(loaTaskComponent.vm.messageToUser).toBeUndefined();
  });
});

describe('LOA Cover Letter Generated button enabled tests', () => {
  // Component to be initialised where the LOA Cover Letter Generated task is In Progress
  // This ensures that isActionable is true isolating whether the LOA component is enabled to just
  // if there are accounts to review
  const tasks = [
    createTask({
      isModifiable: true,
      type: TaskTypeEnum.LoaCoverLetterGenerated,
      status: new TaskStatus(TaskStatusEnum.InProgress),
    }),
    createTask(),
  ];

  it('Ensures that the Generate LOA Cover component is not modifiable when there are no accounts to review', async () => {
    const goals = [
      createGoal({
        accounts: [],
      }),
    ];
    const propsData = {
      clientCase: createCase({ tasks, goals }),
      task: tasks[0],
      subTask: tasks[0].subTasks[0],
      isReadonly: false,
    };
    const wrapper = makeWrapper(propsData);
    // The task should be actionable, but there should be no accounts to review
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.isActionable).toBe(true);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.accountsToReview.length).toEqual(0);

    // Therefore the generate document component should be disabled
    const loaTaskComponent = wrapper
      .getComponent('[data-testid="loa-cover-letter-task-item"]')
      .findComponent({ name: 'task-item-document-generation' });
    expect(loaTaskComponent.vm.isModifiable).toBe(false);
    expect(loaTaskComponent.vm.messageToUser).toBeDefined();
  });

  it('Ensures that the Generate LOA Cover component is modifiable when there are accounts to review', async () => {
    const goals = [
      createGoal({
        accounts: [
          createAccount({
            status: accountStatusToSelectOption(
              AccountStatusEnum.ActiveNonAdvised,
            ),
          }),
        ],
      }),
    ];
    const propsData = {
      clientCase: createCase({ tasks, goals }),
      task: tasks[0],
      subTask: tasks[0].subTasks[0],
      isReadonly: false,
    };
    // Ensure that we have an account to review
    const wrapper = makeWrapper(propsData);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.isActionable).toBe(true);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.vm.accountsToReview.length).toEqual(1);

    // There should be no user message and the component should be enabled
    const loaTaskComponent = wrapper
      .getComponent('[data-testid="loa-cover-letter-task-item"]')
      .findComponent({ name: 'task-item-document-generation' });
    expect(loaTaskComponent.vm.isModifiable).toBe(true);
    expect(loaTaskComponent.vm.messageToUser).toBeUndefined();
  });
});
