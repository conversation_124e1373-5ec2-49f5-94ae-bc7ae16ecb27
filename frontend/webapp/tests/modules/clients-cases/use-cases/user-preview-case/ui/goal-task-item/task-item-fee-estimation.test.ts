import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import {
  createCase,
  createAccount,
  createTask,
  createGoal,
} from '@tests/helpers';

import { CaseId } from '@aventur-shared/modules/cases';
import { Goal } from '@aventur-shared/modules/goals';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { ExpectedFeeType } from '@aventur-shared/modules/accounts/models/expected-fee-type';
import { TaskGoal, TaskTypeEnum } from '@aventur-shared/modules/tasks';
import { AccountStatusEnum } from '@aventur-shared/modules/accounts';

import { Modal } from '@/modules/ui';
import TaskItemFeeEstimation from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item/task-item-fee-estimation.vue';

type Props = {
  title: 'Existing' | 'Proposed';
  caseId: CaseId;
  goal: TaskGoal;
  accounts: Goal['accounts'];
  isModifiable: boolean;
};

const account = createAccount({
  status: {
    id: AccountStatusEnum.Proposed,
    name: 'Proposed',
  },
  advices: [
    {
      id: 1,
      description: 'test',
      isAccepted: false,
      isImplemented: false,
      type: 1,
    },
    {
      id: 2,
      description: 'test2',
      isAccepted: false,
      isImplemented: false,
      type: 1,
    },
  ],
});
const goal = createGoal({
  accounts: [account],
});
const task = createTask({
  type: TaskTypeEnum.CreateAdvice,
});
const clientCase = createCase({
  goals: [goal],
  tasks: [task],
});

const props: Props = {
  caseId: clientCase.id,
  goal: {
    taskSlug: task.slug,
    ...goal,
    ...task['subTasks'][0],
  },
  accounts: [account],
  isModifiable: true,
  title: 'Existing',
};

function makeWrapper(props: Props) {
  return mount(TaskItemFeeEstimation, {
    props,
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Task item acceptance from client', () => {
  it('displays proper account type', async () => {
    const wrapper = makeWrapper(props);
    const h1 = wrapper.find('h1');
    expect(h1.exists()).toBe(true);
    expect(h1.text()).toBe(`${props.title}`);
  });

  it('displays proper button text for task without fee estimtes', () => {
    const wrapper = makeWrapper({
      ...props,
      accounts: [
        createAccount({
          expectedFees: [],
        }),
      ],
    });
    const button = wrapper.findComponent(Modal).find('button');
    expect(button.text()).toBe('Add estimate');
  });

  it('displays proper button text for task with estimates', () => {
    const wrapper = makeWrapper({
      ...props,
      accounts: [
        createAccount({
          expectedFees: [
            {
              id: 1,
              amount: '1',
              dueDate: new DateTime('2020-12-12'),
              initial: new ExpectedFeeType(1),
            },
          ],
        }),
      ],
    });
    const button = wrapper.findComponent(Modal).find('button');
    expect(button.text()).toBe('1 added');
  });
});
