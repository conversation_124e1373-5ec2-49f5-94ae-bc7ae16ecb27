/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as vueRouterMod from 'vue-router';
import { flushPromises, mount } from '@vue/test-utils';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { createTesting<PERSON>inia } from '@pinia/testing';
import { directive as tippyDirective } from 'vue-tippy';

import * as helpers from '@tests/helpers';

import { formatName } from '@aventur-shared/utils/user';
import { GoalId } from '@aventur-shared/modules/goals';
import {
  TaskStatus,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@aventur-shared/modules/tasks';
import {
  AccountStatusEnum,
  accountStatusToSelectOption,
} from '@aventur-shared/modules/accounts/models/account';

import CaseTaskList from '@modules/clients-cases/use-cases/user-preview-case/ui/case-task-list.vue';
import TaskItem from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item.vue';

vi.mock('vue-router');
const useRouteMock = vi.spyOn(vueRouterMod, 'useRoute');
// @ts-ignore
useRouteMock.mockReturnValue({ hash: 'test' });

const advisorsRoles = [
  { value: 'superadmin', label: 'Superadmin' },
  { value: 'adviser', label: 'Adviser' },
  { value: 'compliance', label: 'Compliance' },
  { value: 'introducer', label: 'Introducer' },
  { value: 'paraplanner', label: 'Paraplanner' },
  { value: 'relationship_manager', label: 'Relationship Manager' },
  { value: 'case_management', label: 'Case Management' },
];

const clients = helpers.createClients(2);
const advisor = helpers.createAdviser();
const goals = [
  helpers.createGoal({
    type: 10,
    name: 'Build Wealth',
    accounts: [
      helpers.createAccount(), // proposed
      helpers.createAccount({
        originalStatus: accountStatusToSelectOption(
          AccountStatusEnum.ActiveNonAdvised,
        ),
        status: accountStatusToSelectOption(AccountStatusEnum.ActiveNonAdvised),
      }),
    ],
  }),
];
const tasks = [
  helpers.createTask({
    type: TaskTypeEnum.ClientAnnualReviewMessage,
    advisor,
    isModifiable: true,
  }),
  helpers.createTask({
    advisor,
    isModifiable: true,
    status: new TaskStatus(TaskStatusEnum.Completed),
  }),
  helpers.createTask({
    advisor,
    type: TaskTypeEnum.AccountsToReview,
    status: new TaskStatus(TaskStatusEnum.Review),
    subTasks: goals.map(({ type: goalId }) =>
      helpers.createSubTask({
        goalId: goalId as GoalId,
      }),
    ),
  }),
];

const clientCase = helpers.createCase({
  relatedAdvisor: advisor,
  clients,
  goals,
  tasks,
});

function makeWrapper() {
  return mount(CaseTaskList, {
    props: {
      clientCase,
      isCaseReadonly: false,
    },
    global: {
      directives: {
        tippy: tippyDirective,
      },
      stubs: {
        TaskItemDetail: true,
      },
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            advisors: {
              advisorsRoles,
            },
          },
        }),
      ],
    },
  });
}

afterEach(() => {
  vi.clearAllMocks();
});

describe('Structure', () => {
  it('Renders correct markup', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.find('.grid h3').text()).toBe(`${tasks.length} tasks`);
    expect(wrapper.findAll('.grid span')[0].text()).toBe('Assignee');
    expect(wrapper.findAll('.grid span')[1].text()).toBe('Due date');
    expect(wrapper.findAll('.grid span')[2].text()).toBe('Status');

    const taskRows = wrapper.findAllComponents(TaskItem);
    expect(taskRows).toHaveLength(tasks.length);
    taskRows.forEach((row, index) => {
      expect(row.findByTestId('task-description').text()).toBe(
        tasks[index].description,
      );

      if (tasks[index].advisor) {
        expect(row.findAll('summary > div')[1].text()).toBe(
          formatName({
            firstName: advisor.firstName,
            lastName: advisor.lastName,
          }),
        );
      }

      if (tasks[index].assignedGroup) {
        expect(row.findAll('summary > div')[1].text()).toBe(
          'Group: Case Management',
        );
      }

      expect(row.findAll('summary > div')[2].text()).toBe(
        tasks[index].dueDate.formatWithShortName(),
      );
      expect(row.findAll('summary > div')[3].text()).toBe(
        tasks[index].status.toString(),
      );
    });
  });
});
