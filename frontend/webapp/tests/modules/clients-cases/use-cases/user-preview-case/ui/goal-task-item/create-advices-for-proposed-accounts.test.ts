/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';

import { AccountStatusEnum } from '@aventur-shared/modules/accounts';
import * as useConfirmationMod from '@aventur-shared/composables/useConfirmation';
import { apiClient } from '@aventur-shared/services/api';
import { CaseId } from '@aventur-shared/modules/cases';
import { Goal } from '@aventur-shared/modules/goals';
import { TaskGoal, TaskTypeEnum } from '@aventur-shared/modules/tasks';

import * as removeProposedAccountActionMod from '@modules/sub-tasks/use-cases/user-removes-proposed-account';
import CreateAdvicesForProposedAccounts from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item/create-advices-for-proposed-accounts.vue';
import {
  createCase,
  createAccount,
  createGoal,
  createTask,
} from '@tests/helpers';

type Props = {
  caseId: CaseId;
  goal: TaskGoal;
  accounts: Goal['accounts'];
  isModifiable: boolean;
};

const account = createAccount({
  status: {
    id: AccountStatusEnum.Proposed,
    name: 'Proposed',
  },
});
const goal = createGoal({
  accounts: [account],
});
const task = createTask({
  type: TaskTypeEnum.CreateAdvice,
});
const clientCase = createCase({
  goals: [goal],
  tasks: [task],
});

const props: Props = {
  caseId: clientCase.id,
  goal: {
    taskSlug: task.slug,
    ...goal,
    ...task['subTasks'][0],
  },
  accounts: [account],
  isModifiable: true,
};

vi.spyOn(useConfirmationMod, 'useConfirmation').mockResolvedValue({
  isAccepted: () => true,
  onAccept: vi.fn(),
  onCancel: vi.fn(),
});

vi.spyOn(removeProposedAccountActionMod, 'removeProposedAccountAction');
vi.spyOn(apiClient, 'patch').mockResolvedValue(undefined);

const confirmationModalSpy = vi.spyOn(useConfirmationMod, 'useConfirmation');

vi.mock('vue-router', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    useRoute: vi.fn(() => {
      return {
        params: {
          goalId: 1,
        },
      };
    }),
  };
});

function makeWrapper(props: Props) {
  return mount(CreateAdvicesForProposedAccounts, {
    props,
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Create advices for proposed accounts', () => {
  it('properly calculates proposed accounts', async () => {
    const wrapper = makeWrapper(props);
    // @ts-ignore
    expect(wrapper.props().accounts.length).toBe(1);
  });

  it('displays confirmation dialog on account remove', async () => {
    const wrapper = makeWrapper(props);
    const removeProposedAccountBtn = wrapper.findByTestId(
      'remove-proposed-account-button',
    );

    removeProposedAccountBtn.trigger('click');
    await flushPromises();
    expect(confirmationModalSpy).toHaveBeenCalledOnce();
    expect(confirmationModalSpy).toHaveBeenCalledWith(
      'Please, confirm proposed account removal',
      'Confirmation is required to perform this action',
    );
  });

  it('calls remove action after confirmation', async () => {
    const wrapper = makeWrapper(props);
    // @ts-ignore
    await wrapper.vm.handleRemoveAccount(1);
    await flushPromises();

    expect(apiClient.patch).toHaveBeenCalledWith(
      `/api/v2/case/${props.caseId}/advice_selection/${props.goal.taskId}`,
      {
        command: 'removeProposedAccount',
        payload: { account_id: 1 },
      },
    );
  });
});
