/* eslint-disable @typescript-eslint/ban-ts-comment */
import { ref } from 'vue';
import { flushPromises, mount } from '@vue/test-utils';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { createTestingPinia } from '@pinia/testing';
import { apiClient } from '@aventur-shared/services/api';
import UpcomingReviewList from '@modules/upcoming-reviews/ui/upcoming-review-list.vue';

vi.mock('vue-router', async (importOriginal) => {
  const $router = await importOriginal();

  return {
    // @ts-ignore
    ...$router,
    useRouter: vi.fn,
    useRoute: vi.fn(() => {
      return {
        name: undefined,
      };
    }),
    onBeforeRouteUpdate: vi.fn,
    onBeforeRouteLeave: vi.fn,
  };
});

// Need to mock the table service
const filters = ref({ time_limited: true });
vi.mock(
  '@aventur-shared/utils/table/use-table-service',
  async (importOriginal) => {
    const mod = await importOriginal();
    return {
      // @ts-ignore
      ...mod,
      useTableService: vi.fn().mockImplementation(() => {
        return {
          changePage: vi.fn().mockImplementation((page) => {
            return {
              addFilter: vi.fn().mockImplementation((newFilters) => {
                filters.value = newFilters;
                return { apply: vi.fn().mockReturnValue(Promise.resolve()) };
              }),
            };
          }),
          addFilter: vi.fn(),
          resetFilters: vi.fn().mockImplementation((resetFilters) => {
            filters.value = { time_limited: filters.value.time_limited };
            return {
              changePage: vi.fn().mockImplementation((page) => {
                return { apply: vi.fn().mockReturnValue(Promise.resolve()) };
              }),
            };
          }),
          page: ref(1),
          filters: filters,
          setTotalItems: vi.fn(),
          config: { totalItems: 999 },
        };
      }),
    };
  },
);

function makeWrapper() {
  return mount(UpcomingReviewList, {
    shallow: true,
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {},
        }),
      ],
    },
  });
}

const mockJarvisGetUpcomingReviewsResponse = {
  total_reviews: 0,
  upcoming_reviews: [
    {
      id: 0,
      clients: [
        {
          id: 0,
          first_name: 'string',
          last_name: 'string',
        },
      ],
      client_owner_id: 0,
      review_month: 0,
      review_year: 0,
      case_data: {
        id: 0,
        adviser_id: 0,
        status: 1,
      },
    },
  ],
};

vi.spyOn(apiClient, 'get').mockResolvedValue(
  mockJarvisGetUpcomingReviewsResponse,
);

afterEach(() => {
  filters.value = { time_limited: true };
  vi.clearAllMocks();
});

describe('API call tests', () => {
  it('Makes the expected call on page load', async () => {
    makeWrapper();

    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledWith(
      '/api/v1/case/upcoming-reviews',
      {
        open_slots_only: false,
        page: 1,
        results_per_page: 10,
        time_limited: true,
      },
    );
  });
  it.each([
    [
      { text: 'search' },
      {
        text: 'search',
        open_slots_only: false,
        page: 1,
        results_per_page: 10,
        time_limited: true,
      },
    ],
    [
      { time_limited: 'false' },
      {
        open_slots_only: false,
        page: 1,
        results_per_page: 10,
        time_limited: false,
      },
    ],
    [
      { client_owner_id: 1 },
      {
        open_slots_only: false,
        client_owner_id: 1,
        page: 1,
        results_per_page: 10,
        time_limited: true,
      },
    ],
    [
      { case_adviser_id: 2 },
      {
        open_slots_only: false,
        case_adviser_id: 2,
        page: 1,
        results_per_page: 10,
        time_limited: true,
      },
    ],
    [
      {
        case_adviser_id: 2,
        text: 'search',
      },
      {
        open_slots_only: false,
        case_adviser_id: 2,
        text: 'search',
        page: 1,
        results_per_page: 10,
        time_limited: true,
      },
    ],
    [
      { case_status: 3 },
      {
        open_slots_only: false,
        case_status: 3,
        page: 1,
        results_per_page: 10,
        time_limited: true,
      },
    ],
    [
      {
        case_status: 3,
        time_limited: 'false',
        client_owner_id: 1,
        case_adviser_id: 2,
      },
      {
        open_slots_only: false,
        case_adviser_id: 2,
        case_status: 3,
        client_owner_id: 1,
        page: 1,
        results_per_page: 10,
        time_limited: false,
      },
    ],
  ])(
    'Makes the expected call given the filters %p expecting corresponding payload %p',
    async (filters, expectedPayload) => {
      const wrapper = makeWrapper();
      // @ts-ignore
      await wrapper.vm.handleUpdateFilters(filters);
      await flushPromises();

      expect(apiClient.get).toHaveBeenCalledTimes(2);
      expect(apiClient.get).toHaveBeenLastCalledWith(
        '/api/v1/case/upcoming-reviews',
        expectedPayload,
      );
    },
  );
  it('Makes the expected call when the filters are reset', async () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    await wrapper.vm.handleResetFilters(filters);
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith(
      '/api/v1/case/upcoming-reviews',
      {
        open_slots_only: false,
        page: 1,
        results_per_page: 10,
        time_limited: true,
      },
    );
  });
});
