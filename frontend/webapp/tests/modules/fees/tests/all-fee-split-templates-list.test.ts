/* eslint-disable @typescript-eslint/ban-ts-comment */
import { afterEach, describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { apiClient } from '@aventur-shared/services/api';
import { TableWithPagination } from '@modules/ui';
import { AllFeeSplitTemplatesList } from '@modules/fees/use-cases/preview-all-fee-split-templates';
import * as createActionMod from '@modules/fees/use-cases/create-fee-split-template';
import * as updateActionMod from '@modules/fees/use-cases/update-fee-split-template';

vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'put').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'get').mockResolvedValue({
  items: [],
  totalItems: 0,
});

const mockCreateAction = vi.spyOn(
  createActionMod,
  'createFeeSplitTemplateAction',
);

const mockUpdateAction = vi.spyOn(
  updateActionMod,
  'updateFeeSplitTemplateAction',
);

vi.mock('vue-router', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    useRoute: vi.fn(() => {
      return {
        query: {
          page: 1,
        },
      };
    }),
    useRouter: vi.fn(() => {
      return {
        replace: vi.fn(),
      };
    }),
    onBeforeRouteUpdate: vi.fn(),
  };
});

afterEach(() => {
  vi.clearAllMocks();
});

function makeWrapper() {
  return mount(AllFeeSplitTemplatesList, {
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Fee split template List', () => {
  it('displays proper children', async () => {
    const wrapper = makeWrapper();
    const tableWithPaginationComp =
      wrapper.findAllComponents(TableWithPagination);

    expect(tableWithPaginationComp.length).toEqual(1);
    expect(apiClient.get).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledWith(
      '/api/internal/v1/fee-split-templates',
      {
        page: '1',
      },
    );
  });

  it('calls create fee split endpoint on save', async () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    await wrapper.vm.handleSaveAndClose('add', {
      adviserId: 1,
      name: 'test',
      isActive: true,
      lines: [
        {
          name: 'name',
          payable: true,
          role: 'test',
          splitInitial: '1',
          splitOngoing: '1',
          type: 'test',
        },
      ],
    });
    expect(mockCreateAction).toHaveBeenCalledTimes(1);
    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/internal/v1/fee-split-templates',
      {
        administrator_id: 1,
        template_name: 'test',
        is_active: true,
        lines: [
          {
            administrator_id: 'name',
            is_payable: true,
            role: 'test',
            split_initial: '0.01',
            split_ongoing: '0.01',
            type: 'test',
          },
        ],
      },
    );
    expect(apiClient.get).toHaveBeenCalledTimes(1);
  });

  it('calls update fee split endpoint on save', async () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    await wrapper.vm.handleEditModalOpen(1);
    expect(apiClient.get).toHaveBeenCalledWith(
      '/api/internal/v1/fee-split-templates/1',
    );
    // @ts-ignore
    await wrapper.vm.handleSaveAndClose('edit', {
      id: 1,
      adviserId: 1,
      name: 'test',
      isActive: true,
      lines: [
        {
          name: 'name',
          payable: true,
          role: 'test',
          splitInitial: '1',
          splitOngoing: '1',
          type: 'test',
        },
      ],
    });
    expect(mockUpdateAction).toHaveBeenCalledTimes(1);
    expect(apiClient.put).toHaveBeenCalledWith(
      '/api/internal/v1/fee-split-templates/1',
      {
        administrator_id: 1,
        template_name: 'test',
        is_active: true,
        lines: [
          {
            administrator_id: 'name',
            is_payable: true,
            role: 'test',
            split_initial: '0.01',
            split_ongoing: '0.01',
            type: 'test',
          },
        ],
      },
    );
  });
});
