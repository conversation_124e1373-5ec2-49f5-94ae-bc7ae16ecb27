import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { apiClient } from '@aventur-shared/services/api';
import { getClientHealthScore } from '@aventur-shared/modules/clients/api';

vi.mock('@aventur-shared/services/api', () => ({
  apiClient: {
    get: vi.fn(),
  },
}));

describe('getClientHealthScore', () => {
  const mockApiResponse = {
    overall_score: 85,
    goal_scores: {
      '1': {
        goal_id: 1,
        score: 90,
        audit_results: {
          metric1: 'passed',
          metric2: 'failed',
        },
      },
      '2': {
        goal_id: 2,
        score: 80,
        audit_results: {
          metric3: 'passed',
        },
      },
    },
    errors: {
      '1': 'Error in goal 1',
      '2': 'Error in goal 2',
    },
    warnings: {
      '1': {
        '101': 'Warning 101 for goal 1',
        '102': 'Warning 102 for goal 1',
      },
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should fetch and transform health score data correctly', async () => {
    const clientId = 123;
    vi.mocked(apiClient.get).mockResolvedValueOnce(mockApiResponse);

    const result = await getClientHealthScore(clientId);

    // Verify API call
    expect(apiClient.get).toHaveBeenCalledWith(
      `/api/v2/clients/${clientId}/health-score`,
    );
    expect(apiClient.get).toHaveBeenCalledTimes(1);

    // Verify transformed response structure
    expect(result).toEqual({
      overallScore: 85,
      goalScores: {
        1: {
          goalId: 1,
          score: 90,
          auditResults: {
            metric1: 'passed',
            metric2: 'failed',
          },
        },
        2: {
          goalId: 2,
          score: 80,
          auditResults: {
            metric3: 'passed',
          },
        },
      },
      errors: {
        1: 'Error in goal 1',
        2: 'Error in goal 2',
      },
      warnings: {
        1: {
          101: 'Warning 101 for goal 1',
          102: 'Warning 102 for goal 1',
        },
      },
    });
  });

  it('should handle empty response data', async () => {
    const emptyResponse = {
      overall_score: 0,
      goal_scores: {},
      errors: {},
      warnings: {},
    };

    vi.mocked(apiClient.get).mockResolvedValueOnce(emptyResponse);

    const result = await getClientHealthScore(123);

    expect(result).toEqual({
      overallScore: 0,
      goalScores: {},
      errors: {},
      warnings: {},
    });
  });

  it('should throw error when API call fails', async () => {
    const error = new Error('API Error');
    vi.mocked(apiClient.get).mockRejectedValueOnce(error);

    await expect(getClientHealthScore(123)).rejects.toThrow('API Error');
  });

  it('should throw error when response has missing properties', async () => {
    const incompleteResponse = {
      overall_score: 75,
      goal_scores: {
        '1': {
          goal_id: 1,
          score: 75,
          audit_results: {},
        },
      },
      // Missing errors and warnings
    };

    vi.mocked(apiClient.get).mockResolvedValueOnce(incompleteResponse as any);

    await expect(getClientHealthScore(123)).rejects.toThrow(
      'Failed to map health score response',
    );
  });
});
