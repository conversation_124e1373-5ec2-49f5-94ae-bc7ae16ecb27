import { createPinia, setActivePinia } from 'pinia';
import { beforeEach, describe, expect, it } from 'vitest';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { useClientStore } from '@aventur-shared/modules/clients';
import { ClientAddress } from '@aventur-shared/modules/factfind/types/Address';
//

describe('Client Store', () => {
  const address1: ClientAddress = {
    isPrimary: true,
    addressLineOne: 'test',
    addressLineTwo: 'test',
    addressLineThree: 'test',
    addressLineFour: 'test',
    city: 'test',
    postCode: 'test',
    countryId: 1,
  };
  const address2: ClientAddress = {
    isPrimary: true,
    addressLineOne: 'test2',
    addressLineTwo: 'test2',
    addressLineThree: 'test2',
    addressLineFour: 'test2',
    city: 'test2',
    postCode: 'test2',
    countryId: 1,
  };
  const addresses = [address1, address2];
  const firstName = 'test';
  const lastName = 'test';
  const mobileNumber = '123';
  const phoneNumber = '321';
  const dateOfBirth = new DateTime('2020-12-12');

  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('updates clients overview data'),
    () => {
      const { updateClientOverviewData, getProfile } = useClientStore();

      expect(getProfile.addresses).toStrictEqual([]);
      expect(getProfile.firstName).toEqual('');
      expect(getProfile.lastName).toEqual('');
      expect(getProfile.mobileNumber).toEqual(null);
      expect(getProfile.phoneNumber).toEqual(null);

      updateClientOverviewData({
        firstName,
        lastName,
        addresses,
        mobileNumber,
        phoneNumber,
        dateOfBirth: new Date(dateOfBirth.formatForForm()),
      });

      expect(getProfile.addresses).toStrictEqual([addresses]);
      expect(getProfile.firstName).toEqual(firstName);
      expect(getProfile.lastName).toEqual(lastName);
      expect(getProfile.mobileNumber).toEqual(mobileNumber);
      expect(getProfile.phoneNumber).toEqual(phoneNumber);
      expect(getProfile.dateOfBirth).toEqual(dateOfBirth);
    };
});
