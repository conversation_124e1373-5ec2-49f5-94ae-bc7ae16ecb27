import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import StatusLinkList from '@modules/clients/components/client/personal-profile-status/status-link-list.vue';
import {
  Status,
  StatusLinkItem,
} from '@modules/clients/components/client/personal-profile-status/types';

vi.mock('vue-router', () => ({
  useRouter: vi.fn(),
}));

vi.mock('@aventur-shared/modules/users', async () => {
  return {
    useUserAbility: vi.fn().mockReturnValue({
      can: vi.fn().mockReturnValue(true),
    }),
  };
});

function makeWrapper(links: StatusLinkItem[]) {
  return mount(StatusLinkList, {
    props: { links },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            client: {
              getProfile: {
                email: null,
              },
            },
          },
        }),
      ],
    },
  });
}

describe('StatusLinkList Rendering Tests', () => {
  it('renders plain links with no status', () => {
    const linkItems = [
      { title: 'First Link', link: 'abc' },
      { title: 'Second Link', link: 'def' },
    ];
    const wrapper = makeWrapper(linkItems);

    const titles = wrapper.findAll('[data-testid="item-title"]');
    expect(titles.length).toEqual(2);

    const chevrons = wrapper.findAll('[data-testid="chevron-icon"]');
    expect(chevrons.length).toEqual(2);

    expect(wrapper.find('[data-testid="optional-status"]').exists()).toBe(
      false,
    );
  });
  it('renders a single plain link with no status and a detailed link with status', () => {
    const linkItems = [
      { title: 'First Link', link: 'abc' },
      {
        title: 'Second Link',
        link: 'def',
        statusValue: { status: Status.Good, description: 'Good' },
      },
    ];
    const wrapper = makeWrapper(linkItems);

    const titles = wrapper.findAll('[data-testid="item-title"]');
    expect(titles.length).toEqual(2);

    const chevrons = wrapper.findAll('[data-testid="chevron-icon"]');
    expect(chevrons.length).toEqual(2);

    expect(wrapper.find('[data-testid="optional-status"]').exists()).toBe(true);
    const description = wrapper.find('[data-testid="status-description"]');
    expect(description.text()).toEqual('Good');
  });
});
