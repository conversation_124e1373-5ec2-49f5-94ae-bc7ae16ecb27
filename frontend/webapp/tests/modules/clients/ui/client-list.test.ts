/* eslint-disable @typescript-eslint/ban-ts-comment */
import { ref } from 'vue';
import { mount, flushPromises } from '@vue/test-utils';
import { afterEach, describe, it, expect, vi } from 'vitest';
import { createTestingPinia } from '@pinia/testing';
import { apiClient } from '@aventur-shared/services/api';
import ClientList from '@/modules/clients/ui/client-list.vue';
import FiltersPanel from '@modules/ui/filters-panel/filters-panel.vue';
import { UserRole } from '@aventur-shared/modules/auth';

vi.mock('vue-router', async (importOriginal) => {
  const $router = await importOriginal();

  return {
    // @ts-ignore
    ...$router,
    useRouter: vi.fn,
    useRoute: vi.fn(() => {
      return {
        name: undefined,
      };
    }),
    onBeforeRouteUpdate: vi.fn,
    onBeforeRouteLeave: vi.fn,
  };
});

const initialUserId = 1;
vi.mock('@aventur-shared/utils/user', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    isAdvisor: vi.fn().mockReturnValue(Promise.resolve(true)),
  };
});

// Need to mock the table service
const filters = ref({ advisor_id: initialUserId });
vi.mock(
  '@aventur-shared/utils/table/use-table-service',
  async (importOriginal) => {
    const mod = await importOriginal();
    return {
      // @ts-ignore
      ...mod,
      useTableService: vi.fn().mockImplementation(() => {
        const apply = vi.fn().mockReturnValue(Promise.resolve());
        const addFilter = vi.fn().mockImplementation((newFilters) => {
          filters.value = {
            ...filters.value,
            ...newFilters,
          };
          return { apply };
        });
        const changePage = vi.fn((page) => {
          return { addFilter, apply };
        });
        const applyFilterRules = vi.fn();
        const resetFilters = vi.fn((resetFilters) => {
          filters.value = {
            advisor_id: initialUserId,
          };
          return { changePage, addFilter, apply };
        });

        return {
          changePage,
          addFilter,
          applyFilterRules,
          resetFilters,
          page: ref(1),
          filters: filters,
          setTotalItems: vi.fn(),
          config: { totalItems: 999 },
        };
      }),
    };
  },
);

function makeWrapper() {
  return mount(ClientList, {
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            users: {
              user: { id: initialUserId, groups: [UserRole.Adviser] },
            },
          },
        }),
      ],
    },
  });
}

const mockJarvisGetTasksResponse = { clients: [], total_client_count: 0 };

vi.spyOn(apiClient, 'get').mockResolvedValue(mockJarvisGetTasksResponse);

afterEach(() => {
  filters.value = { advisor_id: initialUserId };
  vi.clearAllMocks();
});

describe('Structure', () => {
  it('Renders the filters panel', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    const filtersPanelWrapper = wrapper.findComponent(FiltersPanel);
    expect(filtersPanelWrapper.exists()).toBe(true);

    await filtersPanelWrapper.findByTestId('filters-toggle')?.trigger('click');
    expect(filtersPanelWrapper.findByTestId('filters-apply')).not.toBeNull();
    expect(filtersPanelWrapper.findByTestId('filters-reset')).not.toBeNull();
    await filtersPanelWrapper.findByTestId('filters-apply').trigger('click');

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith(
      '/api/internal/v1/clients/',
      {
        advisor_id: initialUserId,
        page: 1,
        results_per_page: 10,
      },
    );
  });
});

describe('API call tests', () => {
  it('Makes the expected call on page load', async () => {
    makeWrapper();
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledWith('/api/internal/v1/clients/', {
      advisor_id: initialUserId,
      page: 1,
      results_per_page: 10,
    });
  });
  it('Makes the expected call when client is provided', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // Emit an event to simulate the assignee being selected
    //@ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:apply', {
      client_id: 1,
    });
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith(
      '/api/internal/v1/clients/',
      {
        advisor_id: initialUserId,
        client_id: 1,
        page: 1,
        results_per_page: 10,
      },
    );
  });
  it('Makes the expected call when type is selected', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // Emit an event to simulate the type being selected
    //@ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:apply', {
      type_id: 1,
    });
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith(
      '/api/internal/v1/clients/',
      {
        advisor_id: initialUserId,
        type_id: 1,
        page: 1,
        results_per_page: 10,
      },
    );
  });
  // This one behaves weirdly so skipping for now
  it.todo('Makes the expected call when status is selected', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // Emit an event to simulate the status being selected
    //@ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:apply', {
      status_id: 1,
    });
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith(
      '/api/internal/v1/clients/',
      {
        advisor_id: initialUserId,
        status_id: 1,
        page: 1,
        results_per_page: 10,
      },
    );
  });
  it('Makes the expected call when the filters are reset', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    // @ts-ignore
    await wrapper.vm.$refs.filtersPanel.$emit('filters:reset');
    await wrapper.vm.$nextTick(); // Wait until $emit has been handled
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenLastCalledWith(
      '/api/internal/v1/clients/',
      {
        advisor_id: initialUserId,
        page: 1,
        results_per_page: 10,
      },
    );
  });
});
