import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTesting<PERSON>inia } from '@pinia/testing';

import {
  createAccount,
  createCase,
  createTask,
  createGoal,
} from '@tests/helpers';

import { apiClient } from '@aventur-shared/services/api';
import { Case, CaseId } from '@aventur-shared/modules/cases';
import { Goal } from '@aventur-shared/modules/goals';
import { TaskGoal, TaskTypeEnum } from '@aventur-shared/modules/tasks';
import { AccountStatusEnum } from '@aventur-shared/modules/accounts';

import { AdviceRow } from '@/modules/clients-cases/ui';
import * as actionMod from '@modules/sub-tasks/use-cases/user-accepts-advice-for-account/action';
import { AcceptanceFromClientForm } from '@modules/sub-tasks/use-cases/user-accepts-advice-for-account/ui/form';
import AcceptanceFromClientModal from '@modules/sub-tasks/use-cases/user-accepts-advice-for-account/ui/acceptance-from-client-modal.vue';

type Props = {
  caseId: CaseId;
  goal: TaskGoal;
  account: Goal['accounts'][0];
  isModifiable: boolean;
  caseAdvisorId: Case['relatedAdvisor']['id'];
  type: 'Existing' | 'Proposed';
};

const account = createAccount({
  status: {
    id: AccountStatusEnum.Proposed,
    name: 'Proposed',
  },
  advices: [
    {
      id: 1,
      description: 'test',
      isAccepted: false,
      isImplemented: false,
      type: 1,
    },
    {
      id: 2,
      description: 'test2',
      isAccepted: false,
      isImplemented: false,
      type: 1,
    },
  ],
});
const goal = createGoal({
  accounts: [account],
});
const task = createTask({
  type: TaskTypeEnum.CreateAdvice,
});
const clientCase = createCase({
  goals: [goal],
  tasks: [task],
});

const props: Props = {
  caseId: clientCase.id,
  goal: {
    taskSlug: task.slug,
    ...goal,
    ...task['subTasks'][0],
  },
  account,
  isModifiable: true,
  caseAdvisorId: clientCase.relatedAdvisor.id,
  type: 'Existing',
};

vi.spyOn(apiClient, 'patch').mockResolvedValue(undefined);
const mockAction = vi.spyOn(actionMod, 'action');

function makeWrapper() {
  return mount(AcceptanceFromClientModal, {
    props,
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Task item acceptance from client modal', () => {
  it('renders component', () => {
    const wrapper = makeWrapper();
    const h1 = wrapper.find('h1');
    expect(h1.exists()).toBe(true);
    expect(h1.text()).toBe(`${props.type} account advice acceptance`);
  });

  it('renders buttons', () => {
    const wrapper = makeWrapper();
    const buttons = wrapper.findAll('button');
    expect(buttons[0].text()).toBe('Save and confirm advice');
    expect(buttons[1].text()).toBe('Cancel and close');
  });

  it('renders advices', () => {
    const wrapper = makeWrapper();
    const adviceRows = wrapper.findAllComponents(AdviceRow);
    expect(adviceRows.length).toBe(props.account.advices.length);
  });

  it('submits the form', async () => {
    const wrapper = makeWrapper();
    const form = wrapper.findComponent(AcceptanceFromClientForm);
    form.vm.$emit('on-submit', {
      advices: [
        {
          id: 1,
          description: '',
          isAccepted: true,
          type: 1,
        },
      ],
    });

    expect(mockAction).toHaveBeenCalledTimes(1);
    expect(apiClient.patch).toHaveBeenCalledTimes(1);
    expect(apiClient.patch).toHaveBeenCalledWith(
      `/api/v2/case/${props.caseId}/acceptance_from_client/${props.goal.taskId}`,
      {
        command: 'updateAdviceAcceptance',
        payload: {
          account_id: props.account.id,
          advice: [{ id: 1, is_accepted: true }],
        },
      },
    );
  });
});
