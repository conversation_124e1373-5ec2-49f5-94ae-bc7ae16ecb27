import { describe, expect, it } from 'vitest';
import { Match } from '@/domain/match.model';

describe('Match', () => {
  it('creates valid match', () => {
    const createMatch = () => new Match(1, 2);
    expect(createMatch).not.toThrowError('Wrong payment set error.');
    expect(createMatch).not.toThrowError('Wrong statement set error.');
  });

  it('returns statement id', () => {
    const statementId = 123;
    const match = new Match(1, statementId);

    expect(match.getStatementId()).toBe(123);
  });

  it('returns payment id', () => {
    const paymentId = 123;
    const match = new Match(paymentId, 99);

    expect(match.getPaymentId()).toBe(123);
  });
});
