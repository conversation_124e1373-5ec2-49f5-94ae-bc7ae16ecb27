import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import ClientGoals from '@/pages/client-page-by-id/goals/client-goals.vue';

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => {
    return {
      params: {
        id: '2',
      },
    };
  }),
}));

vi.mock('@aventur-shared/modules/clients/api', () => {
  return {
    getClientHealthScore: vi.fn().mockResolvedValue({
      overallScore: 75,
      goalScores: {
        59: { score: 80, auditResults: {} },
        57: { score: 70, auditResults: {} },
      },
      errors: {},
      warnings: {},
    }),
  };
});

describe('ClientGoals', () => {
  function createWrapper() {
    return mount(ClientGoals, {
      global: {
        plugins: [
          createTestingPinia({
            createSpy: vi.fn,
            initialState: {
              client: {
                goals: [
                  {
                    id: 59,
                    goalTypeId: 1,
                    name: 'Emergency Fund',
                    objectives: null,
                    attributes: {
                      type: 12,
                    },
                    cases: [],
                    clientIds: [2],
                    linkedHoldings: [
                      {
                        id: 42,
                        accountNumber: 'gdfg345t',
                        providerName: 'Ipswich Building Society',
                        productTypeName: 'Bank Account - Savings Account',
                      },
                    ],
                    riskProfile: null,
                    cashForecast: null,
                  },
                  {
                    id: 57,
                    goalTypeId: 10,
                    name: 'Save for a new guitar',
                    objectives: 'Save for a new guitar',
                    attributes: {
                      targetAmount: 10000,
                      targetDate: '2026-01-01',
                      type: 1,
                    },
                    cases: [],
                    clientIds: [2],
                    linkedHoldings: [
                      {
                        id: 42,
                        accountNumber: 'gdfg345t',
                        providerName: 'Ipswich Building Society',
                        productTypeName: 'Bank Account - Savings Account',
                      },
                      {
                        id: 45,
                        accountNumber: 'fds23456342',
                        providerName: 'Willis Towers Watson',
                        productTypeName: 'ISA (stocks & shares)',
                      },
                    ],
                    riskProfile: null,
                    cashForecast: null,
                  },
                  {
                    id: 58,
                    goalTypeId: 10,
                    name: 'Buy a Porsche 911',
                    objectives: 'Buy a Porsche 911',
                    attributes: {
                      targetAmount: 10000,
                      targetDate: '2026-01-01',
                      type: 1,
                    },
                    cases: [],
                    clientIds: [2],
                    linkedHoldings: [
                      {
                        id: 42,
                        accountNumber: 'gdfg345t',
                        providerName: 'Ipswich Building Society',
                        productTypeName: 'Bank Account - Savings Account',
                      },
                      {
                        id: 45,
                        accountNumber: 'fds23456342',
                        providerName: 'Willis Towers Watson',
                        productTypeName: 'ISA (stocks & shares)',
                      },
                    ],
                    riskProfile: null,
                    cashForecast: null,
                  },
                  {
                    id: 71,
                    goalTypeId: 10,
                    name: 'Save for a new TV',
                    objectives: 'Save for a new TV',
                    attributes: {
                      targetAmount: 10000,
                      targetDate: '2026-01-01',
                      type: 1,
                    },
                    cases: [],
                    clientIds: [2],
                    linkedHoldings: [
                      {
                        id: 42,
                        accountNumber: 'gdfg345t',
                        providerName: 'Ipswich Building Society',
                        productTypeName: 'Bank Account - Savings Account',
                      },
                      {
                        id: 45,
                        accountNumber: 'fds23456342',
                        providerName: 'Willis Towers Watson',
                        productTypeName: 'ISA (stocks & shares)',
                      },
                    ],
                    riskProfile: null,
                    cashForecast: null,
                  },
                  {
                    id: 60,
                    goalTypeId: 6,
                    name: 'Estate Planning & Wills',
                    objectives: 'Main residence downpayment',
                    attributes: {
                      targetAmount: 99000,
                      targetDate: '2027-01-01',
                      type: 2,
                    },
                    cases: [],
                    clientIds: [2],
                    linkedHoldings: [],
                    riskProfile: null,
                    cashForecast: null,
                  },
                ],
              },
              refdata: {
                goals: [
                  { id: 1, name: 'Protection' },
                  { id: 6, name: 'Estate Planning' },
                  { id: 10, name: 'Savings' },
                ],
              },
            },
          }),
        ],
      },
    });
  }

  it('renders the overall score section', async () => {
    const wrapper = createWrapper();

    expect(wrapper.text()).toContain('Overall Score');
    expect(wrapper.findComponent({ name: 'score-donut' }).exists()).toBe(true);
  });

  it('groups goals by goal type in sub component', async () => {
    const wrapper = createWrapper();

    // Wait for any async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 0));

    const goalTypeGroups = wrapper.findAllComponents({
      name: 'goal-type-group',
    });

    // We expect 3 groups based on our test data (Protection, Estate Planning, and Savings)
    expect(goalTypeGroups.length).toBe(3);

    // Test the content of each group
    const groupNames = goalTypeGroups.map((group) => group.props('goalName'));
    expect(groupNames).toContain('Protection');
    expect(groupNames).toContain('Estate Planning');
    expect(groupNames).toContain('Savings');
  });

  it('groups goals by their type', async () => {
    const wrapper = createWrapper();
    await wrapper.vm.$nextTick();

    const vm = wrapper.vm as any;
    const groupedGoals = vm.goalsWithHealthScores;

    // Check if goals are grouped correctly
    expect(Object.keys(groupedGoals).length).toBe(3);
    expect(groupedGoals[1]).toBeDefined(); // Protection goals
    expect(groupedGoals[6]).toBeDefined(); // Estate Planning goals
    expect(groupedGoals[10]).toBeDefined(); // Savings goals

    // Check specific goals in each group
    expect(groupedGoals[1][0].name).toBe('Emergency Fund');
    expect(groupedGoals[10][0].name).toBe('Save for a new guitar');
    expect(groupedGoals[10][1].name).toBe('Buy a Porsche 911');
    expect(groupedGoals[10][2].name).toBe('Save for a new TV');
    expect(groupedGoals[6][0].name).toBe('Estate Planning & Wills');
  });

  it('correctly finds goal names by ID', async () => {
    const wrapper = createWrapper();

    const vm = wrapper.vm as any;
    expect(vm.findGoalNameById(10)).toBe('Savings');
    expect(vm.findGoalNameById(999)).toBe('Unknown Goal');
  });

  it('computes goals with health scores correctly', async () => {
    const wrapper = createWrapper();
    await wrapper.vm.$nextTick();

    const vm = wrapper.vm as any;
    const goalsWithScores = vm.goalsWithHealthScores;

    expect(goalsWithScores[1]).toBeDefined(); // Emergency Fund goal type
    expect(goalsWithScores[10]).toBeDefined(); // Savings goal type
    expect(goalsWithScores[1][0].healthScore.score).toBe(80);
  });

  it('handles missing health scores gracefully', async () => {
    const wrapper = createWrapper();
    await wrapper.vm.$nextTick();

    const vm = wrapper.vm as any;
    const goalsWithScores = vm.goalsWithHealthScores;

    // Check goals without explicit health scores get default values
    expect(goalsWithScores[6][0].healthScore.score).toBe(0);
    expect(goalsWithScores[6][0].healthScore.auditResults).toEqual({});
  });
});
