import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import GoalTypeGroup from '@/pages/client-page-by-id/goals/goal-type-group.vue';
import ScoreDonut from '@aventur-shared/components/score-donut.vue';
import Alert from '@aventur-shared/components/Alert.vue';

// Mock the components we don't want to test
vi.mock('./score-donut.vue', () => ({
  default: {
    name: 'ScoreDonut',
    props: ['score'],
    template: '<div data-testid="score-donut">{{ score }}</div>',
  },
}));

describe('GoalTypeGroup', () => {
  const mockGoals = [
    {
      id: 1,
      name: 'Test Goal 1',
      healthScore: {
        score: 85,
        auditResults: {
          risk_score: 90,
          performance_score: 80,
        },
      },
      errors: null,
      warnings: {},
      linkedHoldings: [],
    },
    {
      id: 2,
      name: 'Test Goal 2',
      healthScore: {
        score: 75,
        auditResults: {
          risk_score: 70,
          performance_score: 80,
        },
      },
      errors: 'Some error message',
      warnings: {
        '123': 'Warning message',
      },
      linkedHoldings: [
        {
          id: 123,
          providerName: 'Test Provider',
          accountNumber: 'ACC123',
        },
      ],
    },
  ];

  const createWrapper = (props = {}) => {
    return mount(GoalTypeGroup, {
      props: {
        goalName: 'Test Goals',
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        goals: mockGoals,
        ...props,
      },
      global: {
        stubs: {
          Alert: true,
        },
      },
    });
  };

  it('renders the goal name correctly', () => {
    const wrapper = createWrapper();
    expect(wrapper.find('[data-testid="goal-type-header"]').text()).toBe(
      'Test Goals',
    );
  });

  it('renders all goals in the list', () => {
    const wrapper = createWrapper();
    const goals = wrapper.findAll('[data-testid="goal-item"]');
    expect(goals).toHaveLength(2);
    expect(goals[0].text()).toContain('Test Goal 1');
    expect(goals[1].text()).toContain('Test Goal 2');
  });

  it('displays score donut for each goal', () => {
    const wrapper = createWrapper();
    const scoreDonuts = wrapper.findAllComponents(ScoreDonut);
    expect(scoreDonuts).toHaveLength(2);
    expect(scoreDonuts[0].props('score')).toBe(85);
    expect(scoreDonuts[1].props('score')).toBe(75);
  });

  it('shows error alert when goal has errors', async () => {
    const wrapper = createWrapper();
    // Click to expand the second goal which has errors
    await wrapper
      .findAll('[data-testid="goal-disclosure-button"]')[1]
      .trigger('click');

    const alert = wrapper.findComponent(Alert);
    expect(alert.exists()).toBe(true);
    expect(alert.props('type')).toBe('error');
    expect(alert.props('message')).toBe('Some error message');
  });

  it('shows warning alerts when goal has warnings', async () => {
    const wrapper = createWrapper();
    // Click to expand the second goal which has warnings
    await wrapper
      .findAll('[data-testid="goal-disclosure-button"]')[1]
      .trigger('click');

    const warningAlerts = wrapper.findAll('[data-testid="goal-warning"]');
    expect(warningAlerts).toHaveLength(1);
  });

  it('formats warning messages correctly with holding information', async () => {
    const wrapper = createWrapper();
    // Click to expand the second goal which has warnings
    await wrapper
      .findAll('[data-testid="goal-disclosure-button"]')[1]
      .trigger('click');

    const warningAlert = wrapper.findComponent('[data-testid="goal-warning"]');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(warningAlert.props('message')).toBe(
      'Test Provider - ACC123: Warning message',
    );
  });

  it('displays audit results in table format when available', async () => {
    const wrapper = createWrapper();
    // Click to expand the first goal
    await wrapper
      .findAll('[data-testid="goal-disclosure-button"]')[0]
      .trigger('click');

    const table = wrapper.find('[data-testid="audit-table"]');
    expect(table.exists()).toBe(true);

    const rows = wrapper.findAll('tbody tr');
    expect(rows).toHaveLength(2);

    // Check if metrics are properly formatted
    expect(rows[0].findAll('td')[0].text()).toBe('Risk Score');
    expect(rows[0].findAll('td')[1].text()).toBe('90');

    expect(rows[1].findAll('td')[0].text()).toBe('Performance Score');
    expect(rows[1].findAll('td')[1].text()).toBe('80');
  });

  it('toggles disclosure panel when clicking the button', async () => {
    const wrapper = createWrapper();
    const button = wrapper.find('button');

    // Initially panel should be hidden
    expect(wrapper.find('[data-headlessui-state="open"]').exists()).toBe(false);

    // Click to open
    await button.trigger('click');
    expect(wrapper.find('[data-headlessui-state="open"]').exists()).toBe(true);

    // Click to close
    await button.trigger('click');
    expect(wrapper.find('[data-headlessui-state="open"]').exists()).toBe(false);
  });
});
