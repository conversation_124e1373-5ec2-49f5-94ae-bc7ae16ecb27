/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { apiClient } from '@aventur-shared/services/api';
import { Client, ClientId } from '@aventur-shared/modules/clients';
import {
  ClientAdvisor,
  ClientTypeEnum,
} from '@aventur-shared/modules/clients/models';
import { BoxTitle } from '@/modules/ui';
import { default as Marketing } from '@/pages/client-page-by-id/factfind/marketing.vue';

vi.spyOn(apiClient, 'get').mockResolvedValue(undefined);
vi.mock('vue-router', async () => {
  const $router = await vi.importActual('vue-router');

  return {
    // @ts-ignore
    ...$router,
    onBeforeRouteLeave: vi.fn,
  };
});

const getClient = (params?: object): Client => ({
  id: 1 as ClientId,
  type: '',
  email: null,
  noEmailReasonId: null,
  firstName: '',
  lastName: null,
  title: 1,
  genderId: null,
  dateOfBirth: null,
  nationalityId: null,
  birthCountryId: null,
  primaryCountryId: null,
  secondaryCountryId: null,
  maritalStatusId: null,
  phoneNumber: null,
  mobileNumber: null,
  addresses: [],
  linkedClients: [],
  advisor: {} as ClientAdvisor,
  clientType: ClientTypeEnum.Individual,
  clientStatus: 1,
  clientSource: 1,
  reviewFrequency: null,
  reviewMonth: null,
  nextReviewMonth: null,
  marketingId: null,
  accessEnabled: null,
  clientAgreementId: null,
  privacyNoticeId: null,
  ...(params || {}),
});

const client = getClient();

function makeWrapper() {
  return mount(Marketing, {
    props: {
      client: getClient(),
      clientId: client.id,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Marketing', () => {
  it('renders component and makes API calls', async () => {
    const wrapper = makeWrapper();
    const boxTitle = wrapper.findComponent(BoxTitle);
    const h3 = boxTitle.find('h3');

    expect(boxTitle.exists()).toBe(true);
    expect(h3.text()).toBe(`Marketing Preferences`);

    await flushPromises();
    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenCalledWith(
      `/api/internal/v1/marketing/subscription-lists`,
    );
    expect(apiClient.get).toHaveBeenCalledWith(
      `/api/internal/v1/clients/${client.id}/marketing/contact`,
    );
  });
});
