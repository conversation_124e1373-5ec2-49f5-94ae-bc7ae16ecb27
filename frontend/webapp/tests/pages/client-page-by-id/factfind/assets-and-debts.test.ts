/* eslint-disable @typescript-eslint/ban-ts-comment */
import { afterEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { createClient } from '@tests/helpers';
import { apiClient } from '@aventur-shared/services/api';
import Toggle from '@aventur-shared/components/Toggle.vue';
import { BoxTitle } from '@/modules/ui';
import { default as AssetsDebts } from '@/pages/client-page-by-id/factfind/assets-and-debts.vue';

const client = createClient();

const refdata = {
  providers: [{ id: 1, name: 'Provider 1', is_active: null }],
  productTypeGroups: [
    {
      id: 6,
      name: 'Bank & Building Society',
      asset_or_debt: 'asset',
      compatible_advice_types: [],
    },
    {
      id: 11,
      name: 'Mortgage',
      asset_or_debt: 'debt',
      compatible_advice_types: [],
    },
  ],
  products: [
    {
      id: 5,
      name: 'Bank Account - Current Account',
      product_type_group_id: 6,
      type: 'account',
    },
    { id: 67, name: 'Mortgage', product_type_group_id: 11, type: 'mortgage' },
  ],
};

vi.mock('vue-router', async () => {
  const $router = await vi.importActual('vue-router');

  return {
    // @ts-ignore
    ...$router,
    onBeforeRouteLeave: vi.fn,
  };
});

function makeWrapper() {
  return mount(AssetsDebts, {
    props: {
      client,
      clientId: client.id,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            refdata,
          },
        }),
      ],
    },
  });
}

afterEach(() => {
  vi.resetAllMocks();
});

describe('Assets & Debts', () => {
  it('renders component and makes API calls', async () => {
    vi.spyOn(apiClient, 'get').mockResolvedValue({
      assets: [],
      debts: [],
    });
    const wrapper = makeWrapper();
    const boxTitles = wrapper.findAllComponents(BoxTitle);
    const h3asset = boxTitles[0].find('h3');
    const h3debt = boxTitles[1].find('h3');

    expect(boxTitles.length).toBe(2);
    expect(h3asset.text()).toBe(`Your Assets`);
    expect(h3debt.text()).toBe(`Your Debts`);

    await flushPromises();
    expect(apiClient.get).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledWith(
      `/api/v1/clients/${client.id}/factfind/assets-and-debts`,
    );

    const assets = wrapper.findByTestId('assets');
    expect(
      wrapper.findElementByText('[data-testid=assets] span', 'Showing 0 of 0'),
    ).to.exist;
    expect(assets.getComponent(Toggle).props('label')).toEqual(
      'Include Inactive',
    );
    expect(assets.getComponent(Toggle).props('disabled')).toBe(true);

    const debts = wrapper.findByTestId('debts');
    expect(
      wrapper.findElementByText('[data-testid=debts] span', 'Showing 0 of 0'),
    ).to.exist;
    expect(debts.getComponent(Toggle).props('label')).toEqual(
      'Include Inactive',
    );
    expect(debts.getComponent(Toggle).props('disabled')).toBe(true);
  });

  it('displays filters correctly', async () => {
    vi.spyOn(apiClient, 'get').mockResolvedValue({
      assets: [
        {
          id: 1,
          client_ids: [client.id],
          group_id: 6,
          type_id: 5,
          status_id: 3,
          current_quantity: 1,
          provider_id: 1,
          account_number: '111-111111',
          sub_account_number: null,
          valuation: null,
          attributes: {},
        },
        {
          id: 2,
          client_ids: [client.id],
          group_id: 6,
          type_id: 5,
          status_id: 3,
          current_quantity: 0,
          provider_id: 1,
          account_number: '222-222222',
          sub_account_number: null,
          valuation: null,
          attributes: {},
        },
      ],
      debts: [
        {
          id: 3,
          client_ids: [client.id],
          group_id: 11,
          type_id: 67,
          status_id: 3,
          current_quantity: 1,
          provider_id: 1,
          account_number: '888-888888',
          valuation: null,
          attributes: {
            mortgage_adviser_id: null,
            secured_against_address: null,
            mortgage_end_date: null,
            interest_rate: null,
            mortgage_product_end_date: null,
            monthly_payment: null,
          },
        },
      ],
    });

    const wrapper = makeWrapper();
    await flushPromises();

    const assets = wrapper.findByTestId('assets');
    expect(
      wrapper.findElementByText('[data-testid=assets] span', 'Showing 1 of 2'),
    ).to.exist;
    expect(assets.getComponent(Toggle).props('label')).toEqual(
      'Include Inactive',
    );
    expect(assets.getComponent(Toggle).props('disabled')).toBe(false);

    const debts = wrapper.findByTestId('debts');
    expect(
      wrapper.findElementByText('[data-testid=debts] span', 'Showing 1 of 1'),
    ).to.exist;
    expect(debts.getComponent(Toggle).props('label')).toEqual(
      'Include Inactive',
    );
    expect(debts.getComponent(Toggle).props('disabled')).toBe(true);
  });

  it('applies filters correctly', async () => {
    vi.spyOn(apiClient, 'get').mockResolvedValue({
      assets: [
        {
          id: 1,
          client_ids: [client.id],
          group_id: 6,
          type_id: 5,
          status_id: 3,
          current_quantity: 1,
          provider_id: 1,
          account_number: '111-111111',
          sub_account_number: null,
          valuation: null,
          attributes: {},
        },
        {
          id: 2,
          client_ids: [client.id],
          group_id: 6,
          type_id: 5,
          status_id: 3,
          current_quantity: 0,
          provider_id: 1,
          account_number: '222-222222',
          sub_account_number: null,
          valuation: null,
          attributes: {},
        },
      ],
      debts: [
        {
          id: 3,
          client_ids: [client.id],
          group_id: 11,
          type_id: 67,
          status_id: 3,
          current_quantity: 1,
          provider_id: 1,
          account_number: '888-888888',
          valuation: null,
          attributes: {
            mortgage_adviser_id: null,
            secured_against_address: null,
            mortgage_end_date: null,
            interest_rate: null,
            mortgage_product_end_date: null,
            monthly_payment: null,
          },
        },
        {
          id: 4,
          client_ids: [client.id],
          group_id: 11,
          type_id: 67,
          status_id: 3,
          current_quantity: 0,
          provider_id: 1,
          account_number: '999-999999',
          valuation: null,
          attributes: {
            mortgage_adviser_id: null,
            secured_against_address: null,
            mortgage_end_date: null,
            interest_rate: null,
            mortgage_product_end_date: null,
            monthly_payment: null,
          },
        },
      ],
    });

    const wrapper = makeWrapper();
    await flushPromises();

    const assets = wrapper.findByTestId('assets');
    assets.getComponent(Toggle).vm.$emit('update:modelValue', true);
    await flushPromises();
    expect(
      wrapper.findElementByText('[data-testid=assets] span', 'Showing 2 of 2'),
    ).to.exist;

    const debts = wrapper.findByTestId('debts');
    debts.getComponent(Toggle).vm.$emit('update:modelValue', true);
    await flushPromises();
    expect(
      wrapper.findElementByText('[data-testid=debts] span', 'Showing 2 of 2'),
    ).to.exist;
  });
});
