/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { VueWrapper, flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { directive as tippyDirective } from 'vue-tippy';

import { apiClient } from '@aventur-shared/services/api';
import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';

import { createClient } from '@tests/helpers';
import { default as Goals } from '@/pages/client-page-by-id/factfind/goals.vue';
import GoalList from '@modules/factfind/ui/goals/GoalList.vue';
import GoalItem from '@modules/factfind/ui/goals/GoalItem.vue';
import GoalTypeGroup from '@modules/factfind/ui/goals/GoalTypeGroup.vue';
//

vi.spyOn(apiClient, 'get').mockResolvedValue(undefined);
vi.mock('vue-router', async () => {
  const $router = await vi.importActual('vue-router');

  return {
    // @ts-ignore
    ...$router,
    onBeforeRouteLeave: vi.fn,
  };
});

const client = createClient();

const clientStore = {
  client_id: client.id,
  profile: {
    id: client.id,
  },
  goals: [],
  holdings: [],
};

const refDataStore = {
  goals: [
    {
      id: ClientGoalTypeEnum.Retirement,
      name: 'Retirement',
      code: 'RE',
      is_default: true,
      description: 'Retirement goal description',
      client_goal: true,
    },
    {
      id: ClientGoalTypeEnum.BuildWealth,
      name: 'Build Wealth',
      code: 'BW',
      is_default: false,
      description: 'BuildWealth goal description',
      client_goal: true,
    },
    {
      id: ClientGoalTypeEnum.EmergencyFund,
      name: 'Emergency Fund',
      code: 'EF',
      is_default: false,
      description: 'EmergencyFund goal description',
      client_goal: true,
    },
  ],
};

function makeWrapper() {
  return mount(Goals, {
    props: {
      client: client,
      clientId: client.id,
    },
    global: {
      directives: {
        tippy: tippyDirective,
      },
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
          initialState: {
            refdata: refDataStore,
            client: clientStore,
          },
        }),
      ],
      components: {
        GoalList: GoalList,
      },
    },
  });
}

const mockJarvisGetTasksResponse = [
  {
    goal_id: 1,
    goal_name: 'Build Wealth',
    goal_objectives: 'BuildWealth goal objectives',
    client_ids: [client.id],
    open_cases: [],
    attributes: {
      target_amount: null,
      target_date: null,
      type_: ClientGoalTypeEnum.BuildWealth,
    },
    linked_holdings: [],
    risk_profile: null,
    cash_forecast: null,
  },
  {
    goal_id: 2,
    goal_name: 'Retirement',
    goal_objectives: 'Retirement goal objectives',
    client_ids: [client.id],
    open_cases: [1],
    attributes: {
      target_amount: 10_000,
      target_date: '01-01-2030',
      type_: ClientGoalTypeEnum.Retirement,
    },
    linked_holdings: [
      {
        holding_id: 1,
        account_number: '999999',
        provider_name: 'Abbey Life',
        product_type: 'ISA (stocks & shares)',
      },
    ],
    risk_profile: null,
    cash_forecast: null,
  },
];

vi.spyOn(apiClient, 'get').mockResolvedValue(mockJarvisGetTasksResponse);

describe('Goals', () => {
  it('renders component and makes API calls', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(apiClient.get).toHaveBeenCalledTimes(2);
    expect(apiClient.get).toHaveBeenNthCalledWith(
      1,
      `/api/v1/clients/${client.id}/factfind/goals`,
    );
    expect(apiClient.get).toHaveBeenNthCalledWith(
      2,
      `/api/internal/v1/active-holdings?client_ids=${client.id}`,
    );

    expect(wrapper.findElementByText('h3', 'Your Goals')).to.exist;
    expect(wrapper.findElementByText('span', 'Showing 2 of 2')).to.exist;
    expect(wrapper.findAllComponents(GoalTypeGroup)).to.be.lengthOf(3);

    // Retirement
    const button_Retirement = wrapper.find(
      `button[data-group-id="${ClientGoalTypeEnum.Retirement}"]`,
    );
    expect(button_Retirement.find('h1').text()).to.contain('Retirement');
    expect(
      button_Retirement.find('[data-group-total-items]').text(),
    ).to.contain('1 goal');

    // Build Wealth
    const button_BuildWealth = wrapper.find(
      `button[data-group-id="${ClientGoalTypeEnum.BuildWealth}"]`,
    );
    expect(button_BuildWealth.find('h1').text()).to.contain('Build Wealth');
    expect(
      button_BuildWealth.find('[data-group-total-items]').text(),
    ).to.contain('1 goal');

    // Emergency Fund
    const button_EmergencyFund = wrapper.find(
      `button[data-group-id="${ClientGoalTypeEnum.EmergencyFund}"]`,
    );
    expect(button_EmergencyFund.find('h1').text()).to.contain('Emergency Fund');
    expect(
      button_EmergencyFund.find('[data-group-total-items]').text(),
    ).to.contain('0 goal');
  });

  it('individual groups renders correct goal items', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    let goalItemWrapper: VueWrapper;

    for (const b of wrapper.findAll(`button[data-group-id]`)) {
      await b.trigger('click');
    }

    const goalItems = wrapper.findAllComponents(GoalItem);
    expect(goalItems).to.lengthOf(2);

    // Retirement
    goalItemWrapper = goalItems[0];
    expect(goalItemWrapper.findByTestId('goal-item-header').text()).to.match(
      /Retirement\s+Target amount:\s+£10,000.00\s+Target date:\s+1 January 2030/,
    );
    expect(goalItemWrapper.find('textarea').element.value).to.be.eq(
      'Retirement goal objectives',
    );
    expect(goalItemWrapper.findByTestId('goal-item-footer').text()).to.match(
      /Clients:\s+1\s+Open cases:\s+1\s+Linked assets:\s+1\s+Risk profile:\s+(-)\s+Cash forecast:\s+(-)/,
    );

    // BuildWealth
    goalItemWrapper = goalItems[1];
    expect(goalItemWrapper.findByTestId('goal-item-header').text()).to.match(
      /Build Wealth\s+Target amount:\s+N\/A\s+Target date:\s+N\/A/,
    );
    expect(goalItemWrapper.find('textarea').element.value).to.be.eq(
      'BuildWealth goal objectives',
    );
    expect(goalItemWrapper.findByTestId('goal-item-footer').text()).to.match(
      /Clients:\s+1\s+Open cases:\s+0\s+Linked assets:\s+0\s+Risk profile:\s+(-)\s+Cash forecast:\s+(-)/,
    );

    // EmergencyFund
    expect(
      wrapper
        .findComponent(
          `button[data-group-id="${ClientGoalTypeEnum.EmergencyFund}"]`,
        )
        .findAllComponents(GoalItem),
    ).to.be.empty;
  });
});
