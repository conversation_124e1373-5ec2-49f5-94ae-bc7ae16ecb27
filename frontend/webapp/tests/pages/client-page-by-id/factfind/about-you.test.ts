/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { apiClient } from '@aventur-shared/services/api';
import { BoxTitle } from '@/modules/ui';
import { createClients } from '@tests/helpers';

import { default as AboutYou } from '@/pages/client-page-by-id/factfind/about-you.vue';
import {
  PersonalDetailsSection,
  ContactDetailsSection,
  FamilyMembersSection,
  FurtherInformationsSection,
  RetirementDetailsSection,
} from '@modules/factfind/use-cases/edit-about-you';

vi.spyOn(apiClient, 'get').mockResolvedValue(undefined);
vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
vi.mock('vue-router', async () => {
  const $router = await vi.importActual('vue-router');

  return {
    // @ts-ignore
    ...$router,
    onBeforeRouteLeave: vi.fn,
  };
});

const client = createClients()[0];

function makeWrapper() {
  return mount(AboutYou, {
    props: {
      client: client,
      clientId: client.id,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('AboutYou', () => {
  it('renders component and sections and makes API calls', async () => {
    const wrapper = makeWrapper();
    const boxTitle = wrapper.findComponent(BoxTitle);
    const h3 = boxTitle.find('h3');

    expect(boxTitle.exists()).toBe(true);
    expect(h3.text()).toBe(`Personal details`);

    const personalDetailsSection = wrapper.findComponent(
      PersonalDetailsSection,
    );
    const contactDetailsSection = wrapper.findComponent(ContactDetailsSection);
    const familyMembersSection = wrapper.findComponent(FamilyMembersSection);
    const furtherInformationsSection = wrapper.findComponent(
      FurtherInformationsSection,
    );
    const retirementDetailsSection = wrapper.findComponent(
      RetirementDetailsSection,
    );

    expect(personalDetailsSection.exists()).toBe(true);
    expect(contactDetailsSection.exists()).toBe(true);
    expect(familyMembersSection.exists()).toBe(true);
    expect(furtherInformationsSection.exists()).toBe(true);
    expect(retirementDetailsSection.exists()).toBe(true);

    await flushPromises();
    expect(apiClient.get).toHaveBeenCalledTimes(1);
    expect(apiClient.get).toHaveBeenCalledWith(
      `/api/v1/clients/${client.id}/factfind/primary-details`,
    );
  });
});
