/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { apiClient } from '@aventur-shared/services/api';
import { NotesList } from '@modules/factfind/ui/notes';
import { default as Notes } from '@/pages/client-page-by-id/factfind/notes.vue';
import { BoxTitle } from '@/modules/ui';
import { AddNoteForm } from '@modules/factfind/ui/notes';
import * as actionMod from '@modules/factfind/use-cases/add-new-note/action';
import { createClient } from '@tests/helpers';

vi.spyOn(apiClient, 'post').mockResolvedValue(undefined);
const mockAction = vi.spyOn(actionMod, 'action');

vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => {
    return {
      params: {
        id: 1,
      },
    };
  }),
}));

const client = createClient();

function makeWrapper() {
  return mount(Notes, {
    props: {
      client: client,
      clientId: client.id,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Notes', () => {
  it('renders component', () => {
    const wrapper = makeWrapper();
    const boxTitle = wrapper.findComponent(BoxTitle);
    const h3 = boxTitle.find('h3');

    expect(boxTitle.exists()).toBe(true);
    expect(h3.text()).toBe(`Notes`);
  });

  it('renders children components', () => {
    const wrapper = makeWrapper();
    const notesList = wrapper.findComponent(NotesList);
    const addNotesForm = wrapper.findComponent(AddNoteForm);

    expect(notesList.exists()).toBe(true);
    expect(addNotesForm.isVisible()).toBe(false);
  });

  it('hides add notes form by default', async () => {
    const wrapper = makeWrapper();
    expect(wrapper.findByTestId('add-note-form').isVisible()).toBe(false);
  });

  it('reveals add notes form on button click', async () => {
    const wrapper = makeWrapper();
    await wrapper.findByTestId('show-form-button').trigger('click');
    expect(wrapper.findByTestId('add-note-form').isVisible()).toBe(true);
  });

  it('handles form submission', async () => {
    const wrapper = makeWrapper();
    const form = wrapper.findComponent(AddNoteForm);

    // @ts-ignore
    const clientId = wrapper.vm.clientId;
    const content = 'test';
    form.vm.$emit('on-submit', {
      content,
    });

    await flushPromises();
    expect(mockAction).toHaveBeenCalledTimes(1);
    expect(apiClient.post).toHaveBeenCalledTimes(1);
    expect(apiClient.post).toHaveBeenCalledWith(
      `/api/internal/v1/clients/${clientId}/notes`,
      {
        content,
      },
    );
  });
});
