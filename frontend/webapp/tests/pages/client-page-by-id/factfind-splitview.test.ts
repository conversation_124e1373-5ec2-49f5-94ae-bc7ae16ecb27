/* eslint-disable @typescript-eslint/ban-ts-comment */
import { expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createClient } from '@tests/helpers';
import { createTestingPinia } from '@pinia/testing';

import { apiClient } from '@aventur-shared/services/api';
import { Client } from '@aventur-shared/modules/clients';
import { formatName } from '@aventur-shared/utils/user';
import { default as SearchField } from '@aventur-shared/components/form/fields/select/SearchSelect.vue';

import { default as FactFind } from '@/pages/client-page-by-id/factfind/client-factfind.vue';
import { default as SplitViewSelector } from '@modules/factfind/ui/SplitViewSelector.vue';
import { Option } from '@modules/ui/popup-options/types';

vi.spyOn(apiClient, 'get');

vi.mock('vue-router', async () => {
  const $router = await vi.importActual('vue-router');

  return {
    // @ts-ignore
    ...$router,
    onBeforeRouteLeave: vi.fn,
  };
});

const makeWrapper = (client: Client) =>
  mount(FactFind, {
    props: {
      client,
      clientId: client.id,
    },
    global: {
      stubs: ['router-link', 'router-view'],
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          stubActions: false,
        }),
      ],
    },
  });

const linkedClients = [createClient(), createClient()].map(
  ({ firstName, lastName, id }) => ({
    firstName,
    lastName,
    linkTypeId: 1,
    linkedClientId: id,
  }),
);
const client = createClient({
  linkedClients,
});

it('Renders splitview controls for linked clients', async () => {
  const wrapper = makeWrapper(client);
  expect(wrapper.findElementByText('label', 'Split View')).to.exist;

  const splitViewSelector = wrapper.getComponent(SplitViewSelector);
  expect(splitViewSelector.props('linkedClients')).toEqual(
    client.linkedClients,
  );

  const options = splitViewSelector
    .getComponent(SearchField)
    .props('options') as Option[];

  expect(options).to.have.length(2);
  expect(options.map(({ label }) => label)).to.contain(
    formatName(linkedClients[0]),
  );
  expect(options.map(({ label }) => label)).to.contain(
    formatName(linkedClients[1]),
  );
});

it('Renders no splitview controls if no linked clients', async () => {
  const wrapper = makeWrapper(createClient());

  expect(wrapper.findElementByText('label', 'Split View')).not.to.exist;
});

it('Makes expected API calls', async () => {
  const wrapper = makeWrapper(client);

  const splitViewSelector = wrapper.getComponent(SplitViewSelector);

  splitViewSelector.vm.$emit('change', linkedClients[0].linkedClientId);
  await flushPromises();

  expect(apiClient.get).toHaveBeenCalledWith(
    `/api/internal/v1/clients/${linkedClients[0].linkedClientId}`,
  );

  splitViewSelector.vm.$emit('change', linkedClients[1].linkedClientId);
  await flushPromises();

  expect(apiClient.get).toHaveBeenCalledWith(
    `/api/internal/v1/clients/${linkedClients[1].linkedClientId}`,
  );
});
