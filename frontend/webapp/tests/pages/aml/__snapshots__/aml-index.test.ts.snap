// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Identity Verification rendering tests > Shows a green icon if the AML result is a pass 1`] = `
"<div data-testid="identity-verification-result" class="flex flex-row items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" data-slot="icon" class="size-5" style="fill: green;">
    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z" clip-rule="evenodd"></path>
  </svg>
  <p>Pass</p>
  <p>Some date...</p>
</div>"
`;

exports[`Identity Verification rendering tests > Shows a loading message when the AML result is being fetched 1`] = `
"<div data-testid="identity-verification-result" class="flex flex-row items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" data-slot="icon" class="size-5" style="fill: red;">
    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z" clip-rule="evenodd"></path>
  </svg>
  <p>Loading...</p>
  <p></p>
</div>"
`;

exports[`Identity Verification rendering tests > Shows a red icon if the AML result does not exist 1`] = `
"<div data-testid="identity-verification-result" class="flex flex-row items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" data-slot="icon" class="size-5" style="fill: red;">
    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z" clip-rule="evenodd"></path>
  </svg>
  <p>Required</p>
  <p></p>
</div>"
`;

exports[`Identity Verification rendering tests > Shows a red icon if the AML result is a fail 1`] = `
"<div data-testid="identity-verification-result" class="flex flex-row items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" data-slot="icon" class="size-5" style="fill: red;">
    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z" clip-rule="evenodd"></path>
  </svg>
  <p>Fail</p>
  <p>Some date...</p>
</div>"
`;

exports[`Identity Verification rendering tests > Shows an orange icon if the AML result is a refer 1`] = `
"<div data-testid="identity-verification-result" class="flex flex-row items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" data-slot="icon" class="size-5" style="fill: orange;">
    <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm11.378-3.917c-.89-.777-2.366-.777-3.255 0a.75.75 0 0 1-.988-1.129c1.454-1.272 3.776-1.272 5.23 0 1.513 1.324 1.513 3.518 0 4.842a3.75 3.75 0 0 1-.837.552c-.676.328-1.028.774-1.028 1.152v.75a.75.75 0 0 1-1.5 0v-.75c0-1.279 1.06-2.107 1.875-2.502.182-.088.351-.199.503-.331.83-.727.83-1.857 0-2.584ZM12 18a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z" clip-rule="evenodd"></path>
  </svg>
  <p>Refer</p>
  <p>Some date...</p>
</div>"
`;
