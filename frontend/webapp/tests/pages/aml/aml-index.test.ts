/* eslint-disable @typescript-eslint/ban-ts-comment */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { IdentityValidationResultDTO } from '@aventur-shared/modules/clients/dtos';
import * as monitoringListGetter from '@aventur-shared/modules/clients/api/client/aml/get-monitoring-list';
import { default as AMLIndex } from '@/pages/aml/aml-index.vue';
import { Address } from '@/models/client';

vi.mock('vue-router', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    useRoute: vi.fn(() => {
      return {
        params: {
          id: 999,
        },
      };
    }),
  };
});

const mockMonitoringListData = {
  id: 'abc',
  lastUpdateDate: new Date(),
  memberNumber: '1',
  monitoringStatus: 'no change',
  numberOfMatches: 0,
  persons: [],
  startDate: new Date(),
  state: 'OK',
};

function makeWrapper(
  initialAMLIdentityValidationResult: IdentityValidationResultDTO | null = null,
  state: Record<any, unknown> = {},
) {
  return mount(AMLIndex, {
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            client: {
              amlIdentityValidationResult: initialAMLIdentityValidationResult,
            },
            ...state,
          },
        }),
      ],
    },
  });
}

describe('Identity Verification rendering tests', () => {
  it('Renders correct markup in case of incomplete profile', async () => {
    const wrapper = makeWrapper();
    expect(wrapper.findByTestId('incomplete-profile-warning').isVisible()).toBe(
      true,
    );
    expect(wrapper.findByTestId('run-aml-check').element.disabled).toBe(true);
  });
  it('Renders correct markup in case of complete profile', async () => {
    const wrapper = makeWrapper(null, {
      client: {
        profile: {
          dateOfBirth: new Date(),
          addresses: [
            new Address({
              address_line_one: '1428 Elm Street',
              city: 'Los Angeles',
              post_code: '90027',
              is_primary: false,
            }),
          ],
        },
      },
    });
    expect(wrapper.findByTestId('incomplete-profile-warning').isVisible()).toBe(
      false,
    );
    expect(wrapper.findByTestId('run-aml-check').element.disabled).toBe(false);
  });
  it('Shows a red icon if the AML result is a fail', async () => {
    const wrapper = makeWrapper({
      result: 'Fail',
      date: 'Some date...',
      detail: [],
      criteria: {
        first_name: 'Joe',
        last_name: 'Bloogs',
        address: '1 A Street ',
        city: 'London',
        post_code: 'N1 1AA',
        country_code: 'GB',
        date_of_birth: '1970-01-01',
      },
    });
    const result_div = wrapper.findByTestId('identity-verification-result');
    await wrapper.vm.$nextTick(() => {
      expect(result_div.html()).toMatchSnapshot();
    });
  });
  it('Shows a red icon if the AML result does not exist', async () => {
    const wrapper = makeWrapper();
    const result_div = wrapper.findByTestId('identity-verification-result');
    await wrapper.vm.$nextTick(() => {
      expect(result_div.html()).toMatchSnapshot();
    });
  });
  it('Shows a green icon if the AML result is a pass', async () => {
    const wrapper = makeWrapper({
      result: 'Pass',
      date: 'Some date...',
      detail: [],
      criteria: {
        first_name: 'Joe',
        last_name: 'Bloogs',
        address: '1 A Street ',
        city: 'London',
        post_code: 'N1 1AA',
        country_code: 'GB',
        date_of_birth: '1970-01-01',
      },
    });
    const result_div = wrapper.findByTestId('identity-verification-result');
    await wrapper.vm.$nextTick(() => {
      expect(result_div.html()).toMatchSnapshot();
    });
  });
  it('Shows an orange icon if the AML result is a refer', async () => {
    const wrapper = makeWrapper({
      result: 'Refer',
      date: 'Some date...',
      detail: [],
      criteria: {
        first_name: 'Joe',
        last_name: 'Bloogs',
        address: '1 A Street ',
        city: 'London',
        post_code: 'N1 1AA',
        country_code: 'GB',
        date_of_birth: '1970-01-01',
      },
    });
    const result_div = wrapper.findByTestId('identity-verification-result');
    await wrapper.vm.$nextTick(() => {
      expect(result_div.html()).toMatchSnapshot();
    });
  });
  it('Shows a loading message when the AML result is being fetched', async () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    wrapper.vm.identityValidationLoading = true;
    const result_div = wrapper.findByTestId('identity-verification-result');
    await wrapper.vm.$nextTick(() => {
      expect(result_div.html()).toMatchSnapshot();
    });
  });
});

describe('Monitoring list rendering tests', () => {
  it('Shows a loading message when the monitoring list is being fetched', async () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    wrapper.vm.monitoringListLoading = true;
    await wrapper.vm.$nextTick(() => {
      expect(wrapper.findByTestId('monitoring-loading')).not.toBe(null);
      expect(wrapper.findByTestId('monitoring-detail')).toBe(null);
      expect(wrapper.findByTestId('monitoring-no-detail')).toBe(null);
    });

  });
  it('Shows no monitoring details message when the api returns HTTP 400 response', async () => {
    const wrapper = makeWrapper();
    await flushPromises();

    expect(wrapper.findByTestId('monitoring-loading')).toBe(null);
    expect(wrapper.findByTestId('monitoring-detail')).toBe(null);
  });
  it('Shows the monitoring detail section when valid data is returned', async () => {
    const wrapper = makeWrapper();
    await flushPromises();
    expect(wrapper.findByTestId('monitoring-loading')).toBe(null);
    // expect(wrapper.findByTestId('monitoring-detail')).not.toBe(null);
    expect(wrapper.findByTestId('monitoring-no-detail')).toBe(null);
  });
});

describe('updateIdentityVerificationDetail()', () => {
  it('Returns required state when the store has no value for AML state', () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    expect(wrapper.vm.identityVerificationDetail).toEqual({
      result: 'Required',
      date: '',
    });
  });
  it('Returns the aml state object when it is not null', () => {
    const result = {
      result: 'Pass',
      date: 'Some date...',
      detail: [],
      criteria: {
        first_name: 'Joe',
        last_name: 'Bloogs',
        address: '1 A Street ',
        city: 'London',
        post_code: 'N1 1AA',
        country_code: 'GB',
        date_of_birth: '1970-01-01',
      },
    };
    const wrapper = makeWrapper(result);
    // @ts-ignore
    expect(wrapper.vm.identityVerificationDetail).toEqual(result);
  });
  it('Returns the loading state when the component calling the API to get the AML state', () => {
    const wrapper = makeWrapper();
    // @ts-ignore
    wrapper.vm.identityValidationLoading = true;
    // @ts-ignore
    expect(wrapper.vm.identityVerificationDetail).toEqual({
      result: 'Loading...',
      date: '',
    });
  });
});
