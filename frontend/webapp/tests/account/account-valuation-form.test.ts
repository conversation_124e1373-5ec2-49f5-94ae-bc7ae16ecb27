import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import accountValuationForm from '@/modules/accounts/ui/account-valuation/form/account-valuation-form.vue';
import * as veeValidate from 'vee-validate';

function createHandleSubmitMock(returnValue: any) {
  return vi.fn(() => ({
    handleSubmit: vi.fn((callback) => {
      return () => {
        if (returnValue !== null) {
          callback(returnValue);
        }
        // If returnValue is null, don't call the callback
      };
    }),
  }));
}

// Mock vee-validate globally
vi.mock('vee-validate', async () => ({
  useForm: vi.fn(() => ({
    handleSubmit: vi.fn((callback) => callback),
  })),
  useField: vi.fn(() => ({
    value: '',
    errorMessage: '',
    handleChange: vi.fn(),
  })),
}));

describe('AccountValuationForm', () => {
  it('emits on-close event when close button is clicked', async () => {
    const wrapper = mount(accountValuationForm);

    await wrapper.findAll('button')[1].trigger('click');

    expect(wrapper.emitted('on-close')).toBeTruthy();
  });

  it('displays correct button text based on amount prop', () => {
    const wrapperWithAmount = mount(accountValuationForm, {
      props: { amount: '100' },
    });
    const wrapperWithoutAmount = mount(accountValuationForm);

    expect(wrapperWithAmount.find('button[type="submit"]').text()).toBe(
      'Update',
    );
    expect(wrapperWithoutAmount.find('button[type="submit"]').text()).toBe(
      'Add',
    );
  });
});

describe('AccountValuationFormEvents', () => {
  it('prevents form submission when fields are empty', async () => {
    vi.mocked(veeValidate.useForm).mockImplementation(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      createHandleSubmitMock(null),
    );

    const wrapper = mount(accountValuationForm);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.onSubmit();

    expect(wrapper.emitted('on-submit')).toBeFalsy();
  });

  it('emits on-submit event with correct values', async () => {
    vi.mocked(veeValidate.useForm).mockImplementation(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      createHandleSubmitMock({
        amount: '1000',
        date: '2023-05-01',
        type: 'actual',
      }),
    );

    const wrapper = mount(accountValuationForm);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.onSubmit();

    expect(wrapper.emitted('on-submit')).toBeTruthy();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.emitted('on-submit')[0]).toEqual([
      { amount: '1000', date: '2023-05-01', type: 'actual' },
    ]);
  });

  it('emits on-submit event with original date when date is changed', async () => {
    vi.mocked(veeValidate.useForm).mockImplementation(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      createHandleSubmitMock({
        amount: '2000',
        date: '2023-06-01',
        type: 'estimate',
      }),
    );

    const wrapper = mount(accountValuationForm, {
      props: {
        date: '2023-05-01',
      },
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    await wrapper.vm.onSubmit();

    expect(wrapper.emitted('on-submit')).toBeTruthy();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    expect(wrapper.emitted('on-submit')[0]).toEqual([
      { amount: '2000', date: '2023-06-01', type: 'estimate' },
      '2023-05-01',
    ]);
  });
});
