/* eslint-disable @typescript-eslint/ban-ts-comment */
import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import { createAdviser } from '@tests/helpers';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { UserRole } from '@aventur-shared/modules/auth';
import { SelectField } from '@aventur-shared/components/form';
import { SingleAccount } from '@aventur-shared/modules/accounts';
import { default as AccountDetails } from '@modules/accounts/use-cases/user-previews-account/ui/account-details.vue';

vi.mock('vue-router', async (importOriginal) => {
  const $router = await importOriginal();
  return {
    // @ts-ignore
    ...$router,
    onBeforeRouteLeave: vi.fn,
  };
});

const account: SingleAccount = {
  id: 1,
  accountNumber: '',
  additionalInfo: '',
  advisor: createAdviser(),
  clients: [],
  created: new DateTime(new Date()),
  feeSplitTemplate: null,
  hasInvestmentMix: false,
  hasPlanInformation: false,
  providerId: 1,
  status: {
    id: 2,
    name: 'Active Advised',
  },
  subAccountNumber: '',
  typeId: 1,
  feeModel: null,
  portfolioModelId: null,
  productLayout: null,
};

function makeWrapper(
  account: SingleAccount,
  userRole: UserRole = UserRole.Adviser,
) {
  return mount(AccountDetails, {
    propsData: {
      account,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
          initialState: {
            user: {
              user: {
                id: 1,
                groups: [userRole],
              },
            },
          },
        }),
      ],
    },
  });
}

describe('Account Details', () => {
  it('Renders correct markup', async () => {
    const wrapper = makeWrapper(account);

    let fields;
    const sections = wrapper.findAll('form > div > div');

    expect(sections[0].find('h3').text()).contains('Account details');
    fields = {
      clients: 'Clients',
      advisorId: 'Advisor',
      provider: 'Provider',
      typeId: 'Account Type',
      status: 'Account Status',
      accountNumber: 'Account Number',
      // subAccountNumber: 'Sub Account Number',
      additionalInfo: 'Additional Info',
    };
    Object.entries(fields).forEach(([field, label]) => {
      expect(sections[0].find(`label[for=${field}]`).text()).contains(label);
    });

    expect(sections[1].find('h3').text()).contains('Fee details');
    fields = {
      feeSplitTemplateId: 'Fee Split Template',
      feeModel: 'Current Fee Model',
    };
    Object.entries(fields).forEach(([field, label]) => {
      expect(sections[1].find(`label[for=${field}]`).text()).contains(label);
    });

    const feeReviewStatusSelectComponent = sections[1]
      .findAllComponents(SelectField)
      .slice(-1)
      .pop();
    expect(feeReviewStatusSelectComponent.props('options')).toEqual([
      { label: 'Tiered Structure', value: 1 },
      { label: '1%', value: 2 },
      { label: '0.75%', value: 3 },
      { label: '0.5%', value: 4 },
      { label: '0%', value: 5 },
      { label: 'Invoice', value: 6 },
      { label: 'Custom', value: 7 },
      { label: '1.5%', value: 8 },
      { label: '0.25%', value: 9 },
    ]);
  });

  it('Should have `Fee Model` field disabled for non-SuperAdmins', async () => {
    const wrapper = makeWrapper(account, UserRole.Adviser); // explicit

    const sections = wrapper.findAll('form > div > div');
    const feeReviewStatusSelectComponent = sections[1]
      .findAllComponents(SelectField)
      .slice(-1)
      .pop();
    expect(feeReviewStatusSelectComponent.props('disabled')).toBeTruthy();
  });

  it('Should have `Fee Model` field enabled for SuperAdmins', async () => {
    const wrapper = makeWrapper(account, UserRole.SuperAdmin);

    const sections = wrapper.findAll('form > div > div');
    const feeReviewStatusSelectComponent = sections[1]
      .findAllComponents(SelectField)
      .slice(-1)
      .pop();
    expect(feeReviewStatusSelectComponent.props('disabled')).toBeFalsy();
  });
});
