/* eslint-disable @typescript-eslint/ban-ts-comment */
import { default as AccountHeader } from '@/pages/account/account-header.vue';
import { describe, expect, it, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTesting<PERSON>inia } from '@pinia/testing';
import { SingleAccount } from '@aventur-shared/modules/accounts/models/account';
import { createAdviser } from '@tests/helpers';
import { DateTime } from '@aventur-shared/utils/dateTime';

vi.mock('vue-router', async (importOriginal) => {
  const mod = await importOriginal();
  return {
    // @ts-ignore
    ...mod,
    useRoute: vi.fn(() => {
      return {
        path: '',
      };
    }),
    useRouter: vi.fn(() => {
      return {
        resolve: vi.fn(() => ({ path: '' })),
      };
    }),
  };
});

function makeWrapper(account: SingleAccount) {
  return mount(AccountHeader, {
    propsData: {
      account,
    },
    global: {
      plugins: [
        createTestingPinia({
          createSpy: vi.fn,
        }),
      ],
    },
  });
}

describe('Account Header', () => {
  it('hides plan information&investment mix tabs when hasPlanInformation prop === false', async () => {
    const account: SingleAccount = {
      id: 1,
      accountNumber: '',
      additionalInfo: '',
      advisor: createAdviser(),
      clients: [],
      created: new DateTime(new Date()),
      feeSplitTemplate: null,
      hasInvestmentMix: false,
      hasPlanInformation: false,
      providerId: 1,
      status: {
        id: 1,
        name: 'status1',
      },
      subAccountNumber: '',
      typeId: 1,
      feeModel: null,
      portfolioModelId: null,
      productLayout: null,
    };
    const wrapper = makeWrapper(account);

    const listItemsText = wrapper.findAll('li').map((item) => item.text());
    expect(listItemsText.includes('Plan information')).toEqual(false);
  });

  it('displays plan information&investment mix tabs when hasPlanInformation prop === true', async () => {
    const account: SingleAccount = {
      id: 1,
      accountNumber: '',
      additionalInfo: '',
      advisor: createAdviser(),
      clients: [],
      created: new DateTime(new Date()),
      feeSplitTemplate: null,
      hasInvestmentMix: true,
      hasPlanInformation: true,
      providerId: 1,
      status: {
        id: 1,
        name: 'status1',
      },
      subAccountNumber: '',
      typeId: 1,
      feeModel: null,
      portfolioModelId: null,
      productLayout: 'investment_layout',
    };
    const wrapper = makeWrapper(account);

    const listItemsText = wrapper.findAll('li').map((item) => item.text());
    expect(listItemsText.includes('Plan information')).toEqual(true);
  });
});
