import { faker } from '@faker-js/faker';
import { some } from 'lodash';

import { UserRole } from '@aventur-shared/modules/auth';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { Task, TaskStatus } from '@aventur-shared/modules/tasks';
import { Client, ClientTypeEnum } from '@aventur-shared/modules/clients';
import { TaskTypeEnum } from '@aventur-shared/modules/tasks/models/task-type';
import { TaskStatusEnum } from '@aventur-shared/modules/tasks/models/task-status';
import { ClientAddress } from '@aventur-shared/modules/factfind/types/Address';
import { Case, CaseId, CaseStatusEnum } from '@aventur-shared/modules/cases';
import { CaseType, SubTask } from '@aventur-shared/modules/cases/models';
import { GoalType } from '@aventur-shared/modules/goals/types/GoalType';
import { CaseGoal } from '@aventur-shared/modules/cases/models/case–goal';
import { CaseTypeEnum } from '@aventur-shared/modules/cases/models/case-type';
import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';
import { AccountStatusEnum } from '@aventur-shared/modules/accounts';
import { Goal, GoalId } from '@aventur-shared/modules/goals';
import { accountStatusToSelectOption } from '@aventur-shared/modules/accounts/models/account';
//

export const taskTypeToSlug = (taskType: TaskTypeEnum) =>
  `${taskType}`.replace(/\B[A-Z]+/g, (match) => `_${match}`).toLowerCase();

export const goalTypeToName = (goalType: GoalType) =>
  `${ClientGoalTypeEnum[goalType]}`.replace(
    /\B[A-Z]+/g,
    (match) => ` ${match}`,
  );

export const taskTypeToDescription = (
  taskType: TaskTypeEnum,
  defaultDescription = 'Basic Task',
) =>
  taskType === TaskTypeEnum.Default
    ? defaultDescription
    : `${taskType}`.replace(/\B[A-Z]+/g, (match) => ` ${match}`);

let caseId = 0;
let goalId = 0;
let taskId = 0;
let clientId = 0;
let adviserId = 0;
let accountId = 0;

const _createAddress = (
  () =>
  (params: Partial<ClientAddress> = {}): ClientAddress =>
    Object.assign(
      {
        addressLineOne: null,
        addressLineTwo: null,
        addressLineThree: null,
        addressLineFour: null,
        city: null,
        country_id: null,
        postCode: null,
        isPrimary: true,
      },
      params,
    )
)();

const _createClient = (
  () =>
  (params: Partial<Client> = {}): Client =>
    Object.assign(
      {
        id: ++clientId,
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        type: '',
        email: faker.internet.email(),
        title: null,
        genderId: null,
        dateOfBirth: null,
        nationalityId: null,
        maritalStatusId: null,
        phoneNumber: null,
        mobileNumber: null,
        addresses: [],
        relations: [],
        advisor: null,
        clientType: ClientTypeEnum.Individual,
        clientStatus: 0,
        clientSource: 0,
        reviewFrequency: null,
        reviewMonth: null,
        nextReviewMonth: null,
        linkedClients: [],
      },
      params,
    )
)();

const _createAdviser = (() => () => ({
  id: ++adviserId,
  firstName: faker.person.firstName(),
  lastName: faker.person.lastName(),
}))();

const _createTask = (
  () =>
  (params: Partial<Task> = {}): Task => {
    const { subTasks, ...$params } = params;
    const taskType = $params.type ?? TaskTypeEnum.Default;

    return Object.assign(
      {
        slug: taskTypeToSlug(taskType),
        caseLevel: !some(subTasks, 'goalId'),
        description: taskTypeToDescription(taskType, $params.description),
        defaultGroup: UserRole.Adviser,
        type: TaskTypeEnum.Default,
        isModifiable: false,
        status: new TaskStatus(TaskStatusEnum.ToDo),
        dueDate: new DateTime(new Date()),
        advisor: _createAdviser(),
        assignedGroup: null,
        subTasks: subTasks ?? [_createSubTask()],
      },
      $params,
    );
  }
)();

const _createSubTask = (
  () =>
  (params: Partial<SubTask> = {}): SubTask =>
    Object.assign(
      {
        taskId: ++taskId,
        caseGoalId:
          params.caseGoalId === undefined ? ++goalId : params.caseGoalId, // case_goal_id
        goalId:
          params.goalId === undefined
            ? ClientGoalTypeEnum.ClientSetup
            : params.goalId,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        name: params.goalId ? goalTypeToName(params.goalId) : `Goal ${goalId}`,
        noDocumentsReason: null,
      },
      params,
    )
)();

// TODO: ClientGoal
const _createGoal = (
  () =>
  (params: Partial<Goal> = {}): Goal => ({
    id: ++goalId as GoalId,
    name: params.type ? goalTypeToName(params.type) : `Goal ${goalId}`,
    type: params?.type || ClientGoalTypeEnum.ClientSetup,
    accounts: params?.accounts || [],
    clients: params?.clients || [...createClients(1)],
    goalObjectives: [],
  })
)();

const _createAccount = (
  () =>
  (params: Partial<Goal['accounts'][0]> = {}): Goal['accounts'][0] =>
    Object.assign(
      {
        id: ++accountId,
        accountNumber: faker.finance.accountNumber(),
        subAccountNumber: faker.finance.accountNumber(),
        providerName: 'Provider1',
        advices: params.advices || [],
        advisorId: createAdviser().id,
        clients: [...createClients(1)],
        expectedFees: [],
        feeSplitTemplate: 1,
        type: faker.finance.accountName(),
        typeGroupId: 1,
        originalStatus: accountStatusToSelectOption(AccountStatusEnum.Proposed),
        status: accountStatusToSelectOption(AccountStatusEnum.Proposed),
      },
      params,
    )
)();

const _createCase = (
  () =>
  (params: Partial<Case> = {}): Case => {
    const $advisor = params?.relatedAdvisor || _createAdviser();
    const $goals = params?.goals ?? [_createGoal()];
    const $tasks = params?.tasks ?? [
      _createTask({
        advisor: $advisor,
        status: new TaskStatus(TaskStatusEnum.InProgress),
        subTasks: $goals.map(({ id: goalId, name }) =>
          _createSubTask({
            goalId,
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            name,
          }),
        ),
      }),
    ];

    return {
      id: ++caseId as CaseId,
      type: params?.type || new CaseType(CaseTypeEnum.NewBusiness),
      name: `Case${goalId}`,
      status: params?.status || CaseStatusEnum.Open,
      createdOn: new Date(),
      completedOn: null,
      daysToComplete: 0,
      relatedAdvisor: $advisor,
      relatedManager: null,
      clients: params?.clients || [_createClient()],
      goals: $goals,
      tasks: params?.tasks || $tasks,
      reviewSlot: null,
    };
  }
)();

export const createAdviser = _createAdviser;
export const createAccount = _createAccount;
export const createAddress = _createAddress;
export const createClient = _createClient;
export const createCase = _createCase;
export const createGoal = _createGoal;
export const createTask = _createTask;
export const createSubTask = _createSubTask;

export const createTasks = (num = 1): Task[] =>
  Array(num)
    .fill(undefined)
    .map(() => _createTask());

export const createClients = (num = 1): Client[] =>
  Array(num)
    .fill(undefined)
    .map(() => _createClient());

export const createGoals = (num = 1): CaseGoal[] =>
  Array(num)
    .fill(undefined)
    .map(() => _createGoal());
