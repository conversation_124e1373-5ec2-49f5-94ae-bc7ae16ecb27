// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { ArraySchema, Schema, StringSchema } from 'yup';
import { Crumb } from '@modules/breadcrumbs/crumb';
import { RouteParams, RouteRecordName } from 'vue-router';

declare module 'yup' {
  interface ArraySchema {
    uniqueValueCheck(appendStr: string, message: string): this;
  }

  interface Schema {
    checkIsNumber(message: string): this;
  }

  interface StringSchema {
    checkIsCorrectPhoneNumber(message: string): this;
  }
}

declare module 'vue-router' {
  interface RouteMeta {
    fromName?: RouteRecordName | null;
    fromParams?: RouteParams;
    breadcrumbs?: (Crumb & { update?: () => void })[];
  }
}
