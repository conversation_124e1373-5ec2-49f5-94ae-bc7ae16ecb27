import { App } from 'vue';
import * as Sentry from '@sentry/vue';

export default {
  install: async (app: App) => {
    if (import.meta.env.VITE_SENTRY_DNS) {
      Sentry.init({
        app,
        environment: import.meta.env.VITE_ENV ?? 'local',
        dsn: String(import.meta.env.VITE_SENTRY_DNS),
        integrations: [
          Sentry.browserTracingIntegration(),
          Sentry.feedbackIntegration({
            autoInject: false,
            showBranding: false,
            isEmailRequired: true,
            enableScreenshot: true,
            onFormClose: () => {
              // @ts-expect-error - custom event
              Sentry.getClient()?.emit('closeFeedbackWidget');
            },
            onSubmitSuccess: () => {
              // @ts-expect-error - custom event
              Sentry.getClient()?.emit('closeFeedbackWidget');
            },
          }),
        ],
        tracePropagationTargets: ['localhost', /^\//],
        tracesSampleRate: 0.1,
        release: __SENTRY_RELEASE__,
        ignoreErrors: ['ResizeObserver loop'],
      });
    }
  },
};
