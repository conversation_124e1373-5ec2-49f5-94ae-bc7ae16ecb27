import { Component } from '@storybook/blocks';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import { HttpResponse, http } from 'msw';

import * as helpers from '../../tests/helpers';

import { GoalId } from '@aventur-shared/modules/goals';
import {
  TaskStatus,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@aventur-shared/modules/tasks';
import {
  AccountStatusEnum,
  accountStatusToSelectOption,
} from '@aventur-shared/modules/accounts/models/account';

import { default as CaseOverviewReadonly } from '@modules/clients-cases/ui/case-overview/case-overview-readonly.vue';
import { default as CaseOverview } from '@modules/clients-cases/ui/case-overview/case-overview.vue';
import { default as CaseTaskList } from '@modules/clients-cases/use-cases/user-preview-case/ui/case-task-list.vue';

const advisor = helpers.createAdviser();
const clients = helpers.createClients(2);
const goals = [
  helpers.createGoal({
    type: 10,
    name: 'Build Wealth',
    accounts: [
      helpers.createAccount(), // proposed
      helpers.createAccount({
        originalStatus: accountStatusToSelectOption(
          AccountStatusEnum.ActiveNonAdvised,
        ),
        status: accountStatusToSelectOption(AccountStatusEnum.ActiveNonAdvised),
      }),
    ],
  }),
];
const tasks = [
  helpers.createTask({
    type: TaskTypeEnum.ClientAnnualReviewMessage,
    caseLevel: true,
    isModifiable: true,
    advisor,
  }),
  helpers.createTask({
    advisor,
    caseLevel: true,
    isModifiable: true,
    status: new TaskStatus(TaskStatusEnum.Completed),
  }),
  helpers.createTask({
    advisor,
    caseLevel: true,
    isModifiable: true,
    status: new TaskStatus(TaskStatusEnum.InProgress),
  }),
  helpers.createTask({
    advisor,
    type: TaskTypeEnum.AccountsToReview,
    status: new TaskStatus(TaskStatusEnum.Review),
    subTasks: goals.map(({ type: goalId }) =>
      helpers.createSubTask({
        goalId: goalId as GoalId,
      }),
    ),
  }),
  helpers.createTask({
    advisor,
    type: TaskTypeEnum.RiskProfile,
    isModifiable: true,
    subTasks: goals.map(({ type: goalId }) =>
      helpers.createSubTask({
        goalId: goalId as GoalId,
      }),
    ),
  }),
  helpers.createTask({
    type: TaskTypeEnum.CashflowPlan,
    advisor,
    isModifiable: true,
    subTasks: goals.map(({ type: goalId }) =>
      helpers.createSubTask({
        goalId: goalId as GoalId,
      }),
    ),
  }),
  helpers.createTask({
    type: TaskTypeEnum.CreateAdvice,
    advisor,
    subTasks: goals.map(({ type: goalId }) =>
      helpers.createSubTask({
        goalId: goalId as GoalId,
      }),
    ),
  }),
  helpers.createTask(),
  helpers.createTask({
    type: TaskTypeEnum.AdviceFeeEstimation,
    advisor,
    subTasks: goals.map(({ type: goalId }) =>
      helpers.createSubTask({
        goalId: goalId as GoalId,
      }),
    ),
  }),
  helpers.createTask(),
  helpers.createTask(),
  helpers.createTask({
    type: TaskTypeEnum.AcceptanceFromClient,
    advisor,
    subTasks: goals.map(({ type: goalId }) =>
      helpers.createSubTask({
        goalId: goalId as GoalId,
      }),
    ),
  }),
  helpers.createTask({
    type: TaskTypeEnum.FinalCheck,
    advisor,
    subTasks: goals.map(({ type: goalId }) =>
      helpers.createSubTask({
        goalId: goalId as GoalId,
      }),
    ),
  }),
];

const meta: Meta<Component> = {
  title: 'Cases/Preview Case',
  component: {},
  args: {
    isCaseReadonly: false,
  },
};

export default meta;
type Story = StoryObj<Component>;

export const OpenCase: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(/api\/internal\/v1\/active-holdings/, async () =>
          HttpResponse.json([]),
        ),
        http.get(/api\/v1\/review-group\/available-review-slots/, async () =>
          HttpResponse.json(null),
        ),
        http.get(/\/api\/v2\/case\/\d+/, async () =>
          HttpResponse.json(
            helpers.createCase({
              relatedAdvisor: advisor,
              clients,
              goals,
              tasks,
            }),
            { status: 200 },
          ),
        ),
        http.patch(/\/api\/v2\/case\/\d+\/\w+/, async () =>
          HttpResponse.json(null),
        ),
      ],
    },
  },
  render: (args) => ({
    components: { CaseOverview, CaseTaskList },
    setup() {
      return { args };
    },
    template: `<CaseOverview v-bind="args" /><br/><CaseTaskList v-bind="args" />`,
  }),
  args: (() => {
    const clientCase = helpers.createCase({
      relatedAdvisor: advisor,
      clients,
      goals,
      tasks,
    });
    return {
      caseId: clientCase.id,
      clientCase: clientCase,
    };
  })(),
};

export const ReadonlyCase: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(/api\/internal\/v1\/active-holdings/, async () =>
          HttpResponse.json([]),
        ),
        http.get(/api\/v1\/review-group\/available-review-slots/, async () =>
          HttpResponse.json(null),
        ),
      ],
    },
  },
  render: (args) => ({
    components: { CaseOverviewReadonly, CaseTaskList },
    setup() {
      return { args };
    },
    template: `<CaseOverviewReadonly v-bind="args" /><br/><CaseTaskList v-bind="args" />`,
  }),
  args: (() => {
    const clientCase = helpers.createCase({
      relatedAdvisor: advisor,
      clients,
      goals,
      tasks,
    });
    return {
      caseId: clientCase.id,
      clientCase: clientCase,
      isCaseReadonly: true,
    };
  })(),
};

export const CompletableCase: Story = {
  parameters: {
    msw: {
      handlers: [
        http.get(/api\/internal\/v1\/active-holdings/, async () =>
          HttpResponse.json([]),
        ),
        http.get(/api\/v1\/review-group\/available-review-slots/, async () =>
          HttpResponse.json(null),
        ),
      ],
    },
  },
  render: (args) => ({
    components: { CaseOverview, CaseTaskList },
    setup() {
      return { args };
    },
    template: `<CaseOverview v-bind="args" /><br/><CaseTaskList v-bind="args" />`,
  }),
  args: (() => {
    const clientCase = helpers.createCase({
      relatedAdvisor: advisor,
      clients,
      goals,
      tasks: [
        helpers.createTask({
          advisor,
          type: TaskTypeEnum.ClientOnboardingMessage,
          status: new TaskStatus(TaskStatusEnum.Completed),
          caseLevel: true,
          isModifiable: true,
        }),
        helpers.createTask({
          advisor,
          isModifiable: true,
          status: new TaskStatus(TaskStatusEnum.Completed),
        }),
      ],
    });
    return {
      caseId: clientCase.id,
      clientCase: clientCase,
    };
  })(),
};
