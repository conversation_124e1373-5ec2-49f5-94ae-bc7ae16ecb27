import { Component } from '@storybook/blocks';
import type { Meta, StoryObj } from '@storybook/vue3';
import { HttpResponse, http } from 'msw';

import * as helpers from '@tests/helpers';
import { createClientCaseForTask } from '#storybook/helpers';

import { useRefData } from '@aventur-shared/stores';
import {
  TaskStatus,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@aventur-shared/modules/tasks';

import { default as TaskItem } from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item.vue';
import { default as TaskItemDetail } from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item-detail.vue';

const loadRefData = () => {
  const { getRefData } = useRefData();
  getRefData().then(() => console.info('RefData loaded'));
};

const meta: Meta<Component> = {
  title: 'Tasks',
  component: {},
  args: {
    isReadonly: false,
  },
  parameters: {
    msw: {
      handlers: [
        http.get(/api\/internal\/v1\/active-holdings/, async () =>
          HttpResponse.json([]),
        ),
        http.get(/api\/v1\/review-group\/available-review-slots/, async () =>
          HttpResponse.json(null),
        ),
      ],
    },
  },
};

export default meta;
type Story = StoryObj<Component>;

export const BaseTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({ type: TaskTypeEnum.Default });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const BaseNonModifiableTask: Story = {
  name: 'Base Task (Non-Modifiable)',
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.Default,
      isModifiable: false,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const BaseReadOnlyTask: Story = {
  name: 'Base Task (Readonly)',
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.Default,
    });
    return {
      isReadonly: true,
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const BaseInProgressTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.Default,
      status: new TaskStatus(TaskStatusEnum.InProgress),
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const BaseCompletedTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.Default,
      status: new TaskStatus(TaskStatusEnum.Completed),
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const BaseNotApplicableTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.Default,
      status: new TaskStatus(TaskStatusEnum.NotApplicable),
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const BaseReviewTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.Default,
      status: new TaskStatus(TaskStatusEnum.Review),
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const BaseCanceledTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.Default,
      status: new TaskStatus(TaskStatusEnum.Canceled),
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const ClientMessageTask: Story = {
  name: 'Client Message Task (Case Level)',
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.ClientOnboardingMessage,
      status: new TaskStatus(TaskStatusEnum.InProgress),
      caseLevel: true,
      isModifiable: true,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const ClientMessageNoEmailTask: Story = {
  name: 'Client Message Task (Case Level; Clients w/o emails)',
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = (() => {
      const advisor = helpers.createAdviser();
      const tasks = [
        helpers.createTask({
          type: TaskTypeEnum.ClientAnnualReviewMessage,
          status: new TaskStatus(TaskStatusEnum.InProgress),
          caseLevel: true,
          advisor,
        }),
      ];

      return helpers.createCase({
        relatedAdvisor: advisor,
        clients: [
          helpers.createClient({
            email: null,
          }),
        ],
        tasks,
      });
    })();

    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const AccountsToReviewTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.AccountsToReview,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const CreateAdviceTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.CreateAdvice,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const RiskProfileTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.RiskProfile,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const CashflowPlanTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.CashflowPlan,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const AdviceFeeEstimationTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.AdviceFeeEstimation,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const AcceptanceFromClientTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.AcceptanceFromClient,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const FinalCheckTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.FinalCheck,
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

export const PlanningReportGeneratedTask: Story = {
  render: (args) => ({
    components: { TaskItem, TaskItemDetail },
    setup() {
      loadRefData();
      return { args };
    },
    template: `<TaskItem v-bind="{ ...args }">
        <TaskItemDetail v-bind="{ ...args }" />
      </TaskItem>`,
  }),
  args: (() => {
    const clientCase = createClientCaseForTask({
      type: TaskTypeEnum.PlanningReportGenerated,
      status: new TaskStatus(TaskStatusEnum.InProgress),
    });
    return {
      clientCase,
      task: clientCase.tasks[0],
    };
  })(),
};

// ImplementAccount = "ImplementAccount" // TODO: Check
