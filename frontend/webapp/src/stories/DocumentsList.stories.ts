import { Component } from '@storybook/blocks';
import type { Meta, StoryObj } from '@storybook/vue3';
import { default as DocumentsList } from '@modules/documents/ui/documents.vue';

const meta: Meta<Component> = {
  component: {},
  title: 'Documents List',
  args: {
    isReadonly: false,
  },
};

export default meta;
type Story = StoryObj<Component>;

export const EmptyListDocumentsNotRequired: Story = {
  name: 'Empty list with documents not required',
  render: (args) => ({
    components: { DocumentsList },
    setup() {
      return { args };
    },
    template: `<DocumentsList v-bind="{...args }"  />`,
  }),
  args: {
    documents: [],
    documentsNotRequired: true,
    isReadOnly: false,
  },
};

export const ExistingDocumentsList: Story = {
  name: 'Existing documents list',
  render: (args) => ({
    components: { DocumentsList },
    setup() {
      return { args };
    },
    template: `<DocumentsList v-bind="{...args }"  />`,
  }),
  args: {
    documents: [
      {
        id: 1,
        name: 'document 1',
        type: 'pdf',
        size: 265318,
        uploadedBy: {
          id: 1,
          firstName: 'Adviser',
          lastName: 'X.',
        },
        uploadedAt: new Date().toISOString(),
        clientAccess: true,
      },
      {
        id: 2,
        name: 'document 2',
        type: 'pdf',
        size: 199618,
        uploadedBy: {
          id: 2,
          firstName: 'Adviser',
          lastName: 'Y.',
        },
        uploadedAt: new Date().toISOString(),
        clientAccess: false,
      },
    ],
  },
};
