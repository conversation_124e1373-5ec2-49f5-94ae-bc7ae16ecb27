<template>
  <button
    :type="props.type"
    :disabled="props.loading"
    :class="buttonStyle()"
    @click="$emit('click')"
  >
    <svg
      v-if="props.loading"
      class="-ml-1 mr-3 size-5 animate-spin text-white"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      />
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
    <slot />
  </button>
</template>

<script setup lang="ts">
  import { PropType } from 'vue';

  defineEmits(['click']);

  type ButtonTypes = 'button' | 'submit' | 'reset' | undefined;

  const props = defineProps({
    primary: {
      type: Boolean,
      required: false,
      default: true,
    },
    type: {
      type: String as PropType<ButtonTypes>,
      required: false,
      default: 'button',
    },
    loading: {
      type: Boolean,
      required: false,
      default: false,
    },
  });

  function buttonStyle() {
    if (props.primary) {
      return 'inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-gray-500';
    }
    return 'inline-flex justify-center px-4 py-2 border border-primary shadow-sm text-sm font-medium rounded-md text-primary bg-white hover:bg-gray-50';
  }
</script>
