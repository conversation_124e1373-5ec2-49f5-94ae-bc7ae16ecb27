<template>
  <div class="flex justify-end">
    <div class="cursor-pointer" @click="toggle">
      <ChevronDownIcon
        :class="`h-6 w-6 shrink-0 text-primary transition-transform duration-200 ${
          isVisible ? 'rotate-180' : ''
        }`"
      />
    </div>
  </div>
  <div
    ref="slotWrapper"
    :class="`${baseClasses} ${isVisible ? visibleClasses : invisibleClasses}`"
    :style="wrapperStyle"
  >
    <slot />
  </div>
</template>

<script lang="ts" setup>
  import { ChevronDownIcon } from '@heroicons/vue/24/outline';
  import { computed, nextTick, onUpdated, ref } from 'vue';

  type Props = {
    defaultVisible?: boolean;
  };

  const props = withDefaults(defineProps<Props>(), {
    defaultVisible: false,
  });

  const maxHeight = ref(5000);
  const baseClasses =
    'h-auto  items-center  transition-all transition duration-200 ';
  const invisibleClasses = 'invisible max-h-0 opacity-0 overflow-hidden';
  const visibleClasses = `visible opacity-100`;

  const isVisible = ref(props.defaultVisible ? true : false);

  const wrapperStyle = computed(() => ({
    maxHeight: `${isVisible.value ? maxHeight.value : 0}px`,
  }));

  const slotWrapper = ref<HTMLElement>();

  const getSlotHeight = () => {
    const slotHeight =
      slotWrapper.value?.children[0]?.getBoundingClientRect()?.height;
    if (!slotHeight || Number.isNaN(+slotHeight)) return;
    maxHeight.value = slotHeight;
  };

  nextTick(() => {
    getSlotHeight();
  });

  onUpdated(() => {
    getSlotHeight();
  });

  const toggle = () => {
    isVisible.value = !isVisible.value;
  };
</script>
