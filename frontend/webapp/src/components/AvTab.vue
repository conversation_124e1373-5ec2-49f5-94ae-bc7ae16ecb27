<template>
  <div v-show="props.slug === selectedTab">
    <slot />
  </div>
</template>

<script setup lang="ts">
  import { inject, ref, watch } from 'vue';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    slug: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits(['on-selected']);

  const selectedTab = inject('selectedTab');
  const selected = ref(selectedTab);

  watch(selected, () => {
    emit('on-selected', selected.value);
  });
</script>
