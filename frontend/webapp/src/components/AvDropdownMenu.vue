<template>
  <Menu as="div" class="relative inline-block text-left">
    <div>
      <MenuButton
        class="focus:ring-primary flex items-center rounded-full bg-gray-100 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100"
      >
        <span class="sr-only">{{ props.label }}</span>
        <EllipsisVerticalIcon class="size-5" aria-hidden="true" />
      </MenuButton>
    </div>

    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <MenuItems
        class="absolute right-0 z-[100] mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none"
      >
        <div class="py-1">
          <MenuItem
            v-for="opt in props.options"
            v-slot="{ active }"
            :key="String(opt.id)"
          >
            <div
              :class="[
                active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                'block px-4 py-2 text-sm',
              ]"
              @click="onClick(String(opt.id))"
            >
              {{ opt.name }}
            </div>
          </MenuItem>
        </div>
      </MenuItems>
    </transition>
  </Menu>
</template>

<script setup lang="ts">
  import { PropType, getCurrentInstance } from 'vue';
  import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
  import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
  import { Options } from '@aventur-shared/types/Common';

  const props = defineProps({
    label: {
      type: String,
      required: true,
    },
    options: {
      type: Array as PropType<Options[]>,
      required: true,
    },
  });

  const emit = defineEmits(['click']);
  const component = getCurrentInstance();

  function onClick(opt: string) {
    emit('click', opt, component?.vnode.key);
  }
</script>
