<template>
  <av-button @click="open = true">
    {{ props.buttonLabel }}
  </av-button>
  <TransitionRoot as="template" :show="open">
    <Dialog
      as="div"
      class="fixed inset-0 z-10 overflow-y-auto"
      @close="open = false"
    >
      <div
        class="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0"
      >
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <DialogOverlay
            class="fixed inset-0 bg-gray-500/75 transition-opacity"
          />
        </TransitionChild>

        <span
          class="hidden sm:inline-block sm:h-screen sm:align-middle"
          aria-hidden="true"
          >&#8203;</span
        >
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enter-to="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-200"
          leave-from="opacity-100 translate-y-0 sm:scale-100"
          leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <div
            class="relative inline-block overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle"
          >
            <div>
              <div class="mt-3" style="min-width: 300px">
                <DialogTitle
                  as="h3"
                  class="text-lg font-medium leading-6 text-gray-900"
                >
                  {{ props.dialogTitle }}
                </DialogTitle>
                <div class="mt-2">
                  <slot name="contents" />
                </div>
              </div>
            </div>
            <av-divider />
            <div class="mt-3 grid grid-flow-row-dense grid-cols-2 gap-3">
              <av-button @click="close"> Close </av-button>
              <slot name="buttons" :close="close" />
            </div>
          </div>
        </TransitionChild>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import {
    Dialog,
    DialogOverlay,
    DialogTitle,
    TransitionChild,
    TransitionRoot,
  } from '@headlessui/vue';
  import AvButton from './AvButton.vue';
  import AvDivider from './AvDivider.vue';

  const open = ref(false);

  const props = defineProps({
    buttonLabel: {
      type: String,
      required: true,
    },
    dialogTitle: {
      type: String,
      required: true,
    },
  });

  function close() {
    open.value = false;
  }
</script>
