<template>
  <div data-feedback-button v-show="!isFormOpen">
    <button>
      <svg
        width="20"
        height="20"
        xmlns="http://www.w3.org/2000/svg"
        style="transform: rotateZ(90deg); transform-origin: 50% 50%"
      >
        <defs>
          <linearGradient
            x1="23.656%"
            y1="50.828%"
            x2="57.117%"
            y2="52.047%"
            id="a"
          >
            <stop stop-color="currentColor" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="currentColor" offset="100%"></stop>
          </linearGradient>
        </defs>
        <g fill="none" fill-rule="evenodd">
          <path
            d="M0 12.8c3.833 1.333 7.166 2 10 2 2.315 0 4.518-.445 6.61-1.335a.997.997 0 011.39.918v.417c-2.167 1.333-4.834 2-8 2-3.166 0-6.5-.667-10-2v-2z"
            fill="url(#a)"
            transform="translate(1 2)"
          ></path>
          <path d="M2.636 11.6L4.273 10l3.272 3.2-1.636 1.6H2.636z"></path>
          <path
            d="M2.42 11.812l8.89-8.693a1.636 1.636 0 012.289 0l.932.911a1.636 1.636 0 010 2.34l-8.857 8.66a2 2 0 01-1.398.57H2.818a1 1 0 01-1-1v-1.358a2 2 0 01.602-1.43z"
            stroke="currentColor"
            stroke-width="1.75"
          ></path>
          <path
            d="M10.182 4.2c0 1.067.273 1.867.818 2.4.545.533 1.364.8 2.455.8"
            stroke="currentColor"
            stroke-width="1.636"
          ></path>
          <path d="M4.273 10.8l2.454 2.4 9-8.8L13.273 2z"></path>
        </g>
      </svg>
      <span>Share feedback</span>
    </button>
  </div>
</template>

<script setup lang="ts">
  import { once } from 'lodash';
  import { onMounted, ref } from 'vue';
  import * as Sentry from '@sentry/vue';
  //

  const isFormOpen = ref(false);

  const updateShadowCss = once(() => {
    let sheet = new CSSStyleSheet();
    sheet.replaceSync(
      `.dialog__title, form { color: #f9fafb; }` +
        `.editor__image-container { background-color: #14312c; }` +
        `.form__input { background-color: #fff; color: #14312c; }` +
        `.btn { outline: none; border: none; }` +
        `.btn:not([type=submit]) { font-weight: normal; text-decoration: underline; }`,
    );
    document
      .querySelector('#sentry-feedback')
      ?.shadowRoot?.adoptedStyleSheets.push(sheet);
  });

  Sentry.getClient()?.on(
    // @ts-expect-error - custom event
    'closeFeedbackWidget',
    () => (isFormOpen.value = false),
  );

  Sentry.getClient()?.on('openFeedbackWidget', () => {
    updateShadowCss();
    isFormOpen.value = true;
  });

  onMounted(() => {
    const feedback = Sentry.getFeedback();

    feedback?.attachTo(
      document.querySelector('[data-feedback-button]') as HTMLButtonElement,
      {
        formTitle: 'Submit feedback',
      },
    );
  });
</script>
