<template>
  <div>
    <p class="block text-sm font-medium text-gray-700">
      {{ props.label }}
    </p>
    <fieldset class="mt-4">
      <legend class="sr-only">Notification method</legend>
      <div class="space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0">
        <div
          v-for="option in options"
          :key="String(option.id)"
          class="flex items-center"
        >
          <input
            :id="String(option.id)"
            :name="props.name"
            type="radio"
            :value="option.id"
            :checked="option.id === inputValue"
            class="text-primary focus:ring-primary size-4 border-gray-300"
            @input="updateValue($event)"
          />
          <label
            :for="String(option.id)"
            class="ml-3 block text-sm text-gray-700"
          >
            {{ option.name }}
          </label>
        </div>
      </div>
    </fieldset>
    <div class="mt-2">
      <p v-if="errorMessage" id="email-error" class="text-sm text-red-600">
        {{ errorMessage }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PropType, toRef } from 'vue';
  import { Options } from '../models/refdata';
  import { useField } from 'vee-validate';

  const props = defineProps({
    modelValue: {
      type: [String, Number, Boolean, null] as PropType<any>,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    options: {
      type: Array as PropType<Options[]>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  function updateValue(event: Event) {
    handleChange(event);
    emit('update:modelValue', (event.target as HTMLInputElement).value);
  }

  const name = toRef(props, 'name');

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
    meta,
  } = useField(name, undefined, {
    initialValue: props.modelValue,
  });
</script>
