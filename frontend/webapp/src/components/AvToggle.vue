<template>
  <SwitchGroup as="div" class="flex items-center">
    <Switch
      v-model="value"
      :class="[
        value ? 'bg-primary' : 'bg-gray-200',
        'focus:ring-primary relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2',
      ]"
    >
      <span
        aria-hidden="true"
        :class="[
          value ? 'translate-x-5' : 'translate-x-0',
          'pointer-events-none inline-block size-5 rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
        ]"
      />
    </Switch>
    <SwitchLabel as="span" class="ml-3">
      <span class="text-sm font-medium text-gray-900">{{ props.label }}</span>
    </SwitchLabel>
  </SwitchGroup>
</template>
<script setup lang="ts">
  import { Switch, SwitchGroup, SwitchLabel } from '@headlessui/vue';
  import { ref, watch } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
  });

  const value = ref(props.modelValue);

  const emit = defineEmits(['update:modelValue']);

  watch(value, (value, prevValue) => {
    if (value != prevValue) emit('update:modelValue', value);
  });
</script>
