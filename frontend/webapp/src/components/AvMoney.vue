<template>
  {{ getFormattedAmount() }}
</template>

<script setup lang="ts">
  const props = defineProps({
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      required: false,
      default: 'GBP',
    },
  });

  function getFormattedAmount() {
    const formatter = new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: props.currency,
    });

    return formatter.format(props.amount);
  }
</script>
