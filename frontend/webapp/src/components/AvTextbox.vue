<template>
  <div>
    <label
      :for="props.name"
      class="block font-sans text-sm font-medium text-gray-700"
    >
      {{ props.label }}
    </label>
    <div class="relative mt-1 rounded-md shadow-sm">
      <input
        :id="props.name"
        :value="inputValue"
        type="text"
        :name="props.name"
        :autocomplete="props.autocomplete"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        class="focus:border-primary focus:ring-primary mt-1 block w-full rounded-md border-gray-300 shadow-sm disabled:border-slate-200 disabled:bg-gray-50 disabled:text-slate-500 disabled:shadow-none sm:text-sm"
        @input="updateValue($event)"
        @blur="handleBlur"
      />
      <div
        v-show="errorMessage"
        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"
      >
        <ExclamationCircleIcon class="size-5 text-red-500" aria-hidden="true" />
      </div>
    </div>
    <div class="mt-2">
      <p v-if="errorMessage" id="email-error" class="text-sm text-red-600">
        {{ errorMessage }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PropType, toRef } from 'vue';
  import { ExclamationCircleIcon } from '@heroicons/vue/20/solid';
  import { useField } from 'vee-validate';

  const props = defineProps({
    modelValue: {
      type: [Number, String, null] as PropType<any>,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    autocomplete: {
      type: String,
      required: false,
      default: '',
    },
    placeholder: {
      type: String,
      required: false,
      default: '',
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  function updateValue(event: Event) {
    handleChange(event);
    emit('update:modelValue', (event.target as HTMLInputElement).value);
  }

  const name = toRef(props, 'name');

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
    meta,
  } = useField(name, undefined, {
    initialValue: props.modelValue,
  });
</script>

<style scoped></style>
