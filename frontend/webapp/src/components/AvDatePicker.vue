<template>
  <div>
    <label :for="props.name" class="block text-sm font-medium text-gray-700">{{
      props.label
    }}</label>
    <div class="relative mt-1 rounded-md shadow-sm">
      <input
        :id="props.name"
        :value="inputValue"
        type="date"
        :name="props.name"
        class="focus:border-primary focus:ring-primary mt-1 block w-full rounded-md border-gray-300 shadow-sm sm:text-sm"
        @input="updateValue($event)"
      />
      <div
        v-show="errorMessage"
        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"
      >
        <ExclamationCircleIcon class="size-5 text-red-500" aria-hidden="true" />
      </div>
    </div>
    <div class="mt-2">
      <p v-if="errorMessage" id="email-error" class="text-sm text-red-600">
        {{ errorMessage }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PropType, toRef } from 'vue';
  import { ExclamationCircleIcon } from '@heroicons/vue/20/solid';
  import { useField } from 'vee-validate';

  const props = defineProps({
    modelValue: {
      type: [Date, String, null] as PropType<any>,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  function updateValue(event: Event) {
    handleChange(event);
    emit('update:modelValue', (event.target as HTMLInputElement).value);
  }

  const name = toRef(props, 'name');

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
    meta,
  } = useField(name, undefined, {
    initialValue: props.modelValue,
  });
</script>

<style scoped></style>
