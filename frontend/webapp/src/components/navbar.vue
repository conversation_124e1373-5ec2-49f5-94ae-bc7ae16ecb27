<template>
  <div class="sticky top-0 z-10 shrink-0 bg-gray-100 py-4" :class="props.class">
    <div
      class="mx-auto flex max-w-7xl flex-1 items-center justify-between px-4 sm:px-6 md:px-8"
    >
      <sidebar-open-button @on-sidebar-open="toggleSidebar" />
      <slot />
    </div>
    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
      <slot name="bottom-bar" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { inject } from 'vue';
  import SidebarOpenButton from '@/modules/ui/sidebar/sidebar-open-button.vue';

  const props = defineProps<{
    class?: string | Record<string, boolean>;
  }>();

  const toggleSidebar = inject('toggleSidebar') as () => void;
</script>
