<template>
  <div>
    <div class="sm:hidden">
      <label for="tabs" class="sr-only">Select a tab</label>
      <select
        id="tabs"
        v-model="selectedTab"
        name="tabs"
        class="focus:border-primary focus:ring-primary block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:outline-none sm:text-sm"
      >
        <option v-for="tab in tabs" :key="tab.slug" :value="tab.slug">
          {{ tab.title }}
        </option>
      </select>
    </div>
    <div class="hidden sm:block">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
          <div
            v-for="tab in tabs"
            :key="tab.slug"
            :class="[
              selectedTab === tab.slug
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
              'whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium',
            ]"
            :aria-current="selectedTab === tab.slug ? 'page' : undefined"
            @click="selectedTab = tab.slug"
          >
            {{ tab.title }}
          </div>
        </nav>
      </div>
    </div>
  </div>
  <slot />
</template>

<script setup lang="ts">
  import { provide, ref, useSlots } from 'vue';

  const slots = useSlots();

  const tabs = ref(
    slots.default?.({}).map((tab, _index) => {
      return { slug: tab.props?.slug, title: tab.props?.title };
    }),
  );
  const selectedTab = ref(tabs.value?.[0].slug);

  provide('selectedTab', selectedTab);
</script>

<style scoped></style>
