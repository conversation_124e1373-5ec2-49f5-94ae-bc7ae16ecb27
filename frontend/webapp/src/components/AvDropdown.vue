<template>
  <div>
    <label :for="props.name" class="block text-sm font-medium text-gray-700">{{
      props.label
    }}</label>
    <div class="relative mt-1 rounded-md shadow-sm">
      <select
        :id="props.name"
        :value="inputValue"
        :name="props.name"
        :autocomplete="props.autocomplete"
        :disabled="props.disabled"
        class="focus:border-primary focus:ring-primary mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:outline-none disabled:bg-gray-200 sm:text-sm"
        @input="updateValue($event)"
      >
        <option v-for="opt in options" :key="String(opt.id)" :value="opt.id">
          {{ opt.name }}
        </option>
      </select>
      <div
        v-show="errorMessage"
        class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"
      >
        <ExclamationCircleIcon class="size-5 text-red-500" aria-hidden="true" />
      </div>
    </div>
    <div class="mt-2">
      <p v-if="errorMessage" id="email-error" class="text-sm text-red-600">
        {{ errorMessage }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { PropType, toRef } from 'vue';
  import { Options } from '../models/refdata';
  import { useField } from 'vee-validate';
  import { ExclamationCircleIcon } from '@heroicons/vue/20/solid';

  const props = defineProps({
    modelValue: {
      type: [String, Number, null] as PropType<any>,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    autocomplete: {
      type: String,
      required: false,
      default: '',
    },
    options: {
      type: Array as PropType<Options[]>,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  function updateValue(event: Event) {
    handleChange(event);
    emit('update:modelValue', Number((event.target as HTMLInputElement).value));
  }

  const name = toRef(props, 'name');

  const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
    meta,
  } = useField(name, undefined, {
    initialValue: props.modelValue,
  });
</script>
