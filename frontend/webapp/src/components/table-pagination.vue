<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <div class="flex items-center justify-between bg-white">
    <div class="flex flex-1 justify-between sm:hidden">
      <a
        class="relative inline-flex cursor-pointer items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
        @click="handlePageChange(activePage - 1)"
      >
        Previous
      </a>
      <a
        class="relative ml-3 inline-flex cursor-pointer items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
        @click="handlePageChange(activePage + 1)"
      >
        Next
      </a>
    </div>
    <div
      class="hidden h-[38px] sm:flex sm:flex-1 sm:items-center sm:justify-between"
    >
      <div>
        <p class="text-sm text-gray-700">
          Showing
          {{ ' ' }}
          <span class="font-medium">{{ showItemsFrom }}</span>
          {{ ' ' }}
          to
          {{ ' ' }}
          <span class="font-medium">{{ showItemsTo }}</span>
          {{ ' ' }}
          of
          {{ ' ' }}
          <span class="font-medium">{{ props.totalItems }}</span>
          {{ ' ' }}
          results
        </p>
      </div>
      <div>
        <nav
          v-if="totalPages > 1"
          class="relative z-0 inline-flex -space-x-px rounded-md shadow-sm"
          aria-label="Pagination"
        >
          <a
            class="relative inline-flex cursor-pointer items-center rounded-l-md border border-gray-300 bg-white px-2 text-sm font-medium text-gray-500 hover:bg-gray-50"
            @click="handlePageChange(activePage - 1)"
          >
            <span class="sr-only">Previous</span>
            <ChevronLeftIcon class="size-5" aria-hidden="true" />
          </a>
          <a
            v-for="page in totalPages"
            :key="page"
            :aria-current="isCurrentPage(page) ? 'page' : undefined"
            :class="{
              'relative inline-flex cursor-pointer items-center border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50':
                !isCurrentPage(page),
              'relative z-10 inline-flex cursor-pointer items-center border border-indigo-500 bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-600':
                isCurrentPage(page),
            }"
            @click="handlePageChange(page)"
            >{{ page }}</a
          >
          <a
            class="relative inline-flex cursor-pointer items-center rounded-r-md border border-gray-300 bg-white p-2 text-sm font-medium text-gray-500 hover:bg-gray-50"
            @click="handlePageChange(activePage + 1)"
          >
            <span class="sr-only">Next</span>
            <ChevronRightIcon class="size-5" aria-hidden="true" />
          </a>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/20/solid';
  import { computed } from 'vue';

  const props = defineProps({
    totalItems: {
      type: Number,
      required: true,
    },
    perPage: {
      type: Number,
      default: 10,
    },
    activePage: {
      type: Number,
      default: 1,
    },
  });

  const emit = defineEmits<{
    (e: 'on-page-change', page: number): void;
  }>();

  const showItemsFrom = computed<number>(() => {
    if (props.activePage > totalPages.value) {
      return 0;
    }
    return props.activePage * props.perPage - (props.perPage - 1);
  });

  const preCalculateItemsTo = computed<number>(
    () => showItemsFrom.value + (props.perPage - 1),
  );

  const showItemsTo = computed<number>(() => {
    if (props.activePage > totalPages.value) {
      return 0;
    }
    if (preCalculateItemsTo.value > props.totalItems) {
      return props.totalItems;
    }
    return preCalculateItemsTo.value;
  });

  const totalPages = computed<number>(() =>
    Math.ceil(props.totalItems / props.perPage),
  );

  function handlePageChange(page: number) {
    if (page > 0 && page <= totalPages.value) {
      emit('on-page-change', page);
    }
  }

  function isCurrentPage(page: number): boolean {
    return props.activePage === page;
  }
</script>
