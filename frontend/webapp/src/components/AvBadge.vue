<template>
  <span
    class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
    :class="classObject"
  >
    <slot />
  </span>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  const props = defineProps({
    colour: {
      type: String,
      required: true,
    },
  });

  const classObject = computed(() => ({
    'bg-gray-100 text-gray-800': props.colour == 'gray',
    'bg-green-100 text-green-800': props.colour == 'green',
    'bg-red-100 text-red-800': props.colour == 'red',
    'bg-yellow-100 text-yellow-800': props.colour == 'yellow',
  }));
</script>

<style scoped></style>
