<template>
  <Line
    :options="chartOptions"
    :data="chartData"
    :chart-id="chartId"
    :plugins="plugins"
    :css-classes="cssClasses"
    :styles="styles"
    :width="width"
    :height="height"
  />
</template>

<script setup lang="ts">
  import { Line } from 'vue-chartjs';
  import 'chartjs-adapter-date-fns';
  import {
    CategoryScale,
    ChartData,
    Chart as ChartJS,
    ChartOptions,
    Legend,
    LineElement,
    LinearScale,
    Plugin,
    PointElement,
    TimeScale,
    Title,
    Tooltip,
  } from 'chart.js';
  import { PropType, ref } from 'vue';

  ChartJS.register(
    Title,
    Tooltip,
    Legend,
    LineElement,
    LinearScale,
    PointElement,
    CategoryScale,
    TimeScale,
  );

  const props = defineProps({
    chartId: {
      type: String,
      default: 'line-chart',
    },
    data: {
      type: Array,
      default: () => [],
      required: true,
    },
    width: {
      type: Number,
      default: 400,
    },
    height: {
      type: Number,
      default: 200,
    },
    cssClasses: {
      default: '',
      type: String,
    },
    styles: {
      type: Object as PropType<Partial<CSSStyleDeclaration>>,
      default: () => ({}),
    },
    plugins: {
      type: Array as PropType<Plugin<'line'>[]>,
      default: () => [],
    },
  });

  const chartData = ref<ChartData<'line'>>({
    datasets: [],
  });

  const chartOptions = ref<ChartOptions<'line'>>({
    responsive: true,
    plugins: {
      legend: { position: 'bottom' },
    },
    scales: {
      // eslint-disable-next-line id-length
      y: {
        ticks: {
          // Include a dollar sign in the ticks
          callback: function (value: number | string) {
            return '£' + value;
          },
        },
      },
      // eslint-disable-next-line id-length
      x: {
        type: 'time',
        time: {
          unit: 'month',
        },
      },
    },
  });

  chartData.value = {
    labels: props.data.map((value: any) => new Date(value.date)),
    datasets: [
      {
        label: 'Valuations',
        backgroundColor: '#2F95AE',
        data: props.data.map((value: any) => value.total),
      },
    ],
  };
</script>
