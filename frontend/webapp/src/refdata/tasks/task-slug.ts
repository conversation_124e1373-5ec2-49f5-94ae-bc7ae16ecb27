export const allSlugs = [
  'withdrawl_request_received',
  'welcome_email',
  'trust_forms_completed',
  'supplementary_factfind',
  'summary_completed',
  'submit_business_to_provider',
  'set_review_date',
  'send_planning_report_to_client',
  'send_confirmation_of_completion_to_client',
  'send_client_review',
  'research_saved',
  'research_completed',
  'request_client_review',
  'provider_docs_to_client',
  'privacy_notice',
  'planning_report_generated',
  'planning_report_completed',
  'ongoing_provider_chasing',
  'meeting_pack',
  'meeting_2',
  'meeting_1',
  'loa_sent_to_providers',
  'loa_sent_to_client',
  'loa_completed',
  'initial_meeting',
  'goal_assigning',
  'full_plan_info_updated',
  'full_plan_info_received',
  'full_plan_info_check',
  'final_illustration_saved',
  'final_check_advice_implemented',
  'fee_chasing',
  'factfind_updated',
  'factfind_up_to_date',
  'compliance_check',
  'complete_plan_info',
  'complete_file_check',
  'client_agreement_updated',
  'client_agreement_completed',
  'book_meeting_1',
  'atr_questions',
  'atr_forecaster',
  'arrange_initial_meeting',
  'app_form_completed',
  'annual_review_message_to_client',
  'aml_completion',
  'advice_selection',
  'advice_planning',
  'advice_implementation',
  'add_new_busiess_case',
  'add_fee_est',
  'accounts_to_review',
  'acceptance_from_client',
] as const;

export type TaskSlug = (typeof allSlugs)[number];
