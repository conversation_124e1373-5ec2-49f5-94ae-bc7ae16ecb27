import { array, number, object, string } from 'yup';
import { apiClient } from '@aventur-shared/services/api';
import { ClientStatuses } from '@aventur-shared/modules/clients';

const getClientStatusesValidationSchema = array()
  .of(
    object({
      id: number().required(),
      name: string().defined().required(),
    }),
  )
  .required();

export default async (): Promise<ClientStatuses[]> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/refdata/client_statuses/`,
  );

  const clientStatuses =
    await getClientStatusesValidationSchema.validate(response);
  return clientStatuses.map((dto) => ({
    id: dto.id,
    name: dto.name,
  }));
};
