import { apiClient } from '@aventur-shared/services/api';
import { User, UserDTO } from '@aventur-shared/modules/users';

export async function getUser(): Promise<User> {
  const userDTO = await apiClient.get<Promise<UserDTO>>('/api/v1/user/me');

  return {
    id: userDTO.id,
    email: userDTO.email,
    firstName: userDTO.first_name,
    lastName: userDTO.last_name,
    type: userDTO.type,
    permissions: userDTO.permissions,
  };
}
