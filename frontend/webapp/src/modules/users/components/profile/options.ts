import {
  ArrowRightOnRectangleIcon,
  Cog6ToothIcon,
} from '@heroicons/vue/24/outline';
import type { FunctionalComponent, HTMLAttributes, VNodeProps } from 'vue';

export interface MenuOptions
  extends Array<{
    icon: FunctionalComponent<HTMLAttributes & VNodeProps>;
    name: string;
    path: string;
    disabled: boolean;
  }> {}

export const options: MenuOptions = [
  { icon: Cog6ToothIcon, name: 'Setting<PERSON>', path: '/settings', disabled: false },
  {
    icon: ArrowRightOnRectangleIcon,
    name: 'Logout',
    path: '/logout',
    disabled: false,
  },
];
