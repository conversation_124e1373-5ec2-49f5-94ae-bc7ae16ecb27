<template>
  <Menu as="div" class="relative ml-3">
    <div>
      <MenuButton
        class="focus:ring-primary flex max-w-xs items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-offset-2"
      >
        <span class="sr-only">Open user menu</span>
        <span
          class="inline-block size-8 overflow-hidden rounded-full bg-gray-100"
        >
          <svg
            class="text-primary size-full"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
        </span>
      </MenuButton>
    </div>
    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <MenuItems
        class="absolute right-0 z-10 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none"
      >
        <div class="px-4 py-3">
          <p class="text-sm">Signed in as</p>
          <p class="truncate text-sm font-semibold text-gray-900">
            {{ user.email }}
          </p>
        </div>
        <div class="py-1">
          <MenuItem
            v-for="item in options.slice(0, -1)"
            :key="item.name"
            v-slot="{ active, disabled }"
            :disabled="item.disabled"
          >
            <router-link
              :class="[
                active ? 'bg-gray-100' : '',
                disabled ? 'cursor-not-allowed text-gray-500/50' : '',
                'flex items-center justify-start px-4 py-2 text-sm text-gray-500',
              ]"
              :to="item.path"
            >
              <component :is="item.icon" class="mr-3 size-5" />
              {{ item.name }}
            </router-link>
          </MenuItem>
        </div>
        <div class="py-1">
          <MenuItem
            v-for="item in options.slice(-1)"
            :key="item.name"
            v-slot="{ active }"
          >
            <router-link
              :class="[
                active ? 'bg-gray-100' : '',
                'flex items-center justify-start px-4 py-2 text-sm text-gray-500',
              ]"
              :to="item.path"
            >
              <component :is="item.icon" class="mr-3 size-5" />
              {{ item.name }}
            </router-link>
          </MenuItem>
        </div>
      </MenuItems>
    </transition>
  </Menu>
</template>

<script setup lang="ts">
  import { RouterLink } from 'vue-router';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { type MenuOptions } from './options';
  import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';

  defineProps<{ options: MenuOptions }>();
  const { user } = useUserStore();
</script>
