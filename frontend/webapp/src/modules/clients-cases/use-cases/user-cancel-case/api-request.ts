import { apiClient } from '@aventur-shared/services/api';
import { CaseCommand, CaseId } from '@aventur-shared/modules/cases';

const caseCommand = CaseCommand.CancelCase;

interface Payload extends Record<string, unknown> {
  reason?: string;
}

export default (caseId: CaseId, reason: string): Promise<void> =>
  apiClient.patch<{ command: typeof caseCommand; payload: Payload }, void>(
    `/api/v2/case/${caseId}`,
    {
      command: caseCommand,
      payload: {},
    },
  );
