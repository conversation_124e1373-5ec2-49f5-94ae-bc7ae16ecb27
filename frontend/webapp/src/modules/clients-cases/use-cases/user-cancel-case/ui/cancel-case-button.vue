<template>
  <popup-options>
    <template #label>
      <span
        role="button"
        class="whitespace-nowrap text-base font-semibold text-red-500"
        @click="handleCancelCase"
      >
        Cancel case
      </span>
    </template>
  </popup-options>
</template>

<script setup lang="ts">
  import { PopupOptions } from '@modules/ui';
  import {
    CancelReason,
    CancelReasonEnum,
  } from '@modules/clients-cases/use-cases/user-cancel-case/cancel-reason';

  export type OnCancelRequestedArgs = {
    reason: CancelReason;
  };

  const emit = defineEmits<{
    (e: 'on-cancel-requested', args: OnCancelRequestedArgs): void;
  }>();

  const handleCancelCase = () => {
    emit('on-cancel-requested', {
      reason: CancelReasonEnum.UNKNOWN,
    });
  };
</script>
