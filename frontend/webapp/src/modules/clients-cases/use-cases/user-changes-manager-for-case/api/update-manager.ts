import { apiClient } from '@aventur-shared/services/api';
import { CaseCommand, CaseId } from '@aventur-shared/modules/cases';
import { Advisor } from '@aventur-shared/modules/advisors';

const caseCommand = CaseCommand.UpdateCaseManager;

interface Payload {
  manager_id: number;
}

export default async (caseId: CaseId, managerId: Advisor['id']) => {
  await apiClient.patch<
    { command: typeof caseCommand; payload: Payload },
    void
  >(`/api/v2/case/${caseId}`, {
    command: caseCommand,
    payload: {
      manager_id: managerId,
    },
  });
};
