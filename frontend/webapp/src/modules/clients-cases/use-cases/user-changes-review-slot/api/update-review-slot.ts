import { apiClient } from '@aventur-shared/services/api';
import { CaseCommand, CaseId } from '@aventur-shared/modules/cases';
import { ReviewSlot } from '@aventur-shared/modules/upcoming-reviews';

const caseCommand = CaseCommand.UpdateCaseReviewSlot;

interface Payload {
  review_slot_id: number;
}

export default async (
  caseId: CaseId,
  reviewSlotId: ReviewSlot['id'],
): Promise<void> => {
  await apiClient.patch<
    { command: typeof caseCommand; payload: Payload },
    void
  >(`/api/v2/case/${caseId}`, {
    command: caseCommand,
    payload: {
      review_slot_id: reviewSlotId,
    },
  });
};
