<template>
  <form @submit="onSubmit">
    <select-field
      label=""
      :value="reviewSlot?.id ? reviewSlot.id.toString() : undefined"
      name="reviewSlotId"
      :options="reviewSlotSelectOptions"
      :searchable="true"
    />
    <div class="flex gap-4">
      <custom-button type="submit" theme="primary" class="flex-1"
        >Change review slot</custom-button
      >
      <custom-button type="button" theme="primary-ghost" @on-click="onCancel"
        >Cancel</custom-button
      >
    </div>
  </form>
</template>

<script setup lang="ts">
  import { SelectField } from '@aventur-shared/components/form';
  import { Button as CustomButton } from '@modules/ui';
  import { useForm } from 'vee-validate';
  import { type FormValues, validationSchema } from './form-model';
  import { ReviewSlot } from '@aventur-shared/modules/upcoming-reviews';
  import { Nullable } from '@aventur-shared/types/Common';
  import { computed } from 'vue';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

  const props = defineProps<{
    availableReviewSlots: ReviewSlot[];
    reviewSlot: Nullable<ReviewSlot>;
  }>();

  const emit = defineEmits(['on-review-slot-change', 'on-cancel']);

  const { handleSubmit } = useForm<FormValues>({
    validationSchema,
    initialValues: {
      reviewSlotId: props.reviewSlot?.id.toString(),
    },
  });

  const reviewSlotSelectOptions = computed<SelectOption[]>(() => {
    const slots = props.availableReviewSlots.map((reviewSlot) => ({
      value: reviewSlot.id,
      label: reviewSlot.description,
    }));
    if (props.reviewSlot) {
      return [
        ...slots,
        {
          label: props.reviewSlot.description,
          value: props.reviewSlot.id,
          disabled: true,
        },
      ];
    }
    return slots;
  });

  const onSubmit = handleSubmit((formValues) => {
    emit(
      'on-review-slot-change',
      props.availableReviewSlots.find(
        (slot) => slot.id === Number(formValues.reviewSlotId),
      ),
    );
  });

  const onCancel = (e: MouseEvent) => {
    emit('on-cancel', e);
  };
</script>
