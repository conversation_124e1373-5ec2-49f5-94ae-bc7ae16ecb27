import { CaseListItem } from '@modules/clients-cases/models';
import getClientCases from '@modules/clients-cases/api/get-client-cases';
import { ClientId } from '@aventur-shared/modules/clients';
import { Paginable } from '@aventur-shared/types/Pagination';

export const userPreviewAllCasesAction = async (
  client_id: ClientId,
  page: number,
): Promise<Paginable<CaseListItem>> | never => {
  try {
    const { items, totalItems } = await getClientCases({ client_id, page });

    return { items, totalItems };
  } catch (e: unknown) {
    throw new Error('Could not fetch cases.');
  }
};
