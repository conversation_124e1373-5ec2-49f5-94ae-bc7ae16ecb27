import { fetchCase } from '@aventur-shared/modules/cases';
import { CaseId } from '@aventur-shared/modules/cases';
import { useClientCase } from '@modules/clients-cases/store/client-case-store';
import { useCaseClients } from '@modules/clients-cases/store/client-case-clients';

export const userPreviewCaseAction = async (caseId: CaseId) => {
  try {
    const { storeCase } = useClientCase();
    const { storeCaseClients } = useCaseClients();
    const clientCase = await fetchCase(caseId);
    storeCase(clientCase);
    storeCaseClients(clientCase.clients);

    return { clientCase };
  } catch (e: unknown) {
    throw new Error('Could not fetch case.');
  }
};
