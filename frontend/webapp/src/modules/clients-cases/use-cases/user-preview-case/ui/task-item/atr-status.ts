import { Nullable } from '@aventur-shared/types/Common';

interface WithTimestamp {
  updated_at?: Nullable<string>;
}

export enum Status {
  REQUIRED = 'Required',
  COMPLETED = 'Completed',
  IN_PROGRESS = 'In Progress',
  REVIEW = 'Needs Review',
}

export const getStatus = (data: WithTimestamp | null) => {
  return data
    ? data.updated_at
      ? Status.COMPLETED
      : data.updated_at === null
        ? Status.REVIEW
        : Status.IN_PROGRESS
    : Status.REQUIRED;
};
