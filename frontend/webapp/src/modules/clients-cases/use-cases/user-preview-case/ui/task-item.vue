<template>
  <details
    ref="details"
    class="group cursor-pointer border-b border-gray-100 px-8 py-3 hover:bg-gray-50 open:hover:bg-transparent"
    :data-type="task.type"
    :data-slug="task.slug"
    :data-descriptions="task.description"
  >
    <summary class="grid grid-cols-12 items-center" @click.stop="toggleOpen()">
      <div class="col-span-4 flex flex-row items-center gap-2 text-sm">
        <div
          :class="classObject"
          class="min-w-[24px] cursor-pointer"
          @click.prevent.stop="handleAvatarClick"
        >
          <p v-if="task.advisor" class="text-xs text-black">
            {{
              `${task.advisor?.firstName?.charAt(0)}${task.advisor?.lastName?.charAt(0)}`
            }}
          </p>
          <p v-else class="p-2 text-xl text-[#7A8796]">
            {{ isReadonly ? '' : '+' }}
          </p>
        </div>
        <p data-testid="task-description">{{ task.description }}</p>
        <InformationCircleIcon
          v-tippy="`${task.process_description}`"
          class="size-5 text-gray-500 hover:cursor-help"
        />
      </div>
      <div class="col-span-2 text-sm">{{ assignedTo }}</div>
      <div class="col-span-2 text-sm">{{ dueDate }}</div>
      <div class="col-span-2 text-sm" data-testid="task-status">
        <template v-if="!isReadonly">
          <popup-options
            :options="taskStatusOptions"
            options-wrapper-class="xl:-ml-8 -mt-2"
            @selected="handleSelectedStatus"
          >
            <template #label>
              <status-indicator :status="task.status.toType()" class="mr-2" />
              <span :class="{ 'text-rose-500': isTaskInReview(task) }">
                {{ task.status.toString() }}
              </span>
            </template>
            <template #option="{ option }">
              <status-indicator
                :status="getStatus(option.value)"
                class="mr-2"
              />
              {{ option.label }}
            </template>
          </popup-options>
        </template>
        <template v-else>
          <status-indicator :status="task.status.toType()" class="mr-2" />
          <span :class="{ 'text-rose-500': isTaskInReview(task) }">
            {{ task.status.toString() }}
          </span>
        </template>
      </div>
      <div class="col-span-2 grid grid-cols-subgrid">
        <div class="col-start-2 -mb-2 justify-self-end">
          <chevron-up-icon
            class="-mt-1 size-5 text-gray-400 group-open:group-hover:text-black"
          />
          <chevron-down-icon
            class="-mt-2 size-5 text-gray-400 group-hover:text-black group-open:group-hover:text-gray-400"
          />
        </div>
      </div>
    </summary>
    <slot :open="isOpen" />
  </details>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { useToggle } from '@vueuse/core';
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { find, get } from 'lodash';
  import { storeToRefs } from 'pinia';
  import { PopupOptions } from '@modules/ui';
  import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/vue/20/solid';
  import { useAdvisorsStore } from '@aventur-shared/modules/advisors';
  import { formatName } from 'aventur-shared/src/utils/user/formatName';
  import {
    Task,
    isTaskInReview,
  } from '@aventur-shared/modules/tasks/models/task';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { TaskStatus, taskStatusOptions } from '@aventur-shared/modules/tasks';
  import { StatusIndicator } from '@modules/tasks';
  import { InformationCircleIcon } from '@heroicons/vue/24/outline';

  const { hash } = useRoute();
  const [isOpen, toggleOpen] = useToggle(false);
  const { getAdvisorsRoles } = storeToRefs(useAdvisorsStore());

  const props = defineProps<{
    task: Task;
    isReadonly: boolean;
  }>();

  const emit = defineEmits(['on-update-status', 'on-avatar-click']);

  const details = ref<HTMLDetailsElement | null>(null);

  const dueDate = computed(
    () => props.task.dueDate?.formatWithShortName() || '-',
  );

  const assignedTo = computed(() => {
    if (props.task.advisor) {
      return formatName({
        firstName: props.task.advisor.firstName as string,
        lastName: props.task.advisor.lastName,
      });
    }
    if (props.task.assignedGroup) {
      const $group = find(getAdvisorsRoles.value, {
        value: props.task.assignedGroup,
      });
      if ($group) return `Group: ${get($group, 'label')}`;
    }
    return '-';
  });

  const classObject = computed(() => ({
    'bg-[#F7C844] border-2 border-transparent border-solid': props.task.advisor,
    'border-solid border border-[#7A8796] bg-white': !props.task.advisor,
    'w-6 h-6 rounded-full flex justify-center items-center': true,
    'hover:border-[#7A8796] hover:border-2': !props.isReadonly,
  }));

  onMounted(() => {
    openDetailsIfHashInUrl();
  });

  const openDetailsIfHashInUrl = () => {
    const expectedElId = `#task-${props.task.slug}`;
    if (hash.includes(expectedElId)) {
      openDetails();
    }
  };

  const handleSelectedStatus = (option: SelectOption) => {
    emit('on-update-status', option.value);
  };

  const openDetails = () => {
    details.value?.setAttribute('open', 'true');
    toggleOpen(true);
  };

  const handleAvatarClick = () => {
    if (props.isReadonly) return;
    if (!isOpen.value) openDetails();

    nextTick(() => emit('on-avatar-click'));
  };

  const getStatus = (value: string | number | boolean) => {
    return new TaskStatus(Number(value as string)).toType();
  };
</script>
