<template>
  <box no-rounded-b>
    <box-section divider="bottom" no-padding-x class="grid grid-cols-12 px-8">
      <div class="col-span-4 self-center">
        <box-title class="text-gray-400">{{
          countText(clientCase.tasks.length, 'task')
        }}</box-title>
      </div>
      <span class="col-span-2 self-center text-sm text-gray-400">Assignee</span>
      <span class="col-span-2 self-center text-sm text-gray-400">Due date</span>
      <span class="col-span-2 self-center text-sm text-gray-400">Status</span>
      <div
        class="col-span-12 my-3 self-center text-sm text-gray-400 lg:col-span-2 lg:my-0"
      >
        <TaskListFilter
          :value="taskFilter"
          :tasks="clientCase.tasks"
          @filter-tasks="handleTaskFilter"
        />
      </div>
    </box-section>
    <box-section no-padding>
      <template v-for="task in clientCase.tasks" :key="`task-${task.slug}`">
        <TaskItem
          v-slot="{ open }"
          :task="task"
          :is-readonly="isCaseReadonly || !task.isModifiable"
          @on-update-status="(value) => handleChangeStatusWrapper(task, value)"
          @on-avatar-click="handleAvatarClick(task.slug)"
        >
          <TaskItemDetail
            v-if="open"
            :ref="
              (el) =>
                (itemDetailArray[task.slug] = el as TaskItemDetailInstanceType)
            "
            :client-case="clientCase"
            :task="task"
            :is-readonly="isCaseReadonly || !task.isModifiable"
            @on-update-status="
              (value) => handleChangeStatusWrapper(task, value)
            "
          />
        </TaskItem>
      </template>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { computed, onMounted, ref, unref } from 'vue';
  import { Box, BoxSection } from '@modules/ui';
  import { countText } from '@aventur-shared/utils/ui/count-text';
  import { Case } from '@aventur-shared/modules/cases';
  import { Task, TaskStatus } from '@aventur-shared/modules/tasks';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { isAccountToReview } from '@aventur-shared/modules/accounts';
  import TaskItem from './task-item.vue';
  import TaskItemDetail from './task-item-detail.vue';
  import TaskListFilter from './task-list-filter.vue';
  import BoxTitle from '@modules/ui/box/box-title.vue';
  import { updateTaskStatus } from '@modules/clients-cases';
  import {
    StatusChangeValidationFlags,
    getTaskReopenValidators,
    getTaskStatusChangeValidators,
  } from '@modules/clients-cases/use-cases/user-update-task/status-change-validation';
  import { useClientCase } from '@modules/clients-cases/store/client-case-store';

  const toast = useToast();
  const route = useRoute();
  const { getCaseAccounts } = useClientCase();

  const props = defineProps<{
    clientCase: Case;
    isCaseReadonly: boolean;
  }>();

  type TaskItemDetailInstanceType = InstanceType<typeof TaskItemDetail> | null;

  const itemDetailArray = ref<Record<string, TaskItemDetailInstanceType>>({});

  const accountsToReview = computed(() => getCaseAccounts(isAccountToReview));

  // task filtering
  const taskFilter = ref<string>('');
  const handleTaskFilter = (needle) => {
    document.querySelectorAll('[data-slug]').forEach((el: Element) => {
      const slug = el.getAttribute('data-slug');
      slug?.toLowerCase().includes((needle ?? '').toLowerCase()) &&
        el.classList.remove('hidden');
      !slug?.toLowerCase().includes((needle ?? '').toLowerCase()) &&
        el.classList.add('hidden');
    });
  };

  const handleChangeStatusWrapper = async (task: Task, value: number) => {
    if (value === task.status.toType()) return;

    const confirmationMessageTitle =
      'Confirmation is required to complete this task';

    const validators = [
      ...getTaskStatusChangeValidators(task, unref(accountsToReview), value),
      ...getTaskReopenValidators(task, value),
    ];

    for (const validator of validators) {
      if (validator.statement) {
        const { isAccepted } = await useConfirmation(
          validator.confirmationMessage,
          confirmationMessageTitle,
        );
        return (
          isAccepted() &&
          (await handleChangeStatus(task, value, {
            [validator.validatorName]: true,
          }))
        );
      }
    }

    return await handleChangeStatus(task, value);
  };

  const handleChangeStatus = async (
    task: Task,
    value: number,
    statusChangeValidators?: StatusChangeValidationFlags,
  ) => {
    if (!value) return;
    try {
      await updateTaskStatus(
        props.clientCase.id,
        task.slug,
        new TaskStatus(value),
        statusChangeValidators,
      );
      toast.success('Task status changed', {
        timeout: 1500,
        hideProgressBar: true,
      });
    } catch (e: unknown) {
      toast.error(e as Error, 'Could not update status.');
      itemDetailArray.value[task.slug]?.resetStatusSelect(
        task.status.toType().toString(),
      );
    }
  };

  const handleAvatarClick = (taskSlug: Task['slug']) => {
    debugger;
    itemDetailArray.value[taskSlug]?.focusCleanSelect();
  };

  const setFilterIfHashInUrl = () => {
    const expectedStr = '#task-';
    if (route.hash.includes(expectedStr)) {
      taskFilter.value = route.hash.replace(expectedStr, '');
      handleTaskFilter(taskFilter.value);
    }
  };

  const updateHistoryState = () =>
    history.replaceState(
      {
        ...history.state,
        current: route.path,
      },
      '',
      route.path,
    );

  onMounted(() => {
    setFilterIfHashInUrl();
    updateHistoryState();
  });
</script>

<style lang="pcss" scoped>
  details:not([data-type='Default']) {
    @apply bg-primary-50 hover:bg-gray-50;
    &[open] {
      @apply hover:bg-primary-50;
    }
  }

  details[open] summary ~ * {
    animation: ease-opacity-t-b 0.5s ease;
  }

  summary {
    cursor: pointer;
  }

  svg {
    transition: all 0.3s;
  }

  summary::-webkit-details-marker {
    display: none;
  }

  :focus {
    outline: none;
  }
</style>
