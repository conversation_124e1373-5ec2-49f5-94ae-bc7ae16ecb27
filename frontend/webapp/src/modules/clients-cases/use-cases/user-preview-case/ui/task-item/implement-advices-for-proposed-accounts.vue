<template>
  <div>
    <h1>Proposed accounts</h1>
    <t-table class="border-separate border-spacing-y-2">
      <t-head>
        <t-head-tr>
          <t-head-th class="px-0 text-base leading-none">Provider</t-head-th>
          <t-head-th class="text-base leading-none">Type</t-head-th>
          <t-head-th class="text-base leading-none">Clients</t-head-th>
          <t-head-th class="text-base leading-none">Acc No.</t-head-th>
          <t-head-th class="text-base leading-none">Sub Acc No.</t-head-th>
          <t-head-th class="text-base leading-none">Advice</t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="account in accounts"
          :key="account.id"
          class="relative border-none px-4 py-3 text-sm"
        >
          <t-body-td class="rounded-l-xl">
            {{ account.providerName }}
          </t-body-td>
          <t-body-td>
            {{ account.type.toString() }}
          </t-body-td>
          <t-body-td
            ><ul>
              <li
                v-for="client in account.clients"
                :key="client.id"
                class="whitespace-nowrap"
              >
                {{ formatName(client) }}
              </li>
            </ul></t-body-td
          >
          <t-body-td>{{ account.accountNumber || '-' }}</t-body-td>
          <t-body-td>{{ account.subAccountNumber || '-' }}</t-body-td>
          <t-body-td class="rounded-r-xl">
            <modal>
              <template #open-button="{ open }">
                <button
                  v-if="getImplementedAdvicesCount(account.advices)"
                  class="hover:-m-2 hover:rounded-full hover:bg-gray-100 hover:p-2"
                  @click="open"
                >
                  Completed
                  {{ getImplementedAdvicesCount(account.advices) }}/{{
                    getAcceptedAdvicesCount(account.advices)
                  }}
                </button>
                <button
                  v-else
                  class="text-secondary whitespace-nowrap font-semibold hover:-m-2 hover:rounded-full hover:bg-gray-100 hover:p-2"
                  :class="{
                    'pointer-events-none cursor-not-allowed opacity-50':
                      !isModifiable,
                  }"
                  :disabled="!isModifiable"
                  @click="open"
                >
                  Confirm advice
                </button>
              </template>
              <template #default="{ close }">
                <confirm-advice-of-proposed-account
                  :case-id="caseId"
                  :goal="goal"
                  :account="account"
                  :case-advisor-id="caseAdvisorId"
                  :is-modifiable="isModifiable"
                  @on-submit="
                    () => {
                      handleAdviceSave();
                      close();
                    }
                  "
                  @on-cancel="close"
                />
              </template>
            </modal>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import { Case } from '@aventur-shared/modules/cases';
  import { formatName } from '@aventur-shared/utils/user';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { GoalAccount as Account, Goal } from '@aventur-shared/modules/goals';
  import {
    Modal,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { userPreviewCaseAction } from '@modules/clients-cases';
  import { ConfirmAdvice as ConfirmAdviceOfProposedAccount } from '@modules/sub-tasks/use-cases/user-confirms-advice-of-proposed-account';

  const props = defineProps<{
    caseId: Case['id'];
    goal: TaskGoal;
    accounts: Goal['accounts'];
    caseAdvisorId: Case['relatedAdvisor']['id'];
    isModifiable: boolean;
  }>();

  const getImplementedAdvicesCount = (advices: Account['advices']): number => {
    return advices.filter((advice) => advice.isImplemented).length;
  };

  const getAcceptedAdvicesCount = (advices: Account['advices']): number => {
    return advices.filter((advice) => advice.isAccepted).length;
  };

  const handleAdviceSave = async () => {
    await userPreviewCaseAction(props.caseId);
  };
</script>
