<template>
  <Disclosure
    v-slot="{ open }"
    :default-open="defaultOpen"
    as="div"
    class="bg-primary-50/35 mx-4 rounded"
  >
    <DisclosureButton
      class="flex w-full items-center justify-start text-left focus:outline-none"
      @click="emit('on-disclosure-btn-click')"
    >
      <p class="grow-0 p-4 font-medium text-gray-600">{{ goal.name }}</p>
      <ChevronRightIcon
        :class="open ? 'rotate-90 transform' : ''"
        class="size-5 grow-0"
      />
      <p class="grow px-4 text-right text-sm text-gray-400"></p>
      <div
        class="bg-primary-50/35 mx-4 flex flex-row items-center justify-end rounded text-right"
      >
        <documents-modal
          :documents="documents"
          :is-read-only="isReadonly"
          :documents-not-required="documentsNotRequired"
          :documents-not-required-reason="documentsNotRequiredReason"
          :documents-context="documentsContext"
          @documents-updated="handleDocumentsUpdated"
        >
          <template #activator="{ open: showDocuments }">
            <a
              class="text-primary flex flex-row items-center whitespace-nowrap text-xs hover:cursor-pointer hover:underline"
              @click.stop="showDocuments"
            >
              <RectangleStackIcon class="mr-1 size-4" />
              Documents ({{ documentsNotRequired ? 'N/A' : documents.length }})
            </a>
          </template>
          <template #header>
            <box-section>
              <div class="flex flex-col items-start">
                <h1 class="text-lg text-[#7A8796]">Task Documents</h1>
                <div class="my-2">
                  <dl>
                    <dt class="flex font-medium text-[#6DBFC0]">
                      <span>
                        {{ formatNames(goalClients) }}
                      </span>
                    </dt>
                    <dd class="flex text-white">
                      {{ task.description }}&nbsp;({{ goal.name }})
                    </dd>
                  </dl>
                </div>
              </div>
            </box-section>
          </template>
        </documents-modal>
      </div>
    </DisclosureButton>
    <DisclosurePanel class="flex flex-col">
      <template v-if="isAccountToReviewTask(task)">
        <box-section no-padding class="p-4">
          <task-item-accounts
            :clients="clientCase.clients"
            :case-id="clientCase.id"
            :accounts="accountsToReview"
            :goal="goal"
            :is-modifiable="isModifiable"
          />
        </box-section>
      </template>

      <template v-if="isCreateAdviceTask(task)">
        <box-section divider="top" no-padding class="p-4">
          <task-item-create-advices
            :case-id="clientCase.id"
            :accounts="accountsToReview"
            :goal="goal"
            :is-modifiable="isModifiable"
          />
        </box-section>
        <box-section divider="top" no-padding class="p-4">
          <create-advices-for-proposed-accounts
            :accounts="proposedAccounts"
            :case-id="clientCase.id"
            :goal="goal"
            :is-modifiable="isModifiable"
          />
        </box-section>
      </template>

      <template v-if="isAdviceFeeEstimationTask(task)">
        <box-section divider="top" no-padding class="p-4">
          <task-item-fee-estimation
            title="Existing accounts"
            :case-id="clientCase.id"
            :goal="goal"
            :accounts="accountsToReview"
            :is-modifiable="isModifiable"
          />
        </box-section>
        <box-section divider="top" no-padding class="p-4">
          <task-item-fee-estimation
            title="Proposed accounts"
            :case-id="clientCase.id"
            :goal="goal"
            :accounts="proposedAccounts"
            :is-modifiable="isModifiable"
          />
        </box-section>
      </template>

      <template v-if="isAcceptanceFromClientTask(task)">
        <box-section divider="top" no-padding class="p-4">
          <task-item-acceptance-from-client
            type="Existing"
            :case-id="clientCase.id"
            :goal="goal"
            :accounts="accountsToReview"
            :case-advisor-id="clientCase.relatedAdvisor.id"
            :is-modifiable="isModifiable"
          />
        </box-section>
        <box-section divider="top" no-padding class="p-4">
          <task-item-acceptance-from-client
            type="Proposed"
            :case-id="clientCase.id"
            :goal="goal"
            :accounts="proposedAccounts"
            :case-advisor-id="clientCase.relatedAdvisor.id"
            :is-modifiable="isModifiable"
          />
        </box-section>
      </template>

      <template v-if="isFinalCheckAllAdviceImplemented(task)">
        <box-section divider="top" no-padding class="p-4">
          <task-item-advice-implemented-final-check
            :case-id="clientCase.id"
            :goal="goal"
            :accounts="accountsToReview"
            :case-advisor-id="clientCase.relatedAdvisor.id"
            :is-modifiable="isModifiable"
          />
        </box-section>
        <box-section divider="top" no-padding class="p-4">
          <implement-advices-for-proposed-accounts
            :case-id="clientCase.id"
            :goal="goal"
            :accounts="proposedAccounts"
            :case-advisor-id="clientCase.relatedAdvisor.id"
            :is-modifiable="isModifiable"
          />
        </box-section>
      </template>

      <template v-if="isRiskProfileTask(task)">
        <box-section divider="top" no-padding class="p-4">
          <task-item-risk-profile
            :case-id="clientCase.id"
            :goal="goal"
            :is-modifiable="isModifiable"
          />
        </box-section>
      </template>

      <template v-if="isCashflowPlanTask(task)">
        <box-section divider="top" no-padding class="p-4">
          <task-item-forecaster-plan
            :case-id="clientCase.id"
            :goal="goal"
            :is-modifiable="isModifiable"
          />
        </box-section>
      </template>

      <template v-if="isPlanningReportGeneratedTask(task)">
        <box-section divider="top" no-padding class="p-4">
          <task-item-document-generation
            :document-type="documentType"
            :is-modifiable="isModifiable"
            @document-generation-requested="handleDocumentGenerationRequest"
          />
        </box-section>
      </template>

      <template v-if="isHeadedLetterGeneratedTask(task)">
        <box-section divider="top" no-padding class="p-4">
          <task-item-document-generation
            :document-type="documentType"
            :is-modifiable="isModifiable"
            @document-generation-requested="handleDocumentGenerationRequest"
          />
        </box-section>
      </template>
    </DisclosurePanel>
  </Disclosure>
</template>

<script setup lang="ts">
  import { map, partial } from 'lodash';
  import { computed, onMounted, ref } from 'vue';
  import {
    Disclosure,
    DisclosureButton,
    DisclosurePanel,
  } from '@headlessui/vue';
  import {
    ChevronRightIcon,
    RectangleStackIcon,
  } from '@heroicons/vue/20/solid';
  import { parseErrorFromResponse } from '@aventur-shared/services/api';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { GoalId } from '@aventur-shared/modules/goals';
  import {
    Task,
    TaskGoalRequired,
    TaskStatusEnum,
    TaskTypeEnum,
    isAcceptanceFromClientTask,
    isAccountToReviewTask,
    isAdviceFeeEstimationTask,
    isCashflowPlanTask,
    isCreateAdviceTask,
    isFinalCheckAllAdviceImplemented,
    isHeadedLetterGeneratedTask,
    isPlanningReportGeneratedTask,
    isRiskProfileTask,
  } from '@aventur-shared/modules/tasks';
  import {
    isAccountToReview,
    isProposedAccount,
  } from '@aventur-shared/modules/accounts';
  import { formatNames } from '@aventur-shared/utils/user';
  import { Case, SubTask } from '@aventur-shared/modules/cases/models';
  import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';
  import { BoxSection } from '@modules/ui';
  import { DocumentType } from '@modules/clients-cases/models/document_generation';
  import { documentGenerationRequest } from '@modules/clients-cases/api';
  import { useClientCase } from '@modules/clients-cases/store/client-case-store';
  import { DocumentLink } from '@modules/documents/types/Document';
  import DocumentsModal from '@modules/documents/ui/documents-modal.vue';
  import { useTaskDocuments } from '@modules/documents/useTaskDocuments';
  import TaskItemAccounts from './task-item/task-item-accounts.vue';
  import TaskItemFeeEstimation from './task-item/task-item-fee-estimation.vue';
  import TaskItemAcceptanceFromClient from './task-item/task-item-acceptance-from-client.vue';
  import TaskItemCreateAdvices from './task-item/task-item-create-advices.vue';
  import TaskItemAdviceImplementedFinalCheck from './task-item/task-item-advice-implemented-final-check.vue';
  import TaskItemDocumentGeneration from './task-item/task-item-document-generation.vue';
  import CreateAdvicesForProposedAccounts from './task-item/create-advices-for-proposed-accounts.vue';
  import ImplementAdvicesForProposedAccounts from './task-item/implement-advices-for-proposed-accounts.vue';
  import TaskItemRiskProfile from './task-item/task-item-risk-profile.vue';
  import TaskItemForecasterPlan from './task-item/task-item-forecaster-plan.vue';

  type TaskTypeDocumentTypeMapping = Record<
    TaskTypeEnum.PlanningReportGenerated | TaskTypeEnum.HeadedLetterGenerated,
    DocumentType[]
  >;

  const toast = useToast();
  const { getTaskGoal, getCaseGoal, getGoalAccounts } = useClientCase();

  const emit = defineEmits(['on-update-status', 'on-disclosure-btn-click']);

  const props = withDefaults(
    defineProps<{
      clientCase: Case;
      task: Task;
      subTask: SubTask;
      isReadonly: boolean;
      defaultOpen?: boolean;
    }>(),
    {
      defaultOpen: false,
    },
  );

  const goal = computed(
    () => getTaskGoal(props.task, props.subTask.caseGoalId) as TaskGoalRequired,
  );
  const goalClients = getCaseGoal(goal.value.caseGoalId as GoalId)!.clients;
  const isModifiable = computed(() => !props.isReadonly);

  const proposedAccounts = computed(() =>
    getGoalAccounts(props.subTask.caseGoalId as GoalId, isProposedAccount),
  );
  const accountsToReview = computed(() =>
    getGoalAccounts(props.subTask.caseGoalId as GoalId, isAccountToReview),
  );

  const documentType = computed(() => {
    const mapping: TaskTypeDocumentTypeMapping = {
      [TaskTypeEnum.PlanningReportGenerated]: [
        DocumentType.SuitabilityReport,
        DocumentType.SuitabilityReportMortgage,
      ],
      [TaskTypeEnum.HeadedLetterGenerated]: [DocumentType.HeadedLetter],
    };

    return !props.subTask.goalId
      ? mapping[props.task.type][0]
      : props.subTask.goalId === ClientGoalTypeEnum.PropertyOwnership
        ? mapping[props.task.type][1]
        : mapping[props.task.type][0];
  });

  const documents = ref<DocumentLink[]>([]);
  const documentsNotRequired = computed(
    () => !!props.subTask.noDocumentsReason,
  );
  const documentsNotRequiredReason = computed(
    () => props.subTask.noDocumentsReason ?? undefined,
  );
  const documentsContext = computed(() => ({
    caseId: props.clientCase.id,
    taskSlug: props.task.slug,
    taskId: goal.value.taskId,
    clientIds: map(goalClients, 'id'),
  }));

  const { getTaskDocuments } = useTaskDocuments(documentsContext.value);

  const handleDocumentGenerationRequest = async (
    documentType: DocumentType,
  ) => {
    const request = partial(
      documentGenerationRequest,
      props.clientCase.id,
      props.task.slug,
      goal.value.taskId,
      documentType,
    );
    try {
      switch (documentType) {
        case DocumentType.SuitabilityReport:
        case DocumentType.SuitabilityReportMortgage:
          await request({
            case_goal_id: goal.value.caseGoalId,
          });
          break;
        case DocumentType.HeadedLetter:
          await request({
            client_id: goalClients[0].id, // TODO: Include all goal clients?
          });
          break;
        default:
          throw new Error('Invalid document type');
      }

      toast.successWithAction({
        'confirm-toast': () =>
          emit('on-update-status', TaskStatusEnum.Completed),
        props: {
          actions: {
            confirm: 'Complete Task',
          },
          message: 'The document generation request has been processed.',
        },
      });
    } catch (e: any) {
      toast.error(parseErrorFromResponse(e).join('\r\n'));
    }
  };

  onMounted(async () => {
    documents.value = await getTaskDocuments();
  });

  const handleDocumentsUpdated = async () => {
    toast.success('Task documents have been updated.');
    documents.value = await getTaskDocuments();
  };
</script>
