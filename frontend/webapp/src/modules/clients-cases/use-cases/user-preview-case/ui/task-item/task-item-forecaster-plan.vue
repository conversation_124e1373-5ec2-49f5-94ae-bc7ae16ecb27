<template>
  <div>
    <t-table class="border-separate border-spacing-y-2">
      <t-head class="bg-transparent">
        <t-head-tr>
          <t-head-th class="px-0 text-base leading-none">Client</t-head-th>
          <t-head-th class="text-base leading-none">Updated at</t-head-th>
          <t-head-th class="text-base leading-none">Status</t-head-th>
          <t-head-th class="text-base leading-none">
            <div class="flex justify-end">
              <svg
                v-if="isLoading"
                class="text-primary size-4 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                />
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            </div>
          </t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="client in props.goal.clients"
          :key="client.id"
          class="relative border-none bg-white px-4 py-3 text-base"
        >
          <t-body-td class="rounded-l-xl">{{ formatName(client) }}</t-body-td>
          <t-body-td>{{
            isLoading ? '-' : getClientForecasterTimestamp(client.id)
          }}</t-body-td>
          <t-body-td>{{
            isLoading ? '-' : getClientForecasterStatus(client.id)
          }}</t-body-td>
          <t-body-td class="rounded-r-xl text-right">
            <forecaster-modal
              :case-id="caseId"
              :goal="goal"
              :client="client"
              :is-read-only="
                !isModifiable &&
                getClientForecasterStatus(client.id) ==
                  ForecasterStatus.COMPLETED
              "
            >
              <template #activator="{ open }">
                <a
                  v-if="!isLoading"
                  class="text-secondary flex flex-row items-center justify-end whitespace-nowrap hover:cursor-pointer hover:underline"
                  @click.stop="open"
                >
                  {{
                    !isModifiable &&
                    getClientForecasterStatus(client.id) ==
                      ForecasterStatus.COMPLETED
                      ? 'View results'
                      : 'Open forecaster'
                  }}
                  <arrow-top-right-on-square-icon class="ml-1 size-4" />
                </a>
              </template>
            </forecaster-modal>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import { onBeforeMount } from 'vue';
  import { useEventBus } from '@vueuse/core';
  import { ArrowTopRightOnSquareIcon } from '@heroicons/vue/24/solid';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { formatName } from '@aventur-shared/utils/user';
  import { formatDatetimeStringForView } from '@aventur-shared/utils/dateTime';
  import { ArrayElement } from '@aventur-shared/types/Common';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { TaskGoalRequired } from '@aventur-shared/modules/tasks';
  import { CaseId } from '@aventur-shared/modules/cases';
  import { forecasterEventKey } from '@aventur-shared/modules/goals';
  import { CaseGoal } from '@aventur-shared/modules/cases/models/case–goal';
  import { fetchForecasterPlan } from '@aventur-shared/modules/goals/api/generate-forecaster-plan';
  import ForecasterModal from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/forecaster-modal.vue';
  import { default as useForecasterStore } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/stores';
  import {
    Status as ForecasterStatus,
    getStatus as getAtrStatus,
  } from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item/atr-status';

  const toast = useToast();
  const { isLoading } = useAPIState();
  const { getForecasterData, saveForecasterData } = useForecasterStore();

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoalRequired;
    isModifiable: boolean;
  }>();

  // Events
  const emit = defineEmits([
    'forecaster:submit',
    'forecaster:reset',
    'forecaster:export',
  ]);
  const eventBus = useEventBus(forecasterEventKey);
  eventBus.on((e) => emit(e.name));

  const getClientForecasterStatus = (clientId: ClientId) => {
    return getAtrStatus(getForecasterData(props.goal.id, clientId));
  };

  // TODO: Maybe refactor this to the store getter
  const getClientForecasterTimestamp = (clientId: ClientId) => {
    const _data = getForecasterData(props.goal.id, clientId);
    return _data && _data.updated_at
      ? formatDatetimeStringForView(_data.updated_at)
      : '-';
  };

  onBeforeMount(async () => {
    try {
      await Promise.all(
        props.goal.clients.map((client: ArrayElement<CaseGoal['clients']>) =>
          fetchForecasterPlan(props.goal.id, client.id)
            .then((data) => {
              data && saveForecasterData(props.goal.id, client.id, data);
            })
            .catch(() => {
              throw new Error(`Unable to load data (${formatName(client)})`);
            }),
        ),
      );
    } catch (e: unknown) {
      toast.error(e as Error);
    }
  });
</script>
