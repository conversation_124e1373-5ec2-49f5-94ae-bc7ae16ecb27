<template>
  <div>
    <h1>{{ title }}</h1>
    <t-table class="border-separate border-spacing-y-2">
      <t-head>
        <t-head-tr>
          <t-head-th class="px-0 text-base leading-none">Provider</t-head-th>
          <t-head-th class="text-base leading-none">Type</t-head-th>
          <t-head-th class="text-base leading-none">Clients</t-head-th>
          <t-head-th class="text-base leading-none">Estimate</t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="account in accounts"
          :key="account.id"
          class="relative border-none px-4 py-3 text-sm"
        >
          <t-body-td class="rounded-l-xl">
            {{ account.providerName }}
          </t-body-td>
          <t-body-td>
            {{ account.type.toString() }}
          </t-body-td>
          <t-body-td>
            <ul>
              <li
                v-for="client in account.clients"
                :key="client.id"
                class="whitespace-nowrap"
              >
                {{ formatName(client) }}
              </li>
            </ul>
          </t-body-td>
          <t-body-td class="rounded-r-xl">
            <modal :config="{ initialFocus: initialFocusRef }">
              <template #open-button="{ open }">
                <button
                  v-if="account.expectedFees.length"
                  class="hover:-m-2 hover:rounded-full hover:bg-gray-100 hover:p-2"
                  @click="open"
                >
                  {{ account.expectedFees.length }} added
                </button>
                <button
                  v-else
                  class="text-secondary font-semibold hover:-m-2 hover:rounded-full hover:bg-gray-100 hover:p-2"
                  :class="{
                    'pointer-events-none cursor-not-allowed opacity-50':
                      !isModifiable,
                  }"
                  :disabled="!isModifiable"
                  @click="open"
                >
                  Add estimate
                </button>
              </template>
              <template #default="{ close }">
                <add-expected-fees-modal
                  :case-id="caseId"
                  :goal="goal"
                  :account="account"
                  :is-modifiable="isModifiable"
                  @on-submit="
                    () => {
                      handleExpectedFeeSave();
                      close();
                    }
                  "
                  @on-cancel="close"
                  @on-load="handleLoadAddExpectedFeesModal"
                />
              </template>
            </modal>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import { Ref, ref } from 'vue';
  import { formatName } from '@aventur-shared/utils/user';
  import { Case } from '@aventur-shared/modules/cases';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { Goal } from '@aventur-shared/modules/goals';
  import {
    Modal,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@/modules/ui';
  import { userPreviewCaseAction } from '@modules/clients-cases';
  import { AddExpectedFeesModal } from '@modules/sub-tasks/use-cases/user-adds-expected-fees-to-proposed-account';

  const props = defineProps<{
    title: string;
    caseId: Case['id'];
    goal: TaskGoal;
    accounts: Goal['accounts'];
    isModifiable: boolean;
  }>();

  const initialFocusRef = ref<HTMLElement | null>(null);

  const handleLoadAddExpectedFeesModal = ({
    focusRef,
  }: {
    focusRef: Ref<HTMLElement>;
  }) => {
    initialFocusRef.value = focusRef.value;
  };

  const handleExpectedFeeSave = async () => {
    await userPreviewCaseAction(props.caseId);
  };
</script>
