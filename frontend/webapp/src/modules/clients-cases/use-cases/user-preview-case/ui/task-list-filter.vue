<template>
  <SelectField
    name=""
    label=""
    placeholder="Select task"
    class="mb-0"
    :value="value"
    :options="options"
    :can-clear="true"
    @on-select="(e) => emit('filter-tasks', e)"
  />
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Case } from '@aventur-shared/modules/cases';
  import { SelectField } from '@aventur-shared/components/form';

  const props = defineProps<{
    value: string;
    tasks: Case['tasks'];
  }>();

  const emit = defineEmits<{
    (e: 'filter-tasks', slug: string | null): void;
  }>();

  const options = computed(() =>
    props.tasks.map((task) => ({
      value: task.slug,
      label: task.description,
    })),
  );
</script>
