<template>
  <div>
    <h1>Existing accounts ({{ accounts.length }})</h1>
    <t-table class="border-separate border-spacing-y-2">
      <t-head>
        <t-head-tr>
          <t-head-th class="px-0 text-base leading-none">Provider</t-head-th>
          <t-head-th class="text-base leading-none">Type</t-head-th>
          <t-head-th class="text-base leading-none">Clients</t-head-th>
          <t-head-th class="text-base leading-none">Acc No.</t-head-th>
          <t-head-th class="text-base leading-none">Sub Acc No.</t-head-th>
          <t-head-th class="text-base leading-none">Advice</t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="account in accounts"
          :key="account.id"
          class="relative border-none px-4 py-3 text-sm"
        >
          <t-body-td class="rounded-l-xl">
            {{ account.providerName }}
          </t-body-td>
          <t-body-td>
            {{ account.type.toString() }}
          </t-body-td>
          <t-body-td
            ><ul>
              <li
                v-for="client in account.clients"
                :key="client.id"
                class="whitespace-nowrap"
              >
                {{ formatName(client) }}
              </li>
            </ul></t-body-td
          >
          <t-body-td>{{ account.accountNumber }}</t-body-td>
          <t-body-td>{{ account.subAccountNumber }}</t-body-td>
          <t-body-td class="rounded-r-xl">
            <modal :config="{ initialFocus: initialFocusRef }">
              <template #open-button="{ open }">
                <button
                  v-if="account.advices.length"
                  class="hover:-m-2 hover:rounded-full hover:bg-gray-100 hover:p-2"
                  @click="open"
                >
                  {{ account.advices.length }} added
                </button>
                <button
                  v-else
                  class="text-secondary whitespace-nowrap font-semibold hover:-m-2 hover:rounded-full hover:bg-gray-100 hover:p-2"
                  :class="{
                    'pointer-events-none cursor-not-allowed opacity-50':
                      !isModifiable,
                  }"
                  @click="open"
                >
                  Add advice
                </button>
              </template>
              <template #default="{ close }">
                <add-advice-modal
                  :case-id="caseId"
                  :goal="goal"
                  :account="account"
                  :is-modifiable="isModifiable"
                  @on-submit="
                    () => {
                      handleAdviceSave();
                      close();
                    }
                  "
                  @on-cancel="close"
                  @on-load="handleLoadAddAdviceModal"
                />
              </template>
            </modal>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import { Ref, computed, ref } from 'vue';
  import {
    Modal,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { Case } from '@aventur-shared/modules/cases';
  import { formatName } from '@aventur-shared/utils/user';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { isAccountToReview } from '@aventur-shared/modules/accounts';
  import { userPreviewCaseAction } from '@/modules/clients-cases';
  import { useClientCase } from '@modules/clients-cases/store/client-case-store';
  import { default as AddAdviceModal } from './modals/add-advice-modal.vue';

  const props = defineProps<{
    caseId: Case['id'];
    goal: TaskGoal;
    isModifiable: boolean;
  }>();

  const { getGoalAccounts } = useClientCase();

  const accounts = computed(() =>
    getGoalAccounts(props.goal.id, isAccountToReview),
  );

  const initialFocusRef = ref<HTMLElement | null>(null);

  const handleLoadAddAdviceModal = ({
    focusRef,
  }: {
    focusRef: Ref<HTMLElement>;
  }) => {
    initialFocusRef.value = focusRef.value;
  };

  const handleAdviceSave = async () => {
    await userPreviewCaseAction(props.caseId);
  };
</script>
