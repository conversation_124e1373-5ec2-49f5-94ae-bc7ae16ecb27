<template>
  <div>
    <div class="grid xl:grid-cols-2">
      <Search
        v-model:search-value="searchValue"
        :options="accountOptions"
        placeholder="Search accounts"
        :disabled="!isModifiable"
        @select-option="($event) => handleCheckAccount($event)"
      >
        <template #top>
          <span class="text-secondary text-sm font-semibold"
            >Available Accounts ({{ accountOptions.length }})</span
          >
        </template>
        <template #option="{ option }">
          <div class="flex items-center px-4 py-1 hover:bg-gray-200">
            <clean-checkbox-input
              name="select-account"
              :model-value="(option as any as SearchOption).value"
              :checked="
                selectedAccounts.has(Number((option as SearchOption).value))
              "
              @click.stop
              @change="handleCheckAccount"
            />
            <div class="ml-4">
              <div class="flex flex-wrap items-center">
                <span>{{
                  (option as any as SearchOption).ctx?.provider.name
                }}</span>
                <span class="mx-2">-</span>
                <span>{{
                  (option as any as SearchOption).ctx?.product?.product_type
                    .name
                }}</span>
              </div>
              <div class="flex flex-wrap items-center text-sm text-gray-500">
                <span
                  >{{ (option as any as SearchOption).ctx?.accountNumber }} /
                  {{ getAccountNameLabel(option as any as SearchOption) }}
                </span>
                <span
                  class="mx-2 inline-block size-1 rounded-full bg-gray-200"
                />
                <span>{{
                  (option as any as SearchOption).ctx?.subAccountNumber
                }}</span>
              </div>
            </div>
          </div>
        </template>
        <template #bottom="{ actions }">
          <div class="flex gap-2">
            <custom-button
              theme="primary"
              class="w-full"
              :disabled="selectedAccounts.size === 0"
              @click="
                () => {
                  handleSelectAccount().then(actions.close);
                }
              "
              ><template v-if="selectedAccounts.size"
                >Add {{ selectedAccounts.size }} selected</template
              ><template v-else
                >Please select account(s) to add</template
              ></custom-button
            >
            <custom-button
              theme="primary-ghost"
              @click="() => cancelAddingAccounts().then(actions.close)"
              >Cancel</custom-button
            >
          </div>
        </template>
      </Search>
    </div>

    <div>
      <h1>Accounts to review ({{ accounts.length }})</h1>
      <t-table class="border-separate border-spacing-y-2">
        <t-head>
          <t-head-tr>
            <t-head-th class="px-0 text-base leading-none">Provider</t-head-th>
            <t-head-th class="text-base leading-none">Type</t-head-th>
            <t-head-th class="text-base leading-none">Clients</t-head-th>
            <t-head-th class="text-base leading-none">Acc No.</t-head-th>
            <t-head-th class="text-base leading-none">Sub Acc No.</t-head-th>
            <t-head-th class="text-base leading-none"></t-head-th>
          </t-head-tr>
        </t-head>
        <t-body>
          <t-body-tr
            v-for="account in accounts"
            :key="account.id"
            class="relative border-none px-4 py-3 text-sm"
          >
            <t-body-td class="rounded-l-xl">
              {{ account.providerName }}
            </t-body-td>
            <t-body-td>
              {{ account.type.toString() }}
            </t-body-td>
            <t-body-td
              ><ul>
                <li
                  v-for="client in account.clients"
                  :key="client.id"
                  class="whitespace-nowrap"
                >
                  {{ formatName(client) }}
                </li>
              </ul></t-body-td
            >
            <t-body-td>{{ account.accountNumber }}</t-body-td>
            <t-body-td>{{ account.subAccountNumber }}</t-body-td>
            <t-body-td class="rounded-r-xl">
              <button
                v-if="isModifiable"
                type="button"
                class="rounded-lg hover:bg-gray-100"
                @click="() => handleRemoveAccount(account.id)"
              >
                <x-mark-icon class="size-5 text-gray-400" />
              </button>
            </t-body-td>
          </t-body-tr>
        </t-body>
      </t-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { sortBy } from 'lodash';
  import { computed, onMounted, ref } from 'vue';
  import { XMarkIcon } from '@heroicons/vue/20/solid';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { formatName } from '@aventur-shared/utils/user';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { Holding } from '@aventur-shared/modules/clients/models';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { Case } from '@aventur-shared/modules/cases';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { Goal } from '@aventur-shared/modules/goals';
  import { getClientsActiveHoldings } from '@aventur-shared/modules/clients/api';
  import { CleanCheckboxInput } from '@aventur-shared/components/form/fields/clean-fields';

  import {
    Button as CustomButton,
    Search,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import {
    attachAccountToTask,
    detachAccountFromTask,
    userPreviewCaseAction,
  } from '@modules/clients-cases';
  import * as viewModel from './task-item-goal-view-model';
  //

  type SearchOption = SelectOption<
    Pick<
      Holding,
      'accountNumber' | 'subAccountNumber' | 'provider' | 'product' | 'clients'
    >
  >;

  const props = defineProps<{
    caseId: Case['id'];
    accounts: Goal['accounts'];
    goal: TaskGoal;
    clients: Goal['clients'];
    isModifiable: boolean;
  }>();

  const toast = useToast();
  const searchValue = ref('');
  const selectedAccounts = ref(new Set<number>());
  const activeHoldings = ref<Holding[]>([]);

  const accountOptions = computed(() =>
    getAccountOptions(activeHoldings.value),
  );

  onMounted(async () => {
    activeHoldings.value = await getClientsActiveHoldings(
      props.clients.map((client) => client.id),
    );
  });

  const getAccountNameLabel = (account: SearchOption) => {
    return account.ctx?.clients?.length
      ? viewModel.getClientsLabel(
          account.ctx?.clients.map((client) => ({
            id: client?.id as ClientId,
            firstName: client?.first_name as string,
            lastName: client?.last_name as string,
            email: client?.email || null,
          })),
        )
      : null;
  };

  const handleSelectAccount = async () => {
    const accountsIds = [...selectedAccounts.value.values()];
    selectedAccounts.value.clear();

    try {
      await attachAccountToTask(props.caseId, props.goal.taskId, accountsIds);
      toast.success('Accounts added to review.');
      await userPreviewCaseAction(props.caseId);
    } catch (e) {
      toast.error(e as Error);
    }
  };

  const cancelAddingAccounts = async () => {
    selectedAccounts.value.clear();
  };

  const handleRemoveAccount = async (accountId: number) => {
    try {
      await detachAccountFromTask(props.caseId, props.goal.taskId, accountId);
      toast.success('Accounts removed from review.');
      await userPreviewCaseAction(props.caseId);
    } catch (e) {
      toast.error(e as Error);
    }
  };

  const getAccountOptions = (holdings: Holding[]): SearchOption[] => {
    const options = viewModel.getSelectAccountOptions(
      holdings,
      props.accounts.map((account) => account.id),
    );
    return sortBy(
      viewModel.searchOptions(options)(searchValue.value),
      (option) => getAccountNameLabel(option),
    );
  };

  const handleCheckAccount = (event: Event) => {
    const val = (event.target as HTMLInputElement).value;
    if (selectedAccounts.value.has(Number(val))) {
      selectedAccounts.value.delete(Number(val));
    } else {
      selectedAccounts.value.add(Number(val));
    }
  };
</script>
