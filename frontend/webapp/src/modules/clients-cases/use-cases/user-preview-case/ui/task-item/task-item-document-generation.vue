<template>
  <p v-if="documentType !== DocumentType.LetterOfAuthority">
    Click below to generate the document automatically. You will receive an
    email with a download link when the document is ready.
  </p>
  <p v-else>
    Click below to generate the letters for each account to review
    automatically. You will receive one email with a download link per-account
    when the corresponding letter is ready.
  </p>
  <alert
    v-if="messageToUser"
    class="mt-[10px] text-sm"
    type="info"
    :message="messageToUser"
  />
  <custom-button
    v-if="!hideButton"
    theme="primary"
    class="mt-3 w-full"
    :disabled="!isModifiable"
    :is-busy="showSpinner"
    @on-click="handleGenerateDocument"
    >Generate Document
  </custom-button>
  <custom-button
    v-else
    theme="primary"
    class="mt-3"
    :disabled="true"
    style="width: 100%"
    >Please check your email for the document download link.
  </custom-button>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Alert } from '@modules/ui';
  import { Button as CustomButton } from '@modules/ui';
  import { DocumentType } from '@/modules/clients-cases/models/document_generation';

  const props = defineProps<{
    documentType: DocumentType;
    isModifiable: boolean;
    messageToUser?: string;
  }>();

  const emit = defineEmits<{
    (event: 'document-generation-requested', documentType: DocumentType): void;
  }>();

  const showSpinner = ref(false);
  const hideButton = ref(false);

  const handleGenerateDocument = () => {
    startSpinCycle();
    emit('document-generation-requested', props.documentType);
  };

  const startSpinCycle = (): void => {
    showSpinner.value = true;
    // Stop spinner after 0.5 seconds then hide the button
    setTimeout(() => {
      showSpinner.value = false;
      hideButton.value = true;
    }, 500);
  };
</script>
