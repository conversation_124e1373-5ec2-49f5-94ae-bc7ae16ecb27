<template>
  <slot name="caption">
    <p>
      Click the button below to send the Aventur branded and tracked email to
      the case client(s). Using this option is highly recommended.
    </p>
  </slot>
  <slot :handle-click="handleClick">
    <alert
      v-if="messageToUser"
      class="mt-[10px] text-sm"
      type="warning"
      :message="messageToUser"
    />
    <template v-else>
      <custom-button
        theme="primary"
        class="mt-3 w-full"
        :disabled="!isModifiable"
        :is-busy="showSpinner"
        style="width: 100%"
        @on-click="handleClick"
        >Send Message
      </custom-button>
      <span
        class="mx-1 my-4 flex items-center gap-2"
        :class="{ 'hover:cursor-default': !isModifiable }"
      >
        <clean-checkbox-input
          id="toggleValue"
          name="toggleValue"
          :model-value="copySelf"
          :checked="copySelf"
          :disabled="!isModifiable"
          @change="handleChange"
        />
        <label
          for="toggleValue"
          :class="
            isModifiable ? 'hover:cursor-pointer' : 'hover:cursor-default'
          "
          >Check to receive a copy to your mailbox</label
        >
      </span>
    </template>
  </slot>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { default as Alert } from '@aventur-shared/components/Alert.vue';
  import { default as CustomButton } from '@aventur-shared/components/BaseButton.vue';
  import { CleanCheckboxInput } from '@aventur-shared/components/form/fields/clean-fields';

  defineProps<{
    isModifiable: boolean;
    messageToUser?: string;
  }>();

  const copySelf = ref(false);
  const showSpinner = ref(false);
  const hideButton = ref(false);

  const emit = defineEmits<{
    (event: 'message-queued', copySelf: boolean): void;
  }>();

  function handleChange(event: InputEvent) {
    copySelf.value = (event.target as HTMLInputElement).checked;
  }

  const handleClick = () => {
    startSpinCycle();
    emit('message-queued', copySelf.value);
  };

  const startSpinCycle = (): void => {
    showSpinner.value = true;
    // Stop spinner after 0.5 seconds then hide the button
    setTimeout(() => {
      showSpinner.value = false;
      hideButton.value = true;
    }, 500);
  };
</script>
