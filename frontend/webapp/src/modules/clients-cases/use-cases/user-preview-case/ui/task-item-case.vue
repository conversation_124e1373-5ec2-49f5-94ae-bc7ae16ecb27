<template>
  <div class="flex flex-col">
    <template v-if="isClientMessageTask(task)">
      <box-section
        data-testid="client-message-task-item"
        divider="top"
        no-padding
        class="p-4"
      >
        <task-item-client-message
          :is-modifiable="isActionable"
          :message-to-user="
            !mailableClients.length
              ? 'No clients with a valid email address found. Please, update client details to enable automated email option.'
              : undefined
          "
          @message-queued="handleMessage"
        />
      </box-section>
    </template>

    <template v-if="isLetterOfAuthorityGeneratedTask(task)">
      <box-section
        data-testid="loa-task-item"
        divider="top"
        no-padding
        class="p-4"
      >
        <task-item-document-generation
          :document-type="documentType"
          :is-modifiable="isModifiable && accountsToReview.length > 0"
          :message-to-user="
            accountsToReview.length === 0
              ? 'There are no accounts to review therefore the LOA cannot be generated'
              : undefined
          "
          @document-generation-requested="handleDocumentGenerationRequest"
        />
      </box-section>
    </template>

    <template v-if="isLetterOfAuthorityCoverLetterGeneratedTask(task)">
      <box-section
        data-testid="loa-cover-letter-task-item"
        divider="top"
        no-padding
        class="p-4"
      >
        <task-item-document-generation
          :document-type="documentType"
          :is-modifiable="isModifiable && accountsToReview.length > 0"
          :message-to-user="
            accountsToReview.length === 0
              ? 'There are no accounts to review therefore the LOA cannot be generated'
              : undefined
          "
          @document-generation-requested="handleDocumentGenerationRequest"
        />
      </box-section>
    </template>

    <template v-if="isFinancialSummaryDocumentGeneratedTask(task)">
      <box-section divider="top" no-padding class="p-4">
        <task-item-document-generation
          :document-type="documentType"
          :is-modifiable="isModifiable"
          @document-generation-requested="handleDocumentGenerationRequest"
        />
      </box-section>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { filter, partial } from 'lodash';
  import { computed } from 'vue';
  import { parseErrorFromResponse } from '@aventur-shared/services/api';
  import { useToast } from '@aventur-shared/composables/useToast';
  import {
    Task,
    TaskStatusEnum,
    TaskTypeEnum,
    isClientMessageTask,
    isFinancialSummaryDocumentGeneratedTask,
    isLetterOfAuthorityCoverLetterGeneratedTask,
    isLetterOfAuthorityGeneratedTask,
    isTaskFinished,
    isTaskOpen,
  } from '@aventur-shared/modules/tasks';
  import { Case } from '@aventur-shared/modules/cases';
  import { isAccountToReview } from '@aventur-shared/modules/accounts';
  import { DocumentType } from '@modules/clients-cases/models/document_generation';
  import { SubTask } from '@aventur-shared/modules/cases/models';
  import { BoxSection } from '@modules/ui';
  import { useClientCase } from '@modules/clients-cases/store/client-case-store';
  import {
    clientMessageRequest,
    documentGenerationRequest,
  } from '@modules/clients-cases/api';
  import TaskItemClientMessage from './task-item/task-item-client-message.vue';
  import TaskItemDocumentGeneration from './task-item/task-item-document-generation.vue';

  type TaskTypeDocumentTypeMapping = Record<
    | TaskTypeEnum.LetterOfAuthorityGenerated
    | TaskTypeEnum.LoaCoverLetterGenerated
    | TaskTypeEnum.FinancialSummaryDocumentGenerated,
    DocumentType[]
  >;

  const toast = useToast();
  const { getCaseAccounts } = useClientCase();

  const emit = defineEmits([
    'on-update-status',
    // make TS happy with handlers passed in TaskItemDetails component
    'on-disclosure-btn-click',
  ]);

  const props = defineProps<{
    clientCase: Case;
    task: Task;
    subTask: SubTask;
    isReadonly: boolean;
  }>();

  const isModifiable = computed(
    () => !props.isReadonly && !isTaskFinished(props.task),
  );
  const isActionable = computed(
    () => isModifiable.value && isTaskOpen(props.task),
  );

  const mailableClients = computed(() =>
    filter(props.clientCase.clients, 'email'),
  );

  const accountsToReview = computed(() => getCaseAccounts(isAccountToReview));

  const documentType = computed(() => {
    const mapping: TaskTypeDocumentTypeMapping = {
      [TaskTypeEnum.LetterOfAuthorityGenerated]: [
        DocumentType.LetterOfAuthority,
      ],
      [TaskTypeEnum.LoaCoverLetterGenerated]: [
        DocumentType.LetterOfAuthorityCoverLetter,
      ],
      [TaskTypeEnum.FinancialSummaryDocumentGenerated]: [
        DocumentType.FinancialSummaryDocument,
      ],
    };

    return mapping[props.task.type][0];
  });

  const handleDocumentGenerationRequest = async (
    documentType: DocumentType,
  ) => {
    const request = partial(
      documentGenerationRequest,
      props.clientCase.id,
      props.task.slug,
      props.subTask.taskId,
      documentType,
    );
    try {
      switch (documentType) {
        case DocumentType.LetterOfAuthority:
          accountsToReview.value.map(async (account) => {
            await request({
              holding_id: account.id,
            });
          });
          break;
        case DocumentType.LetterOfAuthorityCoverLetter: {
          // Group accounts to review by provider and generate the document per-provider
          const accountsByProvider = accountsToReview.value.reduce(
            (providerAccounts, account) => {
              providerAccounts[account.providerName] =
                providerAccounts[account.providerName] || [];
              providerAccounts[account.providerName].push(account.id);
              return providerAccounts;
            },
            Object.create(null),
          );
          for (const providerName in accountsByProvider) {
            await request({
              holding_ids: accountsByProvider[providerName],
              adviser_id: props.clientCase.relatedAdvisor.id,
            });
          }
          break;
        }
        case DocumentType.FinancialSummaryDocument:
          await request({
            case_id: props.clientCase.id,
          });
          break;
        default:
          throw new Error('Invalid document type');
      }

      toast.successWithAction({
        'confirm-toast': () =>
          emit('on-update-status', TaskStatusEnum.Completed),
        props: {
          actions: {
            confirm: 'Complete Task',
          },
          message: 'The document generation request has been processed.',
        },
      });
    } catch (e: any) {
      toast.error(parseErrorFromResponse(e).join('\r\n'));
    }
  };

  const handleMessage = async (copySelf: boolean) => {
    try {
      await clientMessageRequest(
        props.clientCase.id,
        props.task.slug,
        props.subTask.taskId,
        { copySelf },
      );

      toast.successWithAction({
        'confirm-toast': () =>
          emit('on-update-status', TaskStatusEnum.Completed),
        props: {
          actions: {
            confirm: 'Complete Task',
          },
          message: 'The message has been successfully queued.',
        },
      });
    } catch {
      toast.error('There was a problem with your request.');
    }
  };
</script>
