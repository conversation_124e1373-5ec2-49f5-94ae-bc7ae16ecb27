<template>
  <box class="mt-4 bg-gray-300/20 pb-4 shadow-none lg:-mx-5">
    <box-section
      no-padding
      class="grid grid-cols-1 gap-4 p-4 sm:grid-cols-2 lg:grid-cols-4"
    >
      <select-field
        label="Assigned Group"
        name="assignedGroup"
        :model-value="task.assignedGroup"
        :options="
          getAdvisorsRoles.map((group) => ({
            value: group.value,
            label: group.label,
          }))
        "
        :disabled="!isModifiable"
        @on-select="handleAssignedGroupChange"
      />
      <select-field
        ref="cleanSelect"
        :searchable="true"
        label="Assignee"
        name="advisor"
        :model-value="task.advisor?.id"
        :options="advisorsSelectOptions"
        :disabled="!isModifiable"
        @on-select="handleSelectAdvisor"
      />
      <date-picker
        id="dueDate"
        label="Due date"
        name="date"
        :value="task.dueDate.formatForForm()"
        :disabled="!isModifiable"
        @on-pick="handleDueDateUpdate"
        @on-failure="toast.error('Could not update due date')"
      />
      <select-field
        id="status"
        ref="statusSelect"
        label="Status"
        name="status"
        data-testid="status-options"
        :value="taskStatus.toType().toString()"
        :options="taskStatusOptions"
        :disabled="isReadonly"
        :can-deselect="false"
        @on-select="handleChangeStatus"
      />
    </box-section>
    <template v-if="task.caseLevel">
      <div
        class="bg-primary-50/35 mx-4 flex flex-row justify-end rounded p-4 text-right"
      >
        <documents-modal
          :documents="documents"
          :is-read-only="!isModifiable"
          :documents-not-required="documentsNotRequired"
          :documents-not-required-reason="documentsNotRequiredReason"
          :documents-context="documentsContext"
          @documents-updated="handleDocumentsUpdated"
        >
          <template #activator="{ open }">
            <a
              class="text-primary flex flex-row items-center whitespace-nowrap text-xs leading-6 hover:cursor-pointer hover:underline"
              @click.stop="open"
            >
              <RectangleStackIcon class="mr-1 size-4" />
              Documents ({{ documentsNotRequired ? 'N/A' : documents.length }})
            </a>
          </template>
          <template #header>
            <box-section>
              <div class="flex flex-col items-start">
                <h1 class="text-lg text-[#7A8796]">Task Documents</h1>
                <div class="my-2">
                  <dl>
                    <dt class="flex font-medium text-[#6DBFC0]">
                      <span>
                        {{ formatNames(clientCase.clients) }}
                      </span>
                    </dt>
                    <dd class="flex text-white">{{ task.description }}</dd>
                  </dl>
                </div>
              </div>
            </box-section>
          </template>
        </documents-modal>
      </div>
    </template>
    <div v-if="!isDefaultTask(task)" class="flex flex-col justify-center gap-2">
      <div
        v-if="!task.caseLevel"
        class="mx-4 flex items-center justify-end space-x-2"
      >
        <span class="grow text-gray-400">Goals</span>
        <button
          class="text-secondary text-xs"
          :class="{
            'text-secondary/50 hover:cursor-default':
              openGoals === task.subTasks.length,
          }"
          @click="() => openGoals !== task.subTasks.length && toggleGoals(true)"
        >
          Expand
        </button>
        <span class="text-gray-400">|</span>
        <button
          class="text-secondary text-xs"
          :class="{
            'text-secondary/50 hover:cursor-default': openGoals === 0,
          }"
          @click="() => openGoals && toggleGoals(false)"
        >
          Collapse
        </button>
      </div>
      <template v-for="subTask in subTasks" :key="subTask.taskId">
        <component
          :is="task.caseLevel ? TaskItemCase : TaskItemGoal"
          :task="task"
          :sub-task="subTask"
          :client-case="clientCase"
          :is-readonly="!isActionable"
          @on-update-status="handleChangeStatus"
          @on-disclosure-btn-click="onDisclosureBtnClick"
        />
      </template>
    </div>
  </box>
</template>

<script setup lang="ts">
  import { map } from 'lodash';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref } from 'vue';
  import { RectangleStackIcon } from '@heroicons/vue/20/solid';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { DatePicker, SelectField } from '@aventur-shared/components/form';
  import {
    AdvisorRoleEnum,
    advisorsProvider,
    useAdvisorsStore,
  } from '@aventur-shared/modules/advisors';
  import { Case } from '@aventur-shared/modules/cases';
  import { formatNames } from '@aventur-shared/utils/user';
  import { isTaskFinished } from '@aventur-shared/modules/tasks/models/task';
  import {
    Task,
    isDefaultTask,
    isTaskOpen,
  } from '@aventur-shared/modules/tasks';
  import {
    ITaskStatus,
    taskStatusOptions,
  } from '@aventur-shared/modules/tasks/models/task-status';
  import {
    userPatchTaskAdvisor,
    userPatchTaskAssignedGroup,
    userPatchTaskDueDate,
  } from '@modules/clients-cases';
  import { Box, BoxSection } from '@modules/ui';
  import {
    DocumentContext,
    DocumentLink,
  } from '@modules/documents/types/Document';
  import { useTaskDocuments } from '@modules/documents/useTaskDocuments';
  import DocumentsModal from '@modules/documents/ui/documents-modal.vue';
  import TaskItemCase from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item-case.vue';
  import TaskItemGoal from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item-goal.vue';

  const toast = useToast();

  const props = defineProps<{
    clientCase: Case;
    task: Task;
    isReadonly: boolean;
  }>();

  const emit = defineEmits(['on-update-status']);

  const { getAdvisorById, activeAdvisors } = advisorsProvider().provide();
  const { getAdvisorsRoles } = storeToRefs(useAdvisorsStore());
  const cleanSelect = ref<InstanceType<typeof SelectField> | null>(null);
  const statusSelect = ref<InstanceType<typeof SelectField> | null>(null);
  const taskStatus = computed<ITaskStatus>(() => props.task.status);
  const taskAdvisor = computed(() =>
    props.task.advisor?.id ? getAdvisorById(props.task.advisor.id) : null,
  );

  const subTasks = computed(() =>
    props.task.caseLevel
      ? props.task.subTasks.slice(0, 1)
      : props.task.subTasks,
  );

  const isModifiable = computed(
    () =>
      !props.isReadonly &&
      props.task.isModifiable &&
      !isTaskFinished(props.task),
  );
  const isActionable = computed(
    () => isModifiable.value && isTaskOpen(props.task),
  );

  const advisorsSelectOptions = computed(() => {
    if (props.task.assignedGroup) {
      return activeAdvisors.value
        .filter((advisor) =>
          advisor.roles.includes(props.task.assignedGroup as AdvisorRoleEnum),
        )
        .map((advisor) => ({
          value: advisor.id,
          label: `${advisor.firstName} ${advisor.lastName}`,
        }));
    }
    return activeAdvisors.value.map((advisor) => ({
      value: advisor.id,
      label: `${advisor.firstName} ${advisor.lastName}`,
    }));
  });

  const documents = ref<DocumentLink[]>([]);
  const documentsNotRequired = computed(
    () => !!subTasks.value[0].noDocumentsReason,
  );
  const documentsNotRequiredReason = computed(
    () => subTasks.value[0].noDocumentsReason ?? undefined,
  );
  const documentsContext = computed<DocumentContext>(() => ({
    caseId: props.clientCase.id,
    taskSlug: props.task.slug,
    taskId: subTasks.value[0].taskId,
    clientIds: map(props.clientCase.clients, 'id'),
  }));

  const { getTaskDocuments } = useTaskDocuments(documentsContext.value);

  const focusCleanSelect = () => {
    cleanSelect.value?.focusSearch();
  };

  const resetStatusSelect = (value: number | string) => {
    statusSelect.value?.resetFieldValue(value);
  };

  defineExpose({
    focusCleanSelect,
    resetStatusSelect,
  });

  const handleSelectAdvisor = async (advisorId: number | null) => {
    if (!props.task.advisor && !advisorId) return;

    await userPatchTaskAdvisor(
      props.clientCase.id,
      props.task.slug,
      advisorId ? getAdvisorById(advisorId) : null,
    );
  };

  const handleDueDateUpdate = async (e: Event) => {
    const value = (e.target as HTMLInputElement).value;
    if (value === '') {
      return;
    }

    await userPatchTaskDueDate(props.clientCase.id, props.task.slug, value);
  };

  const handleAssignedGroupChange = async (group: AdvisorRoleEnum | null) => {
    if (!props.task.assignedGroup && !group) return;

    if (
      taskAdvisor.value &&
      group &&
      !taskAdvisor.value.roles.includes(group as AdvisorRoleEnum)
    ) {
      await handleSelectAdvisor(null);
    }

    await userPatchTaskAssignedGroup(
      props.clientCase.id,
      props.task.slug,
      group,
    );
  };

  const handleChangeStatus = async (value: number) => {
    emit('on-update-status', value);
  };

  const openGoals = ref(0);
  const onDisclosureBtnClick = () => {
    window.setTimeout(() => {
      openGoals.value = document.querySelectorAll(
        `[data-slug=${props.task.slug}] div[data-headlessui-state] > button[data-headlessui-state=open]`,
      ).length;
    }, 100);
  };

  const toggleGoals = (open: boolean) => {
    const btnSelector = !open
      ? 'button[data-headlessui-state=open]'
      : 'button:not([data-headlessui-state=open])';
    document
      .querySelectorAll(
        `[data-slug=${props.task.slug}] div[data-headlessui-state] > ${btnSelector}`,
      )
      .forEach((button) => (button as HTMLButtonElement).click());
  };

  onMounted(async () => {
    if (props.task.caseLevel) {
      documents.value = await getTaskDocuments();
    }
  });

  const handleDocumentsUpdated = async () => {
    toast.success('Task documents have been updated.');
    documents.value = await getTaskDocuments();
  };
</script>
