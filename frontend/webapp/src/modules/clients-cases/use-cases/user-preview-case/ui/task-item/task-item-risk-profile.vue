<template>
  <div>
    <t-table class="border-separate border-spacing-y-2">
      <t-head class="bg-transparent">
        <t-head-tr>
          <t-head-th class="px-0 text-base leading-none">Client</t-head-th>
          <t-head-th class="text-base leading-none">Updated at</t-head-th>
          <t-head-th class="text-base leading-none">Status</t-head-th>
          <t-head-th class="text-base leading-none">
            <div class="flex justify-end">
              <svg
                v-if="isLoading"
                class="text-primary size-4 animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                />
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            </div>
          </t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="client in props.goal.clients"
          :key="client.id"
          class="relative border-none bg-white px-4 py-3 text-base"
        >
          <t-body-td>{{ formatName(client) }}</t-body-td>
          <t-body-td>
            {{ isLoading ? '-' : getClientRiskProfileTimestamp(client.id) }}
          </t-body-td>
          <t-body-td>
            {{ isLoading ? '-' : getClientRiskProfileStatus(client.id) }}
          </t-body-td>
          <t-body-td class="text-right">
            <risk-profile-question-modal
              :goal="goal"
              :client="client"
              :is-read-only="
                !isModifiable &&
                getClientRiskProfileStatus(client.id) ==
                  RiskProfileStatus.COMPLETED
              "
            >
              <template #activator="{ open }">
                <a
                  v-if="!isLoading"
                  class="text-secondary flex flex-row items-center justify-end whitespace-nowrap hover:cursor-pointer hover:underline"
                  @click.stop="open"
                >
                  {{
                    !isModifiable &&
                    getClientRiskProfileStatus(client.id) ==
                      RiskProfileStatus.COMPLETED
                      ? 'View results'
                      : 'Open questionnaire'
                  }}
                  <arrow-top-right-on-square-icon class="ml-1 size-4" />
                </a>
              </template>
            </risk-profile-question-modal>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import { onBeforeMount } from 'vue';
  import { useEventBus } from '@vueuse/core';
  import { ArrayElement } from '@aventur-shared/types/Common';
  import { ArrowTopRightOnSquareIcon } from '@heroicons/vue/24/solid';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { formatName } from '@aventur-shared/utils/user';
  import { formatDatetimeStringForView } from '@aventur-shared/utils/dateTime';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { TaskGoalRequired } from '@aventur-shared/modules/tasks';
  import { CaseId } from '@aventur-shared/modules/cases';
  import { CaseGoal } from '@aventur-shared/modules/cases/models/case–goal';
  import { fetchRiskProfile } from '@aventur-shared/modules/goals/api/calculate-risk-profile';
  import { profilerEventKey } from '@aventur-shared/modules/goals';
  import { useRiskProfileStore } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import RiskProfileQuestionModal from '@modules/sub-tasks/use-cases/user-calculates-risk-profile/ui/risk-profile-modal.vue';
  import {
    Status as RiskProfileStatus,
    getStatus as getAtrStatus,
  } from '@modules/clients-cases/use-cases/user-preview-case/ui/task-item/atr-status';

  const toast = useToast();
  const { isLoading } = useAPIState();
  const { getRiskProfile, saveRiskProfile } = useRiskProfileStore();

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoalRequired;
    isModifiable: boolean;
  }>();

  // Events
  const emit = defineEmits(['profile:submit', 'profile:reset']);
  const eventBus = useEventBus(profilerEventKey);
  eventBus.on((e) => emit(e.name));

  const getClientRiskProfileStatus = (clientId: ClientId) => {
    return getAtrStatus(getRiskProfile(props.goal.id, clientId));
  };

  const getClientRiskProfileTimestamp = (clientId: ClientId) => {
    const _data = getRiskProfile(props.goal.id, clientId);
    return _data && _data.updated_at
      ? formatDatetimeStringForView(_data.updated_at)
      : '-';
  };

  onBeforeMount(async () => {
    try {
      await Promise.all(
        props.goal.clients.map((client: ArrayElement<CaseGoal['clients']>) =>
          fetchRiskProfile(props.goal.id, client.id)
            .then((data) => {
              data && saveRiskProfile(props.goal.id, client.id, data);
            })
            .catch(() => {
              throw new Error(`Unable to load data (${formatName(client)})`);
            }),
        ),
      );
    } catch (e: unknown) {
      toast.error(e as Error);
    }
  });
</script>
