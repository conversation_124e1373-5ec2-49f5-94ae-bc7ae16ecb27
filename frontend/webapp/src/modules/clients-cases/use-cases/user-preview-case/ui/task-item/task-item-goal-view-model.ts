import { formatName } from '@aventur-shared/utils/user';
import { Goal } from '@aventur-shared/modules/goals';
import { Holding } from '@aventur-shared/modules/clients/models';
import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

export const getSelectAccountOptions = (
  holdings: Holding[],
  selectedHoldings: number[],
): SelectOption<
  Pick<
    Holding,
    'accountNumber' | 'subAccountNumber' | 'provider' | 'product' | 'clients'
  >
>[] => {
  return holdings
    .filter((holding) => !selectedHoldings.includes(holding.id))
    .map((holding) => ({
      value: holding.id.toString(),
      label: holding.provider.name,
      ctx: {
        accountNumber: holding.accountNumber,
        subAccountNumber: holding.subAccountNumber,
        clients: holding.clients,
        product: holding.product,
        provider: holding.provider,
      },
    }));
};

export const searchOptions =
  (
    options: SelectOption<
      Pick<
        Holding,
        | 'accountNumber'
        | 'subAccountNumber'
        | 'provider'
        | 'product'
        | 'clients'
      >
    >[],
  ) =>
  (searchValue: string) => {
    return options.filter((opt) => {
      return [
        opt.label.toLowerCase(),
        // [opt.ctx?.client.first_name, opt.ctx?.client.last_name]
        //   .join(' ')
        //   .toLowerCase(),
        opt.ctx?.accountNumber.toLowerCase(),
        opt.ctx?.subAccountNumber.toLowerCase(),
      ].some((haystack) => haystack?.includes(searchValue.toLowerCase()));
    });
  };

export const getClientsLabel = (clients: Goal['clients']) => {
  return clients.map((client) => formatName(client)).join(' & ');
};
