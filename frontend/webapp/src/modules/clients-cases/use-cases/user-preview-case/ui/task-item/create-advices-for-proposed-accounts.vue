<template>
  <div>
    <h1>Proposed accounts ({{ accounts.length }})</h1>
    <t-table class="border-separate border-spacing-y-2">
      <t-head>
        <t-head-tr>
          <t-head-th class="px-0 text-base leading-none">Provider</t-head-th>
          <t-head-th class="text-base leading-none">Type</t-head-th>
          <t-head-th class="text-base leading-none">Clients</t-head-th>
          <t-head-th class="text-base leading-none">Advice</t-head-th>
          <t-head-th class="text-base leading-none"></t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="account in accounts"
          :key="account.id"
          class="relative border-none px-4 py-3 text-sm"
        >
          <t-body-td class="rounded-l-xl">
            {{ account.providerName }}
          </t-body-td>
          <t-body-td>
            {{ account.type.toString() }}
          </t-body-td>
          <t-body-td
            ><ul>
              <li
                v-for="client in account.clients"
                :key="client.id"
                class="whitespace-nowrap"
              >
                {{ formatName(client) }}
              </li>
            </ul></t-body-td
          >
          <t-body-td>
            <modal :config="{ initialFocus: initialFocusRef }">
              <template #open-button="{ open }">
                <button
                  v-if="account.advices.length"
                  class="hover:-m-2 hover:rounded-full hover:bg-gray-100 hover:p-2"
                  @click="open"
                >
                  {{ account.advices.length }} added
                </button>
                <button
                  v-else
                  class="text-secondary whitespace-nowrap font-semibold hover:-m-2 hover:rounded-full hover:bg-gray-100 hover:p-2"
                  :class="{
                    'pointer-events-none cursor-not-allowed opacity-50':
                      !isModifiable,
                  }"
                  :disabled="!isModifiable"
                  @click="open"
                >
                  Add advice
                </button>
              </template>
              <template #default="{ close }">
                <update-proposed-accounts-advices-modal
                  :case-id="caseId"
                  :goal="goal"
                  :account="account"
                  :is-modifiable="isModifiable"
                  @on-submit="
                    () => {
                      handleAdviceSave();
                      close();
                    }
                  "
                  @on-cancel="close"
                  @on-load="handleLoadAddAdviceModal"
                />
              </template>
            </modal>
          </t-body-td>
          <t-body-td class="rounded-r-xl">
            <button
              data-testid="remove-proposed-account-button"
              :class="{
                '!p-0': isModifiable,
                'cursor-not-allowed !p-0 hover:no-underline': !isModifiable,
              }"
              :disabled="!isModifiable"
              @click="() => handleRemoveAccount(account.id)"
            >
              <x-mark-icon class="size-5 text-gray-400" />
            </button>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
  <create-proposed-account-modal
    :case-id="caseId"
    :goal="goal"
    :is-modifiable="isModifiable"
  />
</template>

<script setup lang="ts">
  import { Ref, ref } from 'vue';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { formatName } from '@aventur-shared/utils/user';
  import { CaseId } from '@aventur-shared/modules/cases';
  import { Goal } from '@aventur-shared/modules/goals';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { AccountId } from '@aventur-shared/modules/accounts';
  import UpdateProposedAccountsAdvicesModal from './modals/update-proposed-accounts-advices-modal.vue';
  import {
    Modal,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@/modules/ui';
  import { userPreviewCaseAction } from '@/modules/clients-cases';
  import { CreateProposedAccountModal } from '@modules/sub-tasks/use-cases/user-creates-proposed-account';
  import { removeProposedAccountAction } from '@modules/sub-tasks/use-cases/user-removes-proposed-account';

  const toast = useToast();

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoal;
    accounts: Goal['accounts'];
    isModifiable: boolean;
  }>();

  const initialFocusRef = ref<HTMLElement | null>(null);

  const handleLoadAddAdviceModal = ({
    focusRef,
  }: {
    focusRef: Ref<HTMLElement>;
  }) => {
    initialFocusRef.value = focusRef.value;
  };

  const handleAdviceSave = async () => {
    await userPreviewCaseAction(props.caseId);
  };

  const handleRemoveAccount = async (accountId: AccountId) => {
    const { isAccepted } = await useConfirmation(
      'Please, confirm proposed account removal',
      'Confirmation is required to perform this action',
    );
    if (!isAccepted()) return;

    try {
      await removeProposedAccountAction(
        props.caseId,
        props.goal.taskId,
        accountId,
      );
      toast.success('Proposed account has been removed');
      await userPreviewCaseAction(props.caseId);
    } catch (e: any) {
      toast.error(
        e.response?.data?.detail?.includes(
          'AttemptToRemoveAccountWithAdviceError',
        )
          ? 'Cannot delete proposed account with advice'
          : 'Could not remove proposed account',
      );
    }
  };
</script>
