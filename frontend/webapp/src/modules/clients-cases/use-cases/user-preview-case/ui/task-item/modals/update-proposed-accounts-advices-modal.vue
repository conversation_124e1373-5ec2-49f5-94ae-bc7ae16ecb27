<template>
  <box>
    <box-section class="rounded-t-lg bg-[#19182C]">
      <div class="flex flex-col items-start">
        <h1 class="text-[#7A8796]">Proposed Account Advice</h1>

        <div class="mb-4 mt-6">
          <dl>
            <dt class="flex text-lg text-[#6DBFC0]">
              {{ account.type }}
            </dt>
            <dd class="flex text-lg font-medium text-white">
              {{ account.providerName }}
            </dd>
          </dl>
        </div>
      </div>
    </box-section>

    <box-section no-padding-x class="flex flex-col gap-1 !pt-0 text-start">
      <advice-form
        type="proposed"
        :advices="
          account.advices.map((advice: GoalAccount['advices'][0]) => ({
            id: advice.id,
            adviceDescription: advice.description,
            adviceType: advice.type,
            amount: advice.amount,
            frequency: advice.frequency,
            portfolioId: advice.portfolioId,
            accountId: advice.accountId,
          }))
        "
        :account-type-group-id="account.typeGroupId as number"
        :account-options="existingAccounts"
        :is-modifiable="isModifiable"
        @on-submit="handleSubmitAdvice"
      />

      <div class="px-8">
        <custom-button
          ref="cancelButtonRef"
          theme="primary-ghost"
          class="w-full"
          @on-click="$emit('on-cancel')"
          >Cancel and close</custom-button
        >

        <span v-if="errorRef" class="self-center text-red-500">{{
          errorRef
        }}</span>
      </div>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { parseErrorFromResponse } from '@aventur-shared/services/api';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { CaseId } from '@aventur-shared/modules/cases';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import { Box, BoxSection, Button as CustomButton } from '@modules/ui';
  import {
    AdviceForm,
    AdviceFormValues,
  } from '@modules/sub-tasks/use-cases/user-adds-advice-to-account';
  import { addAdviceAction } from '@modules/sub-tasks/use-cases/user-adds-advice-to-account/action';

  const toast = useToast();

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoal;
    account: GoalAccount;
    isModifiable: boolean;
  }>();

  const emit = defineEmits(['on-cancel', 'on-submit', 'on-load']);
  const errorRef = ref<string>();
  const cancelButtonRef = ref(null);

  const existingAccounts = ref<GoalAccount[]>(
    props.goal.accounts.filter((account) => account.status.name !== 'Proposed'),
  );

  onMounted(() => {
    emit('on-load', { focusRef: cancelButtonRef });
  });

  const handleSubmitAdvice = async (formValues: AdviceFormValues) => {
    try {
      await addAdviceAction(props.caseId, props.goal.id, props.goal.taskId, {
        accountId: props.account.id,
        advices: (formValues.advices || []).map((formAdvice) => ({
          id: formAdvice.id,
          type: formAdvice.adviceType as number,
          description: formAdvice.adviceDescription,
          amount: formAdvice.amount as number,
          frequency: formAdvice.frequency,
          portfolioId: formAdvice.portfolioId,
          accountId: formAdvice.accountId,
        })),
      });
      toast.success('Account advice has been updated.');
      emit('on-submit');
    } catch (e: unknown) {
      errorRef.value =
        parseErrorFromResponse(e).join('\r\n') ||
        'Unexpected error during saving advice.';
    }
  };
</script>
