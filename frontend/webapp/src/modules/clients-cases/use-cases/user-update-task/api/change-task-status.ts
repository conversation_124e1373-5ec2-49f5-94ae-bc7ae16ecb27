import { apiClient } from '@aventur-shared/services/api';
import { CaseId, TaskSlugCommand } from '@aventur-shared/modules/cases';
import { StatusChangeValidationFlagsDTO } from '@modules/clients-cases/use-cases/user-update-task/status-change-validation';

type Command = TaskSlugCommand.ChangeTaskStatus;

type Body = {
  status: number;
} & StatusChangeValidationFlagsDTO;

export default async (caseId: CaseId, taskSlug: string, body: Body) => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/${taskSlug}`,
    {
      command: TaskSlugCommand.ChangeTaskStatus,
      payload: body,
    },
  );
};
