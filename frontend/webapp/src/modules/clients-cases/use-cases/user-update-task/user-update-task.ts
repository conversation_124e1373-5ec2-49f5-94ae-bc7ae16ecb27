import { Case, CaseId, fetchCase } from '@aventur-shared/modules/cases';
import {
  patchClientTaskAdvisor,
  patchClientTaskAssigneeGroup,
  patchClientTaskDueDate,
} from '@modules/clients-cases/api';
import changeTaskStatusRequest from './api/change-task-status';
import { DateTime, IDateTime } from '@aventur-shared/utils/dateTime';
import { Advisor } from '@aventur-shared/modules/advisors';
import { ITaskStatus } from '@aventur-shared/modules/tasks/models/task-status';
import {
  type StatusChangeValidationFlags,
  mapTaskStatusChangeValidators,
} from './status-change-validation';
import { useClientCase } from '@modules/clients-cases/store/client-case-store';

type Task = Case['tasks'][0];

const _updateStoreTask = (
  taskSlug: Task['slug'],
  fieldName: string,
  fieldValue: Task['advisor'] | IDateTime | string | null | Task['status'],
) => {
  const { updateCaseTask, getCaseTask } = useClientCase();
  const task = getCaseTask(taskSlug);
  if (task) {
    const updatedTask: Task = { ...task, [fieldName]: fieldValue };
    updateCaseTask(taskSlug, updatedTask);
  }
};

export const userPatchTaskAdvisor = async (
  caseId: CaseId,
  taskSlug: Task['slug'],
  advisor: Advisor | null,
) => {
  const advisorId = advisor?.id ? advisor.id : null;
  await patchClientTaskAdvisor(caseId, taskSlug, advisorId);
  _updateStoreTask(taskSlug, 'advisor', advisor);
};

export const userPatchTaskAssignedGroup = async (
  caseId: CaseId,
  taskSlug: Task['slug'],
  assigneeGroup: string | null,
) => {
  await patchClientTaskAssigneeGroup(caseId, taskSlug, assigneeGroup);
  _updateStoreTask(taskSlug, 'assignedGroup', assigneeGroup);
};

export const userPatchTaskDueDate = async (
  caseId: CaseId,
  taskSlug: Task['slug'],
  dueDate: string | null,
) => {
  const validApiFormat = dueDate !== '' ? dueDate : null;
  const validDomainFormat = dueDate !== null ? new DateTime(dueDate) : '';

  await patchClientTaskDueDate(caseId, taskSlug, validApiFormat);
  _updateStoreTask(taskSlug, 'dueDate', validDomainFormat);
};

export const updateTaskStatus = async (
  caseId: CaseId,
  taskSlug: Task['slug'],
  status: ITaskStatus,
  statusChangeValidators?: StatusChangeValidationFlags,
) => {
  try {
    await changeTaskStatusRequest(caseId, taskSlug, {
      status: status.toType(),
      ...mapTaskStatusChangeValidators(statusChangeValidators),
    });

    const clientCase = await fetchCase(caseId);
    const { updateCaseTasks } = useClientCase();
    updateCaseTasks(clientCase.tasks);
  } catch (e) {
    if (e instanceof Error) throw e;
    else {
      throw new Error('Status change error');
    }
  }
};
