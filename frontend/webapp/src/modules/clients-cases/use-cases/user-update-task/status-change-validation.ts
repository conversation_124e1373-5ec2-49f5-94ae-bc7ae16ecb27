import {
  Task,
  TaskStatusEnum,
  isAccountToReviewTask,
  isDefaultTask,
  isTaskCompleted,
} from '@aventur-shared/modules/tasks';
import { GoalAccount } from '@aventur-shared/modules/goals';

export interface StatusChangeValidationFlags {
  confirmedAction?: boolean;
}

export interface StatusChangeValidationFlagsDTO {
  confirmed_action?: boolean;
}

type TaskStatusChangeValidators = Array<{
  confirmationMessage: string;
  validatorName: keyof StatusChangeValidationFlags;
  statement: boolean;
}>;

export const getTaskStatusChangeValidators = (
  task: Task,
  accountsToReview: GoalAccount[],
  value: number,
): TaskStatusChangeValidators => {
  return [
    {
      confirmationMessage: 'Please, confirm no accounts to review added',
      validatorName: 'confirmedAction',
      statement:
        isAccountToReviewTask(task) &&
        accountsToReview.length === 0 &&
        value === TaskStatusEnum.Completed,
    },
  ];
};

export const getTaskReopenValidators = (
  task: Task,
  status: TaskStatusEnum,
): TaskStatusChangeValidators => {
  return [
    {
      confirmationMessage:
        'This action may affect other tasks. Please, confirm the task re-opening.',
      validatorName: 'confirmedAction',
      statement:
        !isDefaultTask(task) &&
        isTaskCompleted(task) &&
        [TaskStatusEnum.ToDo, TaskStatusEnum.InProgress].includes(status),
    },
  ];
};

export const mapTaskStatusChangeValidators = (
  statusChangeValidators?: StatusChangeValidationFlags,
) => {
  const map: Record<
    keyof StatusChangeValidationFlags,
    keyof StatusChangeValidationFlagsDTO
  > = {
    confirmedAction: 'confirmed_action',
  };

  return (
    statusChangeValidators &&
    Object.keys(statusChangeValidators).reduce((obj, key) => {
      const foundKey = map[key as keyof StatusChangeValidationFlags];
      return foundKey
        ? { ...obj, [foundKey]: statusChangeValidators[key] }
        : obj;
    }, {})
  );
};
