import { apiClient } from '@aventur-shared/services/api';
import { CaseCommand, CaseId } from '@aventur-shared/modules/cases';

const caseCommand = CaseCommand.CompleteCase;

interface Payload extends Record<string, unknown> {}

export default (caseId: CaseId): Promise<void> =>
  apiClient.patch<{ command: typeof caseCommand; payload: Payload }, void>(
    `/api/v2/case/${caseId}`,
    {
      command: caseCommand,
      payload: {},
    },
  );
