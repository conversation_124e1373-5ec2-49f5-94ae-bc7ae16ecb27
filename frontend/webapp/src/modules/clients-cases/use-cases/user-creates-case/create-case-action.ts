import { parseErrorFromResponse } from '@aventur-shared/services/api';
import { CreateCaseModel } from './create-case-model';
import { createClientCase } from '../../api';

export default async (createCaseModel: CreateCaseModel) => {
  try {
    const { id } = await createClientCase({
      clientIds: createCaseModel.clientIds,
      goalIds: createCaseModel.goalsIds,
      advisor: createCaseModel.advisor,
      caseType: createCaseModel.caseType,
      reviewSlotId: createCaseModel.reviewSlotId,
    });

    return { id };
  } catch (e) {
    throw new Error(`Error creating case: ${parseErrorFromResponse(e)}`);
  }
};
