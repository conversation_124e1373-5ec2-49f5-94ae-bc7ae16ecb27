<template>
  <modal>
    <template #open-button="{ open }">
      <custom-button
        theme="primary"
        :class="buttonClass"
        :style="buttonStyle"
        @on-click="handleOpenCreateCaseModal(open)"
        >{{ openButtonTitle }}
      </custom-button>
    </template>

    <template #default="{ close }">
      <box class="text-left">
        <box-section
          title="Create case"
          divider="bottom"
          class="flex justify-between"
        >
          <custom-button
            theme="gray-ghost"
            class="-mx-5 -my-2"
            @on-click="close()"
          >
            <XMarkIcon class="size-4" />
          </custom-button>
        </box-section>
        <box-section no-padding class="pt-4">
          <create-case-form
            :error-message="errorMessage"
            :passed-in-review-slot-id="passedInReviewSlotId"
            :passed-in-adviser-id="passedInAdviserId"
            :passed-in-case-type="passedInCaseType"
            :clients="clients"
            @on-create="(formValues) => handleCreateCase(close)(formValues)"
          />
        </box-section>
        <box-section no-padding-y class="pb-4">
          <custom-button theme="gray-ghost" class="w-full" @on-click="close()"
            >Cancel & close
          </custom-button>
        </box-section>
      </box>
    </template>
  </modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { XMarkIcon } from '@heroicons/vue/24/outline';
  import { Box, BoxSection, Button as CustomButton, Modal } from '@modules/ui';
  import { CaseClient } from '@aventur-shared/modules/cases/models/case';
  import CreateCaseForm from '../create-case-form/create-case-form.vue';
  import {
    CreateCaseFormValues,
    createCaseAction,
  } from '@modules/clients-cases/use-cases/user-creates-case';
  import { IReviewType } from '@aventur-shared/modules/cases/types/review-types';

  const errorMessage = ref('');
  const router = useRouter();

  withDefaults(
    defineProps<{
      passedInReviewSlotId?: number | null;
      passedInAdviserId?: number | null;
      passedInCaseType?: IReviewType | null;
      openButtonTitle?: string;
      clients?: CaseClient[];
      buttonClass?: string;
      buttonStyle?: string;
    }>(),
    {
      passedInReviewSlotId: null,
      passedInAdviserId: null,
      passedInCaseType: null,
      openButtonTitle: 'New case',
      clients: undefined,
      buttonClass: '-my-2 -mx-5',
      buttonStyle: '',
    },
  );

  const handleCreateCase =
    (closeModal: CallableFunction) =>
    async (formValues: CreateCaseFormValues) => {
      try {
        const { id: caseId } = await createCaseAction({
          clientIds: formValues.clientIds,
          advisor: formValues.advisor,
          caseType: formValues.caseType as number,
          goalsIds: formValues.goals,
          reviewSlotId: formValues.reviewSlotId,
        });
        closeModal();
        await router.push({
          name: 'client-case',
          params: {
            id: formValues.clientIds[0],
            caseId: caseId,
          },
        });
      } catch (e: unknown) {
        if (e instanceof Error) {
          errorMessage.value = e.message;
        }
      }
    };

  const handleOpenCreateCaseModal = (open: CallableFunction) => {
    errorMessage.value = '';
    open();
  };
</script>
