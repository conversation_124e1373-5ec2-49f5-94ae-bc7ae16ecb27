import { array, number, object } from 'yup';
import { fieldRequiredMessage } from '@aventur-shared/utils/form/validation-messages';
import { CreateCaseModel } from '@modules/clients-cases/use-cases/user-creates-case/create-case-model';
import {
  CaseTypeEnum,
  isAnnualReviewCase,
} from '@aventur-shared/modules/cases/models/case-type';
import { validationMessages } from '@aventur-shared/utils/form';

export type FormValues = {
  clientIds: CreateCaseModel['clientIds'];
  advisor: CreateCaseModel['advisor'];
  caseType: CreateCaseModel['caseType'] | null;
  goals: CreateCaseModel['goalsIds'];
  reviewSlotId: CreateCaseModel['reviewSlotId'];
};

export const getValidationSchema = (hasAvailableReviewSlots: boolean) =>
  object({
    clientIds: array()
      .min(1, 'At least 1 client has to be selected')
      .of(number().required())
      .required(),
    advisor: number()
      .default(null)
      .transform((val) => (typeof val === 'number' ? Number(val) : null))
      .nullable(),
    goals: array()
      .default([])
      .min(1, 'At least 1 goal has to be selected')
      .of(
        number()
          .default(null)
          .transform((val) => (typeof val === 'number' ? Number(val) : null)),
      ),
    caseType: number().nullable().default(null).required(fieldRequiredMessage),
    reviewSlotId: number()
      .nullable()
      .when(['caseType'], {
        is: (value: CaseTypeEnum) =>
          isAnnualReviewCase(value) && hasAvailableReviewSlots,
        then: (schema) =>
          schema.required(validationMessages.fieldRequiredMessage),
      }),
  });
