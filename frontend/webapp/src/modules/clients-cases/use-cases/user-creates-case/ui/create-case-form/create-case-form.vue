<template>
  <form @submit="onSubmit">
    <box-section divider="bottom" no-padding-y>
      <multi-select-field
        label="Client"
        name="clientIds"
        :options="clientList"
        :disabled="!getClientId"
        :hint="
          getClientId
            ? undefined
            : 'Use individual client details to change this selection'
        "
        @on-select="handleClientSelect"
      />
      <select-field
        label="Case advisor"
        name="advisor"
        :options="getAdvisorsSelectOptions(activeAdvisors)"
      />

      <select-field
        label="Case type"
        name="caseType"
        :options="
          caseTypesOptions.map((caseType) => ({
            value: caseType.id,
            label: caseType.name,
          }))
        "
        :disabled="!!props.passedInReviewSlotId"
      />

      <select-field
        label="Review Slot (optional)"
        name="reviewSlotId"
        :options="
          availableReviewSlots.map((reviewSlot) => ({
            value: reviewSlot.id,
            label: reviewSlot.description,
          }))
        "
        :disabled="!!props.passedInReviewSlotId"
        :can-clear="true"
        :show-warning="showAnnualReviewCaseWarning"
        :warning-message="getAnnualReviewCaseWarningMessage()"
      />

      <box-section v-if="showNoGoalsWarning && selectedClientIds.length">
        Please select goals first. You can do that in
        <router-link
          class="text-primary"
          :to="{
            name: 'factfind-goals',
            params: {
              id: selectedClientIds[0],
            },
          }"
          >Factfind Goals
        </router-link>
        section.
      </box-section>
      <box-section v-else-if="!values.caseType">
        Please select case type first.
      </box-section>
      <checkbox-group
        v-else
        label="Select goals to add to case"
        name="goals"
        :groups="true"
        :options="clientGoalSelectOptions"
      />
    </box-section>
    <box-section no-padding-y class="py-2">
      <custom-button
        :disabled="values.goals && !values.goals.length"
        theme="primary"
        type="submit"
        class="w-full"
        >Save and add case
      </custom-button>
    </box-section>
    <box-section v-if="errorMessage" no-padding-y class="py-1 text-center">
      <span class="text-red-500">{{ errorMessage }}</span>
    </box-section>
  </form>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, toRef, watch } from 'vue';
  import { useForm } from 'vee-validate';
  import { storeToRefs } from 'pinia';
  import { isEmpty, map } from 'lodash';
  import {
    CheckboxGroup,
    MultiSelectField,
    SelectField,
  } from '@aventur-shared/components/form';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';
  import { BoxSection, Button as CustomButton } from '@modules/ui';
  import {
    caseTypesOptions,
    isAnnualReviewCase as isAnnualReviewCaseCheck,
    isOnboardingCase as isOnboardingCaseCheck,
  } from '@aventur-shared/modules/cases/models/case-type';
  import { ClientId, useClientStore } from '@aventur-shared/modules/clients';
  import { CaseClient } from '@aventur-shared/modules/cases/models/case';
  import {
    SelectOption,
    SelectOptionGroup,
  } from '@aventur-shared/components/form/fields/field-model';
  import { getCaseAvailableReviewSlots } from '@aventur-shared/modules/upcoming-reviews/api';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';
  import { ReviewSlot } from '@aventur-shared/modules/upcoming-reviews';
  import { useActiveClients } from '@aventur-shared/stores';
  import { formatName } from '@aventur-shared/utils/user';
  import useClientsGoalsSelectOptions from './use-clients-goals';
  import { FormValues, getValidationSchema } from './form-model';
  import { IReviewType } from '@aventur-shared/modules/cases/types/review-types';

  const props = withDefaults(
    defineProps<{
      errorMessage?: string;
      passedInReviewSlotId: number | null;
      passedInAdviserId: number | null;
      passedInCaseType: IReviewType | null;
      clients?: CaseClient[];
    }>(),
    {
      errorMessage: undefined,
      passedInReviewSlotId: null,
      passedInAdviserId: null,
      passedInCaseType: null,
      clients: undefined,
    },
  );

  const emit = defineEmits<{
    (e: 'on-create', formValues: FormValues): void;
  }>();

  const availableReviewSlots = ref<ReviewSlot[]>([]);
  const selectedClientIds = ref<ClientId[]>([]);
  const hasReviewSlotsOutsideOfMaxAssignablePeriod = ref<boolean>();

  const {
    getOnboardingGoal,
    getClientId,
    getProfile: client,
  } = storeToRefs(useClientStore());
  const { activeAdvisors } = advisorsProvider().provide();
  const { handleClientSelect, goalSelectOptions } =
    useClientsGoalsSelectOptions();
  const { linkedClients } = useLinkedClientList(toRef(client));
  const { list: activeClients } = useActiveClients();

  const clientList = computed<SelectOption[]>(() => {
    if (getClientId.value) {
      return linkedClients.value;
    }

    if (props.clients) {
      return props.clients.map(($client) => ({
        value: $client.id,
        label: formatName($client),
        disabled: true,
      }));
    }

    // default to  active clients list
    return activeClients.map(($client) => ({
      value: $client.id,
      label: formatName($client),
      disabled: false,
    }));
  });

  const validationSchema = computed(() =>
    getValidationSchema(!!availableReviewSlots.value.length),
  );

  const clients = props.clients ? props.clients : [client.value];
  const { handleSubmit, values, resetForm } = useForm<FormValues>({
    validationSchema,
    initialValues: {
      clientIds: map(clients, 'id'),
      advisor: props.passedInAdviserId,
      goals: [],
      caseType: props.passedInCaseType?.toValue(),
      reviewSlotId: props.passedInReviewSlotId,
    },
  });

  const showAnnualReviewCaseWarning = computed(
    () =>
      isAnnualReviewCase.value &&
      (!availableReviewSlots.value.length ||
        !hasReviewSlotsOutsideOfMaxAssignablePeriod.value),
  );

  const showNoGoalsWarning = computed(
    () =>
      values.caseType &&
      !isOnboardingCase.value &&
      isEmpty(goalSelectOptions.value),
  );

  const isOnboardingCase = computed(() =>
    isOnboardingCaseCheck(values.caseType),
  );

  const isAnnualReviewCase = computed(() =>
    isAnnualReviewCaseCheck(values.caseType),
  );

  watch([() => values.caseType, () => goalSelectOptions.value.length], () => {
    resetForm({
      values: {
        clientIds: values.clientIds,
        advisor: values.advisor,
        caseType: values.caseType,
        goals: [],
        reviewSlotId: values.reviewSlotId,
      },
    });
  });

  watch([() => values.clientIds], async () => {
    await loadReviewSlots();
  });

  onMounted(async () => {
    selectedClientIds.value = props.clients
      ? map(props.clients, 'id')
      : [client.value.id];
    await advisorsProvider().create();
    await loadReviewSlots();
    await handleClientSelect(selectedClientIds.value);
  });

  const getAnnualReviewCaseWarningMessage = () => {
    if (isAnnualReviewCase.value) {
      if (!availableReviewSlots.value.length) {
        return 'All annual reviews should be connected to a review slot - please ensure that the client is correctly setup';
      } else if (!hasReviewSlotsOutsideOfMaxAssignablePeriod.value)
        return 'No available review slots in the next 3 months, please ask support to investigate';
    }
  };

  const clientGoalSelectOptions = computed<
    SelectOption[] | SelectOptionGroup[]
  >(() => {
    if (isOnboardingCase.value) {
      return [
        {
          label: getOnboardingGoal.value.name,
          value: getOnboardingGoal.value.id!,
        },
      ];
    }
    return goalSelectOptions.value;
  });

  const loadReviewSlots = async () => {
    const client_ids = values.clientIds ?? [];
    try {
      const {
        availableReviewSlots: slots,
        hasReviewSlotsOutsideOfMaxAssignablePeriod: areSlotsAvailable,
      } = await getCaseAvailableReviewSlots({ client_ids });
      availableReviewSlots.value = slots;
      hasReviewSlotsOutsideOfMaxAssignablePeriod.value = areSlotsAvailable;
    } catch (e: unknown) {
      if (e instanceof Error) {
        throw e;
      }
      throw new Error(
        'Something went wrong during client review slot fetching.',
      );
    }
  };

  const onSubmit = handleSubmit((formValues) => {
    emit('on-create', formValues);
  });
</script>
