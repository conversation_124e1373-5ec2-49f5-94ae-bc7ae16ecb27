import { computed, ref } from 'vue';
import { find, groupBy, map, pull, sortBy } from 'lodash';
import { formatName } from '@aventur-shared/utils/user';
import { ClientId } from '@aventur-shared/modules/clients/types';
import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
import { useActiveClients, useRefData } from '@aventur-shared/stores';
import { Client } from '@aventur-shared/modules/clients';
import { getClientGoals } from '@aventur-shared/modules/factfind/api';

import {
  ClientGoal,
  ClientGoalTypeEnum,
} from '@aventur-shared/modules/clients/models';

interface ClientGoals {
  id: ClientGoal['id'];
  name: ClientGoal['name'];
  type: ClientGoalTypeEnum;
  clientId: ClientId;
  clients: Array<{
    id: ClientId;
    clientFormattedName: ReturnType<typeof formatName>;
  }>;
}

const useClientsGoalsSelectOptions = () => {
  const { getGoalById } = useRefData();
  const { list: activeClients } = useActiveClients();

  const clientsGoals = ref<ClientGoals[]>([]);

  const clientsIdsInClientsGoals = computed(() => [
    ...new Set(map(clientsGoals.value, 'clientId')),
  ]);

  const goalSelectOptions = computed(() => {
    const goals = groupBy(
      clientsGoals.value,
      (goal) => getGoalById(goal.type)?.name,
    );

    const mapper = (options: ClientGoals[], label: string) => ({
      label,
      options: options.reduce((acc, clientGoal) => {
        const goalInCurrentSelectOptions = find(acc, ['value', clientGoal.id]);

        const formattedNames = map(
          clientGoal.clients,
          'clientFormattedName',
        ).join(', ');

        if (goalInCurrentSelectOptions) {
          goalInCurrentSelectOptions.disabled = false;
        } else {
          acc.push({
            label: `${clientGoal.name} - ${formattedNames}`,
            value: clientGoal.id!,
            disabled:
              pull(
                map(clientGoal.clients, 'id'),
                ...clientsIdsInClientsGoals.value,
              ).length != 0,
          });
        }
        return acc;
      }, [] as SelectOption[]),
    });

    return map(goals, mapper);
  });

  function getClientsIdsDiff(selectedClientsIds: ClientId[]) {
    const clientsIdsAddedToSelect = selectedClientsIds.filter(
      (clientId) => !clientsIdsInClientsGoals.value.includes(clientId),
    );
    const clientsIdsRemovedFromSelect = clientsIdsInClientsGoals.value.filter(
      (clientId) => !selectedClientsIds.includes(clientId),
    );
    return {
      added: clientsIdsAddedToSelect,
      removed: clientsIdsRemovedFromSelect,
    };
  }

  async function getClientsGoalsToAdd(clientsIdsAddedToSelect: ClientId[]) {
    const clientsGoalsToAdd = await Promise.all(
      clientsIdsAddedToSelect.map(async (clientId) => {
        const newClientGoals = await getClientGoals(clientId);
        return sortBy(
          newClientGoals
            .filter(
              ({ goalTypeId }) => goalTypeId !== ClientGoalTypeEnum.ClientSetup,
            )
            .map((clientGoal) => {
              const clients = clientGoal.clientIds.map((id) => {
                const $client = find(activeClients, { id }) as Client;
                return {
                  id: $client.id,
                  clientFormattedName: formatName($client as Client),
                };
              });
              return {
                id: clientGoal.id,
                type: clientGoal.goalTypeId,
                name: clientGoal.name,
                clientId,
                clients,
              };
            }),
          'type',
        );
      }),
    );
    return clientsGoalsToAdd.flat();
  }

  async function handleClientSelect(selectedClientsIds: ClientId[]) {
    const { added, removed } = getClientsIdsDiff(selectedClientsIds);
    const clientsGoalsToAdd = await getClientsGoalsToAdd(added);

    clientsGoals.value = [...clientsGoals.value, ...clientsGoalsToAdd].filter(
      (clientGoal) => !removed.includes(clientGoal.clientId),
    );
  }

  return {
    goalSelectOptions,
    handleClientSelect,
  };
};

export default useClientsGoalsSelectOptions;
