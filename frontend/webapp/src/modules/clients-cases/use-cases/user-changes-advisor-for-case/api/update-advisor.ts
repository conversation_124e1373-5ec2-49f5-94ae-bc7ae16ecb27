import { apiClient } from '@aventur-shared/services/api';
import { CaseCommand, CaseId } from '@aventur-shared/modules/cases';
import { Advisor } from '@aventur-shared/modules/advisors';

const caseCommand = CaseCommand.UpdateCaseAdviser;

interface Payload {
  adviser_id: number;
}

export default async (caseId: CaseId, advisorId: Advisor['id']) => {
  await apiClient.patch<
    { command: typeof caseCommand; payload: Payload },
    void
  >(`/api/v2/case/${caseId}`, {
    command: caseCommand,
    payload: {
      adviser_id: advisorId,
    },
  });
};
