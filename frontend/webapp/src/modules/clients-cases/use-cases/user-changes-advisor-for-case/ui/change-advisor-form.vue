<template>
  <form @submit="onSubmit">
    <select-field
      label=""
      name="advisorId"
      :value="advisorId.toString()"
      :options="
        advisors.map((a) => ({
          value: a.id.toString(),
          label: formatName({
            firstName: a.firstName || '',
            lastName: a.lastName || '',
          }),
        }))
      "
      :searchable="true"
    />
    <div class="flex gap-4">
      <custom-button type="submit" theme="primary" class="flex-1"
        >Update</custom-button
      >
      <custom-button type="button" theme="primary-ghost" @on-click="onCancel"
        >Cancel</custom-button
      >
    </div>
  </form>
</template>

<script setup lang="ts">
  import { SelectField } from '@aventur-shared/components/form';
  import { Button as CustomButton } from '@modules/ui';
  import { useForm } from 'vee-validate';
  import { type FormValues, validationSchema } from './form-model';
  import { Advisor } from '@aventur-shared/modules/advisors';
  import { formatName } from '@aventur-shared/utils/user';

  const { handleSubmit } = useForm<FormValues>({
    validationSchema,
  });

  defineProps<{
    advisorId: number;
    advisors: Advisor[];
  }>();

  const emit = defineEmits(['on-advisor-change', 'on-cancel']);

  const onSubmit = handleSubmit((formValues) => {
    emit('on-advisor-change', formValues);
  });

  const onCancel = (e: MouseEvent) => {
    emit('on-cancel', e);
  };
</script>
