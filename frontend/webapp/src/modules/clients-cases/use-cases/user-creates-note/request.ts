import { apiClient } from '@aventur-shared/services/api';
import { CaseCommand, CaseId } from '@aventur-shared/modules/cases';

const caseCommand = CaseCommand.AddCaseNote;

interface Body {
  content: string;
}

export default async (caseId: CaseId, content: string): Promise<void> => {
  await apiClient.patch<
    { command: typeof caseCommand; payload: Body },
    Promise<unknown>
  >(`/api/v2/case/${caseId}`, {
    command: caseCommand,
    payload: { content },
  });
};
