import { CaseListItem } from '@modules/clients-cases/models';
import getClientCases from '@modules/clients-cases/api/get-client-cases';
import { ClientId } from '@aventur-shared/modules/clients';
import { queryParam } from '@aventur-shared/modules/cases';
import { Paginable } from '@aventur-shared/types/Pagination';

export const userPreviewOpenCasesAction = async (
  client_id: ClientId,
  page: number,
): Promise<Paginable<CaseListItem>> | never => {
  try {
    const { items, totalItems } = await getClientCases({
      client_id,
      status: queryParam.openQueryParam,
      page: page,
    });

    return { items, totalItems };
  } catch (e: unknown) {
    throw new Error('Could not fetch open cases.');
  }
};
