import { apiClient } from '@aventur-shared/services/api';
import { QueryParams } from '@aventur-shared/types/api';
import { Paginable } from '@aventur-shared/types/Pagination';
import { CaseId, statusFromDTO } from '@aventur-shared/modules/cases';
import { CaseListItem } from '@modules/clients-cases/models';
import { CaseType } from '@aventur-shared/modules/cases/models';

interface GetClientCasesDTO {
  data: Array<{
    id: number;
    name: string;
    case_type: number;
    adviser: {
      id: number;
      first_name: string;
      last_name: string;
    };
    tasks_complete: number;
    tasks_total: number;
    status: 1 | 2 | 3; // 1 -> Open, 2 -> Completed, 3 -> Cancelled
  }>;
  count: number;
}

export default async (
  queryParams?: QueryParams,
): Promise<Paginable<CaseListItem>> => {
  const response = await apiClient.get<Promise<GetClientCasesDTO>>(
    `/api/v2/case`,
    queryParams,
  );

  return {
    items: response.data.map((clientCase) => ({
      id: clientCase.id as CaseId,
      caseType: new CaseType(clientCase.case_type),
      name: clientCase.name,
      progress: {
        completed: clientCase.tasks_complete,
        total: clientCase.tasks_total,
      },
      relatedAdvisor: {
        id: clientCase.adviser.id,
        firstName: clientCase.adviser.first_name,
        lastName: clientCase.adviser.last_name,
      },
      status: statusFromDTO(clientCase.status),
    })),
    totalItems: response.count,
  };
};
