import { apiClient } from '@aventur-shared/services/api';
import { ArrayElement } from '@aventur-shared/types/Common';
import { TaskId } from '@aventur-shared/modules/tasks';
import { Case, CaseId, TaskCustomCommand } from '@aventur-shared/modules/cases';

type Command = TaskCustomCommand.ClientMessage;

type ClientMessageParams = {
  copySelf: boolean;
};

type ClientMessagePayload = {
  copy_self: boolean;
};

export default async (
  caseId: CaseId,
  taskSlug: ArrayElement<Case['tasks']>['slug'],
  taskId: TaskId,
  params: ClientMessageParams,
) => {
  await apiClient.patch<
    {
      command: Command;
      payload: ClientMessagePayload;
    },
    Promise<void>
  >(`/api/v2/case/${caseId}/${taskSlug}/${taskId}`, {
    command: TaskCustomCommand.ClientMessage,
    payload: { copy_self: params.copySelf },
  });
};
