import { apiClient } from '@aventur-shared/services/api';
import { Task } from '@aventur-shared/modules/tasks';
import { CaseId, TaskSlugCommand } from '@aventur-shared/modules/cases';

type Command = TaskSlugCommand.UpdateTaskAssigneeGroup;

type Body = {
  assignee_group: string | null;
};

export default async (
  caseId: CaseId,
  taskSlug: Task['slug'],
  assigneeGroup: string | null,
) => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/${taskSlug}`,
    {
      command: TaskSlugCommand.UpdateTaskAssigneeGroup,
      payload: {
        assignee_group: assigneeGroup,
      },
    },
  );
};
