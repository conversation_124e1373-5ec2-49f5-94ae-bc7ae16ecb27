import { array, number, object, string } from 'yup';
import { apiClient } from '@aventur-shared/services/api';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { CaseId } from '@aventur-shared/modules/cases';
import { type Note } from '@modules/clients-cases/models';

const getNotesSchema = array()
  .of(
    object({
      id: number().required(),
      author: object({
        id: number().required(),
        first_name: string().defined().nullable(),
        last_name: string().defined().nullable(),
      }).required(),
      content: string().max(10000).defined(),
      created_at: string().required(),
    }),
  )
  .defined();

export default async (caseId: CaseId): Promise<Note[]> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v2/case/${caseId}/notes`,
  );
  const notesDTO = await getNotesSchema.validate(response);

  return notesDTO.map((note) => ({
    id: note.id,
    author: {
      id: note.author.id,
      firstName: note.author.first_name,
      lastName: note.author.last_name,
    },
    content: note.content,
    created: new DateTime(note.created_at),
  }));
};
