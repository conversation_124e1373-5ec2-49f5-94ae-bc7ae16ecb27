import { apiClient } from '@aventur-shared/services/api';
import { Case, CaseId } from '@aventur-shared/modules/cases';
import { GoalId } from '@aventur-shared/modules/goals';
import { ClientId } from '@aventur-shared/modules/clients/types';

type CreatedCaseDTO = number;

interface CreateCasePayload {
  advisor: Case['relatedAdvisor']['id'] | null;
  goalIds: Array<GoalId>;
  clientIds: ClientId[];
  caseType: ReturnType<Case['type']['toNumber']>;
  reviewSlotId?: number | null;
}

interface Body {
  adviser_id: Case['relatedAdvisor']['id'] | null;
  goal_ids: Array<GoalId>;
  client_ids: ClientId[];
  case_type: number;
  review_slot_id?: number;
}

export default async (
  payload: CreateCasePayload,
): Promise<Pick<Case, 'id'>> => {
  const dto: Body = {
    adviser_id: payload.advisor,
    client_ids: payload.clientIds,
    goal_ids: payload.goalIds,
    case_type: payload.caseType,
  };
  if (payload.reviewSlotId !== null && payload.reviewSlotId !== undefined) {
    dto['review_slot_id'] = payload.reviewSlotId;
  }

  const createdCaseId = await apiClient.post<Body, Promise<CreatedCaseDTO>>(
    `/api/v2/case`,
    dto,
  );

  return { id: createdCaseId as CaseId };
};
