import { apiClient } from '@aventur-shared/services/api';
import { Task } from '@aventur-shared/modules/tasks';
import { CaseId, TaskSlugCommand } from '@aventur-shared/modules/cases';
import { AdvisorId } from '@aventur-shared/modules/advisors';

type Command = TaskSlugCommand.UpdateTaskAssignee;

type Body = {
  assignee_id: AdvisorId | null;
};

export default async (
  caseId: CaseId,
  taskSlug: Task['slug'],
  advisorId: AdvisorId | null,
) => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/${taskSlug}`,
    {
      command: TaskSlugCommand.UpdateTaskAssignee,
      payload: {
        assignee_id: advisorId,
      },
    },
  );
};
