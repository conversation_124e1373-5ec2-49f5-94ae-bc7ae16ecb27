// @deprecated
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { apiClient } from '@aventur-shared/services/api';
import {
  TaskDTO,
  dtoToDomain as taskDtoToDomain,
} from '@aventur-shared/modules/tasks/dtos/task-dto';
import { Case } from '@modules/clients-cases';

interface GetGoalTasksResponse extends Array<TaskDTO> {}

export default async (
  caseId: Case['id'],
  goalId: Case['goals'][0]['id'],
): Promise<Case['goals'][0]['tasks']> => {
  const res = await apiClient.get<Promise<GetGoalTasksResponse>>(
    `/api/v1/case/${caseId}/goal/${goalId}/tasks`,
  );

  return res.map(taskDtoToDomain);
};
