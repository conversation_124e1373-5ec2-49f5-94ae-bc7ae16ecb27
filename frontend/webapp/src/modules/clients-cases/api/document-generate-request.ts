import { apiClient } from '@aventur-shared/services/api';
import { ArrayElement } from '@aventur-shared/types/Common';
import { TaskId } from '@aventur-shared/modules/tasks';
import { Case, CaseId, TaskCustomCommand } from '@aventur-shared/modules/cases';
import { DocumentType } from '@/modules/clients-cases/models/document_generation';

type Command = TaskCustomCommand.RequestDocument;

type DocumentRequestPayload = {
  document_type: DocumentType;
  document_args: Record<string, unknown>;
};

export default async (
  caseId: CaseId,
  taskSlug: ArrayElement<Case['tasks']>['slug'],
  taskId: TaskId,
  documentType: DocumentType,
  queryArgs: DocumentRequestPayload['document_args'],
) => {
  await apiClient.patch<
    {
      command: Command;
      payload: DocumentRequestPayload;
    },
    Promise<void>
  >(`/api/v2/case/${caseId}/${taskSlug}/${taskId}`, {
    command: TaskCustomCommand.RequestDocument,
    payload: {
      document_type: documentType,
      document_args: queryArgs,
    },
  });
};
