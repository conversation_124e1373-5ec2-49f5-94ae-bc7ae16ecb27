import { apiClient } from '@aventur-shared/services/api';
import { Task } from '@aventur-shared/modules/tasks';
import { CaseId, TaskSlugCommand } from '@aventur-shared/modules/cases';

type Command = TaskSlugCommand.UpdateTaskDueDate;

type Body = {
  due_date: string | null;
};

export default async (
  caseId: CaseId,
  taskSlug: Task['slug'],
  dueDate: string | null,
) => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/${taskSlug}`,
    {
      command: TaskSlugCommand.UpdateTaskDueDate,
      payload: {
        due_date: dueDate,
      },
    },
  );
};
