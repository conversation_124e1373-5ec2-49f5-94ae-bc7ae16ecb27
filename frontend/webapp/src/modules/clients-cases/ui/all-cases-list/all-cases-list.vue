<template>
  <div class="flex flex-col">
    <t-table>
      <t-head class="text-xs text-gray-500">
        <t-head-tr>
          <t-head-th>Name</t-head-th>
          <t-head-th>Case Type</t-head-th>
          <t-head-th>Advisor</t-head-th>
          <t-head-th>Progress</t-head-th>
          <t-head-th>Status</t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="item in cases"
          :key="item.id"
          class="cursor-pointer hover:bg-gray-50"
          @click="handleClick(item.id)"
        >
          <t-body-td>
            {{ item.name }}
          </t-body-td>
          <t-body-td>
            {{ item.caseType }}
          </t-body-td>
          <t-body-td>
            {{ item.relatedAdvisor.firstName }}
            {{ item.relatedAdvisor.lastName }}
          </t-body-td>
          <t-body-td>
            {{ item.progress.completed }} of {{ item.progress.total }}
          </t-body-td>
          <t-body-td>
            {{ item.status }}
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui/table';
  import { CaseListItem } from '@modules/clients-cases/models';

  defineProps<{
    cases: CaseListItem[];
  }>();
  const emit = defineEmits<{ (e: 'onRowClick', caseId: number): void }>();

  const handleClick = (caseId: number) => {
    emit('onRowClick', caseId);
  };
</script>
