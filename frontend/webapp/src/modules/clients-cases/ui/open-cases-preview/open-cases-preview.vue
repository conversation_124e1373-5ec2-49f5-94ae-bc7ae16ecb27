<template>
  <box>
    <box-section divider="bottom" class="flex justify-between">
      <h3 class="text-base font-semibold text-gray-900">Open Cases</h3>
      <create-case-modal :passed-in-adviser-id="client?.advisor.id" />
    </box-section>
    <box-section v-if="cases.length > 0" no-padding-x no-padding-y>
      <table-with-pagination
        :classes="{
          paginationResultsClass: 'mr-5 mt-5',
          tablePaginationClass: 'pb-0',
        }"
        :pagination="{
          perPage: tableService.config.perPage,
          activePage: unref(tableService.page),
          totalItems: unref(tableService.config.totalItems),
        }"
        :show-results="true"
        @on-page-change="(page) => changePage(page)"
      >
        <t-table>
          <t-head>
            <t-head-tr>
              <t-head-th>Name</t-head-th>
              <t-head-th>Case Type</t-head-th>
              <t-head-th>Advisor</t-head-th>
              <t-head-th>Progress</t-head-th>
            </t-head-tr>
          </t-head>
          <t-body>
            <t-body-tr
              v-for="item in cases"
              :key="item.id"
              class="cursor-pointer bg-white"
              @click="
                router.push({
                  name: 'client-case',
                  params: { caseId: item.id },
                })
              "
            >
              <t-body-td>
                {{ item.name }}
              </t-body-td>
              <t-body-td>
                {{ item.caseType }}
              </t-body-td>
              <t-body-td>
                {{ item.relatedAdvisor.firstName }}
                {{ item.relatedAdvisor.lastName }}
              </t-body-td>
              <t-body-td>
                {{ item.progress.completed }} of {{ item.progress.total }}
              </t-body-td>
            </t-body-tr>
          </t-body>
        </t-table>
      </table-with-pagination>
    </box-section>
    <box-section v-else>
      <span class="text-gray-500">No cases</span>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { Box, BoxSection } from '@modules/ui';
  import { CaseListItem } from '@modules/clients-cases/models';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui/table';
  import { useRouter } from 'vue-router';
  import TableWithPagination from '@modules/ui/table-with-pagination/table-with-pagination.vue';
  import { CreateCaseModal } from '../../use-cases/user-creates-case';
  import { useTableService } from '@aventur-shared/utils/table/use-table-service';
  import { onMounted, ref, unref } from 'vue';
  import { userPreviewOpenCasesAction } from '@modules/clients-cases';
  import { Client } from '@aventur-shared/modules/clients';

  const props = defineProps<{ client: Client }>();

  const router = useRouter();
  const cases = ref<CaseListItem[]>([]);

  const { ...tableService } = useTableService('cases', {
    initialPage: 1,
    perPage: 10,
    totalItems: cases.value.length || 0,
  });

  onMounted(() => {
    fetchCases().then(() => {});
  });

  const changePage = (page: number) => {
    tableService
      .changePage(page)
      .apply()
      .then(() => fetchCases());
  };

  const fetchCases = async () => {
    const { items, totalItems } = await userPreviewOpenCasesAction(
      props.client.id,
      tableService.page.value,
    );
    cases.value = items;
    tableService.setTotalItems(totalItems);
  };
</script>
