<template>
  <div class="flex w-full gap-1">
    <div class="my-0 flex flex-1 flex-col">
      <span>{{ title }}</span>
      <div class="text-gray-400">
        <document-text-icon class="mr-1 inline size-5 self-center" />
        <span class="break-all">{{ description }}</span>
      </div>
    </div>

    <div><slot /></div>
  </div>
</template>

<script lang="ts" setup>
  import { DocumentTextIcon } from '@heroicons/vue/24/outline';

  defineProps<{
    title: string;
    description: string | null;
  }>();
</script>
