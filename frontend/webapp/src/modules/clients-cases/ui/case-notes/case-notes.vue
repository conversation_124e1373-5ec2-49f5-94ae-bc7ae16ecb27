<template>
  <box>
    <box-section
      ><div class="flex justify-between">
        <span class="inline-flex gap-2">
          <box-title
            >Notes
            <span class="text-sm text-gray-500"
              >({{ notes.length }})</span
            ></box-title
          >
          <span v-if="notes.length" class="flex gap-2">
            <span class="text-gray-400">|</span>
            <button
              class="-m-1 block rounded-full p-1 italic text-gray-400 hover:bg-gray-50 lg:inline"
              @click="showNotes = !showNotes"
            >
              {{ showNotes ? 'Hide' : 'Show' }}
              {{ countText(notes.length, 'note') }}
            </button>
          </span>
        </span>
        <button
          v-if="!showForm"
          class="-m-1 block rounded-full p-1 text-gray-400 hover:bg-gray-50 lg:inline"
          @click="showForm = true"
        >
          + Add note
        </button>
      </div>
    </box-section>
    <box-section v-if="showForm" divider="top"
      ><add-note-form @on-submit="onSubmit" @on-cancel="showForm = false"
    /></box-section>
    <box-section v-if="showNotes" divider="top">
      <note-row
        v-for="note in notes"
        :key="note.id"
        :note="note"
        class="break-all"
      >
        {{ note.content }}
      </note-row>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { AddNoteForm } from './form';
  import { Box, BoxSection } from '@modules/ui';
  import { Note } from '@modules/clients-cases/models';
  import BoxTitle from '@modules/ui/box/box-title.vue';
  import { NoteRow } from '@modules/ui/notes';
  import { countText } from '@aventur-shared/utils/ui/count-text';

  const showNotes = ref(false);
  const showForm = ref(false);

  defineProps<{
    notes: Note[];
  }>();

  const emit = defineEmits<{
    (e: 'on-submit', content: string): void;
  }>();

  function onSubmit(fv: { content: string }) {
    emit('on-submit', fv.content);
    showForm.value = false;
  }
</script>
