<template>
  <form @submit.prevent="onSubmit">
    <text-area-field
      name="content"
      label=""
      rows="3"
      placeholder="Add note here..."
    />
    <custom-button type="submit" theme="primary">Add note</custom-button>
    <custom-button
      type="button"
      theme="gray-ghost"
      class="ml-1"
      @on-click="emit('on-cancel')"
      >Cancel</custom-button
    >
  </form>
</template>

<script setup lang="ts">
  import { Button as CustomButton } from '@modules/ui';
  import { TextAreaField } from '@aventur-shared/components/form';
  import { useForm } from 'vee-validate';
  import { type FormValues, validationSchema } from './form-model';

  const emit = defineEmits<{
    (e: 'on-submit', formValues: FormValues): void;
    (e: 'on-cancel'): void;
  }>();

  const { handleSubmit, resetForm } = useForm<FormValues>({
    validationSchema,
  });

  const onSubmit = handleSubmit(async (formValues) => {
    emit('on-submit', formValues);
    resetForm();
  });
</script>
