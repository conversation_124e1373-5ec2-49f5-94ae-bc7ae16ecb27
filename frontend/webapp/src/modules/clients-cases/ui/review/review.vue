<template>
  <box>
    <box-section title="Next review" divider="bottom" />
    <box-section v-if="frequency && month" class="flex justify-between">
      <span>{{ frequency }}</span>
      <span>{{ month }}</span>
    </box-section>
    <box-section v-else> Not specified </box-section>
  </box>
</template>

<script setup lang="ts">
  import { Box, BoxSection } from '@modules/ui';
  import {
    Frequency,
    Month,
  } from '@aventur-shared/modules/clients/models/review-frequency';

  defineProps<{
    frequency: Frequency | null;
    month: Month | null;
  }>();
</script>
