<template>
  <div class="space-y-2 text-end">
    <custom-button
      :disabled="disabled"
      theme="primary"
      data-testid="complete-case"
      @on-click="handleCompleteRequested"
    >
      Complete case
    </custom-button>
    <p v-if="disabled" class="text-sm text-gray-400">
      Complete all tasks to complete case
    </p>
  </div>
</template>

<script setup lang="ts">
  import { Button as CustomButton } from '@modules/ui';

  defineProps<{
    disabled: boolean;
  }>();

  const emit = defineEmits<{
    (e: 'on-complete-requested'): void;
  }>();

  const handleCompleteRequested = () => {
    emit('on-complete-requested');
  };
</script>
