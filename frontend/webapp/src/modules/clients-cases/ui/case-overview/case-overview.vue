<template>
  <box>
    <Disclosure v-slot="{ open }" :default-open="true">
      <DisclosureButton class="flex min-h-28 w-full">
        <box-section
          :divider="open ? 'bottom' : undefined"
          class="flex w-full items-center justify-between"
        >
          <case-detail title="Case" bigger-text>{{
            clientCase.name
          }}</case-detail>
          <complete-case-button
            v-if="!open"
            :disabled="!closable"
            @on-complete-requested="handleCompleteRequested"
          />
        </box-section>
      </DisclosureButton>
      <DisclosurePanel>
        <box-section class="pb-8">
          <div class="grid gap-4">
            <div class="col-span-2 grid grid-cols-subgrid">
              <case-detail title="Type">{{
                String(clientCase.type)
              }}</case-detail>
              <case-detail title="Created on">
                <span>{{
                  formatDatetime(clientCase.createdOn, 'dd LLLL y')
                }}</span>
              </case-detail>
            </div>
            <div class="col-span-2 grid grid-cols-subgrid">
              <case-detail title="Status">{{ clientCase.status }}</case-detail>
            </div>
            <case-detail title="Goals" class="col-span-2 md:col-span-1">
              <p v-for="goal in clientCase.goals" :key="goal.id">
                <case-goal-objectives-modal
                  v-if="goal.goalObjectives.length"
                  :client-case="clientCase"
                  :goal-id="goal.id"
                />
                <span v-else>{{ goal.name }}</span>
              </p>
            </case-detail>
            <case-detail title="Clients" class="col-span-2 md:col-span-1">
              <p v-for="client in clientCase.clients" :key="client.id">
                <a
                  :href="`/clients/${client.id}`"
                  class="flex items-center justify-start underline decoration-dotted decoration-from-font hover:no-underline"
                >
                  {{ formatName(client) }}
                  <ArrowTopRightOnSquareIcon
                    class="text-secondary ml-2 size-4"
                  />
                </a>
              </p>
            </case-detail>
            <case-detail
              title="Assigned adviser"
              class="relative col-span-2 min-w-[150px] md:col-span-1"
              ><span
                class="group inline-flex cursor-pointer items-center hover:-mx-3 hover:rounded-xl hover:bg-gray-100 hover:px-3"
                data-testid="openChangeAdviserFormHandler"
                @click="handleChangeAdvisorRequest"
                >{{
                  formatName({
                    firstName: advisor.firstName || '',
                    lastName: advisor.lastName || '',
                  })
                }}<chevron-down-icon
                  class="ml-2 size-5 text-gray-300 group-hover:text-gray-900"
              /></span>
              <div
                v-if="isDuringAdvisorEdit"
                class="absolute -left-5 top-[20px] z-10 w-full min-w-[400px] rounded-lg bg-white p-4 shadow"
              >
                <change-advisor-form
                  :advisor-id="advisor.id"
                  :advisors="advisors"
                  @on-advisor-change="handleAdvisorChange"
                  @on-cancel.stop="handleAdvisorChangeCancel"
                />
              </div>
            </case-detail>
            <case-detail
              title="Case Manager"
              class="relative col-span-2 min-w-[150px] md:col-span-1"
              ><span
                class="group inline-flex cursor-pointer items-center hover:-mx-3 hover:rounded-xl hover:bg-gray-100 hover:px-3"
                data-testid="openChangeManagerFormHandler"
                @click="handleChangeManagerRequest"
                >{{
                  formatName({
                    firstName: manager?.firstName || 'No Manager Assigned',
                    lastName: manager?.lastName || '',
                  })
                }}<chevron-down-icon
                  class="ml-2 size-5 text-gray-300 group-hover:text-gray-900"
              /></span>
              <div
                v-if="isDuringManagerEdit"
                class="absolute -left-5 top-[20px] z-10 w-full min-w-[400px] rounded-lg bg-white p-4 shadow"
              >
                <change-advisor-form
                  :advisor-id="advisor.id"
                  :advisors="advisors"
                  @on-advisor-change="handleManagerChange"
                  @on-cancel.stop="handleManagerChangeCancel"
                />
              </div>
            </case-detail>
            <case-detail
              title="Review slot"
              class="relative col-span-2 min-w-[150px] md:col-span-1"
            >
              <template v-if="!showAnnualReviewCaseWarning">
                <span
                  class="group inline-flex cursor-pointer items-center hover:-mx-3 hover:rounded-xl hover:bg-gray-100 hover:px-3"
                  data-testid="openReviewSlotFormHandler"
                  @click="handleChangeReviewSlotRequest"
                >
                  {{ reviewSlot?.description ?? 'Not set' }}
                  <chevron-down-icon
                    class="ml-2 size-5 text-gray-300 group-hover:text-gray-900"
                  />
                </span>
                <div
                  v-if="isDuringReviewSlotEdit"
                  class="absolute -left-5 top-[20px] z-10 w-full min-w-[400px] rounded-lg bg-white p-4 shadow"
                >
                  <change-review-slot-form
                    :review-slot="reviewSlot"
                    :available-review-slots="availableReviewSlots"
                    @on-review-slot-change="handleReviewSlotChange"
                    @on-cancel.stop="handleReviewSlotChangeCancel"
                  />
                </div>
              </template>
              <template v-else>
                <div
                  class="flex justify-start"
                  data-testid="reviewSlotWarningMessage"
                >
                  <div>No available review slots</div>
                  <div>
                    <ExclamationCircleIcon
                      v-tippy="
                        'No available review slots in the next 3 months. Please contact support for assistance.'
                      "
                      class="ml-1 mt-0.5 size-5 text-gray-700 hover:cursor-help"
                    />
                  </div>
                </div>
              </template>
            </case-detail>
          </div>
        </box-section>
        <box-section divider="top">
          <div class="flex items-center justify-between space-x-4">
            <cancel-case-button @on-cancel-requested="handleCancelRequested" />
            <complete-case-button
              :disabled="!closable"
              @on-complete-requested="handleCompleteRequested"
            />
          </div>
        </box-section>
      </DisclosurePanel>
    </Disclosure>
  </box>
</template>

<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref } from 'vue';
  import { ChevronDownIcon } from '@heroicons/vue/24/outline';
  import {
    ArrowTopRightOnSquareIcon,
    ExclamationCircleIcon,
  } from '@heroicons/vue/20/solid';
  import {
    Disclosure,
    DisclosureButton,
    DisclosurePanel,
  } from '@headlessui/vue';
  import { Box, BoxSection } from '@modules/ui';
  import { format as formatDatetime } from '@aventur-shared/utils/dateTime';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { formatName } from '@aventur-shared/utils/user';
  import { Case, CaseId } from '@aventur-shared/modules/cases';
  import { AdvisorStatusEnum } from '@aventur-shared/modules/advisors';
  import { Advisor, getAdvisors } from '@aventur-shared/modules/advisors';
  import { canBeClosed } from '@aventur-shared/modules/cases/models/case';
  import { ReviewSlot } from '@aventur-shared/modules/upcoming-reviews/models/client-available-review-slots';
  import { getCaseAvailableReviewSlots } from '@aventur-shared/modules/upcoming-reviews/api';
  import { isAnnualReviewCase as isAnnualReviewCaseCheck } from '@aventur-shared/modules/cases/models/case-type';
  import { getClientGoals } from '@aventur-shared/modules/factfind/api';
  import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';
  import { useRiskProfileStore } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import { useForecasterStore } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan';
  import { useClientCase } from '@modules/clients-cases/store/client-case-store';
  import { OnCancelRequestedArgs } from '@modules/clients-cases/use-cases/user-cancel-case';
  import { changeReviewSlotForCaseAction } from '@modules/clients-cases/use-cases/user-changes-review-slot';
  import { ChangeReviewSlotForm } from '@modules/clients-cases/use-cases/user-changes-review-slot';
  import {
    ChangeAdvisorForm,
    type FormValues,
    changeAdvisorForCaseAction,
  } from '@modules/clients-cases/use-cases/user-changes-advisor-for-case';
  import { changeManagerForCaseAction } from '@modules/clients-cases/use-cases/user-changes-manager-for-case';
  import CaseDetail from './case-detail.vue';
  import { useViewModel } from './case-overview.view-model';
  import CancelCaseButton from './cancel-case-button.vue';
  import CompleteCaseButton from './complete-case-button.vue';
  import CaseGoalObjectivesModal from './case-goal-objectives-modal.vue';

  const { getCaseGoalByType } = useClientCase();
  const { saveRiskProfile, reset: resetRiskProfiles } = useRiskProfileStore();
  const { saveForecasterData, reset: resetForecasterData } =
    useForecasterStore();

  const props = defineProps<{
    clientCase: Case;
    caseId: CaseId;
  }>();

  const emit = defineEmits([
    'on-case-cancel-success',
    'on-case-cancel-failure',
    'on-case-complete-success',
    'on-case-complete-failure',
  ]);

  const viewModel = useViewModel(props.caseId);
  const advisors = ref<Advisor[]>([]);
  const advisor = ref<Case['relatedAdvisor']>(props.clientCase.relatedAdvisor);
  const manager = ref<Case[`relatedManager`]>(props.clientCase.relatedManager);
  const reviewSlot = ref<Case['reviewSlot']>(props.clientCase.reviewSlot);
  const isDuringAdvisorEdit = ref(false);
  const isDuringManagerEdit = ref(false);
  const isDuringReviewSlotEdit = ref(false);
  const availableReviewSlots = ref<ReviewSlot[]>([]);
  const toast = useToast();
  const hasReviewSlotsOutsideOfMaxAssignablePeriod = ref<boolean>();

  const closable = computed(() =>
    canBeClosed(Object.values(props.clientCase.tasks)),
  );
  const isAnnualReviewCase = computed(() =>
    isAnnualReviewCaseCheck(props.clientCase.type.toNumber()),
  );
  const showAnnualReviewCaseWarning = computed(
    () =>
      isAnnualReviewCase.value &&
      !reviewSlot.value &&
      !availableReviewSlots.value.length,
  );

  onMounted(async () => {
    await getReviewSlots();
    await setGoalsAtrDetails();
  });

  onUnmounted(() => {
    resetRiskProfiles();
    resetForecasterData();
  });

  const handleChangeAdvisorRequest = async () => {
    isDuringAdvisorEdit.value = true;
    await loadAdvisers();
  };

  const handleChangeManagerRequest = async () => {
    isDuringManagerEdit.value = true;
    await loadAdvisers();
  };

  const loadAdvisers = async () => {
    if (advisors.value.length === 0) {
      await getAdvisors({
        status: AdvisorStatusEnum.ACTIVE,
      }).then((response) => (advisors.value = response.items));
    }
  };

  const getReviewSlots = async () => {
    const client_ids = props.clientCase.clients.map((client) => client.id);
    await getCaseAvailableReviewSlots({ client_ids })
      .then((response) => {
        availableReviewSlots.value = response.availableReviewSlots;
        hasReviewSlotsOutsideOfMaxAssignablePeriod.value =
          response.hasReviewSlotsOutsideOfMaxAssignablePeriod;
      })
      .catch(() =>
        toast.error(
          "Something went wrong during client's review slot fetching.",
        ),
      );
  };

  const setGoalsAtrDetails = async () => {};
  props.clientCase.clients.map(async (client) => {
    (await getClientGoals(client.id))
      .filter(({ goalTypeId }) => goalTypeId !== ClientGoalTypeEnum.ClientSetup)
      .map((goal) => {
        const caseGoal = getCaseGoalByType(goal.goalTypeId);
        if (caseGoal) {
          // Set timestamps to null to indicate a distinct status of 'Needs Review'
          goal.riskProfile &&
            saveRiskProfile(caseGoal.id, client.id, {
              ...goal.riskProfile,
              updated_at: null,
            });
          goal.cashForecast &&
            saveForecasterData(caseGoal.id, client.id, {
              ...goal.cashForecast,
              updated_at: null,
            });
        }
      });
  });

  const handleChangeReviewSlotRequest = async () => {
    isDuringReviewSlotEdit.value = true;

    if (availableReviewSlots.value.length === 0) {
      await getReviewSlots();
    }
  };

  const handleReviewSlotChange = async (selectedReviewSlot: ReviewSlot) => {
    isDuringReviewSlotEdit.value = false;
    if (selectedReviewSlot.id === props.clientCase.reviewSlot?.id) return;

    try {
      await changeReviewSlotForCaseAction({
        reviewSlotId: selectedReviewSlot.id,
        caseId: props.caseId,
      });
      await getReviewSlots();
      reviewSlot.value = selectedReviewSlot;
      toast.success('Review slot has been updated');
    } catch {
      toast.error('Could not update review slot');
    }
  };

  const handleAdvisorChange = async (formValues: FormValues) => {
    isDuringAdvisorEdit.value = false;
    if (Number(formValues.advisorId) === advisor.value.id) return;

    try {
      await changeAdvisorForCaseAction({
        advisorId: Number(formValues.advisorId),
        caseId: props.caseId,
      });
      advisor.value = advisors.value.find(
        (advisor) => advisor.id === Number(formValues.advisorId),
      ) as Advisor;
      toast.success('Advisor has been updated');
    } catch {
      toast.error('Could not update an advisor');
    }
  };

  const handleAdvisorChangeCancel = () => {
    isDuringAdvisorEdit.value = false;
  };

  const handleManagerChange = async (formValues: FormValues) => {
    isDuringManagerEdit.value = false;
    if (manager.value && Number(formValues.advisorId) === manager.value.id)
      return;

    try {
      await changeManagerForCaseAction({
        managerId: Number(formValues.advisorId),
        caseId: props.caseId,
      });
      manager.value = advisors.value.find(
        (advisor) => advisor.id === Number(formValues.advisorId),
      ) as Advisor;
      toast.success('Manager has been updated');
    } catch {
      toast.error('Could not update manager');
    }
  };

  const handleManagerChangeCancel = () => {
    isDuringManagerEdit.value = false;
  };

  const handleReviewSlotChangeCancel = () => {
    isDuringReviewSlotEdit.value = false;
  };

  const handleCancelRequested = async (args: OnCancelRequestedArgs) => {
    const { onAccept } = await useConfirmation(
      `Are you sure you want to cancel this case?`,
    );

    onAccept(() => {
      viewModel.handleCancelCaseViewAction(args).then(
        () => {
          emit('on-case-cancel-success');
        },
        () => {
          emit('on-case-cancel-failure');
        },
      );
    });
  };

  const handleCompleteRequested = async () => {
    viewModel.handleCompleteCaseViewAction().then(
      () => {
        emit('on-case-complete-success');
      },
      () => {
        emit('on-case-complete-failure');
      },
    );
  };
</script>
