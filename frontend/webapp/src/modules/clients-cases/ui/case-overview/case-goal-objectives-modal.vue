<template>
  <modal>
    <template #open-button="{ open }">
      <a
        href="#"
        class="underline decoration-dotted decoration-from-font hover:no-underline"
        @click="open"
        >{{ goal.name }}</a
      >
    </template>
    <template #default>
      <box>
        <box-section
          v-for="{ client, objectives } in goalObjectives"
          :key="client.id"
          :title="`${goal.name} - ${formatName(client)}`"
        >
          <span class="whitespace-pre-wrap">{{ objectives }}</span>
        </box-section>
      </box>
    </template>
  </modal>
</template>

<script setup lang="ts">
  import { find } from 'lodash';
  import { computed } from 'vue';

  import { formatName } from '@aventur-shared/utils/user';
  import { GoalId } from '@aventur-shared/modules/goals';
  import { Case, CaseClient } from '@aventur-shared/modules/cases';
  import { CaseGoal } from '@aventur-shared/modules/cases/models/case–goal';

  import { Box, BoxSection, Modal } from '@modules/ui';

  const props = defineProps<{
    clientCase: Case;
    goalId: GoalId;
  }>();

  const goal = computed(
    () => find(props.clientCase.goals, { id: props.goalId }) as CaseGoal,
  );

  const goalObjectives = computed(() => {
    return goal.value['goalObjectives'].map(({ clientId, content }) => ({
      client: find(goal.value.clients, { id: clientId }) as CaseClient,
      objectives: content,
    }));
  });
</script>
