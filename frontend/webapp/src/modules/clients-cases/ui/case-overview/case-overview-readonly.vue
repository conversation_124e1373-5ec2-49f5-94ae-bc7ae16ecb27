<template>
  <box>
    <box-section
      divider="bottom"
      class="flex min-h-28 w-full items-center justify-between"
    >
      <case-detail title="Case" bigger-text>{{ clientCase.name }}</case-detail>
    </box-section>
    <box-section class="pb-8">
      <div class="grid gap-4">
        <div class="col-span-2 grid grid-cols-subgrid">
          <case-detail title="Type">{{ String(clientCase.type) }}</case-detail>
          <case-detail title="Created on">
            <span>{{ formatDatetime(clientCase.createdOn, 'dd LLLL y') }}</span>
          </case-detail>
        </div>
        <div class="col-span-2 grid grid-cols-subgrid">
          <case-detail title="Status">{{ clientCase.status }}</case-detail>
          <case-detail v-if="clientCase.completedOn" title="Completed on">
            <span>{{ formatWithMonthName(clientCase.completedOn) }}</span> ({{
              clientCase.daysToComplete
            }}
            days)
          </case-detail>
        </div>
        <case-detail title="Goals" class="col-span-2 md:col-span-1">
          <p v-for="goal in clientCase.goals" :key="goal.id">
            <case-goal-objectives-modal
              :client-case="clientCase"
              :goal-id="goal.id"
            />
          </p>
        </case-detail>
        <case-detail title="Clients" class="col-span-2 md:col-span-1">
          <p v-for="client in clientCase.clients" :key="client.id">
            <a
              :href="`/clients/${client.id}`"
              class="flex items-center justify-start underline decoration-dotted decoration-from-font hover:no-underline"
            >
              {{ formatName(client) }}
              <ArrowTopRightOnSquareIcon class="text-secondary ml-2 size-4" />
            </a>
          </p>
        </case-detail>
        <case-detail
          title="Assigned advisor"
          class="relative col-span-2 min-w-[150px] md:col-span-1"
        >
          {{
            formatName({
              firstName: advisor.firstName || '',
              lastName: advisor.lastName || '',
            })
          }}
        </case-detail>
        <case-detail
          title="Case Manager"
          class="relative col-span-2 min-w-[150px] md:col-span-1"
        >
          {{
            formatName({
              firstName: manager?.firstName || 'No Manager Assigned',
              lastName: manager?.lastName || '',
            })
          }}
        </case-detail>
        <case-detail
          v-show="clientCase.completedOn"
          title="Review slot"
          class="min-w-[150px]"
        >
          {{ clientCase.reviewSlot?.description ?? 'No review slot added' }}
        </case-detail>
      </div>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { ArrowTopRightOnSquareIcon } from '@heroicons/vue/20/solid';
  import { formatName } from '@aventur-shared/utils/user';
  import {
    format as formatDatetime,
    formatWithMonthName,
  } from '@aventur-shared/utils/dateTime';
  import { Case, CaseId } from '@aventur-shared/modules/cases';
  import { Box, BoxSection } from '@modules/ui';
  import CaseDetail from './case-detail.vue';
  import CaseGoalObjectivesModal from './case-goal-objectives-modal.vue';

  const props = defineProps<{
    clientCase: Case;
    caseId: CaseId;
  }>();

  const advisor = ref<Case['relatedAdvisor']>(props.clientCase.relatedAdvisor);
  const manager = ref<Case['relatedManager']>(props.clientCase.relatedManager);
</script>
