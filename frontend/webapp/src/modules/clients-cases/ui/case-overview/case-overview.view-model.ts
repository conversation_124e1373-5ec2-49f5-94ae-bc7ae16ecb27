import { CaseId } from '@aventur-shared/modules/cases';
import {
  OnCancelRequestedArgs,
  cancelCaseAction,
} from '@modules/clients-cases/use-cases/user-cancel-case';
import { completeCaseAction } from '@modules/clients-cases/use-cases/user-complete-case';

const handleCancelCaseViewAction =
  (caseId: CaseId) =>
  async ({ reason }: OnCancelRequestedArgs) => {
    await cancelCaseAction({
      caseId,
      reason,
    });
  };

const handleCompleteCaseViewAction = (caseId: CaseId) => async () => {
  await completeCaseAction(caseId);
};

export const useViewModel = (caseId: CaseId) => {
  return {
    handleCancelCaseViewAction: handleCancelCaseViewAction(caseId),
    handleCompleteCaseViewAction: handleCompleteCaseViewAction(caseId),
  };
};
