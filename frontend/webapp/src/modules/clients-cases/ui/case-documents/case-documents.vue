<template>
  <box>
    <box-section
      ><div class="flex justify-between">
        <span class="inline-flex gap-2">
          <box-title
            >Documents
            <span class="text-sm text-gray-500"
              >({{ caseDocuments.length }})</span
            ></box-title
          >
          <span v-if="caseDocuments.length" class="flex gap-2">
            <span class="text-gray-400">|</span>
            <button
              class="-m-1 block rounded-full p-1 italic text-gray-400 hover:bg-gray-50 lg:inline"
              @click="showDocuments = !showDocuments"
            >
              {{ showDocuments ? 'Hide' : 'Show' }}
              {{ countText(caseDocuments.length, 'document') }}
            </button>
          </span>
        </span>
      </div>
    </box-section>
    <box-section v-if="showDocuments" divider="top">
      <documents-list :documents="caseDocuments" is-read-only />
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { useEventBus } from '@vueuse/core';
  import { CaseId } from '@aventur-shared/modules/cases';
  import { Box, BoxSection } from '@modules/ui';
  import BoxTitle from '@modules/ui/box/box-title.vue';
  import DocumentsList from '@modules/documents/ui/documents-list.vue';
  import {
    DocumentLink,
    DocumentsEvent,
    DocumentsEventType,
    documentsEventKey,
  } from '@modules/documents/types/Document';
  import { fetchDocuments } from '@/modules/documents/api';
  import { countText } from '@aventur-shared/utils/ui/count-text';

  const eventBus = useEventBus<DocumentsEventType>(documentsEventKey);

  const caseDocuments = ref<DocumentLink[]>([]);
  const showDocuments = ref(false);

  const props = defineProps<{
    caseId: CaseId;
  }>();

  onMounted(async () => {
    caseDocuments.value = await fetchDocuments(props.caseId);
  });

  eventBus.on(async (e) => {
    if (e === DocumentsEvent.UPDATE) {
      caseDocuments.value = await fetchDocuments(props.caseId);
    }
  });
</script>
