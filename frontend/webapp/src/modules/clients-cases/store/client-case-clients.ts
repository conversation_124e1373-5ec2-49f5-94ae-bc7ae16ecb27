import { defineStore } from 'pinia';
import { Case } from '@aventur-shared/modules/cases';

interface State {
  items: Case['clients'];
}

type Getters = {
  caseClients: (state: State) => Case['clients'];
};

interface Actions {
  storeCaseClients: (clients: Case['clients']) => void;
}

export const useCaseClients = defineStore<
  'case-clients',
  State,
  Getters,
  Actions
>('case-clients', {
  state: () => ({
    items: [],
  }),
  getters: {
    caseClients(state: State) {
      return state.items;
    },
  },
  actions: {
    storeCaseClients(clients: Case['clients']) {
      this.items = clients;
    },
  },
});
