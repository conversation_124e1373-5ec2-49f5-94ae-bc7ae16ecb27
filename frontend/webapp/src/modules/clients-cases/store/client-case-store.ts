import { defineStore } from 'pinia';
import { ArrayElement } from '@aventur-shared/types/Common';
import { Case } from '@aventur-shared/modules/cases';
import { GoalAccount } from '@aventur-shared/modules/goals';
import {
  Task,
  TaskGoal,
  TaskGoalRequired,
} from '@aventur-shared/modules/tasks';
import { SubTask } from '@aventur-shared/modules/cases/models';
import { CaseGoal } from '@aventur-shared/modules/cases/models/case–goal';
import { AccountStatus } from '@aventur-shared/modules/accounts/models/account';
import { ClientGoalTypeEnum } from '@aventur-shared/modules/clients/models';

type State = {
  clientCase: Case | null;
};

type Getters = {
  getCaseAccounts(
    state: State,
  ): (predicate?: (status: AccountStatus) => boolean) => GoalAccount[];
  getGoalAccounts(
    state: State,
  ): (
    caseGoalId: CaseGoal['id'],
    predicate?: (status: AccountStatus) => boolean,
  ) => GoalAccount[];
  getCaseGoal(
    state: State,
  ): (caseGoalId: CaseGoal['id']) => CaseGoal | undefined;
  getTaskGoal(
    state: State,
  ): (
    task: Task,
    caseGoalId: SubTask['caseGoalId'],
  ) => TaskGoal | TaskGoalRequired;
  getCaseTask(state: State): (slug: Task['slug']) => Task | undefined;
  getCaseGoalByType(
    state: State,
  ): (goalType: ClientGoalTypeEnum) => CaseGoal | undefined;
};

type Actions = {
  resetCase(): void;
  storeCase(clientCase: Case): void;
  updateCaseTasks(tasks: Task[]): void;
  updateCaseTask(taskSlug: Task['slug'], updatedTask: Task): void;
};

export const useClientCase = defineStore<
  'client-case-store',
  State,
  Getters,
  Actions
>('client-case-store', {
  state: () => ({
    clientCase: null,
  }),
  getters: {
    getCaseAccounts(state: State) {
      return (predicate?: (status: AccountStatus) => boolean) => {
        const accounts = state.clientCase?.goals.reduce(
          (items, goal) => [...items, ...goal.accounts],
          [] as GoalAccount[],
        );
        return (
          (predicate
            ? accounts?.filter(({ originalStatus }) =>
                predicate(originalStatus),
              )
            : accounts) ?? []
        );
      };
    },
    getGoalAccounts(state: State) {
      return (caseGoalId, predicate?) => {
        const accounts = state.clientCase?.goals.find(
          (goal: CaseGoal) => goal.id === caseGoalId,
        )?.accounts;

        return (
          (predicate
            ? accounts?.filter(({ originalStatus }) =>
                predicate(originalStatus),
              )
            : accounts) ?? []
        );
      };
    },
    getCaseGoal(state: State) {
      return (caseGoalId: CaseGoal['id']) =>
        state.clientCase?.goals.find(
          (goal: CaseGoal) => goal.id === caseGoalId,
        );
    },
    getCaseGoalByType(state: State) {
      return (goalId: number) =>
        state.clientCase?.goals.find((goal: CaseGoal) => goal.type === goalId);
    },
    getTaskGoal(state: State) {
      return (task: Task, caseGoalId: SubTask['caseGoalId']) => ({
        taskSlug: task.slug,
        ...(state.clientCase?.goals.find(
          (goal: CaseGoal) => goal.id === caseGoalId,
        ) as CaseGoal),
        ...(task.subTasks.find(
          (subTask: ArrayElement<Task['subTasks']>) =>
            subTask.caseGoalId === caseGoalId,
        ) as SubTask),
      });
    },
    getCaseTask(state: State) {
      return (slug: Task['slug']) =>
        state.clientCase?.tasks.filter(($t: Task) => $t.slug === slug).pop();
    },
  },
  actions: {
    resetCase() {
      this.clientCase = null;
    },
    storeCase(clientCase: Case) {
      this.clientCase = clientCase;
    },
    updateCaseTasks(tasks: Task[]) {
      this.clientCase.tasks = tasks;
    },
    updateCaseTask(taskSlug: Task['slug'], updatedTask: Task) {
      const taskIndex = this.clientCase.tasks.findIndex(
        ($t: Task) => $t.slug === taskSlug,
      );
      this.clientCase.tasks[taskIndex] = updatedTask;
    },
  },
});
