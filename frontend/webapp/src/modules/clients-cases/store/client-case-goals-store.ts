// @deprecated
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

import { defineStore } from 'pinia';
import { Case } from '@modules/clients-cases/models';
import { Task, TaskId } from '@aventur-shared/modules/tasks';
import { GoalAccount } from '@aventur-shared/modules/goals';
import { TaskStatusEnum } from '@aventur-shared/modules/tasks/models/task-status';

type Goal = Case['goals'][0];
type Account = Goal['accounts'][0];
type Advice = Account['advices'][0];

interface State {
  items: Goal[];
}

type Getters = {
  allGoals: (state: State) => Goal[];
  getGoalById: (state: State) => (id: Goal['id']) => Goal | null;
  getTasksCount: (state: State) => (id: Goal['id']) => number;
  getTaskById: (
    state: State,
  ) => (goalId: Goal['id'], taskId: TaskId) => Task | null;
};

interface Actions {
  storeGoals: (goals: Goal[]) => void;
  updateTask: (goalId: Goal['id'], task: Task) => void;
  updateGoalTasks: (goalId: Goal['id'], tasks: Task[]) => void;
  updateGoalAccounts: (goalId: Goal['id'], accounts: Account[]) => void;
  setAccountAdviceImplemented: (
    goalId: Goal['id'],
    accountId: Account['id'],
    adviceId: Advice['id'],
    payload: boolean,
  ) => void;
  setAccountAdviceAccepted: (
    goalId: Goal['id'],
    accountId: Account['id'],
    adviceId: Advice['id'],
    payload: boolean,
  ) => void;
  changeAccountFeeTemplate: (
    goalId: Goal['id'],
    accountId: Account['id'],
    payload: Account['feeSplitTemplate'],
  ) => void;
  updateAccount: (
    goalId: Goal['id'],
    accountId: Account['id'],
    payload: Partial<
      Pick<
        GoalAccount,
        | 'accountNumber'
        | 'subAccountNumber'
        | 'feeSplitTemplate'
        | 'expectedFees'
        | 'advices'
      >
    >,
  ) => void;
  removeAccount: (goalId: Goal['id'], accountId: Account['id']) => void;
}

export const useClientCaseGoals = defineStore<
  'client-case-goals',
  State,
  Getters,
  Actions
>('client-case-goals', {
  state: () => ({
    items: [],
  }),
  getters: {
    allGoals(state: State) {
      return state.items;
    },
    getGoalById() {
      const goals: Goal[] = this.allGoals;
      return (id): Goal | null => {
        const found = goals.find((goal) => goal.id === id);

        return found || null;
      };
    },
    getTaskById() {
      const goals: Goal[] = this.allGoals;
      return (goalId: Goal['id'], taskId: TaskId) => {
        const foundGoal = goals.find((goal) => goal.id === goalId);
        if (!foundGoal) {
          return null;
        }
        return foundGoal.tasks.find((task: Task) => task.id === taskId) || null;
      };
    },

    getTasksCount() {
      const getGoalById = this.getGoalById;

      return function (goalId: Goal['id']) {
        const goal = getGoalById(goalId);

        return goal.tasks.filter((task: Goal['tasks'][0]) =>
          [
            TaskStatusEnum.Completed,
            TaskStatusEnum.NotApplicable,
            TaskStatusEnum.Canceled,
          ].includes(task.status.toType()),
        ).length;
      };
    },
  },
  actions: {
    storeGoals(goals: Goal[]) {
      this.items = goals;
    },
    updateTask(goalId, updatedTask) {
      const goals = this.allGoals;
      const goalIndex = goals.findIndex((goal: Goal) => goal.id === goalId);
      const taskIndex = goals[goalIndex].tasks.findIndex(
        (task: Task) => task.id === updatedTask.id,
      );

      goals[goalIndex].tasks[taskIndex] = updatedTask;
    },
    updateGoalTasks(goalId: Goal['id'], tasks: Task[]) {
      const goals = this.allGoals;
      const goalIndex = goals.findIndex((goal: Goal) => goal.id === goalId);

      goals[goalIndex].tasks = tasks;
    },
    updateGoalAccounts(goalId: Goal['id'], accounts: Account[]) {
      const goals = this.allGoals;
      const goalIndex = goals.findIndex((goal: Goal) => goal.id === goalId);

      goals[goalIndex].accounts = accounts;
    },
    removeAccount(goalId: Goal['id'], accountId: GoalAccount['id']) {
      const goals = this.allGoals;
      const goalIndex = goals.findIndex((goal: Goal) => goal.id === goalId);

      goals[goalIndex].accounts = goals[goalIndex].accounts.filter(
        (account) => account.id != accountId,
      );
    },
    setAccountAdviceImplemented(goalId, accountId, adviceId, payload) {
      const goals = this.allGoals;
      const goalIndex = goals.findIndex((goal: Goal) => goal.id === goalId);
      const accountIndex = goals[goalIndex].accounts.findIndex(
        (account: Account) => account.id === accountId,
      );
      const adviceIndex = goals[goalIndex].accounts[
        accountIndex
      ].advices.findIndex((advice: Advice) => advice.id === adviceId);

      goals[goalIndex].accounts[accountIndex].advices[
        adviceIndex
      ].isImplemented = payload;
    },

    setAccountAdviceAccepted(goalId, accountId, adviceId, payload) {
      const goals: Goal[] = this.allGoals;
      const goalIndex = goals.findIndex((goal: Goal) => goal.id === goalId);
      const accountIndex = goals[goalIndex].accounts.findIndex(
        (account: Account) => account.id === accountId,
      );
      const adviceIndex = goals[goalIndex].accounts[
        accountIndex
      ].advices.findIndex((advice: Advice) => advice.id === adviceId);

      goals[goalIndex].accounts[accountIndex].advices[adviceIndex].isAccepted =
        payload;
    },

    changeAccountFeeTemplate(goalId, accountId, payload) {
      const goals = this.allGoals;
      const goalIndex = goals.findIndex((goal: Goal) => goal.id === goalId);
      const accountIndex = goals[goalIndex].accounts.findIndex(
        (account: Account) => account.id === accountId,
      );

      goals[goalIndex].accounts[accountIndex].feeSplitTemplate = payload;
    },
    updateAccount(goalId, accountId, payload) {
      const goals = this.allGoals;
      const goalIndex = goals.findIndex((goal: Goal) => goal.id === goalId);
      const accountIndex = goals[goalIndex].accounts.findIndex(
        (account: Account) => account.id === accountId,
      );

      goals[goalIndex].accounts[accountIndex] = {
        ...goals[goalIndex].accounts[accountIndex],
        ...payload,
      };
    },
  },
});
