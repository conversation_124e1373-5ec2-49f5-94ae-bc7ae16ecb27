<template>
  <form class="flex flex-col" @submit="onSubmit">
    <FieldArray v-slot="{ fields, push, remove }: any" name="expectedFees">
      <p class="pb-4">
        Expected Fees
        <span
          v-if="fields.length === 0 && errors.expectedFees"
          class="text-red-500"
        >
          ({{ errors.expectedFees }})
        </span>
      </p>
      <div
        v-for="(field, fieldIdx) in fields"
        :key="field.key"
        class="flex items-center gap-1"
      >
        <div class="mt-3 flex flex-1 flex-col gap-2 sm:flex-row">
          <select-field
            label="Initial"
            class="flex-auto sm:basis-28"
            :name="`expectedFees[${fieldIdx}].initial`"
            :options="feeTypeSelectOptions()"
            :searchable="true"
            :disabled="!isModifiable"
          />

          <text-field
            class="flex-1 grow"
            label="Amount"
            :name="`expectedFees[${fieldIdx}].amount`"
            :disabled="!isModifiable"
          />

          <date-picker
            :name="`expectedFees[${fieldIdx}].dueDate`"
            label="Due date"
            :disabled="!isModifiable"
          />
        </div>
        <button
          v-if="isModifiable"
          type="button"
          class="mt-4 flex h-fit hover:rounded-md hover:bg-gray-100"
          @click="remove(fieldIdx)"
        >
          <x-mark-icon class="size-5" />
        </button>
      </div>
      <div class="mb-4 flex flex-row flex-nowrap justify-end">
        <custom-button
          v-if="isModifiable"
          theme="secondary"
          type="button"
          class="text-sm"
          @click="
            push({
              initial: null,
              amount: null,
              dueDate: null,
            })
          "
        >
          Add more
        </custom-button>
      </div>
    </FieldArray>

    <custom-button
      type="submit"
      theme="primary"
      class="w-full"
      :disabled="!isModifiable"
    >
      Save and add advice
    </custom-button>
  </form>
</template>

<script setup lang="ts">
  import { FieldArray, useForm } from 'vee-validate';
  import { FormValues, validationSchema } from './form-model';
  import { Button as CustomButton } from '@modules/ui';
  import {
    DatePicker,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import {
    ExpectedFeeType,
    ExpectedFeeTypeEnum,
  } from '@aventur-shared/modules/accounts/models/expected-fee-type';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

  const props = defineProps<{
    account: GoalAccount;
    isModifiable: boolean;
  }>();

  const emit = defineEmits<{
    (e: 'on-submit', formValues: FormValues): void;
  }>();

  const { handleSubmit, errors } = useForm<FormValues>({
    initialValues: {
      expectedFees: props.account.expectedFees.length
        ? props.account.expectedFees.map((fee) => ({
            id: fee.id,
            initial: fee.initial.toNumber(),
            dueDate: fee.dueDate.formatForForm(),
            amount: fee.amount,
          }))
        : [
            {
              id: undefined,
              initial: null,
              dueDate: null,
              amount: null,
            },
          ],
    },
    validationSchema,
  });

  const onSubmit = handleSubmit(async (formValues: FormValues) => {
    emit('on-submit', formValues);
  });

  const feeTypeSelectOptions = (): SelectOption[] => {
    const result: SelectOption[] = [];
    for (const key in ExpectedFeeTypeEnum) {
      if (isNaN(Number(key))) {
        const feeType = new ExpectedFeeType(Number(ExpectedFeeTypeEnum[key]));
        result.push({ value: feeType.toNumber(), label: feeType.toString() });
      }
    }
    return result;
  };
</script>
