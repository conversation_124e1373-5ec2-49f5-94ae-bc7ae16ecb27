import * as yup from 'yup';
import * as validationMessages from '@aventur-shared/utils/form/validation-messages';
import { fieldRequiredMessage } from '@aventur-shared/utils/form/validation-messages';
import { Nullable } from '@aventur-shared/types/Common';

export type FormValues = {
  expectedFees: Array<{
    id: number | undefined;
    initial: Nullable<number>;
    amount: Nullable<string>;
    dueDate: Nullable<string>;
  }>;
};

const feesSchema = yup.object({
  id: yup.number(),
  initial: yup.number().nullable().default(null).required(fieldRequiredMessage),
  amount: yup
    .mixed()
    .checkIsNumber(validationMessages.invalidNumberMessage)
    .when({
      is: (value: string) => value,
      then: () =>
        yup
          .number()
          .min(-500000, 'Value must be higher than -500.000')
          .max(500000, 'Value must be lower than 500.000'),
    })
    .transform((value) => value || null)
    .required(fieldRequiredMessage),
  dueDate: yup.string().nullable().required(fieldRequiredMessage),
});

export const validationSchema = yup.object().shape({
  expectedFees: yup.array(feesSchema).min(0),
});
