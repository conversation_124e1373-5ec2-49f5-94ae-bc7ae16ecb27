<template>
  <box>
    <box-section class="rounded-t-lg bg-[#19182C]">
      <div class="flex flex-col items-start">
        <h1 class="text-[#7A8796]">Account Expected Fees</h1>
        <div class="mb-4 mt-6">
          <dl>
            <dt class="flex text-lg text-[#6DBFC0]">
              {{ account.type }}
            </dt>
            <dd class="flex text-lg font-medium text-white">
              {{ account.providerName }}
            </dd>
          </dl>
        </div>
      </div>
    </box-section>

    <box-section class="flex flex-col gap-1 text-start">
      <add-expected-fees-form
        :account="account"
        :is-modifiable="isModifiable"
        @on-submit="handleSubmitExpectedFees"
      />
      <custom-button
        ref="cancelButtonRef"
        theme="gray-ghost"
        class="w-full"
        @on-click="$emit('on-cancel')"
        >Cancel and close
      </custom-button>
      <span v-if="errorRef" class="self-center text-red-500">{{
        errorRef
      }}</span>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import { DateTime } from '@aventur-shared/utils/dateTime';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { CaseId } from '@aventur-shared/modules/cases';
  import { ExpectedFeeType } from '@aventur-shared/modules/accounts/models/expected-fee-type';
  import {
    AddExpectedFeesForm,
    type FormValues,
    updateAccountExpectedFees,
  } from '@modules/sub-tasks/use-cases/user-adds-expected-fees-to-proposed-account';
  import { Box, BoxSection, Button as CustomButton } from '@modules/ui';

  const toast = useToast();

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoal;
    account: GoalAccount;
    isModifiable: boolean;
  }>();

  const emit = defineEmits(['on-cancel', 'on-submit', 'on-load']);
  const errorRef = ref<string>();
  const cancelButtonRef = ref(null);

  onMounted(() => {
    emit('on-load', { focusRef: cancelButtonRef });
  });

  const handleSubmitExpectedFees = async (formValues: FormValues) => {
    try {
      await updateAccountExpectedFees(
        props.caseId,
        props.goal.id,
        props.goal.taskId,
        {
          accountId: props.account.id,
          expectedFees: (formValues.expectedFees || []).map((fee) => ({
            id: fee.id,
            dueDate: new DateTime(fee.dueDate),
            initial: new ExpectedFeeType(fee.initial as number),
            amount: fee.amount as string,
          })),
        },
      );
      toast.success('Expected fees have been updated.');
      emit('on-submit');
    } catch (e: unknown) {
      errorRef.value = (e as Error).message;
    }
  };
</script>
