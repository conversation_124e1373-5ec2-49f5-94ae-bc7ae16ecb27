import { type GoalAccount, type GoalId } from '@aventur-shared/modules/goals';
import { addExpectedFeesToProposedAccountRequest } from '@aventur-shared/modules/goals/api/add-expected-fees-to-proposed-account-request';
import { AccountId } from '@aventur-shared/modules/accounts';
import { CaseId } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';

type ExpectedFeesModel = {
  accountId: AccountId;
  expectedFees: GoalAccount['expectedFees'];
};

export const action = async (
  caseId: CaseId,
  caseGoalId: GoalId,
  taskId: TaskId,
  model: ExpectedFeesModel,
): Promise<void> => {
  try {
    await addExpectedFeesToProposedAccountRequest(
      caseId,
      caseGoalId,
      taskId,
      model.accountId,
      model.expectedFees.map((fee) => ({
        id: fee.id,
        initial: fee.initial.toNumber(),
        dueDate: fee.dueDate.formatForForm(),
        amount: fee.amount,
      })),
    );
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw new Error(e.message);
    }
    throw e;
  }
};
