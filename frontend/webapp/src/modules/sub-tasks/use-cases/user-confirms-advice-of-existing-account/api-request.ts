import { apiClient } from '@aventur-shared/services/api';
import { Case, CaseId, TaskCustomCommand } from '@aventur-shared/modules/cases';
import { AccountId, Advice } from '@aventur-shared/modules/accounts';
import { AdviceDTO } from '@aventur-shared/modules/accounts/models/advice';
import { AccountDTO } from '@aventur-shared/modules/accounts/dtos/account-dto';
import {
  ExpectedFee,
  type ExpectedFeeDto,
} from '@aventur-shared/modules/accounts';
import { TaskId } from '@aventur-shared/modules/tasks';

type AdvicePayload = Pick<Advice, 'id' | 'isImplemented'>;

type Command = TaskCustomCommand.UpdateAdviceImplementation;

type Body = {
  account_id: AccountDTO['id'];
  advice: Pick<AdviceDTO, 'id' | 'is_implemented'>[];
  fee_split_template_id: number;
  adviser_id: number;
  expected_fees: ExpectedFeeDto[];
};

type ConfirmAdvicePayload = {
  accountId: AccountId;
  advices: AdvicePayload[];
  feeSplitTemplateId: number;
  advisorId: Case['relatedAdvisor']['id'];
  expectedFees: ExpectedFee[];
};

export const confirmAdvice = async (
  caseId: CaseId,
  taskId: TaskId,
  {
    accountId,
    advices,
    feeSplitTemplateId,
    advisorId,
    expectedFees,
  }: ConfirmAdvicePayload,
): Promise<void> => {
  const body: Body = {
    advice: advices.map((advice) => ({
      id: advice.id,
      is_implemented: advice.isImplemented,
    })),
    account_id: accountId,
    adviser_id: advisorId,
    fee_split_template_id: feeSplitTemplateId,
    expected_fees: expectedFees.map((fee) => ({
      id: fee.id,
      fee_type: fee.initial.toNumber(),
      amount: fee.amount,
      due_date: fee.dueDate.formatForForm(),
    })),
  };

  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/ongoing_provider_chasing/${taskId}`,
    {
      command: TaskCustomCommand.UpdateAdviceImplementation,
      payload: { ...body },
    },
  );
};
