<template>
  <form class="flex flex-col" @submit="onSubmit">
    <select-field
      class="mb-4 flex-1"
      name="advisorId"
      label="Adviser"
      :options="getAdvisorsSelectOptions(activeAdvisors)"
      :searchable="true"
      :disabled="!isModifiable"
      @on-select="handleAdvisorSelect"
    />

    <select-field
      class="mb-4 flex-1"
      name="feeSplitTemplate"
      label="Fee split template"
      :options="
        feeSplitTemplates.map((a) => ({
          value: a.id,
          label: a.name,
        }))
      "
      :searchable="true"
      :disabled="!isModifiable || !values.advisorId"
    />

    <FieldArray v-slot="{ fields }: any" name="advices">
      <p class="pb-4">{{ fields.length }} advice added</p>

      <div v-for="(field, fieldIdx) in fields" :key="field.key">
        <advice-row
          :title="getAdviceStringifiedType(field.value.type)"
          :description="field.value.description"
        >
          <switch-field
            :value="true"
            :name="`advices[${fieldIdx}].isImplemented`"
            :disabled="!isModifiable || !field.value.isAccepted"
            :label="`${
              field.value.isAccepted ? 'Complete?' : 'No confirmation'
            }`"
          />
        </advice-row>
        <divider class="mb-4" />
      </div>
    </FieldArray>

    <custom-button
      type="submit"
      theme="primary"
      class="w-full"
      :disabled="
        !hasChanges ||
        !isModifiable ||
        (advices.length === 0 &&
          'advices' in values &&
          values.advices.length === 0)
      "
    >
      Save and add advice
    </custom-button>
  </form>
</template>

<script setup lang="ts">
  import { FieldArray, useForm } from 'vee-validate';
  import { FormValues, validationSchema } from './form-model';
  import { Button as CustomButton, Divider } from '@modules/ui';
  import { SelectField, SwitchField } from '@aventur-shared/components/form';
  import { useRefData } from '@aventur-shared/stores';
  import { storeToRefs } from 'pinia';
  import { AdviceRow } from '@modules/clients-cases/ui';
  import { useFeeSplitTemplates } from '@aventur-shared/modules/refdata/fee-split-template';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';
  import { computed, onMounted, toRefs } from 'vue';

  const { activeAdvisors } = advisorsProvider().provide();

  const props = defineProps<{
    advices: FormValues['advices'];
    advisorId: FormValues['advisorId'];
    feeSplitTemplate: FormValues['feeSplitTemplate'];
    isModifiable: boolean;
  }>();

  const { advices, isModifiable } = toRefs(props);

  const emit = defineEmits<{
    (e: 'on-submit', formValues: FormValues): void;
  }>();

  const { adviceTypes } = storeToRefs(useRefData());
  const { feeSplitTemplates, fetchForAdvisor: fetchAdvisorFeeSplitTemplates } =
    useFeeSplitTemplates();

  onMounted(() => {
    advisorsProvider().create();
    if (props.advisorId) {
      fetchAdvisorFeeSplitTemplates(props.advisorId);
    }
  });

  const { handleSubmit, values, setFieldValue, meta } = useForm<FormValues>({
    initialValues: {
      advices: props.advices,
      advisorId: props.advisorId,
      feeSplitTemplate: props.feeSplitTemplate,
      expectedFees: [],
    },
    validationSchema,
  });

  const hasChanges = computed(() => meta.value.dirty);

  const onSubmit = handleSubmit(async (formValues: FormValues) => {
    emit('on-submit', formValues);
  });

  const getAdviceStringifiedType = (adviceType: number): string => {
    return adviceTypes.value.find((type) => type.id === adviceType)?.name || '';
  };

  function handleAdvisorSelect(advisor: number) {
    setFieldValue('feeSplitTemplate', null);
    if (advisor) {
      fetchAdvisorFeeSplitTemplates(advisor);
    }
  }
</script>
