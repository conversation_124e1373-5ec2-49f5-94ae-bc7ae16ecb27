import * as yup from 'yup';
import { validationMessages } from '@aventur-shared/utils/form';
import { fieldRequiredMessage } from '@aventur-shared/utils/form/validation-messages';
import { Nullable } from '@aventur-shared/types/Common';

export type FormValues = {
  advices: Array<{
    id: number | undefined;
    isImplemented: boolean;
    description: string;
    type: number;
  }>;
  advisorId: Nullable<number>;
  feeSplitTemplate: Nullable<number>;
  expectedFees: Array<{
    id: number | undefined;
    initial: number;
    dueDate: string;
    amount: string;
  }>;
};

export const validationSchema = yup.object().shape({
  advices: yup.array().of(
    yup.object().shape({
      isConfisImplementedirmed: yup.boolean().nullable(),
    }),
  ),
  advisorId: yup.number().nullable().required(fieldRequiredMessage),
  feeSplitTemplate: yup
    .number()
    .typeError(validationMessages.fieldRequiredMessage)
    .required(validationMessages.fieldRequiredMessage),
});
