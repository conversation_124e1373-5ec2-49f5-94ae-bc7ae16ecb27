<template>
  <box>
    <box-section class="rounded-t-lg bg-[#19182C]">
      <div class="flex flex-col items-start">
        <h1 class="text-[#7A8796]">Existing Account Advice</h1>

        <div class="mb-4 mt-6">
          <dl>
            <dt class="flex text-lg text-[#6DBFC0]">
              {{ account.type }}
            </dt>
            <dd class="flex text-lg font-medium text-white">
              {{ account.providerName }}
            </dd>
          </dl>
        </div>

        <div class="flex gap-4">
          <dl>
            <dt class="flex text-sm text-gray-400">Account No.</dt>
            <dd class="flex font-medium text-white">
              {{ account.accountNumber }}
            </dd>
          </dl>

          <dl>
            <dt class="flex text-sm text-gray-400">Sub Account No.</dt>
            <dd class="flex font-medium text-white">
              {{ account.subAccountNumber }}
            </dd>
          </dl>
        </div>
      </div>
    </box-section>

    <box-section class="flex flex-col gap-1 text-start">
      <advice-form
        :advices="
          account.advices.map((advice: GoalAccount['advices'][0]) => ({
            id: advice.id,
            isImplemented: advice.isImplemented,
            isAccepted: advice.isAccepted,
            description: advice.description,
            type: advice.type,
          }))
        "
        :advisor-id="account.advisorId || caseAdvisorId"
        :fee-split-template="account.feeSplitTemplate"
        :is-modifiable="isModifiable"
        @on-submit="handleSubmitAdvice"
      />

      <custom-button
        theme="gray-ghost"
        class="w-full"
        @on-click="$emit('on-cancel')"
        >Cancel and close</custom-button
      >

      <span v-if="errorRef" class="self-center text-red-500">{{
        errorRef
      }}</span>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import { DateTime } from '@aventur-shared/utils/dateTime';
  import { ExpectedFeeType } from '@aventur-shared/modules/accounts/models/expected-fee-type';
  import { parseErrorFromResponse } from '@aventur-shared/services/api';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { Case, CaseId } from '@aventur-shared/modules/cases';
  import { Box, BoxSection, Button as CustomButton } from '@modules/ui';
  import {
    Form as AdviceForm,
    FormValues,
  } from '@modules/sub-tasks/use-cases/user-confirms-advice-of-existing-account';
  import { action } from '../action';

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoal;
    account: GoalAccount;
    isModifiable: boolean;
    caseAdvisorId: Case['relatedAdvisor']['id'];
  }>();

  const emit = defineEmits(['on-cancel', 'on-submit']);
  const errorRef = ref<string>();

  const handleSubmitAdvice = async (formValues: FormValues) => {
    try {
      await action(props.caseId, props.goal.taskId, {
        accountId: props.account.id,
        advices: (formValues.advices || []).map((formAdvice) => ({
          id: formAdvice.id,
          isImplemented: formAdvice.isImplemented,
        })),
        advisorId: formValues.advisorId as number,
        feeSplitTemplateId: formValues.feeSplitTemplate as number,
        expectedFees: (formValues.expectedFees || []).map((fee) => ({
          id: fee.id,
          dueDate: new DateTime(fee.dueDate),
          initial: new ExpectedFeeType(fee.initial as number),
          amount: fee.amount as string,
        })),
      });
      emit('on-submit');
    } catch (e: unknown) {
      errorRef.value =
        parseErrorFromResponse(e).join('\r\n') ||
        'Unexpected error during saving advice.';
    }
  };
</script>
