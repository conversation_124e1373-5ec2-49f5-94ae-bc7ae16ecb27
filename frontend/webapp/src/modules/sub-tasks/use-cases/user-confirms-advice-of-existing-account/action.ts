import { ConfirmAdviceModel as Model } from './model';
import { confirmAdvice as confirmAdviceRequest } from './api-request';
import { TaskId } from '@aventur-shared/modules/tasks';
import { CaseId } from '@aventur-shared/modules/cases';

export const action = async (
  caseId: CaseId,
  taskId: TaskId,
  model: Model,
): Promise<void> => {
  await confirmAdviceRequest(caseId, taskId, {
    accountId: model.accountId,
    advices: model.advices,
    feeSplitTemplateId: model.feeSplitTemplateId,
    advisorId: model.advisorId,
    expectedFees: model.expectedFees,
  });
};
