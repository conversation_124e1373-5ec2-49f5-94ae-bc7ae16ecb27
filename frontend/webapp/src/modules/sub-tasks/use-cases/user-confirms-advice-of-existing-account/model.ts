import { Case } from '@aventur-shared/modules/cases';
import { type GoalAccount } from '@aventur-shared/modules/goals';
import { ExpectedFee } from '@aventur-shared/modules/accounts';
import { FeeSplitTemplate } from '@aventur-shared/modules/refdata/fee-split-template';

export interface ConfirmAdviceModel {
  accountId: GoalAccount['id'];
  advices: Array<Pick<GoalAccount['advices'][0], 'id' | 'isImplemented'>>;
  advisorId: Case['relatedAdvisor']['id'];
  feeSplitTemplateId: FeeSplitTemplate['id'];
  expectedFees: Array<
    Pick<ExpectedFee, 'id' | 'initial' | 'amount' | 'dueDate'>
  >;
}
