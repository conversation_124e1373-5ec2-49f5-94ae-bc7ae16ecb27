<template>
  <modal>
    <template #open-button="{ open }">
      <custom-button
        theme="secondary"
        class="mt-3"
        :disabled="!isModifiable"
        @on-click="handleOpenCreateProposedAccountModal(open)"
        >Add Proposed Account
      </custom-button>
    </template>

    <template #default="{ close }">
      <box class="text-left">
        <box-section
          title="Add Proposed Account"
          divider="bottom"
          class="flex justify-between"
        >
          <custom-button
            theme="gray-ghost"
            class="-mx-5 -my-2"
            @on-click="close"
          >
            <XMarkIcon class="size-4" />
          </custom-button>
        </box-section>
        <box-section no-padding class="pt-4">
          <create-proposed-account-form
            :account-options="existingAccounts"
            @on-create="
              (formValues) => handleCreateProposedAccount(close)(formValues)
            "
          />
        </box-section>
        <box-section no-padding-y class="pb-4">
          <custom-button theme="gray-ghost" class="w-full" @on-click="close"
            >Cancel & close</custom-button
          >
          <p v-if="errorMessage" class="text-center text-red-500">
            {{ errorMessage }}
          </p>
        </box-section>
      </box>
    </template>
  </modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Box, BoxSection, Button as CustomButton, Modal } from '@modules/ui';
  import { XMarkIcon } from '@heroicons/vue/24/outline';
  import { parseErrorFromResponse } from '@aventur-shared/services/api';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { CaseId } from '@aventur-shared/modules/cases';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { userPreviewCaseAction } from '@modules/clients-cases';
  import {
    CreateProposedAccountForm,
    FormValues,
    createProposedAccountAction,
  } from '@modules/sub-tasks/use-cases/user-creates-proposed-account';
  import { GoalAccount } from '@aventur-shared/modules/goals';

  const toast = useToast();

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoal;
    isModifiable: boolean;
  }>();

  const existingAccounts = ref<GoalAccount[]>(
    props.goal.accounts.filter((account) => account.status.name !== 'Proposed'),
  );

  const errorMessage = ref('');

  const handleCreateProposedAccount =
    (closeModal: CallableFunction) => async (formValues: FormValues) => {
      try {
        await createProposedAccountAction(props.caseId, props.goal.taskId, {
          provider: formValues.provider as number,
          accountType: formValues.accountType as number,
          advices: formValues.advices.map((advice) => ({
            description: advice.adviceDescription,
            groupId: advice.groupId,
            id: advice.id,
            isAccepted: advice.isAccepted,
            isImplemented: advice.isImplemented,
            type: advice.adviceType as number,
            amount: advice.amount,
            frequency: advice.frequency,
            portfolioId: advice.portfolioId,
            accountId: advice.accountId,
          })),
          clientIds: formValues.clientIds,
        });
        toast.success('Proposed account has been created');
        await userPreviewCaseAction(props.caseId);
        closeModal();
      } catch (e: unknown) {
        errorMessage.value =
          parseErrorFromResponse(e).join('\r\n') ||
          'Could not create proposed account.';
      }
    };

  const handleOpenCreateProposedAccountModal = (open: CallableFunction) => {
    errorMessage.value = '';
    open();
  };
</script>
