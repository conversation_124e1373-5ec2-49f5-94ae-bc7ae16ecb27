import { Ref } from 'vue';
import { validationMessages } from '@aventur-shared/utils/form';
import { array, number, object } from 'yup';
import { ClientId } from '@aventur-shared/modules/clients';
import { Nullable } from '@aventur-shared/types/Common';
import {
  MetaType,
  validationSchema as adviceValidationSchema,
} from '../../../user-adds-advice-to-account/ui/advice-form-model';
import { AdviceFrequencyEnum } from '@aventur-shared/modules/factfind/models/frequency';

export const validationSchema = (adviceTypeMetaMap: Ref<MetaType>) =>
  object({
    clientIds: array()
      .of(number())
      .min(1, 'At least 1 client has to be selected'),
    provider: number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required(validationMessages.fieldRequiredMessage),
    accountType: number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required(validationMessages.fieldRequiredMessage),
  }).concat(adviceValidationSchema(adviceTypeMetaMap));

export type FormValues = {
  provider: Nullable<number>;
  accountType: Nullable<number>;
  advices: {
    id: number | undefined;
    adviceType: Nullable<number>;
    groupId: Nullable<number>;
    adviceDescription: string;
    amount?: number;
    frequency?: AdviceFrequencyEnum;
    portfolioId?: number;
    accountId?: number;
    isImplemented: boolean;
    isAccepted: boolean;
  }[];
  clientIds: ClientId[];
};
