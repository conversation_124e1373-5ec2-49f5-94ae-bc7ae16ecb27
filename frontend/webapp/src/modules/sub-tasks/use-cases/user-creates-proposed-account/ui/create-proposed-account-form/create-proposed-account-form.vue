<template>
  <form @submit="onSubmit">
    <box-section no-padding-y>
      <checkbox-group
        name="clientIds"
        label="Clients"
        :options="
          caseClients.map((c) => ({
            value: c.id,
            label: formatName(c),
          }))
        "
      />
      <div class="grid xl:grid-cols-2 xl:gap-4">
        <select-field
          label="Provider"
          name="provider"
          :options="
            getProviders.map((provider) => ({
              label: provider.name,
              value: provider.id,
            }))
          "
        />

        <select-field
          label="Account Type"
          name="accountType"
          :options="
            accountTypes.map((holding) => ({
              label: holding.name,
              value: holding.id,
            })) || []
          "
        />
      </div>
    </box-section>
    <box-section no-padding-y>
      <FieldArray v-slot="{ fields, push, remove }: any" name="advices">
        <p class="mb-2">{{ fields?.length }} advice added</p>
        <div
          v-for="(field, fieldIdx) in fields"
          :key="field.key"
          :class="['flex w-full gap-1 px-0 py-4']"
        >
          <div class="my-0 flex-1">
            <select-field
              class="!mb-1 flex-1"
              :name="`advices[${fieldIdx}].adviceType`"
              label="Advice Type"
              :options="adviceTypeSelectionOptions"
              :searchable="true"
            />
            <text-field
              v-if="formRowAdviceTypeMap[fieldIdx].requiresAmount"
              class="!mb-1"
              label="Amount"
              :name="`advices[${fieldIdx}].amount`"
            />
            <select-field
              v-if="formRowAdviceTypeMap[fieldIdx].requiresFrequency"
              class="!mb-1 flex-1"
              :name="`advices[${fieldIdx}].frequency`"
              label="Frequency"
              :options="adviceFrequencySelectOptions"
              :searchable="true"
            />
            <select-field
              v-if="formRowAdviceTypeMap[fieldIdx].requiresPortfolio"
              class="!mb-1 flex-1"
              :name="`advices[${fieldIdx}].portfolioId`"
              label="Portfolio"
              :options="portfolioSelectionOptions"
              :searchable="true"
            />
            <select-field
              v-if="formRowAdviceTypeMap[fieldIdx].requiresAccount"
              class="!mb-1 flex-1"
              :name="`advices[${fieldIdx}].accountId`"
              label="Source Account"
              :options="accountSelectionOptions"
              :searchable="true"
            />
            <text-area-field
              class="!mb-1"
              :name="`advices[${fieldIdx}].adviceDescription`"
              label="Notes"
              placeholder="Add notes"
            />
          </div>
          <div class="!mt-0">
            <button
              type="button"
              class="flex h-fit hover:rounded-md hover:bg-gray-200"
              @click="remove(fieldIdx)"
            >
              <x-mark-icon class="size-5" />
            </button>
          </div>
        </div>
        <custom-button
          v-if="
            adviceTypeSelectionOptions.length !== fields?.length &&
            values.accountType
          "
          theme="secondary"
          type="button"
          @click="
            push({
              adviceType: null,
              adviceDescription: '',
            })
          "
          >Add advice row</custom-button
        >
        <span
          v-else-if="
            values.accountType && adviceTypeSelectionOptions.length === 0
          "
          >There are no advice types for given account type</span
        >
        <span v-else>Please select account type first</span>
      </FieldArray>
    </box-section>
    <box-section no-padding-y class="mt-7">
      <custom-button theme="primary" type="submit" class="w-full"
        >Save and add account</custom-button
      >
    </box-section>
  </form>
</template>

<script setup lang="ts">
  import { FieldArray, useForm } from 'vee-validate';
  import { FormValues, validationSchema } from './form-model';
  import {
    CheckboxGroup,
    SelectField,
    TextAreaField,
    TextField,
  } from '@aventur-shared/components/form';
  import { BoxSection, Button as CustomButton } from '@modules/ui';
  import { useRefData } from '@aventur-shared/stores';
  import { XMarkIcon } from '@heroicons/vue/24/outline';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeMount, ref, watch } from 'vue';
  import { useProducts } from '@aventur-shared/composables/useProducts';
  import { useCaseClients } from '@modules/clients-cases/store/client-case-clients';
  import { formatName } from '@aventur-shared/utils/user';
  import { Product } from '@aventur-shared/modules/refdata/types/Product';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import { MetaType } from '../../../user-adds-advice-to-account/ui/advice-form-model';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { adviceFrequencySelectOptions } from '@aventur-shared/modules/factfind/models/frequency';

  const props = defineProps<{
    accountOptions: GoalAccount[];
  }>();

  const accountTypes = ref<Product[]>([]);

  const emit = defineEmits<{
    (e: 'on-create', formValues: FormValues): void;
  }>();

  const { caseClients } = useCaseClients();
  const { getAccountLikeProducts } = useProducts();

  const {
    getAdviceTypesForProposedAccountsByTypeId,
    getProviders,
    getPortfolioModels,
  } = storeToRefs(useRefData());

  const adviceTypeSelectionOptions = computed((): SelectOption[] =>
    getAdviceTypesForProposedAccountsByTypeId
      .value(values.accountType as number)
      .map((advice) => ({
        label: advice.name,
        value: advice.id,
      })),
  );

  const adviceTypeMetaMap = computed((): MetaType => {
    return getAdviceTypesForProposedAccountsByTypeId
      .value(values.accountType as number)
      .reduce((map, option) => {
        map[option.id] = {
          requiresAmount: option.requires_amount,
          requiresFrequency: option.requires_frequency,
          requiresPortfolio: option.requires_portfolio,
          requiresAccount: option.requires_account,
        };
        return map;
      }, {});
  });

  const { handleSubmit, values, setFieldValue } = useForm<FormValues>({
    validationSchema: validationSchema(adviceTypeMetaMap),
    initialValues: {
      accountType: null,
      advices: [],
      provider: null,
      clientIds: [],
    },
  });

  const formRowAdviceTypeMap = computed(() => {
    const formRowAdviceTypeMap = {};
    values.advices.forEach((option, index) => {
      if (
        option.adviceType === null ||
        Object.keys(adviceTypeMetaMap.value).length === 0
      ) {
        formRowAdviceTypeMap[index] = {
          requiresAmount: false,
          requiresFrequency: false,
          requiresPortfolio: false,
          requriesAccount: false,
        };
      } else {
        formRowAdviceTypeMap[index] = {
          requiresAmount:
            adviceTypeMetaMap.value[option.adviceType].requiresAmount,
          requiresFrequency:
            adviceTypeMetaMap.value[option.adviceType].requiresFrequency,
          requiresPortfolio:
            adviceTypeMetaMap.value[option.adviceType].requiresPortfolio,
          requiresAccount:
            adviceTypeMetaMap.value[option.adviceType].requiresAccount,
        };
      }
    });
    return formRowAdviceTypeMap;
  });

  watch(
    () => values.accountType,
    () => {
      setFieldValue('advices', []);
    },
  );

  onBeforeMount(async () => {
    accountTypes.value = await getAccountLikeProducts();
  });

  const accountSelectionOptions = ref<SelectOption[]>(
    props.accountOptions
      .map((account) => ({
        label: `${account.providerName} - ${account.type} - ${account.accountNumber}`,
        value: account.id,
      }))
      .sort((first, second) => first.label.localeCompare(second.label)),
  );

  const portfolioSelectionOptions = ref<SelectOption[]>(
    getPortfolioModels.value.map((option) => ({
      label: option.name,
      value: option.id as string,
    })),
  );

  const onSubmit = handleSubmit((formValues) => {
    emit('on-create', {
      advices: formValues.advices ?? [],
      clientIds: formValues.clientIds,
      accountType: formValues.accountType,
      provider: formValues.provider,
    });
  });
</script>
