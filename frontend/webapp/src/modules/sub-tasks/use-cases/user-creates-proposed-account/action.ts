import { CreateProposedAccountModel } from './create-proposed-account-model';
import { createProposedAccount } from '@aventur-shared/modules/accounts/api';
import { TaskId } from '@aventur-shared/modules/tasks';
import { CaseId } from '@aventur-shared/modules/cases';

export default async (
  caseId: CaseId,
  taskId: TaskId,
  model: CreateProposedAccountModel,
) =>
  await createProposedAccount(caseId, taskId, {
    account_type_id: model.accountType,
    provider_id: model.provider,
    advice_lines: model.advices.map((advice) => ({
      advice_type_id: advice.type,
      note: advice.description,
      amount: advice.amount,
      frequency: advice.frequency,
      portfolio_id: advice.portfolioId,
      referenced_account_id: advice.accountId,
    })),
    client_ids: model.clientIds,
  });
