import { StateTree, defineStore } from 'pinia';

import { Nullable } from '@aventur-shared/types/Common';
import {
  ContributionItem,
  ContributionType,
  ForecasterData,
  ForecasterOptions,
  WithdrawalItem,
  WithdrawalType,
} from '@aventur-shared/modules/goals';
import { ClientId } from '@aventur-shared/modules/clients';
import { AboutYou } from '@aventur-shared/modules/factfind';
import { getClientAboutYou } from '@aventur-shared/modules/factfind/api';
//

interface State {
  data: Map<string, ForecasterData>;
}

type Getters = {
  getForecasterData: (
    state: State,
  ) => (goalId: number, clientId: ClientId) => Nullable<ForecasterData>;
  getContributions: (
    state: State,
  ) => (goalId: number, clientId: ClientId) => ContributionItem[];
  getWithdrawals: (
    state: State,
  ) => (goalId: number, clientId: ClientId) => WithdrawalItem[];
  getOptions: (
    state: State,
  ) => (goalId: number, clientId: ClientId) => Partial<ForecasterOptions>;
};

interface Actions {
  reset: () => void;
  saveForecasterData: (
    goalId: number,
    clientId: ClientId,
    data: ForecasterData | null,
  ) => void;
  resetForecasterData: (goalId: number, clientId: ClientId) => void;
  getClientDetails: (clientId: number) => Promise<AboutYou>;
}

const contribution_types = Object.values(ContributionType) as string[];
const withdrawals_types = Object.values(WithdrawalType) as string[];

export const useStore = defineStore<
  'goal-forecaster-plan',
  State,
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  Getters,
  Actions
>('goal-forecaster-plan', {
  state: () => ({
    data: new Map(),
  }),
  getters: {
    getForecasterData(state: State) {
      return (goalId: number, clientId: ClientId) =>
        state.data.get(JSON.stringify({ goalId, clientId })) || null;
    },
    getContributions(state: State) {
      return (goalId: number, clientId: ClientId) =>
        state.data
          .get(JSON.stringify({ goalId, clientId }))
          ?.cashflows.filter((cf) =>
            contribution_types.includes(cf.type),
          ) as ContributionItem[];
    },
    getWithdrawals(state: State) {
      return (goalId: number, clientId: ClientId) =>
        state.data
          .get(JSON.stringify({ goalId, clientId }))
          ?.cashflows.filter((cf) =>
            withdrawals_types.includes(cf.type),
          ) as WithdrawalItem[];
    },
    getOptions(state: State) {
      return (goalId: number, clientId: ClientId) =>
        state.data.get(JSON.stringify({ goalId, clientId }))?.options || {
          selectedRisk: null,
          inflationAdjusted: false,
        };
    },
  },
  actions: {
    reset() {
      this.$reset();
    },
    saveForecasterData(
      goalId: number,
      clientId: ClientId,
      data: ForecasterData | null,
    ) {
      data && this.data.set(JSON.stringify({ goalId, clientId }), { ...data });
    },
    resetForecasterData(goalId: number, clientId: ClientId) {
      this.data.delete(JSON.stringify({ goalId, clientId }));
    },
    async getClientDetails(clientId: number) {
      return await getClientAboutYou(clientId as ClientId);
    },
  },
  persist: [
    {
      pick: ['data'],
      serializer: {
        deserialize: (value: string) => {
          return new Map(JSON.parse(value));
        },
        serialize: (value: StateTree) => {
          return JSON.stringify([...value.data.entries()]);
        },
      },
    },
  ],
});
