import { filter, find, get, keys, map } from 'lodash';
import { Ref, computed, provide, reactive, ref, toValue } from 'vue';
import { useTimeoutFn } from '@vueuse/core';
import { DateTime } from 'luxon';
import {
  birthdateInjectionKey,
  useAge,
} from '@aventur-shared/composables/useAge';
import { Nullable } from '@aventur-shared/types/Common';
import { ClientId } from '@aventur-shared/modules/clients';
import {
  CashflowItem,
  ContributionItem,
  ForecasterResultsDTO,
  ForecasterStatsValue,
  GoalId,
  RiskLevelType,
  WithdrawalItem,
  WithdrawalType,
  buildingPlanInjectionKey,
  recommendedRiskInjectionKey,
  selectedRiskInjectionKey,
} from '@aventur-shared/modules/goals';
import { DEFAULT_INFLATION_RATE } from '@aventur-shared/constants';
import {
  formatMoneyValue,
  formatPercentValue,
  getGrowthRateByRisk,
  getMaxDrawdownByRisk,
} from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/components/stats';
import { generateForecasterPlan } from '@aventur-shared/modules/goals/api/generate-forecaster-plan';
import { default as useCashflowPlanStore } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/stores';

export const useForecaster = (goalId: GoalId, clientId: ClientId) => {
  const { getForecasterData, saveForecasterData, resetForecasterData } =
    useCashflowPlanStore();

  const birthdate = ref<Nullable<string>>();
  const retirementAge = ref<number>();
  const alreadyRetired = ref<boolean>(false);

  const { age2date, date2age } = useAge(birthdate as Ref<string>);

  // risk
  const selectedRisk = ref<RiskLevelType>();
  const recommendedRisk = ref<RiskLevelType>();

  // inflation
  const inflationAdjusted = ref(true);
  const inflationRate = ref<Nullable<number>>(null);

  // comments
  const comments = ref<Nullable<string>>();

  const terminalDate = ref<number>();
  const setTerminalDate = (value: number | string) =>
    (terminalDate.value = Number(value));
  const resetTerminalDate = () => (terminalDate.value = undefined);

  // contributions
  const selectedContribution = ref<ContributionItem | undefined>();
  const $contributions = reactive<Set<ContributionItem>>(new Set());
  const addContribution = (item: ContributionItem, withPlan = true): void => {
    selectedContribution.value &&
      $contributions.delete(selectedContribution.value);
    $contributions.add(item);
    withPlan && generateForecast().then();
  };
  const editContribution = (item: ContributionItem) => {
    selectedContribution.value = toValue(item);
  };
  const deleteContribution = (item: ContributionItem): void => {
    $contributions.delete(item);
    generateForecast().then();
  };

  // withdrawals
  const selectedWithdrawal = ref<WithdrawalItem>();
  const $withdrawals = reactive<Set<WithdrawalItem>>(new Set());
  const addWithdrawal = (item: WithdrawalItem, withPlan = true): void => {
    // if adding new - check for exising full-withdrawals
    if (item.type === WithdrawalType.FullWithdrawal) {
      if (!selectedWithdrawal.value) {
        if (terminalDate.value) {
          throw new Error('Only single full withdrawal allowed.');
        }
      }
      setTerminalDate(item.start);
    }
    // if editing - delete existing
    selectedWithdrawal.value && $withdrawals.delete(selectedWithdrawal.value);

    $withdrawals.add(item);
    withPlan && generateForecast().then();
  };
  const editWithdrawal = (item: WithdrawalItem) => {
    selectedWithdrawal.value = item;
  };
  const deleteWithdrawal = (item: WithdrawalItem): void => {
    $withdrawals.delete(item);
    item.type === WithdrawalType.FullWithdrawal && resetTerminalDate();
    generateForecast().then();
  };

  // calculate plan
  const datasets = ref<ForecasterResultsDTO['data']>({});
  const extras = ref<Nullable<ForecasterResultsDTO['extras']>>(null);

  const isForecastCompleted = !!getForecasterData(goalId, clientId)?.updated_at;

  const buildingPlan = ref(false);
  const { isPending, start, stop } = useTimeoutFn(
    () => (buildingPlan.value = true),
    250,
    { immediate: false },
  );

  const generateForecast = async (persist = false) => {
    if (
      !(
        birthdate.value &&
        retirementAge.value &&
        recommendedRisk.value &&
        selectedRisk.value &&
        [...$contributions].length
      )
    )
      return;

    if (!isPending.value) {
      start();
    }

    const cb = (item: CashflowItem) => {
      const { type, amount, start, end } = item;
      return {
        type,
        amount,
        start: age2date(Number(start)),
        end: end ? age2date(Number(end)) : null,
      };
    };

    const cashflows = [
      ...map([...$contributions], cb),
      ...map([...$withdrawals], cb),
    ] as CashflowItem[];

    // we only save forecaster data if it hasn't been completed yet,
    // or it's a plan submission
    if (!isForecastCompleted || persist) {
      saveForecasterData(goalId, clientId, {
        cashflows: cashflows,
        options: {
          selectedRisk: selectedRisk.value,
          inflationAdjusted: inflationAdjusted.value,
          inflationRate: inflationRate.value,
        },
        comments: comments.value,
      });
    }

    const { data: _data, extras: _extras } = await generateForecasterPlan(
      goalId,
      clientId,
      persist,
      {
        cashflows,
        retirement_age: retirementAge.value,
        inflation: (inflationAdjusted.value && inflationRate.value) || null,
        recommended_risk: recommendedRisk.value,
        selected_risk: selectedRisk.value,
        comments: comments.value,
      },
    );

    datasets.value = _data;
    extras.value = _extras;

    buildingPlan.value = false;
    stop();
  };

  const resetForecast = (): void => {
    // risk
    selectedRisk.value = undefined;
    // inflation
    inflationAdjusted.value = false;
    inflationRate.value = DEFAULT_INFLATION_RATE;
    // dates
    terminalDate.value = undefined;
    // cashflows
    $contributions.clear();
    $withdrawals.clear();
    // results
    datasets.value = {};
    // stores
    resetForecasterData(goalId, clientId);
  };

  const retirementCashValue = computed<Nullable<number>>(
    (): Nullable<number> => {
      let _key: string | undefined = undefined;
      if (
        birthdate.value &&
        retirementAge.value &&
        retirementAge.value > date2age(birthdate.value)
      ) {
        const millis = DateTime.fromISO(
          age2date(retirementAge.value),
        ).toMillis();

        _key = find(keys(datasets.value), (key: string): boolean => {
          return millis === Number(key);
        }) as string;
      }
      return datasets.value && _key
        ? (get(datasets.value, [_key, 'CashBalance'], null) as Nullable<number>)
        : null;
    },
  );

  const getRetirementValueForRisk = (risk: RiskLevelType): Nullable<number> => {
    let _key: string | undefined = undefined;
    if (
      birthdate.value &&
      retirementAge.value &&
      retirementAge.value > date2age(birthdate.value)
    ) {
      const millis = DateTime.fromISO(age2date(retirementAge.value)).toMillis();

      _key = find(keys(datasets.value), (key: string): boolean => {
        return millis === Number(key);
      }) as string;
    }
    return datasets.value && _key
      ? (get(datasets.value, [_key, `GrowthBalance${risk}`], undefined) as
          | number
          | null)
      : null;
  };

  const getTerminalValueForRisk = (risk: RiskLevelType): Nullable<number> => {
    let _key: string | undefined = undefined;
    if (birthdate.value && terminalDate.value) {
      const td = age2date(terminalDate.value);
      const millis = DateTime.fromISO(td).toMillis();

      _key = find(keys(datasets.value), (key: string): boolean => {
        return millis === Number(key);
      }) as string;
    }
    return datasets.value && _key
      ? (get(datasets.value, [_key, `GrowthBalance${risk}`], 0) as
          | number
          | null)
      : null;
  };

  const getLossCapacityForRisk = (risk: RiskLevelType): Nullable<string> => {
    const absolute_loss_capacity = extras.value?.abs_value[
      `GrowthBalance${risk}`
    ]
      ? formatMoneyValue(extras.value?.abs_value[`GrowthBalance${risk}`])
      : null;
    const relative_loss_capacity = extras.value?.pct_value[
      `GrowthBalance${risk}`
    ]
      ? formatPercentValue(extras.value?.pct_value[`GrowthBalance${risk}`])
      : null;
    return [absolute_loss_capacity, relative_loss_capacity]
      .filter((val) => val)
      .join(' / ');
  };

  // stats
  const stats = computed(
    () =>
      map(filter([recommendedRisk.value, selectedRisk.value]), (risk) => [
        risk,
        inflationAdjusted.value ? inflationRate.value : '0.0',
        getGrowthRateByRisk(risk as RiskLevelType),
        getMaxDrawdownByRisk(risk as RiskLevelType),
        retirementCashValue.value,
        getRetirementValueForRisk(risk as RiskLevelType),
        getTerminalValueForRisk(risk as RiskLevelType),
        getLossCapacityForRisk(risk as RiskLevelType),
      ]) as Array<ForecasterStatsValue[]>,
  );

  // provide
  provide(birthdateInjectionKey, birthdate);
  provide(recommendedRiskInjectionKey, recommendedRisk);
  provide(selectedRiskInjectionKey, selectedRisk);
  provide(buildingPlanInjectionKey, buildingPlan);

  return {
    birthdate,
    retirementAge,
    alreadyRetired,
    recommendedRisk,
    selectedRisk,
    inflationAdjusted,
    inflationRate,
    comments,
    terminalDate,
    contributions: {
      selectedItem: selectedContribution,
      resetSelectedItem: (isOpen: boolean) => {
        !isOpen && (selectedContribution.value = undefined);
      },
      items: $contributions,
      add: addContribution,
      edit: editContribution,
      delete: deleteContribution,
    },
    withdrawals: {
      selectedItem: selectedWithdrawal,
      resetSelectedItem: (isOpen: boolean) => {
        !isOpen && (selectedWithdrawal.value = undefined);
      },
      items: $withdrawals,
      add: addWithdrawal,
      edit: editWithdrawal,
      delete: deleteWithdrawal,
    },
    datasets,
    extras,
    generateForecast,
    resetForecast,
    stats,
    age2date,
    date2age,
  };
};
