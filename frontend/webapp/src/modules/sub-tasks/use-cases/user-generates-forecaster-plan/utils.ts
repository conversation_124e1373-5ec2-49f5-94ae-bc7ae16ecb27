import { DateTime } from 'luxon';
import { Maybe } from '@aventur-shared/types/Common';
import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
import {
  CashflowItem,
  ContributionType,
  WithdrawalType,
} from '@aventur-shared/modules/goals';

export const getAnnualizedCashflowItemAmount = (
  item: CashflowItem,
): CashflowItem['amount'] => {
  if (!item.amount) {
    return undefined;
  }

  let amount = Number(item.amount);
  switch (item.type) {
    case ContributionType.MonthlyDeposit:
    case WithdrawalType.MonthlyWithdrawal:
      amount *= 12;
      break;
    case ContributionType.QuarterlyDeposit:
    case WithdrawalType.QuarterlyWithdrawal:
      amount *= 4;
      break;
    case ContributionType.AnnualDeposit:
    case WithdrawalType.AnnualWithdrawal:
      amount *= 1;
      break;
    case ContributionType.LumpSumDeposit:
    case WithdrawalType.LumpSumWithdrawal:
      amount *= 1;
      break;
  }

  return amount;
};

export const formatCashflowAmount = (
  amount: CashflowItem['amount'],
  options: Intl.NumberFormatOptions = {},
) =>
  amount
    ? formatWithCurrency(new Money(Number(amount)), {
        maximumFractionDigits: 0,
        ...options,
      })
    : '-';

export const formatISODateString = (str: Maybe<string>) =>
  str
    ? DateTime.fromISO(str).setLocale('en-GB').toLocaleString(DateTime.DATE_MED)
    : '';
