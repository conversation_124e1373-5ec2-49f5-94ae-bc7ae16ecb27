<template>
  <div class="flex flex-row gap-x-6">
    <div class="bg-primary/5 w-96 shrink-0 rounded-md">
      <div class="h-full min-h-full">
        <div class="mt-10">
          <disclosure-section title="About you">
            <div class="mb-5">
              <div class="flex py-3">
                <span class="grow leading-7">
                  Date of Birth:&nbsp;
                  <span class="underline decoration-dashed">{{
                    formattedBirthdate
                  }}</span>
                </span>
                <span
                  class="inline-flex cursor-default items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10"
                >
                  {{ currentAgeStrict }} y.o.
                </span>
              </div>
            </div>
            <template v-if="alreadyRetired">
              <alert>
                <template v-if="(retiredYearsAgo as number) < 0">
                  Retired<span
                    class="text-primary mx-1 text-sm font-semibold"
                    >{{ Math.abs(retiredYearsAgo as number) }}</span
                  >year(s) ahead at the current official retirement age of
                  <span class="text-primary ml-0.5 text-sm font-semibold">{{
                    DEFAULT_RETIREMENT_AGE
                  }}</span>
                </template>
                <template v-else>
                  Retired<span
                    class="text-primary mx-1 text-sm font-semibold"
                    >{{ retiredYearsAgo }}</span
                  >year(s) ago at the age of
                  <span class="text-primary ml-0.5 text-sm font-semibold">{{
                    retirementAge
                  }}</span>
                </template>
              </alert>
            </template>
            <template v-else>
              <age-selector
                v-model="retirementAge"
                :options="ages"
                label="Retirement age"
                @update:model-value="() => buildPlan()"
              />
            </template>
          </disclosure-section>
          <disclosure-section title="Contributions">
            <template v-for="(c, t) in contributionList" :key="t">
              <div class="mb-5">
                <p class="text-sm text-gray-700">
                  {{ getCashflowItemTitle(t as ContributionType) }}
                </p>
                <contribution
                  v-for="(data, index) in c"
                  :key="index"
                  :data="data"
                  @planner:edit-contribution="contributions.edit"
                  @planner:delete-contribution="contributions.delete"
                />
              </div>
            </template>
            <button
              type="button"
              class="inline-flex items-center rounded-md border border-dashed border-gray-300 bg-white p-3 text-sm font-medium leading-4 text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
              @click="addContributionModalOpen = true"
            >
              <PlusIcon class="-ml-1 mr-1 size-5" aria-hidden="true" />
              Add contribution
            </button>
          </disclosure-section>
          <disclosure-section title="Withdrawals">
            <template v-for="(w, t) in withdrawalList" :key="t">
              <div class="mb-5">
                <p class="text-sm text-gray-700">
                  {{ getCashflowItemTitle(t as WithdrawalType) }}
                </p>
                <component
                  :is="
                    t === WithdrawalType.FullWithdrawal
                      ? FullWithdrawal
                      : Withdrawal
                  "
                  v-for="(data, index) in w"
                  :key="index"
                  :data="data"
                  @planner:edit-withdrawal="withdrawals.edit"
                  @planner:delete-withdrawal="withdrawals.delete"
                />
              </div>
            </template>
            <button
              type="button"
              class="inline-flex items-center rounded-md border border-dashed border-gray-300 bg-white p-3 text-sm font-medium leading-4 text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2"
              @click="addWithdrawalModalOpen = true"
            >
              <PlusIcon class="-ml-1 mr-1 size-5" aria-hidden="true" />
              Add withdrawal
            </button>
          </disclosure-section>
          <disclosure-section title="Investment Risk">
            <div class="mb-5">
              <div class="flex py-1">
                <span class="w-1/2 min-w-[100px] pt-0.5"> Risk Attitude</span>
                <span class="grow cursor-default"></span>
                <span
                  class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs text-blue-700 ring-1 ring-inset ring-blue-700/10"
                >
                  {{ riskProfile?.risk_attitude ?? '-' }}
                </span>
              </div>
              <div class="flex py-1">
                <span class="w-1/2 min-w-[100px] pt-0.5">Loss Tolerance</span>
                <span class="grow cursor-default"></span>
                <span
                  class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs text-blue-700 ring-1 ring-inset ring-blue-700/10"
                >
                  {{ riskProfile?.loss_tolerance ?? '-' }}
                </span>
              </div>
              <div class="mt-4 text-xs">
                Complete <strong>ATR Assessment</strong> to determine
                <em>risk attitude</em> and <em>loss tolerance</em> values.
              </div>
            </div>
            <div class="grid grid-cols-1 space-y-4 pt-2">
              <risk-selector
                v-model="recommendedRisk"
                title="Recommended Risk"
                :aria-disabled="!!recommendedRisk"
                :disabled="!!recommendedRisk"
                @update:model-value="() => buildPlan()"
              />
              <risk-selector
                v-model="selectedRisk"
                title="Selected Risk"
                @update:model-value="() => buildPlan()"
              />
            </div>
          </disclosure-section>
          <disclosure-section title="Extras">
            <SwitchGroup as="div" class="flex content-start items-center">
              <SwitchLabel as="span" class="mr-3 flex grow flex-nowrap text-sm">
                <span class="text-gray-500">
                  Adjust for
                  <span v-if="inflationAdjusted" class="font-bold"
                    >{{ inflationRate }}%</span
                  >
                  inflation
                </span>
                <InformationCircleIcon
                  v-tippy="
                    'Contributions and withdrawals adjusted to the selected annual inflation rate'
                  "
                  class="ml-1 size-5 text-gray-700 hover:cursor-help"
                />
              </SwitchLabel>
              <Switch
                v-model="inflationAdjusted"
                :class="[
                  inflationAdjusted ? 'bg-primary-900' : 'bg-gray-200',
                  'focus:ring-primary-900 relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2',
                ]"
                @update:model-value="() => buildPlan()"
              >
                <span
                  aria-hidden="true"
                  :class="[
                    inflationAdjusted ? 'translate-x-5' : 'translate-x-0',
                    'pointer-events-none inline-block size-5 rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                  ]"
                />
              </Switch>
            </SwitchGroup>
            <div v-if="inflationAdjusted" class="mt-6">
              <InflationSelector
                v-model="inflationRate"
                @update:model-value="() => buildPlan()"
              />
              <alert type="info" class="mt-8">
                <template v-if="inflationRate == 2">
                  There were 27 countries regarded by the Bank of England's
                  Centre for Central Banking Studies as fully fledged inflation
                  targeters at the beginning of 2012. While the targets may vary
                  from state to state, the UK government sets the inflation
                  target at 2% p.a.
                </template>
                <template v-if="inflationRate == 5">
                  During the observation period from 1960 to 2021, the average
                  inflation rate was 5.1% per year. Overall, the price increase
                  was 1,894.04%.
                </template>
                <template v-if="inflationRate == 10">
                  So far the highest annual inflation rate in nearly three
                  decades was recorded in October 2022 at 11.1% p.a. For more
                  details
                  <a
                    href="https://www.ons.gov.uk/economy/inflationandpriceindices"
                    class="whitespace-nowrap text-blue-700 underline hover:cursor-pointer hover:text-blue-600 hover:no-underline"
                    >click here&raquo;</a
                  >
                </template>
              </alert>
            </div>
            <alert v-else type="info" class="mt-4">
              Since 1989 there were a few periods with the low inflation. The
              CPI went down below zero in autumn 2015 and then again in later
              summer of 2020 with the lowest reading of 0.2% p.a.
            </alert>
          </disclosure-section>
        </div>
      </div>
    </div>
    <div class="max-w-2xl grow overflow-y-hidden bg-white">
      <div class="h-full min-h-full">
        <div class="flex w-full flex-col place-content-start gap-y-10">
          <div v-if="Object.keys(datasets).length" ref="chartRef">
            <forecaster-chart :data="datasets" />
          </div>
          <div ref="statsRef">
            <forecaster-stats :stats="stats" />
          </div>
          <div ref="commentRef">
            <forecaster-comment v-model="comments" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <add-contribution-modal
    :key="selectedContributionKey"
    v-model:is-open="addContributionModalOpen"
    v-model:item="contributions.selectedItem"
    @update:is-open="contributions.resetSelectedItem"
    @planner:add-contribution="contributions.add"
  />
  <add-withdrawal-modal
    :key="selectedWithdrawalKey"
    v-model:is-open="addWithdrawalModalOpen"
    v-model:item="withdrawals.selectedItem"
    @update:is-open="withdrawals.resetSelectedItem"
    @planner:add-withdrawal="handleAddWithdrawal"
  />
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, onBeforeUnmount, ref, watch } from 'vue';
  import { useEventBus } from '@vueuse/core';
  import { DateTime, Settings as DateTimeSettings } from 'luxon';
  import { groupBy, map, sortBy, toString } from 'lodash';
  import { InformationCircleIcon, PlusIcon } from '@heroicons/vue/20/solid';
  import { Switch, SwitchGroup, SwitchLabel } from '@headlessui/vue';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { Nullable } from '@aventur-shared/types/Common';
  import {
    AGES,
    DEFAULT_RETIREMENT_AGE,
    MINIMUM_RETIREMENT_AGE,
  } from '@aventur-shared/constants/age';
  import { DEFAULT_INFLATION_RATE } from '@aventur-shared/constants';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { DocumentType } from '@modules/clients-cases/models/document_generation';
  import { documentGenerationRequest } from '@modules/clients-cases/api';
  import { fetchForecasterPlan } from '@aventur-shared/modules/goals/api/generate-forecaster-plan';
  import {
    CashflowItem,
    CashflowItemsList,
    ContributionItem,
    ContributionType,
    ForecasterEvent,
    RiskProfileData,
    WithdrawalItem,
    WithdrawalType,
    forecasterEventKey,
    getCashflowItemTitle,
  } from '@aventur-shared/modules/goals';
  import { TaskGoalRequired } from '@aventur-shared/modules/tasks';
  import { CaseId } from '@aventur-shared/modules/cases';
  import {
    AddContributionModal,
    AddWithdrawalModal,
    AgeSelector,
    Contribution,
    ForecasterChart,
    ForecasterComment,
    ForecasterStats,
    FullWithdrawal,
    InflationSelector,
    RiskSelector,
    Withdrawal,
  } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan';
  import { fetchRiskProfile } from '@aventur-shared/modules/goals/api/calculate-risk-profile';
  import { Alert } from '@modules/ui';
  import DisclosureSection from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/components/disclosure-section.vue';
  import { useForecasterStore } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan';
  import { useRiskProfileStore } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import { formatISODateString } from '../utils';
  import { useForecaster } from '../use-forecaster';

  DateTimeSettings.defaultZone = 'utc';

  const toast = useToast();

  const props = defineProps<{
    caseId: CaseId | null;
    clientId: ClientId;
    goal: TaskGoalRequired;
  }>();

  const {
    getForecasterData,
    saveForecasterData,
    getContributions,
    getWithdrawals,
    getOptions,
    getClientDetails,
  } = useForecasterStore();
  const { getRiskProfile, saveRiskProfile } = useRiskProfileStore();
  const riskProfile = ref<Nullable<RiskProfileData>>(
    getRiskProfile(props.goal.id, props.clientId),
  );

  const {
    birthdate,
    retirementAge,
    alreadyRetired,
    recommendedRisk,
    selectedRisk,
    inflationAdjusted,
    inflationRate,
    comments,
    contributions,
    withdrawals,
    datasets,
    generateForecast,
    resetForecast,
    date2age,
    stats,
  } = useForecaster(props.goal.id, props.clientId);

  const retiredYearsAgo = computed(() =>
    alreadyRetired.value
      ? currentAgeStrict.value - (retirementAge.value ?? DEFAULT_RETIREMENT_AGE)
      : null,
  );

  const formattedBirthdate = computed<string>(() =>
    formatISODateString(birthdate.value),
  );

  const currentAgeStrict = computed<number>(() => {
    return birthdate.value ? date2age(new Date().toISOString(), true) : 0;
  });

  const ages = computed<number[]>(() => {
    if (!birthdate.value) {
      return AGES;
    }
    return AGES.filter((val: number) => val >= MINIMUM_RETIREMENT_AGE);
  });

  // Contributions
  const contributionList = computed<CashflowItemsList<ContributionItem>>(() =>
    groupBy(sortBy(Array.from(contributions.items), 'type'), 'type'),
  );
  const selectedContributionKey = computed<symbol>(() =>
    Symbol(toString(contributions.selectedItem.value)),
  );
  const addContributionModalOpen = ref(false);
  watch(contributions.selectedItem, () => {
    contributions.selectedItem.value && (addContributionModalOpen.value = true);
  });
  const handleAddWithdrawal = (item: WithdrawalItem) => {
    try {
      withdrawals.add(item);
    } catch (e) {
      toast.error(e as Error);
    }
  };

  // Withdrawals
  const selectedWithdrawalKey = computed<symbol>(() =>
    Symbol(toString(withdrawals.selectedItem.value)),
  );
  const withdrawalList = computed<CashflowItemsList<WithdrawalItem>>(() =>
    groupBy(sortBy(Array.from(withdrawals.items), 'type'), 'type'),
  );
  const addWithdrawalModalOpen = ref(false);
  watch(withdrawals.selectedItem, () => {
    withdrawals.selectedItem.value && (addWithdrawalModalOpen.value = true);
  });

  const buildPlan = async (persist = false) => {
    if (
      !(
        birthdate.value &&
        retirementAge.value &&
        recommendedRisk.value &&
        selectedRisk.value &&
        [...contributions.items].length
      )
    ) {
      return;
      // throw new Error(
      //   'Date of birth, retirement age, recommended and selected risk levels at least one contribution are required.',
      // );
    }

    if (persist && !comments.value?.trim()) {
      throw new Error('Please, provide a comment before submitting.');
    }

    try {
      await generateForecast(persist);
      if (persist) {
        toast.success('The forecaster plan successfully saved.');
        const data = await fetchForecasterPlan(props.goal.id, props.clientId);
        if (data) {
          saveForecasterData(props.goal.id, props.clientId, data);
        }
      }
    } catch (e) {
      toast.error(e as Error);
    }
  };

  const exportPlan = async () => {
    try {
      await documentGenerationRequest(
        props.caseId as CaseId,
        props.goal.taskSlug,
        props.goal.taskId,
        DocumentType.ForecasterCashPlan,
        {
          case_goal_id: props.goal.id,
          client_id: props.clientId,
        },
      );
      toast.success('The requested document will be delivered to your email.');
    } catch (e: any) {
      toast.error(e);
    }
  };

  // hooks
  onBeforeMount(async () => {
    if (getRiskProfile(props.goal.id, props.clientId) == null) {
      await fetchRiskProfile(props.goal.id, props.clientId).then((data) => {
        data && saveRiskProfile(props.goal.id, props.clientId, data);
      });
    }
    riskProfile.value = getRiskProfile(props.goal.id, props.clientId);
    const options = getOptions(props.goal.id, props.clientId);
    const { personalDetails, retirementDetails } = await getClientDetails(
      props.clientId,
    );

    // restore saved parameters
    if (personalDetails.dateOfBirth) {
      birthdate.value = DateTime.fromJSDate(
        personalDetails.dateOfBirth.valueOf(),
      ).toISODate();
    }

    retirementAge.value = DEFAULT_RETIREMENT_AGE;
    if (options.retirementAge) {
      retirementAge.value = retirementDetails?.alreadyRetired
        ? (retirementDetails.retirementAge ?? options.retirementAge)
        : options.retirementAge;
      alreadyRetired.value = !!retirementDetails?.alreadyRetired;
    }

    recommendedRisk.value = riskProfile.value?.recommended_risk;
    if (recommendedRisk.value && options.selectedRisk) {
      selectedRisk.value = options.selectedRisk;
    }

    inflationAdjusted.value = options.inflationAdjusted ?? false;
    inflationRate.value = options.inflationRate || DEFAULT_INFLATION_RATE;

    comments.value = getForecasterData(props.goal.id, props.clientId)?.comments;

    const cb = (item: CashflowItem) => {
      const { type, amount, start, end } = item;
      return {
        type,
        amount,
        start: date2age(String(start)),
        end: end ? date2age(String(end)) : null,
      };
    };

    map(getContributions(props.goal.id, props.clientId), cb).forEach(
      (item: CashflowItem) =>
        contributions.add(item as ContributionItem, false),
    );
    map(getWithdrawals(props.goal.id, props.clientId), cb).forEach(
      (item: CashflowItem) => withdrawals.add(item as WithdrawalItem, false),
    );

    // EventBus
    const eventBus = useEventBus(forecasterEventKey);
    eventBus.on(async (e) => {
      try {
        switch (e.name) {
          case ForecasterEvent.SUBMIT:
            await buildPlan(true);
            return;
          case ForecasterEvent.EXPORT:
            await exportPlan();
            return;
        }
        if (e.name === ForecasterEvent.RESET) {
          resetForecast();
        }
      } catch (e: any) {
        toast.error(e);
      }
    });

    // re-run plan if applicable
    await buildPlan();
  });

  onBeforeUnmount(() => {
    const eventBus = useEventBus(forecasterEventKey);
    eventBus.reset();
  });
</script>
