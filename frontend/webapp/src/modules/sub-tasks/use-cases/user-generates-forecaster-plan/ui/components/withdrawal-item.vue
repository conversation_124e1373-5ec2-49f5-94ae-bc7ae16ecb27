<template>
  <div class="mb-2">
    <div>
      <label for="price" class="block text-sm text-gray-700"></label>
      <div
        class="relative mt-1 rounded-md shadow-sm"
        @click.stop="emit('planner:editWithdrawal', data)"
      >
        <div
          class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
        >
          <span class="text-gray-500 sm:text-sm">&pound;</span>
        </div>
        <input
          id="price"
          type="text"
          name="price"
          class="block w-full rounded-md border-gray-300 bg-gray-50 py-3 pl-7 pr-12 hover:cursor-pointer hover:border-gray-400 hover:bg-gray-100 sm:text-sm"
          :value="`-${amountFormatted}`"
          readonly
        />
        <div class="absolute inset-y-0 right-0 flex items-center">
          <span
            class="relative -ml-px inline-flex items-center space-x-2 rounded-r-md p-2 text-sm text-gray-500"
          >
            <span>{{ period }}</span>
            <XMarkIcon
              class="size-5 text-gray-400 hover:cursor-pointer hover:text-gray-700"
              aria-hidden="true"
              @click.stop="emit('planner:deleteWithdrawal', data)"
            />
          </span>
        </div>
      </div>
    </div>
    <div class="m-1 flex text-xs text-gray-500">
      <span class="grow"
        >Age {{ data.start
        }}<template v-if="data.end">-{{ data.end }}</template></span
      >
      <span class="text-red-600">
        <template
          v-if="
            data.type !== WithdrawalType.LumpSumWithdrawal &&
            data.type !== WithdrawalType.FullWithdrawal
          "
        >
          -{{ annualAmountFormatted }} / year
        </template>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import {
    WithdrawalItem,
    WithdrawalType,
  } from '@aventur-shared/modules/goals';
  import { Money, formatMoney } from '@aventur-shared/utils/money';
  import { getAnnualizedCashflowItemAmount } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/utils';

  const props = defineProps<{
    data: WithdrawalItem;
  }>();

  const period = computed<string>((): string => {
    switch (props.data.type) {
      case WithdrawalType.MonthlyWithdrawal:
        return 'per month';
      case WithdrawalType.QuarterlyWithdrawal:
        return 'per quarter';
      case WithdrawalType.AnnualWithdrawal:
        return 'per annum';
      case WithdrawalType.LumpSumWithdrawal:
      default:
        return '';
    }
  });

  const amountFormatted = computed<string>((): string =>
    formatMoney(new Money(Number(props.data.amount))),
  );

  const annualAmountFormatted = computed<string>((): string =>
    formatMoney(
      new Money(getAnnualizedCashflowItemAmount(props.data) as number),
    ),
  );

  const emit = defineEmits<{
    (e: 'planner:editWithdrawal', value: WithdrawalItem): void;
    (e: 'planner:deleteWithdrawal', value: WithdrawalItem): void;
  }>();
</script>
