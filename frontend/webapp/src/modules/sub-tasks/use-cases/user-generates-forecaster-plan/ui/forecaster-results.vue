<template>
  <div class="flex flex-row gap-x-6">
    <div class="bg-primary/5 w-96 shrink-0 rounded-md">
      <div class="h-full min-h-full">
        <div class="mt-10">
          <disclosure-section title="About you">
            <div>
              <div class="flex py-3">
                <span class="grow leading-7">
                  Date of Birth:&nbsp;
                  <span class="underline decoration-dashed">{{
                    formattedBirthdate
                  }}</span>
                </span>
                <span
                  class="inline-flex cursor-default items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10"
                >
                  {{ currentAgeStrict }} y.o.
                </span>
              </div>
            </div>
            <template v-if="alreadyRetired">
              <alert class="mt-5">
                Retired<span class="text-primary ml-1 text-sm font-semibold">{{
                  retiredYearsAgo
                }}</span>
                year(s) ago at the age of
                <span class="text-primary ml-1 text-sm font-semibold">{{
                  retirementAge
                }}</span>
              </alert>
            </template>
            <template v-else>
              <span v-if="retirementAge" class="grow leading-7">
                Retirement Age:
                <span class="underline decoration-dashed">{{
                  retirementAge
                }}</span>
              </span>
              <alert v-else class="mt-5" type="warning">
                <strong>Missing data: Retirement Age</strong>
                <p>
                  You may need to re-evaluate the open case ATR forecast or
                  contact technical support for assistance.
                </p>
              </alert>
            </template>
          </disclosure-section>
          <disclosure-section title="Contributions">
            <div
              v-for="(item, index) in contributionList"
              :key="index"
              class="my-4"
            >
              <div>{{ getCashflowItemTitle(item.type) }}</div>
              <div class="mt-1 h-6 text-left font-medium">
                {{ formatCashflowAmount(item.amount) }}
              </div>
              <div class="flex text-xs text-gray-500">
                <span class="grow"
                  >Age {{ item.start
                  }}<template v-if="item.end">-{{ item.end }}</template></span
                >
                <span class="text-emerald-600">
                  <template
                    v-if="item.type !== ContributionType.LumpSumDeposit"
                  >
                    {{
                      formatCashflowAmount(
                        getAnnualizedCashflowItemAmount(item),
                      )
                    }}
                    / year
                  </template>
                </span>
              </div>
            </div>
          </disclosure-section>
          <disclosure-section title="Withdrawals">
            <div
              v-for="(item, index) in withdrawalList"
              :key="index"
              class="my-4"
            >
              <div>{{ getCashflowItemTitle(item.type) }}</div>
              <div class="mt-1 h-6 text-left font-medium">
                <template v-if="item.type !== WithdrawalType.FullWithdrawal">
                  {{ formatCashflowAmount(item.amount) }}
                </template>
                <template v-else>-</template>
              </div>
              <div class="flex text-xs text-gray-500">
                <span class="grow"
                  >Age {{ item.start
                  }}<template v-if="item.end">-{{ item.end }}</template></span
                >
                <span class="text-red-600">
                  <template
                    v-if="
                      item.type !== WithdrawalType.LumpSumWithdrawal &&
                      item.type !== WithdrawalType.FullWithdrawal
                    "
                  >
                    -{{
                      formatCashflowAmount(
                        getAnnualizedCashflowItemAmount(item),
                      )
                    }}
                    / year
                  </template>
                </span>
              </div>
            </div>
          </disclosure-section>
          <disclosure-section title="Investment Risk">
            <div class="mb-5">
              <div class="flex py-1">
                <span class="w-1/2 min-w-[100px] pt-0.5"> Risk Attitude</span>
                <span class="grow cursor-default"></span>
                <span
                  class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs text-blue-700 ring-1 ring-inset ring-blue-700/10"
                >
                  {{ riskProfile?.risk_attitude || '-' }}
                </span>
              </div>
              <div class="flex py-1">
                <span class="w-1/2 min-w-[100px] pt-0.5">Loss Tolerance</span>
                <span class="grow cursor-default"></span>
                <span
                  class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs text-blue-700 ring-1 ring-inset ring-blue-700/10"
                >
                  {{ riskProfile?.loss_tolerance || '-' }}
                </span>
              </div>
            </div>
          </disclosure-section>
        </div>
      </div>
    </div>
    <div class="max-w-2xl grow overflow-y-hidden bg-white">
      <div v-if="Object.keys(datasets).length">
        <ForecasterChart :data="datasets" />
      </div>
      <div class="mt-5">
        <ForecasterStats :stats="stats" />
      </div>
      <div class="mt-5">
        <p class="py-2 text-left text-sm font-semibold text-gray-900">
          Comments
        </p>
        <pre class="whitespace-pre-wrap">{{ comments }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount, ref } from 'vue';
  import { DateTime, Settings as DateTimeSettings } from 'luxon';
  import { map, sortBy } from 'lodash';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { Nullable } from '@aventur-shared/types/Common';
  import {
    CashflowItem,
    ContributionItem,
    ContributionType,
    ForecasterData,
    GoalId,
    RiskProfileData,
    WithdrawalItem,
    WithdrawalType,
    getCashflowItemTitle,
  } from '@aventur-shared/modules/goals';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { DEFAULT_INFLATION_RATE } from '@aventur-shared/constants';
  import { Alert } from '@modules/ui';
  import {
    ForecasterChart,
    ForecasterStats,
  } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan';
  import { useForecasterStore } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan';
  import { useRiskProfileStore } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import DisclosureSection from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/components/disclosure-section.vue';
  import {
    formatCashflowAmount,
    formatISODateString,
    getAnnualizedCashflowItemAmount,
  } from '../utils';
  import { useForecaster } from '../use-forecaster';

  DateTimeSettings.defaultZone = 'utc';

  const props = defineProps<{
    goalId: GoalId;
    forecasterData: ForecasterData | null;
    clientId: ClientId;
  }>();

  const toast = useToast();
  const {
    saveForecasterData,
    getForecasterData,
    getContributions,
    getWithdrawals,
    getOptions,
    getClientDetails,
  } = useForecasterStore();
  const { getRiskProfile } = useRiskProfileStore();
  const riskProfile = ref<Nullable<RiskProfileData>>(
    getRiskProfile(props.goalId, props.clientId),
  );

  const {
    birthdate,
    retirementAge,
    alreadyRetired,
    recommendedRisk,
    selectedRisk,
    inflationAdjusted,
    inflationRate,
    comments,
    contributions,
    withdrawals,
    datasets,
    generateForecast,
    date2age,
    stats,
  } = useForecaster(props.goalId, props.clientId);

  const retiredYearsAgo = computed(() =>
    alreadyRetired.value
      ? currentAgeStrict.value - (retirementAge.value ?? 0)
      : null,
  );

  const formattedBirthdate = computed<string>(() =>
    formatISODateString(birthdate.value),
  );

  const currentAgeStrict = computed<number>(() => {
    return birthdate.value ? date2age(new Date().toISOString(), true) : 0;
  });

  // Contributions
  const contributionList = computed<ContributionItem[]>(() =>
    sortBy(Array.from(contributions.items), 'type'),
  );

  // Withdrawals
  const withdrawalList = computed<WithdrawalItem[]>(() =>
    sortBy(Array.from(withdrawals.items), 'type'),
  );

  const buildPlan = async (persist = false) => {
    try {
      await generateForecast(persist);
    } catch (e) {
      toast.error(e as Error);
    }
  };

  // hooks
  onBeforeMount(async () => {
    riskProfile.value = getRiskProfile(props.goalId, props.clientId);
    const options = getOptions(props.goalId, props.clientId);
    const { personalDetails, retirementDetails } = await getClientDetails(
      props.clientId,
    );

    if (props.forecasterData) {
      saveForecasterData(props.goalId, props.clientId, props.forecasterData);
    }

    // restore saved parameters
    if (personalDetails.dateOfBirth) {
      birthdate.value = DateTime.fromJSDate(
        personalDetails.dateOfBirth.valueOf(),
      ).toISODate();
    }

    if (options.retirementAge) {
      retirementAge.value = options.retirementAge;
      alreadyRetired.value = !!retirementDetails?.alreadyRetired;
    }

    recommendedRisk.value = riskProfile.value?.recommended_risk;
    if (recommendedRisk.value && options.selectedRisk) {
      selectedRisk.value = options.selectedRisk;
    }

    inflationAdjusted.value = options.inflationAdjusted ?? false;
    inflationRate.value = options.inflationRate || DEFAULT_INFLATION_RATE;

    comments.value = getForecasterData(props.goalId, props.clientId)?.comments;

    const cb = (item: CashflowItem) => {
      const { type, amount, start, end } = item;
      return {
        type,
        amount,
        start: date2age(String(start)),
        end: end ? date2age(String(end)) : null,
      };
    };

    map(getContributions(props.goalId, props.clientId), cb).forEach(
      (item: CashflowItem) =>
        contributions.add(item as ContributionItem, false),
    );
    map(getWithdrawals(props.goalId, props.clientId), cb).forEach(
      (item: CashflowItem) => withdrawals.add(item as WithdrawalItem, false),
    );

    await buildPlan();
  });
</script>
