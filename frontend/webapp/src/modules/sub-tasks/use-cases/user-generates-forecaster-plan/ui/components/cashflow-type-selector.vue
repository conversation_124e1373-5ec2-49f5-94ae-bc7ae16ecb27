<template>
  <RadioGroup v-model="modelValue">
    <RadioGroupLabel></RadioGroupLabel>
    <div class="space-y-4">
      <RadioGroupOption
        v-if="selectedValue"
        as="template"
        :value="selectedValue"
      >
        <div
          class="bg-primary-100 relative block rounded-lg border px-6 py-4 shadow-sm"
        >
          <span class="flex items-center">
            <span class="flex flex-col text-sm">
              <RadioGroupLabel as="span" class="font-medium text-gray-900">{{
                getCashflowItemTitle(selectedValue)
              }}</RadioGroupLabel>
              <RadioGroupDescription as="span" class="text-gray-500"
                >{{ getCashflowItemCaption(selectedValue) }}
              </RadioGroupDescription>
            </span>
          </span>
        </div>
      </RadioGroupOption>
      <RadioGroupOption
        v-for="t in props.options"
        v-else
        :key="t"
        v-slot="{ checked }"
        as="template"
        :value="t"
      >
        <div
          :class="[
            checked
              ? 'border-primary-900 bg-primary-100'
              : 'bg-white hover:ring-1',
            'ring-primary-900 relative block cursor-pointer rounded-lg border px-6 py-4 shadow-sm focus:outline-none sm:flex sm:justify-between',
          ]"
        >
          <span class="flex items-center">
            <span class="flex flex-col text-sm">
              <RadioGroupLabel as="span" class="font-medium text-gray-900">{{
                getCashflowItemTitle(t)
              }}</RadioGroupLabel>
              <RadioGroupDescription as="span" class="text-gray-500"
                >{{ getCashflowItemCaption(t) }}
              </RadioGroupDescription>
            </span>
          </span>
        </div>
      </RadioGroupOption>
    </div>
  </RadioGroup>
</template>

<script setup lang="ts">
  import { ref, useAttrs } from 'vue';
  import {
    RadioGroup,
    RadioGroupDescription,
    RadioGroupLabel,
    RadioGroupOption,
  } from '@headlessui/vue';
  import {
    ContributionType,
    WithdrawalType,
    getCashflowItemCaption,
    getCashflowItemTitle,
  } from '@aventur-shared/modules/goals';

  type OptionType = ContributionType | WithdrawalType;

  const attrs = useAttrs();

  const props = defineProps<{
    options: OptionType[];
  }>();

  const modelValue = ref<OptionType>();
  const selectedValue = ref<OptionType>(attrs.value as OptionType);
</script>
