<template>
  <Combobox :model-value="selectedItem" as="div" :disabled="props.disabled">
    <ComboboxLabel class="block text-sm text-gray-700">{{
      props.label
    }}</ComboboxLabel>
    <div class="relative mt-1">
      <div
        class="relative w-full cursor-default overflow-hidden rounded-lg border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm"
      >
        <ComboboxInput
          class="w-full border-none border-gray-300 bg-gray-50 py-3 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0"
          :display-value="(item: unknown) => String(item)"
          :disabled="props.disabled"
          aria-autocomplete="none"
          autocomplete="off"
          @change="onChange"
        />
        <div
          class="absolute inset-y-0 right-0 flex items-center pr-9 text-gray-500"
        >
          years
        </div>
        <ComboboxButton
          class="absolute inset-y-0 right-0 flex items-center pr-2"
          @click.stop="toggleOptions"
        >
          <ChevronUpDownIcon class="size-5 text-gray-400" aria-hidden="true" />
        </ComboboxButton>
      </div>
      <TransitionRoot
        leave="transition ease-in duration-100"
        leave-from="opacity-100"
        leave-to="opacity-0"
        :show="optionsDisplayed"
        @after-leave="query = ''"
      >
        <ComboboxOptions
          class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border-gray-300 bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
        >
          <div
            v-if="filteredOptions.length === 0"
            class="relative cursor-default select-none px-4 py-2 text-gray-300"
          >
            No results
          </div>
          <ComboboxOption
            v-for="a in filteredOptions"
            :key="a"
            v-slot="{ active, selected }"
            :value="a"
            as="template"
            @click.stop="setValue(a)"
          >
            <li
              :class="[
                active
                  ? 'bg-primary-100 text-primary-900 cursor-pointer'
                  : 'cursor-default text-gray-900',
                'relative select-none py-2 pl-3 pr-9',
              ]"
            >
              <span
                :class="[
                  selected ? 'font-semibold' : 'font-normal',
                  'block truncate',
                ]"
                >{{ a }}</span
              >
              <span
                v-if="selected"
                :class="[
                  active ? 'text-white' : 'text-primary-900',
                  'absolute inset-y-0 right-0 flex items-center pr-4',
                ]"
              >
                <CheckIcon class="size-5" aria-hidden="true" />
              </span>
            </li>
          </ComboboxOption>
        </ComboboxOptions>
      </TransitionRoot>
    </div>
  </Combobox>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import {
    Combobox,
    ComboboxButton,
    ComboboxInput,
    ComboboxLabel,
    ComboboxOption,
    ComboboxOptions,
    TransitionRoot,
  } from '@headlessui/vue';
  import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid';
  import { AGES } from '@aventur-shared/constants/age';
  import { Nullable } from '@aventur-shared/types/Common';

  const props = withDefaults(
    defineProps<{
      modelValue?: Nullable<number>;
      options?: number[];
      label?: string;
      disabled?: boolean;
    }>(),
    {
      modelValue: null,
      options: () => AGES,
      label: '',
      disabled: false,
    },
  );

  const query = ref('');
  const optionsDisplayed = ref(false);

  const selectedItem = computed<Nullable<number>>(() => props.modelValue);
  const filteredOptions = computed<number[]>(() =>
    query.value === ''
      ? props.options
      : props.options.filter((item) =>
          String(item).startsWith(`${query.value}`),
        ),
  );

  const emit = defineEmits<{
    (e: 'update:modelValue', value: number): void;
  }>();

  const toggleOptions = (): void => {
    optionsDisplayed.value = !optionsDisplayed.value;
    query.value = '';
  };
  const setValue = (value: number) => {
    optionsDisplayed.value = false;
    emit('update:modelValue', Number(value));
  };
  const onChange = ($event) => {
    optionsDisplayed.value = true;
    query.value = String($event.target.value);
    if (filteredOptions.value.length === 1) {
      setValue($event.target.value);
    }
  };
</script>
