<template>
  <Disclosure v-slot="{ open }" :default-open="props.defaultOpen">
    <DisclosureButton
      :class="open ? '' : 'border-b border-gray-200'"
      class="flex w-full justify-between px-10 py-4 text-left text-sm focus:outline-none"
    >
      <span class="text-lg">{{ title }}</span>
      <ChevronUpDownIcon
        :class="open ? 'rotate-180 transform' : ''"
        class="size-5"
      />
    </DisclosureButton>
    <DisclosurePanel class="flex flex-col px-10 pb-10 text-sm">
      <slot />
    </DisclosurePanel>
  </Disclosure>
</template>

<script setup lang="ts">
  import {
    Disclosure,
    DisclosureButton,
    DisclosurePanel,
  } from '@headlessui/vue';
  import { ChevronUpDownIcon } from '@heroicons/vue/20/solid';

  const props = withDefaults(
    defineProps<{ title: string; defaultOpen?: boolean }>(),
    {
      defaultOpen: true,
    },
  );
</script>
