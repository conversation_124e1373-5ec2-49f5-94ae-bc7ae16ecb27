<template>
  <div class="flex min-w-full flex-col">
    <p class="block text-sm text-gray-700">
      {{ label }}
      <template v-if="hasValue">
        <span class="text-primary-900 text-xl font-bold">{{
          attrs.value[0] || attrs.value
        }}</span>
        <template v-if="!!attrs.value[1]">
          <span> to </span>
          <span class="text-primary-900 text-xl font-bold">{{
            attrs.value[1]
          }}</span>
        </template>
      </template>
    </p>
    <vue-slider
      v-model="age"
      :min="props.minValue"
      :min-range="1"
      :marks="marks"
      :enable-cross="false"
      :contained="true"
      :dot-size="24"
      :height="6"
      :duration="0.2"
      :process-style="processStyles"
      tooltip-placement="bottom"
      @update:model-value="emit('update:modelValue', $event)"
    />
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, useAttrs } from 'vue';
  import VueSlider, { Styles } from 'vue-slider-component';
  import { AGES } from '@aventur-shared/constants/age';
  import { AgeRangeType } from '@aventur-shared/composables/useAge';
  import { filter, map, toNumber } from 'lodash';

  const attrs: Record<string, any> = useAttrs();
  const props = withDefaults(
    defineProps<{
      dataValue?: string;
      label: string;
      minValue?: number;
      maxValue?: number;
    }>(),
    {
      dataValue: '',
      minValue: AGES.at(0),
      maxValue: undefined,
    },
  );

  const hasValue = computed<boolean>(() => {
    return (
      (Array.isArray(attrs.value) && attrs.value.length > 0) || !!attrs.value
    );
  });
  const hasDataValue = computed<boolean>(
    () => !!filter(props.dataValue.split(',')).length,
  );

  const age = ref<AgeRangeType>();
  age.value = !hasDataValue.value
    ? (filter([props.minValue, props.maxValue]) as [number, number | never])
    : (map(filter(props.dataValue.split(',')), toNumber) as [
        number,
        number | never,
      ]);

  const marks = computed<number[]>(() => {
    const _marks: number[] = [];
    let mark = props.minValue;
    while (mark < (props.maxValue || (AGES.slice(-1).at(0) as number))) {
      mark % 10 === 0 && _marks.push(mark);
      mark++;
    }
    return _marks;
  });

  const processStyles = computed<Styles>(() => ({
    backgroundColor:
      Array.isArray(attrs.value) && attrs.value.length == 2
        ? '#215249'
        : '#ccc',
  }));

  const emit = defineEmits<{
    (e: 'update:modelValue', value: AgeRangeType): void;
  }>();

  onMounted(() => {
    emit('update:modelValue', age.value as AgeRangeType);
  });
</script>
