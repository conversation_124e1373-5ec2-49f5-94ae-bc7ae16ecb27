<template>
  <div data-planner="comment">
    <label
      for="comment"
      class="block text-sm font-medium leading-6 text-gray-900"
      >Comments and risk discussion notes</label
    >
    <div class="mt-2">
      <textarea
        rows="4"
        name="comment"
        :value="modelValue"
        class="block min-h-16 w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
        @input="onInput"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  defineProps<{
    modelValue?: string | null;
  }>();
  const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
  }>();
  const onInput = ($event) => emit('update:modelValue', $event?.target?.value);
</script>
