<template>
  <div class="mb-2">
    <div>
      <label for="price" class="block text-sm text-gray-700"></label>
      <div
        class="relative mt-1 rounded-md shadow-sm"
        @click.stop="emit('planner:editContribution', data)"
      >
        <div
          class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
        >
          <span class="text-gray-500 sm:text-sm">&pound;</span>
        </div>
        <input
          id="price"
          type="text"
          name="price"
          class="block w-full rounded-md border-gray-300 bg-gray-50 py-3 pl-7 pr-12 hover:cursor-pointer hover:border-gray-400 hover:bg-gray-100 sm:text-sm"
          :value="amountFormatted"
          readonly
        />
        <div class="absolute inset-y-0 right-0 flex items-center">
          <span
            class="relative -ml-px inline-flex items-center space-x-2 rounded-r-md p-2 text-sm text-gray-500"
          >
            <span>{{ period }}</span>
            <XMarkIcon
              class="size-5 text-gray-400 hover:cursor-pointer hover:text-gray-700"
              aria-hidden="true"
              @click.stop="emit('planner:deleteContribution', data)"
            />
          </span>
        </div>
      </div>
    </div>
    <div class="m-1 flex text-xs text-gray-500">
      <span class="grow"
        >Age {{ data.start
        }}<template v-if="data.end">-{{ data.end }}</template></span
      >
      <span class="text-emerald-600">
        <template v-if="data.type !== ContributionType.LumpSumDeposit">
          {{ annualAmountFormatted }} / year
        </template>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import { Money, formatMoney } from '@aventur-shared/utils/money';
  import {
    ContributionItem,
    ContributionType,
  } from '@aventur-shared/modules/goals';
  import { getAnnualizedCashflowItemAmount } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/utils';

  const props = defineProps<{
    data: ContributionItem;
  }>();

  const period = computed<string>((): string => {
    switch (props.data.type) {
      case ContributionType.MonthlyDeposit:
        return 'per month';
      case ContributionType.QuarterlyDeposit:
        return 'per quarter';
      case ContributionType.AnnualDeposit:
        return 'per annum';
      case ContributionType.LumpSumDeposit:
      default:
        return '';
    }
  });

  const amountFormatted = computed<string>((): string =>
    formatMoney(new Money(Number(props.data.amount))),
  );

  const annualAmountFormatted = computed<string>((): string =>
    formatMoney(
      new Money(getAnnualizedCashflowItemAmount(props.data) as number),
    ),
  );

  const emit = defineEmits<{
    (e: 'planner:editContribution', value: ContributionItem): void;
    (e: 'planner:deleteContribution', value: ContributionItem): void;
  }>();
</script>
