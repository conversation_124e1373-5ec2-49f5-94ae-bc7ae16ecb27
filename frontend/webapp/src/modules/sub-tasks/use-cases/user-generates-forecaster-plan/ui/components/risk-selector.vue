<template>
  <RadioGroup
    v-model="selectedRisk"
    :disabled="props.disabled"
    @update:model-value="setRisk"
  >
    <RadioGroupLabel class="my-3 flex flex-col">
      <span>{{ props.title }}</span>
    </RadioGroupLabel>
    <div class="flex flex-row items-center justify-between space-x-2">
      <RadioGroupOption
        v-for="v in risk"
        :key="v"
        v-slot="{ checked }"
        as="template"
        :value="v"
      >
        <div
          :class="[
            !props.disabled
              ? selectedRisk && checked
                ? 'bg-primary-900 border-transparent text-white'
                : 'border-gray-200 bg-white text-gray-400 hover:cursor-pointer hover:bg-gray-50'
              : '',
            props.disabled
              ? selectedRisk && checked
                ? 'bg-primary-900 border-transparent text-white opacity-60'
                : 'cursor-default border-transparent bg-gray-50 text-gray-400'
              : '',
            'flex items-center justify-center rounded-md border p-3 text-sm font-medium uppercase sm:flex-1',
          ]"
        >
          <RadioGroupLabel as="span" class="text-sm font-medium">{{
            v
          }}</RadioGroupLabel>
        </div>
      </RadioGroupOption>
    </div>
  </RadioGroup>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import {
    RadioGroup,
    RadioGroupLabel,
    RadioGroupOption,
  } from '@headlessui/vue';
  import { RiskLevelType } from '@aventur-shared/modules/goals';
  import { RiskLevel } from '@aventur-shared/constants';

  const props = withDefaults(
    defineProps<{
      title: string;
      modelValue?: RiskLevelType;
      disabled?: boolean;
    }>(),
    {
      modelValue: undefined,
      disabled: false,
    },
  );

  const risk = Object.values(RiskLevel).sort();

  const setRisk = (): void => {
    selectedRisk.value && emit('update:modelValue', selectedRisk.value);
  };

  const selectedRisk = ref<RiskLevelType | undefined>(props.modelValue);
  const emit = defineEmits<{
    (e: 'update:modelValue', value: RiskLevelType): void;
  }>();
  watch(
    () => props.modelValue,
    (value) => {
      selectedRisk.value = value;
    },
  );
</script>
