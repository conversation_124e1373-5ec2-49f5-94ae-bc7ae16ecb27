<template>
  <slide-over :config="{ maxWidth: 'screen-xl' }">
    <template #open-button="{ open }">
      <slot name="activator" :open="open" />
    </template>

    <template #header>
      <box-section>
        <div class="flex flex-col items-start">
          <h1 class="text-lg text-[#7A8796]">Cash Forecast</h1>
          <div class="my-2">
            <dl>
              <dt class="flex font-medium text-[#6DBFC0]">
                <span> {{ formatName(props.client) }} </span>
              </dt>
              <dd class="flex text-white">
                {{ goal.name }}
              </dd>
            </dl>
          </div>
        </div>
      </box-section>
    </template>

    <template #default>
      <box-section
        v-if="props.isReadOnly && isSubmitted"
        class="w-full rounded-lg bg-white"
      >
        <div class="flex w-full flex-col content-start">
          <ForecasterResults
            :goal-id="goal.id"
            :forecaster-data="null"
            :client-id="props.client.id"
          />
        </div>
      </box-section>
      <box-section v-else class="w-full rounded-lg bg-white">
        <div class="flex w-full flex-col content-start">
          <Forecaster
            :case-id="caseId"
            :goal="goal"
            :client-id="props.client.id"
          />
        </div>
      </box-section>
    </template>

    <template #footer>
      <div v-if="!props.isReadOnly" class="flex justify-between space-x-3">
        <custom-button
          type="button"
          theme="primary-ghost"
          :disabled="false"
          @on-click="emitForecasterEvent('forecaster:reset')"
        >
          Reset
        </custom-button>
        <div class="flex space-x-3">
          <custom-button
            type="button"
            theme="text-like"
            :is-busy="false"
            @on-click="emitForecasterEvent('forecaster:export')"
            >Generate PDF</custom-button
          >
          <custom-button
            type="button"
            theme="primary"
            :is-busy="false"
            @on-click="emitForecasterEvent('forecaster:submit')"
            >Submit & Save</custom-button
          >
        </div>
      </div>
    </template>
  </slide-over>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useEventBus } from '@vueuse/core';
  import { defineRule } from 'vee-validate';
  import { required } from '@vee-validate/rules';
  import { formatName } from '@aventur-shared/utils/user';
  import { ArrayElement } from '@aventur-shared/types/Common';
  import { TaskGoalRequired } from '@aventur-shared/modules/tasks';
  import { CaseId } from '@aventur-shared/modules/cases';
  import { BoxSection } from '@modules/ui';
  import { Button as CustomButton } from '@modules/ui';
  import SlideOver from '@modules/ui/slide-over/slide-over.vue';
  import Forecaster from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/forecaster.vue';
  import {
    ForecasterEventType,
    Goal,
    forecasterEventKey,
  } from '@aventur-shared/modules/goals';
  import { default as useCashflowPlanStore } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/stores';
  import ForecasterResults from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/forecaster-results.vue';

  defineRule('required', required);
  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoalRequired;
    client: ArrayElement<Goal['clients']>;
    isReadOnly: boolean;
  }>();

  const { getForecasterData } = useCashflowPlanStore();

  const forecasterData = computed(() =>
    getForecasterData(props.goal.id, props.client.id),
  );
  const isSubmitted = computed(() => {
    return !!forecasterData.value?.updated_at;
  });

  const eventBus = useEventBus(forecasterEventKey);

  const emitForecasterEvent = (eventName: ForecasterEventType) =>
    eventBus.emit({ name: eventName });
</script>
