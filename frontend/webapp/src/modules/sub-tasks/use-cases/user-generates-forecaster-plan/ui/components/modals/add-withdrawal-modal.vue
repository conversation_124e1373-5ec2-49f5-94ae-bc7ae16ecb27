<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-50" @close="onClose">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black/25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex min-h-full items-center justify-center p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-100"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-md overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all"
            >
              <DialogTitle
                as="h3"
                class="border-b bg-gray-200 p-4 leading-6 text-gray-700"
              >
                {{ isEditMode ? 'Update Withdrawal' : 'Add Withdrawal' }}
              </DialogTitle>
              <div class="p-4">
                <form @submit="onSubmit">
                  <div class="flex flex-col">
                    <Field
                      v-slot="{ field }"
                      v-model="selectedType"
                      name="type"
                      rules="required"
                    >
                      <cashflow-type-selector
                        v-bind="field"
                        :options="selectOptions"
                      />
                    </Field>
                  </div>
                  <div
                    v-if="
                      ![selectedType, values.type].includes(
                        WithdrawalType.FullWithdrawal,
                      )
                    "
                    class="flex flex-col"
                  >
                    <Field
                      v-slot="{ field }"
                      v-model="selectedAmount"
                      name="amount"
                      rules="required"
                    >
                      <div class="my-5">
                        <label for="amount" class="block text-sm text-gray-700"
                          >Amount</label
                        >
                        <div class="relative mt-1 flex rounded-md shadow-sm">
                          <div
                            class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
                          >
                            <span class="text-gray-500 sm:text-sm"
                              >&pound;</span
                            >
                          </div>
                          <input
                            v-bind="field"
                            id="amount"
                            v-maska="maskOptions"
                            type="text"
                            autocomplete="off"
                            class="block w-full flex-1 rounded-none rounded-l-md border-gray-300 bg-gray-50 pl-7 pr-12 sm:text-sm"
                          />
                          <span
                            class="inline-flex items-center rounded-r-md border border-l-0 border-gray-300 bg-gray-50 px-3 text-gray-500 sm:text-sm"
                            >.00</span
                          >
                        </div>
                      </div>
                    </Field>
                  </div>
                  <div class="flex flex-row">
                    <Field v-slot="{ field }" name="age" rules="required">
                      <template
                        v-if="hasMaxValue(selectedType as WithdrawalType)"
                      >
                        <age-range-selector
                          v-bind="field"
                          :min-value="startAge"
                          :max-value="endAge"
                          :data-value="
                            values.age
                              ? [values.age[0], values.age[1]].join(',')
                              : undefined
                          "
                          label="Age"
                        />
                      </template>
                      <template v-else>
                        <age-range-selector
                          v-bind="field"
                          :min-value="startAge"
                          :data-value="
                            values.age ? [values.age[0]].join(',') : undefined
                          "
                          label="Age"
                        />
                      </template>
                    </Field>
                  </div>
                  <div class="mt-20 flex flex-col items-center">
                    <template v-if="Object.keys(errors).length">
                      <div class="-mt-10 mb-10 w-full">
                        <div class="rounded-md bg-yellow-50 p-4">
                          <div class="flex">
                            <div class="ml-3">
                              <h3 class="text-sm font-medium text-yellow-800">
                                Incorrect or missing values
                              </h3>
                              <div class="mt-2 text-sm text-yellow-700">
                                <p>
                                  Please check the form and provide all required
                                  values.
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <button type="submit" class="btn btn-primary w-full">
                      Submit
                    </button>
                    <a
                      class="mt-5 text-xs text-gray-500 hover:cursor-pointer"
                      @click="onClose"
                    >
                      Cancel & close
                    </a>
                  </div>
                </form>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
  import { Ref, computed, inject, onMounted, ref, toValue, watch } from 'vue';
  import { Field, defineRule, useForm } from 'vee-validate';
  import { required } from '@vee-validate/rules';
  import { MaskInputOptions } from 'maska';
  import {
    Dialog,
    DialogPanel,
    DialogTitle,
    TransitionChild,
    TransitionRoot,
  } from '@headlessui/vue';
  import { AGES } from '@aventur-shared/constants/age';
  import {
    birthdateInjectionKey,
    useAge,
  } from '@aventur-shared/composables/useAge';
  import {
    WithdrawalItem,
    WithdrawalType,
  } from '@aventur-shared/modules/goals';
  import {
    AgeRangeSelector,
    CashflowTypeSelector,
  } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan';

  const { values, errors, handleSubmit, resetForm, setValues } = useForm();
  defineRule('required', required);

  const birthdate = inject(birthdateInjectionKey) as Ref<string>;
  const { date2age } = useAge(birthdate);

  const props = defineProps<{
    isOpen: boolean;
    item?: Ref<WithdrawalItem | undefined>;
  }>();

  const maskOptions: MaskInputOptions = {
    mask: '90',
    tokens: {
      9: {
        pattern: /[1-9]/,
      },
      0: {
        pattern: /\d/,
        multiple: true,
      },
    },
    eager: true,
  };

  // type
  const selectedType = ref<WithdrawalType>();
  const selectOptions = Object.keys(WithdrawalType) as Array<WithdrawalType>;
  const hasMaxValue = (selectedType: WithdrawalType) =>
    ![WithdrawalType.LumpSumWithdrawal, WithdrawalType.FullWithdrawal].includes(
      selectedType,
    );
  watch(selectedType, (value, oldValue) => {
    if (value === WithdrawalType.FullWithdrawal) {
      selectedAmount.value = 0;
    }
    setValues(
      {
        ...values,
        age:
          value !== WithdrawalType.FullWithdrawal &&
          value !== WithdrawalType.LumpSumWithdrawal
            ? oldValue !== WithdrawalType.FullWithdrawal &&
              oldValue !== WithdrawalType.LumpSumWithdrawal
              ? values.age
              : [startAge.value, endAge.value]
            : [startAge.value],
      },
      false,
    );
  });

  // amount
  const selectedAmount = ref<number>();

  // dates
  const startAge = computed<number | undefined>(() => {
    if (!birthdate) {
      return AGES[0];
    }
    const current_age = date2age(new Date().toISOString(), true);
    return AGES.filter((val) => val >= current_age).at(0);
  });

  const endAge = computed<number | undefined>(() => {
    return AGES.filter((val) => val >= (startAge.value ?? 0))
      .slice(-1)
      .at(0);
  });

  const isEditMode = computed<boolean>(() => {
    return !!props.item?.value;
  });

  onMounted(() => {
    if (isEditMode.value) {
      const data = toValue(props.item);
      setValues({
        type: data?.type,
        amount: data?.amount,
        age: [data?.start, data?.end],
      });
    }
  });

  const emit = defineEmits<{
    (e: 'update:isOpen', value: boolean): void;
    (e: 'planner:addWithdrawal', value: WithdrawalItem): void;
  }>();

  const onClose = () => {
    emit('update:isOpen', false);
    window.setTimeout(() => {
      setValues({
        type: null,
        amount: null,
        age: null,
      });
      resetForm();
    }, 100);
  };

  const onSubmit = handleSubmit((values, { resetForm }) => {
    if (!birthdate) return;

    const age = Array.of(values.age).flat();
    const item: WithdrawalItem = {
      start: Number(age.at(0)),
      end: Number(age.at(1)) || null,
      amount: values.amount ? Number(values.amount) : undefined,
      type: values.type,
    };

    emit('planner:addWithdrawal', item);
    emit('update:isOpen', false);
    resetForm();
  });
</script>
