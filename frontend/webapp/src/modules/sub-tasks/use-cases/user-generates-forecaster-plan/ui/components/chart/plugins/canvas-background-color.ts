import { ChartOptions } from 'chart.js';

export const plugin = {
  id: 'canvasBackgroundColor',
  /**
   * @param chart Chart
   * @param args Record<string, unknown>
   * @param options ChartOptions
   */
  beforeDraw: (chart, args: Record<string, unknown>, options: ChartOptions) => {
    const { ctx } = chart;
    ctx.save();
    ctx.globalCompositeOperation = 'destination-over';
    ctx.fillStyle = options.color || '#F9FAFB';
    ctx.fillRect(0, 0, chart.width, chart.height);
    ctx.restore();
  },
};
