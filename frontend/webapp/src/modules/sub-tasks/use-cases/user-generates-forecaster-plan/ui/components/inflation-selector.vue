<template>
  <div class="flex min-w-full flex-col">
    <p class="block text-sm text-gray-700">
      {{ label }}
    </p>
    <vue-slider
      v-model="rate"
      :data="[2, 5, 10]"
      :marks="marks"
      :dot-size="24"
      :height="6"
      :duration="0.2"
      :contained="true"
      :process-style="processStyles"
      tooltip-placement="top"
      :tooltip-formatter="($v) => `${$v}%`"
      @update:model-value="updateValue"
    />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { debounce } from 'lodash';
  import { Nullable } from '@aventur-shared/types/Common';
  import VueSlider, { Styles, Value } from 'vue-slider-component';
  import { DEFAULT_INFLATION_RATE } from '@aventur-shared/constants';

  const props = defineProps<{
    modelValue: Nullable<number>;
    label?: string;
  }>();

  const emit = defineEmits<{
    (e: 'update:model-value', value: Value): void;
  }>();

  const updateValue = debounce((value: Value) => {
    emit('update:model-value', value);
  }, 250);

  const rate = computed<number>({
    get: () => props.modelValue ?? DEFAULT_INFLATION_RATE,
    set: (value) => updateValue(value),
  });

  const marks = (value: Value) => ({
    label: `${value}%`,
    labelActiveStyle:
      value == rate.value
        ? {
            color: '#215249',
            fontWeight: 'bold',
          }
        : {},
  });
  const processStyles = computed<Styles>(() => ({
    backgroundColor: '#ccc',
  }));
</script>
