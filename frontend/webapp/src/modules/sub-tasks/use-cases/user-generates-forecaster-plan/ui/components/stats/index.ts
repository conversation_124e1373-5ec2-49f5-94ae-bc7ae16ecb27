import { filter, keys, map } from 'lodash';
import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
import {
  ForecasterStatsType,
  ForecasterStatsValue,
  RiskLevelType,
  RiskProfile,
} from '@aventur-shared/modules/goals';

export const getGrowthRateByRisk = (risk: RiskLevelType): string | undefined =>
  RiskProfile[risk]?.[0]
    ? Intl.NumberFormat('en-GB', { minimumFractionDigits: 1 }).format(
        RiskProfile[risk]?.[0],
      )
    : undefined;

export const getMaxDrawdownByRisk = (
  risk: RiskLevelType,
): string | undefined =>
  RiskProfile[risk]?.[1].at(0)
    ? Intl.NumberFormat('en-GB', { minimumFractionDigits: 1 }).format(
        RiskProfile[risk][1][0],
      )
    : undefined;

export const getMaxReturnByRisk = (risk: RiskLevelType): string | undefined =>
  RiskProfile[risk]?.[1].at(1)
    ? Intl.NumberFormat('en-GB', { minimumFractionDigits: 1 }).format(
        RiskProfile[risk][1][1],
      )
    : undefined;

export const formatMoneyValue = (value?: ForecasterStatsValue) =>
  value !== undefined
    ? formatWithCurrency(new Money(Number(value)), { maximumFractionDigits: 0 })
    : null;

export const formatPercentValue = (value?: ForecasterStatsValue) =>
  value !== undefined
    ? (Number(value) / 100).toLocaleString('en', {
        style: 'percent',
        minimumFractionDigits: 1,
      })
    : null;

export const formatStringValue = (value: ForecasterStatsValue) =>
  value ? String(value) : null;

export const formatStats = (
  stats: Array<ForecasterStatsValue[]>,
): ForecasterStatsType[] => {
  const names: {
    [k: string | number | symbol]: (
      value: ForecasterStatsValue,
    ) => string | null;
  } = {
    'Assumed inflation rate': formatPercentValue,
    'Growth rate, p.a.': formatPercentValue,
    'Maximum drawdown': formatPercentValue,
    'Invested value at retirement': formatMoneyValue,
    'Estimated value at retirement': formatMoneyValue,
    'Estimated terminal value': formatMoneyValue,
    'Estimated capacity for loss': formatStringValue,
  };

  return filter(
    ['Recommended risk', 'Selected risk'].map((ni, i) => {
      if (stats[i] === undefined) {
        return map([ni, ...keys(names)], (name) => ({ name, value: null }));
      }
      return [
        { name: ni, value: stats[i][0] },
        ...map(keys(names), (nj, j) => ({
          name: nj,
          value: names[nj](stats[i][j + 1]),
        })),
      ];
    }),
  );
};
