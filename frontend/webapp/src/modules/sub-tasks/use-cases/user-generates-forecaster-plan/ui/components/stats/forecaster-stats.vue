<template>
  <div class="flex flex-col font-bold" data-planner="stats">
    <div class="px-4 sm:px-6 lg:px-8">
      <div class="mt-8 flow-root">
        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle">
            <table class="min-w-full">
              <tbody class="bg-white">
                <template v-for="(s, n) in formattedStats" :key="n">
                  <tr class="border-t border-gray-200">
                    <th
                      colspan="1"
                      scope="colgroup"
                      class="bg-gray-50 py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3"
                    >
                      {{ getHeaderName(n) }}
                    </th>
                    <th
                      colspan="1"
                      scope="colgroup"
                      class="bg-gray-50 py-2 pl-4 pr-3 text-right text-sm font-semibold text-gray-900 sm:pl-3"
                    >
                      {{ getHeaderValue(n) }}
                    </th>
                  </tr>
                  <tr
                    v-for="(stat, index) in s.slice(1)"
                    :key="index"
                    :class="[
                      index === 0 ? 'border-gray-300' : 'border-gray-200',
                      'border-t',
                    ]"
                  >
                    <td
                      class="flex whitespace-nowrap py-2 pl-4 pr-3 text-sm text-gray-500 sm:pl-3"
                    >
                      <div class="flex flex-nowrap items-center">
                        {{ stat.name }}&nbsp;
                        <template v-if="stat.name.includes('loss')">
                          <InformationCircleIcon
                            v-tippy="
                              'The figure returned is the £/% of the first years contributions that can be lost and still maintain a positive balance at age 99 or earlier full withdrawal (if any).  ' +
                              'If the fund depletes before age 99 or earlier full withdrawal (if any), the calculation will return a 0% value.'
                            "
                            class="ml-1 flex size-5 text-gray-700 hover:cursor-help"
                          />
                        </template>
                      </div>
                    </td>
                    <td
                      class="whitespace-nowrap py-2 pl-4 pr-3 text-right text-sm font-medium text-gray-900 sm:pl-3"
                    >
                      {{ stat.value ?? '-' }}
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { formatStats } from './index';
  import { ForecasterStatsValue } from '@aventur-shared/modules/goals';
  import { InformationCircleIcon } from '@heroicons/vue/20/solid';

  const props = defineProps<{
    stats: Array<ForecasterStatsValue[]>;
  }>();
  const formattedStats = computed(() => formatStats(props.stats));
  const getHeaderName = (index: number) => formattedStats.value[index][0].name;
  const getHeaderValue = (index: number) =>
    formattedStats.value[index][0].value;
</script>
