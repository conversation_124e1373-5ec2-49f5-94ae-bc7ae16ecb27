<template>
  <div class="relative">
    <loading
      v-model:active="isActive"
      :can-cancel="true"
      :is-full-page="false"
      class="z-40"
    />
    <div data-planner="chart">
      <div class="flex h-96 flex-col">
        <div
          id="legend"
          data-legend
          class="flex bg-gray-50 px-5 pb-2 pt-5 text-xs lg:text-sm"
        >
          <span class="grow text-lg font-extrabold text-gray-600"
            >Financial Plan</span
          >
        </div>
        <div class="flex grow">
          <Line :data="chartData" :options="chartOptions" />
        </div>
      </div>
      <div class="flex h-64 flex-col">
        <div data-legend class="flex bg-gray-50 px-5 text-sm">
          <span class="grow text-lg font-extrabold text-gray-600"
            >Risk Level</span
          >
        </div>
        <div class="flex grow">
          <Line :data="riskChartData" :options="riskChartOptions" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, inject } from 'vue';
  import { filter, map, max, values } from 'lodash';
  import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
  import Loading from 'vue-loading-overlay';
  import {
    CategoryScale,
    ChartData,
    ChartDataset,
    Chart as ChartJS,
    ChartOptions,
    Legend,
    LineElement,
    LinearScale,
    LogarithmicScale,
    PointElement,
    Title,
    Tooltip,
  } from 'chart.js';
  import { Line } from 'vue-chartjs';
  import { plugin as CanvasBackgroundColor } from './plugins/canvas-background-color';
  import { plugin as HtmlLegend } from './plugins/html-legend';
  import {
    ForecasterResults,
    buildingPlanInjectionKey,
    recommendedRiskInjectionKey,
    selectedRiskInjectionKey,
  } from '@aventur-shared/modules/goals';
  import { ChartColours } from '@aventur-shared/constants';

  ChartJS.register(
    CategoryScale,
    LinearScale,
    LogarithmicScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend,
    HtmlLegend,
    CanvasBackgroundColor,
  );

  const recommendedRisk = inject(recommendedRiskInjectionKey);
  const selectedRisk = inject(selectedRiskInjectionKey);
  const isActive = inject(buildingPlanInjectionKey);

  const props = withDefaults(
    defineProps<{
      // isUpdating: boolean;
      data: ForecasterResults['data'];
    }>(),
    {
      // isUpdating: false,
      data: () => ({}),
    },
  );

  const scaleMax = computed(() =>
    Math.max(
      max(map(values(props.data), `GrowthBalance${recommendedRisk?.value}`)) ??
        0,
      max(map(values(props.data), `GrowthBalance${selectedRisk?.value}`)) ?? 0,
    ),
  );
  const stepSize = computed(() => (scaleMax.value < 1000000 ? 10e3 : 10e4));

  const chartData = computed<ChartData<'line'>>(() => {
    const datasets = [
      {
        label: 'Cash Balance',
        data: map(values(props.data), 'CashBalance'),
        backgroundColor: ChartColours.MID_PURPLE,
        borderColor: ChartColours.MID_PURPLE,
        pointStyle: false,
        yAxisID: 'y_left',
      },
      {
        label: 'Selected Risk',
        data: map(values(props.data), `GrowthBalance${selectedRisk?.value}`),
        backgroundColor: ChartColours.DARK_GREEN,
        borderColor: ChartColours.DARK_GREEN,
        borderDash: [5, 5],
        pointStyle: false,
        yAxisID: 'y_left',
      },
      {
        label: 'Recommended Risk',
        data: map(values(props.data), `GrowthBalance${recommendedRisk?.value}`),
        backgroundColor: ChartColours.LIGHT_GREEN,
        borderColor: ChartColours.LIGHT_GREEN,
        pointStyle: false,
        yAxisID: 'y_left',
      },
    ];

    return {
      labels: map(values(props.data), 'Age'),
      datasets: filter(
        datasets,
        (item) => filter(item.data).length,
      ) as ChartDataset<never, never>[], // TODO: provide types
    };
  });
  const chartOptions = computed<ChartOptions<'line'>>(() => ({
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: 20,
    },
    plugins: {
      htmlLegend: {
        // ID of the container to put the legend in
        containerID: 'legend',
      },
      title: {
        display: false,
      },
      legend: {
        display: false,
      },
    },
    scales: {
      // eslint-disable-next-line id-length
      x: {
        type: 'linear',
        ticks: {
          stepSize: 10,
        },
      },
      y_left: {
        type: 'linear',
        position: 'left',
        ticks: {
          stepSize: stepSize.value,
          callback: (value) => {
            value = Number(value);
            if (!Number.isInteger(value)) {
              value = Math.round(value); // HALF_AWAY_FROM_ZERO
            }
            return formatWithCurrency(new Money(value), {
              maximumFractionDigits: 0,
            });
          },
        },
        min: 0,
        max: stepSize.value * Math.ceil(scaleMax.value / stepSize.value),
      },
    },
  }));

  const riskChartData = computed<ChartData<'line'>>(() => {
    const datasets = [
      {
        label: 'Selected Risk',
        data: map(values(props.data), `RiskLevel${selectedRisk?.value}`),
        backgroundColor: ChartColours.DARK_GREEN,
        borderColor: ChartColours.DARK_GREEN,
        borderDash: [5, 5],
        pointStyle: false,
        yAxisID: 'y_right',
      },
      {
        label: 'Recommended Risk',
        data: map(values(props.data), `RiskLevel${recommendedRisk?.value}`),
        backgroundColor: ChartColours.LIGHT_GREEN,
        borderColor: ChartColours.LIGHT_GREEN,
        pointStyle: false,
        yAxisID: 'y_right',
      },
    ];

    return {
      labels: map(values(props.data), 'Age'),
      datasets: filter(
        datasets,
        (item) => filter(item?.data).length,
      ) as ChartDataset<never, never>[], // TODO: provide types
    };
  });
  const riskChartOptions = computed<ChartOptions<'line'>>(() => ({
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: 20,
    },
    plugins: {
      title: {
        display: false,
      },
      legend: {
        display: false,
      },
    },
    scales: {
      // eslint-disable-next-line id-length
      x: {
        type: 'linear',
        ticks: {
          stepSize: 10,
        },
        title: {
          display: true,
          text: 'Age (years)',
        },
      },
      y_right: {
        type: 'linear',
        reverse: false,
        min: 1,
        max: 5,
        offset: true,
        ticks: {
          stepSize: 1,
          callback: (value) => {
            if (scaleMax.value > 10_000_000) {
              return String(value).padStart(19, ' ');
            }
            if (scaleMax.value > 1_000_000) {
              return String(value).padStart(17, ' ');
            }
            if (scaleMax.value > 100_000) {
              return String(value).padStart(14, ' ');
            }
            if (scaleMax.value > 10_000) {
              return String(value).padStart(12, ' ');
            }
          },
        },
      },
    },
  }));
</script>
