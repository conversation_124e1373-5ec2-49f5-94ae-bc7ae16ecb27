import { apiClient } from '@aventur-shared/services/api';
import { GoalAccount, GoalId } from '@aventur-shared/modules/goals';
import { AccountId, Advice } from '@aventur-shared/modules/accounts';
import { AdviceDTO } from '@aventur-shared/modules/accounts/models/advice';
import { CaseId, TaskCustomCommand } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';

type AdvicePayload = Pick<
  Advice,
  | 'type'
  | 'description'
  | 'id'
  | 'amount'
  | 'frequency'
  | 'portfolioId'
  | 'accountId'
>;
type AdviceBody = Pick<
  AdviceDTO,
  | 'note'
  | 'advice_type_id'
  | 'id'
  | 'amount'
  | 'frequency'
  | 'portfolio_id'
  | 'referenced_account_id'
>[];
type AddAdviceToAccountDTO = AdviceDTO[];

type Command = TaskCustomCommand.UpdateAccountAdvice;

type Body = {
  account_id: GoalAccount['id'];
  case_goal_id: GoalId;
  advice_lines: AdviceBody;
};

export const addAdviceToAccount = async (
  caseId: CaseId,
  caseGoalId: GoalId,
  taskId: TaskId,
  model: {
    accountId: AccountId;
    advices: AdvicePayload[];
  },
): Promise<void> => {
  const body: Body = {
    account_id: model.accountId,
    case_goal_id: caseGoalId,
    advice_lines: model.advices.map((advice) => ({
      id: advice.id,
      note: advice.description,
      advice_type_id: advice.type,
      amount: advice.amount,
      frequency: advice.frequency,
      portfolio_id: advice.portfolioId,
      referenced_account_id: advice.accountId,
    })),
  };

  await apiClient.patch<
    { command: Command; payload: Body },
    Promise<AddAdviceToAccountDTO>
  >(`/api/v2/case/${caseId}/advice_selection/${taskId}`, {
    command: TaskCustomCommand.UpdateAccountAdvice,
    payload: { ...body },
  });
};
