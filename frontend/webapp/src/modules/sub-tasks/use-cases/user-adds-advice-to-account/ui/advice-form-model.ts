import * as yup from 'yup';
import { Ref } from 'vue';
import { validationMessages } from '@aventur-shared/utils/form';
import { AdviceFrequencyEnum } from '@aventur-shared/modules/factfind/models/frequency';

export interface FormValues {
  advices: Array<{
    id: number | undefined;
    adviceType: number | null;
    adviceDescription: string;
    amount?: number;
    frequency?: AdviceFrequencyEnum;
    portfolioId?: number;
    accountId?: number;
  }>;
}

export interface MetaType {
  [key: number]: {
    requiresAmount: boolean;
    requiresFrequency: boolean;
    requiresPortfolio: boolean;
    requiresAccount: boolean;
  };
}

export const validationSchema = (adviceTypeMetaMap: Ref<MetaType>) =>
  yup.object().shape({
    advices: yup.array().of(
      yup.object().shape({
        adviceType: yup
          .number()
          .nullable()
          .required(validationMessages.fieldRequiredMessage),
        adviceDescription: yup.string().nullable(),
        amount: yup
          .number()
          .typeError('Amount must be a number')
          .nullable()
          .when('adviceType', (adviceType: number[] | null, schema: any) => {
            if (
              adviceType !== null &&
              adviceTypeMetaMap.value[adviceType[0]]?.requiresAmount
            ) {
              return schema.required('Amount is required');
            }
            return schema.notRequired().nullable();
          }),
        frequency: yup
          .string()
          .oneOf(
            Object.values(AdviceFrequencyEnum),
            'Invalid frequency selected',
          )
          .nullable()
          .when('adviceType', (adviceType: number[] | null, schema: any) => {
            if (
              adviceType !== null &&
              adviceTypeMetaMap.value[adviceType[0]]?.requiresFrequency
            ) {
              return schema.required('Frequency selection is required');
            }
            return schema.notRequired().nullable();
          }),
        portfolioId: yup
          .number()
          .nullable()
          .when('adviceType', (adviceType: number[] | null, schema: any) => {
            if (
              adviceType !== null &&
              adviceTypeMetaMap.value[adviceType[0]]?.requiresPortfolio
            ) {
              return schema.required('Portfolio selection is required');
            }
            return schema.notRequired().nullable();
          }),
        accountId: yup
          .number()
          .nullable()
          .when('adviceType', (adviceType: number[] | null, schema: any) => {
            if (
              adviceType !== null &&
              adviceTypeMetaMap.value[adviceType[0]]?.requiresAccount
            ) {
              return schema.required('Destination account is required');
            }
            return schema.notRequired().nullable();
          }),
      }),
    ),
  });
