<template>
  <form class="flex flex-col" @submit="onSubmit">
    <FieldArray v-slot="{ fields, push, remove }: any" name="advices">
      <div
        v-for="(field, fieldIdx) in fields"
        :key="field.key"
        :class="[
          'flex w-full gap-1 py-4 pl-8 pr-4',
          fieldIdx % 2 === 0 ? '' : 'bg-slate-100',
        ]"
      >
        <div class="my-0 flex-1">
          <select-field
            class="!mb-1 flex-1"
            :name="`advices[${fieldIdx}].adviceType`"
            label="Advice Type"
            :options="adviceTypeSelectionOptions"
            :searchable="true"
            :disabled="!isModifiable"
          />
          <text-field
            v-if="formRowAdviceTypeMap[fieldIdx].requiresAmount"
            class="!mb-1"
            label="Amount"
            :name="`advices[${fieldIdx}].amount`"
          />
          <select-field
            v-if="formRowAdviceTypeMap[fieldIdx].requiresFrequency"
            class="!mb-1 flex-1"
            :name="`advices[${fieldIdx}].frequency`"
            label="Frequency"
            :options="adviceFrequencySelectOptions"
            :searchable="true"
            :disabled="!isModifiable"
          />
          <select-field
            v-if="formRowAdviceTypeMap[fieldIdx].requiresPortfolio"
            class="!mb-1 flex-1"
            :name="`advices[${fieldIdx}].portfolioId`"
            label="Portfolio"
            :options="portfolioSelectionOptions"
            :searchable="true"
            :disabled="!isModifiable"
          />
          <select-field
            v-if="formRowAdviceTypeMap[fieldIdx].requiresAccount"
            class="!mb-1 flex-1"
            :name="`advices[${fieldIdx}].accountId`"
            :label="
              props.type === 'proposed'
                ? 'Source Account'
                : 'Destination Account'
            "
            :options="accountSelectionOptions"
            :searchable="true"
            :disabled="!isModifiable || accountSelectionOptions.length === 0"
            :placeholder="
              accountSelectionOptions.length === 0
                ? 'Please create a proposed account first'
                : undefined
            "
          />
          <text-area-field
            class="!mb-1"
            :disabled="!isModifiable"
            :name="`advices[${fieldIdx}].adviceDescription`"
            label="Notes"
            placeholder="Add notes"
          />
        </div>
        <div class="!mt-0">
          <button
            type="button"
            :disabled="!isModifiable"
            class="flex h-fit hover:rounded-md hover:bg-gray-200"
            @click="remove(fieldIdx)"
          >
            <x-mark-icon class="size-5" />
          </button>
        </div>
      </div>

      <div class="px-8">
        <custom-button
          class="my-4 w-fit"
          theme="secondary"
          :disabled="!isModifiable"
          @on-click="() => push({ adviceDescription: '', adviceType: null })"
        >
          Add advice row
        </custom-button>
      </div>
    </FieldArray>

    <div class="px-8">
      <custom-button
        type="submit"
        theme="primary"
        class="w-full"
        :disabled="
          !isModifiable ||
          (advices.length === 0 &&
            'advices' in values &&
            values.advices.length === 0)
        "
      >
        Save and add advice
      </custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { FieldArray, useForm } from 'vee-validate';
  import { FormValues, MetaType, validationSchema } from './advice-form-model';
  import { Button as CustomButton } from '@modules/ui';
  import {
    SelectField,
    TextAreaField,
    TextField,
  } from '@aventur-shared/components/form';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import { useRefData } from '@aventur-shared/stores';
  import { storeToRefs } from 'pinia';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { adviceFrequencySelectOptions } from '@aventur-shared/modules/factfind/models/frequency';

  const props = defineProps<{
    advices: FormValues['advices'];
    isModifiable: boolean;
    type: 'proposed' | 'existing';
    accountTypeGroupId: number;
    accountOptions: GoalAccount[];
  }>();
  const emit = defineEmits<{
    (e: 'on-submit', formValues: FormValues): void;
  }>();

  const {
    getAdviceTypesForExistingAccountsByTypeGroupId,
    getAdviceTypesForProposedAccountsByTypeGroupId,
    getPortfolioModels,
  } = storeToRefs(useRefData());

  const adviceTypeSelectionOptions = ref<SelectOption[]>([]);
  const adviceTypeMetaMap = ref<MetaType>({});

  onMounted(async () => {
    const advices =
      props.type === 'existing'
        ? getAdviceTypesForExistingAccountsByTypeGroupId.value(
            props.accountTypeGroupId,
          )
        : getAdviceTypesForProposedAccountsByTypeGroupId.value(
            props.accountTypeGroupId,
          );

    adviceTypeSelectionOptions.value = advices
      .map((option) => ({
        label: option.name,
        value: option.id,
      }))
      .sort((first, second) => first.label.localeCompare(second.label));
    adviceTypeMetaMap.value = advices.reduce((map, option) => {
      map[option.id] = {
        requiresAmount: option.requires_amount,
        requiresFrequency: option.requires_frequency,
        requiresPortfolio: option.requires_portfolio,
        requiresAccount: option.requires_account,
      };
      return map;
    }, {});
  });

  const { handleSubmit, values, setFieldValue } = useForm<FormValues>({
    initialValues: {
      advices: props.advices,
    },
    validationSchema: validationSchema(adviceTypeMetaMap),
  });

  const formRowAdviceTypeMap = computed(() => {
    const formRowAdviceTypeMap = {};
    values.advices.forEach((option, index) => {
      if (
        option.adviceType === null ||
        Object.keys(adviceTypeMetaMap.value).length === 0
      ) {
        formRowAdviceTypeMap[index] = {
          requiresAmount: false,
          requiresFrequency: false,
          requiresPortfolio: false,
          requriesAccount: false,
        };
      } else {
        formRowAdviceTypeMap[index] = {
          requiresAmount:
            adviceTypeMetaMap.value[option.adviceType].requiresAmount,
          requiresFrequency:
            adviceTypeMetaMap.value[option.adviceType].requiresFrequency,
          requiresPortfolio:
            adviceTypeMetaMap.value[option.adviceType].requiresPortfolio,
          requiresAccount:
            adviceTypeMetaMap.value[option.adviceType].requiresAccount,
        };
      }
    });
    return formRowAdviceTypeMap;
  });

  const accountSelectionOptions = ref<SelectOption[]>(
    props.accountOptions
      .map((account) => ({
        label:
          account.status.name === 'Proposed'
            ? `${account.providerName} - ${account.type}`
            : `${account.providerName} - ${account.type} - ${account.accountNumber}`,
        value: account.id,
      }))
      .sort((first, second) => first.label.localeCompare(second.label)),
  );

  const portfolioSelectionOptions = ref<SelectOption[]>(
    getPortfolioModels.value.map((option) => ({
      label: option.name,
      value: option.id as string,
    })),
  );

  const onSubmit = handleSubmit(async (formValues: FormValues) => {
    emit('on-submit', {
      advices: formValues.advices,
    });
  });
</script>
