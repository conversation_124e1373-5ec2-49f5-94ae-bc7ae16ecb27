import { GoalId } from '@aventur-shared/modules/goals';
import { AdviceModel } from './model';
import { addAdviceToAccount as addAdviceToAccountRequest } from './api-request';
import { CaseId } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';

export const addAdviceAction = async (
  caseId: CaseId,
  caseGoalId: GoalId,
  taskId: TaskId,
  model: AdviceModel,
): Promise<void> =>
  await addAdviceToAccountRequest(caseId, caseGoalId, taskId, model);
