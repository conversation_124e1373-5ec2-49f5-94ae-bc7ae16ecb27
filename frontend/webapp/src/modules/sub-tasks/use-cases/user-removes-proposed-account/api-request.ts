import { GoalAccount } from '@aventur-shared/modules/goals';
import { apiClient } from '@aventur-shared/services/api';
import { TaskId } from '@aventur-shared/modules/tasks';
import { CaseId, TaskCustomCommand } from '@aventur-shared/modules/cases';

type Command = TaskCustomCommand.RemoveProposedAccount;

type Body = {
  account_id: GoalAccount['id'];
};

export default async (
  caseId: CaseId,
  taskId: TaskId,
  accountId: GoalAccount['id'],
) => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/advice_selection/${taskId}`,
    {
      command: TaskCustomCommand.RemoveProposedAccount,
      payload: { account_id: accountId },
    },
  );
};
