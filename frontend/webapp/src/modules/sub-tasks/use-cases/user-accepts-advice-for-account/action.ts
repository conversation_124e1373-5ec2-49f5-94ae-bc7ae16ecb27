import { acceptAdviceRequest } from '@aventur-shared/modules/goals/api/accept-advice-request';
import { GoalAccount } from '@aventur-shared/modules/goals';
import { CaseId } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';

export interface AcceptAdviceModel {
  accountId: GoalAccount['id'];
  advices: Array<Pick<GoalAccount['advices'][0], 'id' | 'isAccepted'>>;
}

export const action = async (
  caseId: CaseId,
  taskId: TaskId,
  model: AcceptAdviceModel,
): Promise<void> => {
  await acceptAdviceRequest(caseId, taskId, {
    account_id: model.accountId,
    advice: model.advices.map((advice) => ({
      id: advice.id,
      is_accepted: advice.isAccepted,
    })),
  });
};
