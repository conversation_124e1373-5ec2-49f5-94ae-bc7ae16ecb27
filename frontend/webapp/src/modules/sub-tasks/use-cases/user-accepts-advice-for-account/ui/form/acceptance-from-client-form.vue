<template>
  <form class="flex flex-col" @submit="onSubmit">
    <FieldArray v-slot="{ fields }: any" name="advices">
      <p class="pb-4">{{ fields.length }} advice added</p>

      <div v-for="(field, fieldIdx) in fields" :key="field.key">
        <advice-row
          :title="getAdviceStringifiedType(field.value.type)"
          :description="field.value.description"
        >
          <multi-state-switch
            :options="acceptanceOptions"
            :name="`advices[${fieldIdx}].isAccepted`"
            :disabled="!isModifiable || field.value.isImplemented"
            label="Client acceptance?"
          />
        </advice-row>
        <divider class="mb-4" />
      </div>
    </FieldArray>

    <custom-button
      type="submit"
      theme="primary"
      class="w-full"
      :disabled="
        !hasChanges ||
        !isModifiable ||
        (advices.length === 0 &&
          'advices' in values &&
          values.advices.length === 0)
      "
    >
      Save and confirm advice
    </custom-button>
  </form>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { computed, toRefs } from 'vue';
  import { FieldArray, useForm } from 'vee-validate';
  import { MultiStateSwitch } from '@aventur-shared/components/form';
  import { useRefData } from '@aventur-shared/stores';
  import { AdviceRow } from '@modules/clients-cases/ui';
  import { Button as CustomButton, Divider } from '@modules/ui';
  import { FormValues, validationSchema } from './form-model';

  const props = defineProps<{
    advices: FormValues['advices'];
    isModifiable: boolean;
  }>();

  const { advices, isModifiable } = toRefs(props);

  const emit = defineEmits<{
    (e: 'on-submit', formValues: FormValues): void;
  }>();

  const { adviceTypes } = storeToRefs(useRefData());

  const acceptanceOptions = [
    { value: true, label: 'Accepted' },
    { value: false, label: 'Declined' },
  ];

  const { handleSubmit, values, meta } = useForm<FormValues>({
    initialValues: {
      advices: props.advices,
    },
    validationSchema,
  });

  const hasChanges = computed(() => meta.value.dirty);

  const onSubmit = handleSubmit(async (formValues: FormValues) => {
    emit('on-submit', formValues);
  });

  const getAdviceStringifiedType = (adviceType: number): string => {
    return adviceTypes.value.find((type) => type.id === adviceType)?.name || '';
  };
</script>
