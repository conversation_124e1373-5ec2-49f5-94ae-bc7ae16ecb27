<template>
  <box>
    <box-section class="rounded-t-lg bg-[#19182C]">
      <div class="flex flex-col items-start">
        <h1 class="text-[#7A8796]">{{ type }} account advice acceptance</h1>

        <div class="mb-4 mt-6">
          <dl>
            <dt class="flex text-lg text-[#6DBFC0]">
              {{ account.type }}
            </dt>
            <dd class="flex text-lg font-medium text-white">
              {{ account.providerName }}
            </dd>
          </dl>
        </div>

        <div v-if="isExistingAccount" class="flex gap-4">
          <dl>
            <dt class="flex text-sm text-gray-400">Account No.</dt>
            <dd class="flex font-medium text-white">
              {{ account.accountNumber }}
            </dd>
          </dl>

          <dl>
            <dt class="flex text-sm text-gray-400">Sub Account No.</dt>
            <dd class="flex font-medium text-white">
              {{ account.subAccountNumber }}
            </dd>
          </dl>
        </div>
      </div>
    </box-section>

    <box-section class="flex flex-col gap-1 text-start">
      <acceptance-from-client-form
        :advices="
          account.advices.map((advice: GoalAccount['advices'][0]) => ({
            id: advice.id,
            isAccepted: advice.isAccepted,
            isImplemented: advice.isImplemented,
            description: advice.description,
            type: advice.type,
          }))
        "
        :is-modifiable="isModifiable"
        @on-submit="handleSubmitAdvice"
      />

      <custom-button
        theme="gray-ghost"
        class="w-full"
        @on-click="$emit('on-cancel')"
        >Cancel and close</custom-button
      >

      <span v-if="errorRef" class="self-center text-red-500">{{
        errorRef
      }}</span>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { parseErrorFromResponse } from '@aventur-shared/services/api';
  import { Case, CaseId } from '@aventur-shared/modules/cases';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import { Box, BoxSection, Button as CustomButton } from '@modules/ui';
  import { AcceptanceFromClientForm, type FormValues } from './form';
  import { action } from '../action';

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoal;
    account: GoalAccount;
    isModifiable: boolean;
    caseAdvisorId: Case['relatedAdvisor']['id'];
    type: 'Existing' | 'Proposed';
  }>();

  const emit = defineEmits(['on-cancel', 'on-submit']);
  const errorRef = ref<string>();

  const isExistingAccount = computed(() => props.type === 'Existing');

  const handleSubmitAdvice = async (formValues: FormValues) => {
    try {
      await action(props.caseId, props.goal.taskId, {
        accountId: props.account.id,
        advices: (formValues.advices || []).map((formAdvice) => ({
          id: formAdvice.id,
          isAccepted: formAdvice.isAccepted,
        })),
      });
      emit('on-submit');
    } catch (e: unknown) {
      errorRef.value =
        parseErrorFromResponse(e).join('\r\n') ||
        'Unexpected error during saving.';
    }
  };
</script>
