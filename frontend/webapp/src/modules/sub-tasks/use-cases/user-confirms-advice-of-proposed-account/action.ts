import { ConfirmAdviceModel as Model } from './model';
import { confirmAdviceRequest } from './api-request';
import { CaseId } from '@aventur-shared/modules/cases';
import { GoalId } from '@aventur-shared/modules/goals';
import { TaskId } from '@aventur-shared/modules/tasks';

export const action = async (
  caseId: CaseId,
  caseGoalId: GoalId,
  taskId: TaskId,
  model: Model,
): Promise<void> => {
  try {
    await confirmAdviceRequest(caseId, caseGoalId, taskId, {
      ...model.account,
      expectedFees: model.account.expectedFees.map((fee) => ({
        id: fee.id,
        initial: fee.initial.toNumber(),
        dueDate: fee.dueDate.formatForForm(),
        amount: fee.amount,
      })),
      advice: model.account.advices,
    });
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw new Error(e.message);
    }
    throw e;
  }
};
