import { type GoalAccount } from '@aventur-shared/modules/goals';
import { FeeSplitTemplate } from '@aventur-shared/modules/refdata/fee-split-template';
import { ExpectedFee } from '@aventur-shared/modules/accounts';
import { Advisor } from '@aventur-shared/modules/advisors';

export interface ConfirmAdviceModel {
  account: {
    accountId: GoalAccount['id'];
    accountNumber: string;
    subAccountNumber: string;
    advices: Array<Pick<GoalAccount['advices'][0], 'id' | 'isImplemented'>>;
    advisorId: Advisor['id'];
    feeSplitTemplateId: FeeSplitTemplate['id'];
    expectedFees: Array<
      Pick<ExpectedFee, 'id' | 'initial' | 'amount' | 'dueDate'>
    >;
  };
}
