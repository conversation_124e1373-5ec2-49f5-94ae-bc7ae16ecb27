import {
  Advice,
  AdviceDTO,
} from '@aventur-shared/modules/accounts/models/advice';
import { AccountDTO } from '@aventur-shared/modules/accounts/dtos/account-dto';
import { type GoalId } from '@aventur-shared/modules/goals';
import { type ExpectedFeeDto } from '@aventur-shared/modules/accounts';
import { apiClient } from '@aventur-shared/services/api';
import { Advisor } from '@aventur-shared/modules/advisors';
import { CaseId, TaskCustomCommand } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';

interface AccountPayload {
  accountId: AccountDTO['id'];
  accountNumber: string;
  subAccountNumber: string;
  advice: Pick<Advice, 'id' | 'isImplemented'>[];
  advisorId: Advisor['id'];
  feeSplitTemplateId: number;
  expectedFees: Array<{
    id: number | undefined;
    initial: number;
    dueDate: string;
    amount: string;
  }>;
}

type Command = TaskCustomCommand.UpdateAdviceImplementation;

interface Body {
  account_id: AccountDTO['id'];
  advice: Pick<AdviceDTO, 'id' | 'is_implemented'>[];
  adviser_id: number;
  fee_split_template_id: number;
  account_number: AccountDTO['acc_no'];
  sub_account_number: AccountDTO['sub_acc_no'];
  expected_fees: ExpectedFeeDto[];
}

export const confirmAdviceRequest = async (
  caseId: CaseId,
  caseGoalId: GoalId,
  taskId: TaskId,
  account: AccountPayload,
): Promise<void> => {
  const body: Body = {
    advice: account.advice.map(($a) => ({
      id: $a.id,
      is_implemented: $a.isImplemented,
    })),
    account_id: account.accountId,
    adviser_id: account.advisorId,
    fee_split_template_id: account.feeSplitTemplateId,
    account_number: account.accountNumber,
    sub_account_number: account.subAccountNumber,
    expected_fees: account.expectedFees.map((fee) => ({
      id: fee.id,
      case_goal_id: caseGoalId,
      fee_type: fee.initial,
      due_date: fee.dueDate,
      amount: fee.amount,
    })),
  };
  await apiClient.patch<
    { command: Command; payload: Body },
    Array<ExpectedFeeDto>
  >(`/api/v2/case/${caseId}/ongoing_provider_chasing/${taskId}`, {
    command: TaskCustomCommand.UpdateAdviceImplementation,
    payload: { ...body },
  });
};
