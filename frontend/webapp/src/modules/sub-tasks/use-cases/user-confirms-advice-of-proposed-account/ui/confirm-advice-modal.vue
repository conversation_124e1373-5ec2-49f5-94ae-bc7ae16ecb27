<template>
  <box>
    <box-section class="rounded-t-lg bg-[#19182C]">
      <div class="flex flex-col items-start">
        <h1 class="text-[#7A8796]">Proposed Account Advice</h1>

        <div class="mb-4 mt-6">
          <dl>
            <dt class="flex text-lg text-[#6DBFC0]">
              {{ account.type }}
            </dt>
            <dd class="flex text-lg font-medium text-white">
              {{ account.providerName }}
            </dd>
          </dl>
        </div>
      </div>
    </box-section>

    <box-section class="flex flex-col gap-1 text-start">
      <advice-form
        :is-modifiable="isModifiable"
        :account="account"
        :advisor-id="account.advisorId || caseAdvisorId"
        @on-submit="handleSubmitAdvice"
      />

      <custom-button
        theme="gray-ghost"
        class="w-full"
        @on-click="$emit('on-cancel')"
        >Cancel and close</custom-button
      >

      <span v-if="errorRef" class="self-center text-red-500">{{
        errorRef
      }}</span>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import { DateTime } from '@aventur-shared/utils/dateTime';
  import { TaskGoal } from '@aventur-shared/modules/tasks';
  import { Case, CaseId } from '@aventur-shared/modules/cases';
  import { ExpectedFeeType } from '@aventur-shared/modules/accounts/models/expected-fee-type';
  import { Box, BoxSection, Button as CustomButton } from '@modules/ui';
  import {
    Form as AdviceForm,
    FormValues,
  } from '@modules/sub-tasks/use-cases/user-confirms-advice-of-proposed-account';
  import { action } from '../action';

  const props = defineProps<{
    caseId: CaseId;
    goal: TaskGoal;
    account: GoalAccount;
    caseAdvisorId: Case['relatedAdvisor']['id'];
    isModifiable: boolean;
  }>();

  const emit = defineEmits(['on-cancel', 'on-submit']);
  const errorRef = ref<string>();

  const handleSubmitAdvice = async (formValues: FormValues) => {
    try {
      await action(props.caseId, props.goal.id, props.goal.taskId, {
        account: {
          accountId: props.account.id,
          accountNumber: formValues.accountNumber as string,
          subAccountNumber: formValues.subAccountNumber as string,
          feeSplitTemplateId: formValues.feeSplitTemplate as number,
          advisorId: formValues.advisor as number,
          expectedFees:
            formValues.fees?.map((fee) => ({
              id: fee.id,
              amount: fee.amount as string,
              dueDate: new DateTime(fee.dueDate as string),
              initial: new ExpectedFeeType(fee.initial as number),
            })) || [],
          advices: formValues.advices.map((formAdvice) => ({
            id: formAdvice.id,
            isImplemented: formAdvice.isImplemented,
          })),
        },
      });
      emit('on-submit');
    } catch (e: unknown) {
      if (e instanceof Error) {
        errorRef.value = e.message;
      } else {
        errorRef.value = 'Unexpected error during saving advice.';
      }
    }
  };
</script>
