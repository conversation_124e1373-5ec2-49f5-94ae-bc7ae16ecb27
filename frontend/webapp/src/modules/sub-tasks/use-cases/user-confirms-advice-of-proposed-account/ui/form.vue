<template>
  <form class="flex flex-col" @submit="onSubmit">
    <div class="flex flex-col gap-2 sm:flex-row">
      <text-field
        label="Account Number"
        name="accountNumber"
        :disabled="!isModifiable"
      />
      <text-field
        label="Sub Account Number"
        name="subAccountNumber"
        :disabled="!isModifiable"
      />
    </div>

    <select-field
      class="mb-4 flex-1"
      name="advisor"
      label="Adviser"
      :options="getAdvisorsSelectOptions(activeAdvisors)"
      :searchable="true"
      :disabled="!isModifiable"
      @on-select="handleAdvisorSelect"
    />

    <select-field
      class="mb-4 flex-1"
      name="feeSplitTemplate"
      label="Fee split template"
      :options="
        feeSplitTemplates.map((a) => ({
          value: a.id,
          label: a.name,
        }))
      "
      :searchable="true"
      :disabled="!isModifiable || !values.advisor"
    />

    <FieldArray v-slot="{ fields }: any" name="advices">
      <p class="pb-4">{{ fields.length }} advice added</p>

      <div v-for="(field, fieldIdx) in fields" :key="field.key">
        <advice-row
          :title="getAdviceStringifiedType(field.value.type)"
          :description="field.value.description"
        >
          <switch-field
            :value="true"
            :name="`advices[${fieldIdx}].isImplemented`"
            :label="`${
              field.value.isAccepted ? 'Complete?' : 'No confirmation'
            }`"
            :disabled="!isModifiable || !field.value.isAccepted"
          />
        </advice-row>
        <divider class="mb-4" />
      </div>
    </FieldArray>
    <FieldArray v-slot="{ fields, push, remove }: any" name="fees">
      <p class="pb-4">
        Expected Fees
        <span v-if="fields.length === 0 && errors.fees" class="text-red-500">
          ({{ errors.fees }})
        </span>
      </p>
      <div
        v-for="(field, fieldIdx) in fields"
        :key="field.key"
        class="flex items-center justify-center gap-1"
      >
        <div class="mt-3 flex flex-1 flex-col gap-2 sm:flex-row">
          <select-field
            label="Initial"
            class="flex-auto sm:basis-28"
            :name="`fees[${fieldIdx}].initial`"
            :options="expectedFeeTypeSelectOptions"
            :searchable="true"
            :disabled="!isModifiable"
          />

          <text-field
            class="flex-1"
            label="Amount"
            :name="`fees[${fieldIdx}].amount`"
            :disabled="!isModifiable"
          />

          <date-picker
            :name="`fees[${fieldIdx}].dueDate`"
            label="Due date"
            :disabled="!isModifiable"
          />
        </div>
        <button
          v-if="isModifiable"
          type="button"
          class="mt-4 flex h-fit hover:rounded-md hover:bg-gray-100"
          @click="remove(fieldIdx)"
        >
          <x-mark-icon class="size-5" />
        </button>
      </div>
      <div class="mb-4 flex flex-row justify-end">
        <custom-button
          v-if="isModifiable"
          theme="gray-ghost"
          type="button"
          class="flex items-center justify-center text-sm"
          @click="
            push({
              initial: null,
              amount: null,
              dueDate: null,
            })
          "
        >
          <plus-icon class="mr-1 size-4" />Add&nbsp;more
        </custom-button>
      </div>
    </FieldArray>

    <custom-button
      type="submit"
      theme="primary"
      class="w-full"
      :disabled="
        !hasChanges ||
        !isModifiable ||
        (account.advices.length === 0 &&
          'advices' in values &&
          values.advices.length === 0)
      "
    >
      Save and add advice
    </custom-button>
  </form>
</template>

<script setup lang="ts">
  import { FieldArray, useForm } from 'vee-validate';
  import { FormValues, validationSchema } from './form-model';
  import { Button as CustomButton, Divider } from '@modules/ui';
  import {
    DatePicker,
    SelectField,
    SwitchField,
    TextField,
  } from '@aventur-shared/components/form';
  import { useRefData } from '@aventur-shared/stores';
  import { storeToRefs } from 'pinia';
  import { AdviceRow } from '@modules/clients-cases/ui';
  import { PlusIcon, XMarkIcon } from '@heroicons/vue/20/solid';
  import { GoalAccount } from '@aventur-shared/modules/goals';
  import { expectedFeeTypeSelectOptions } from './form-model';
  import { useFeeSplitTemplates } from '@aventur-shared/modules/refdata/fee-split-template';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';
  import { computed, onMounted } from 'vue';
  import { Nullable } from '@aventur-shared/types/Common';

  const { activeAdvisors } = advisorsProvider().provide();

  const props = defineProps<{
    account: GoalAccount;
    advisorId: Nullable<number>;
    isModifiable: boolean;
  }>();
  const emit = defineEmits<{
    (e: 'on-submit', formValues: FormValues): void;
  }>();

  const { adviceTypes } = storeToRefs(useRefData());

  const { feeSplitTemplates, fetchForAdvisor: fetchAdvisorFeeSplitTemplates } =
    useFeeSplitTemplates();

  onMounted(() => {
    advisorsProvider().create();
    if (props.advisorId) {
      fetchAdvisorFeeSplitTemplates(props.advisorId);
    }
  });

  const { handleSubmit, values, errors, setFieldValue, meta } =
    useForm<FormValues>({
      initialValues: {
        advices: props.account.advices.map((advice) => ({
          id: advice.id,
          isImplemented: advice.isImplemented || false,
          isAccepted: advice.isAccepted || false,
          description: advice.description,
          type: advice.type,
        })),
        accountNumber: props.account.accountNumber || null,
        feeSplitTemplate: props.account.feeSplitTemplate || null,
        advisor: props.advisorId,
        subAccountNumber: props.account.subAccountNumber || '',
        fees: props.account.expectedFees.length
          ? props.account.expectedFees.map((fee) => ({
              id: fee.id,
              initial: fee.initial.toNumber(),
              dueDate: fee.dueDate.formatForForm(),
              amount: fee.amount,
            }))
          : [
              {
                id: undefined,
                initial: null,
                dueDate: null,
                amount: null,
              },
            ],
      },
      validationSchema,
    });

  const hasChanges = computed(() => meta.value.dirty);

  const onSubmit = handleSubmit(async (formValues: FormValues) => {
    emit('on-submit', formValues);
  });

  const getAdviceStringifiedType = (adviceType: number): string => {
    return adviceTypes.value.find((type) => type.id === adviceType)?.name || '';
  };

  function handleAdvisorSelect(advisor: number) {
    setFieldValue('feeSplitTemplate', null);
    if (advisor) {
      fetchAdvisorFeeSplitTemplates(advisor);
    }
  }
</script>
