import * as yup from 'yup';
import {
  fieldRequiredMessage,
  invalidNumberMessage,
} from '@aventur-shared/utils/form/validation-messages';
import { Nullable } from '@aventur-shared/types/Common';
import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

const advicesSchema = yup.object({
  id: yup.number(),
  isImplemented: yup.boolean(),
});

const feesSchema = yup.object({
  id: yup.number(),
  initial: yup.number().nullable().default(null).required(fieldRequiredMessage),
  amount: yup
    .mixed()
    .checkIsNumber(invalidNumberMessage)
    .when({
      is: (value: string) => value,
      then: () =>
        yup
          .number()
          .min(-500000, 'Value must be higher than -500.000')
          .max(500000, 'Value must be lower than 500.000'),
    })
    .transform((value) => value || null)
    .required(fieldRequiredMessage),
  dueDate: yup.string().nullable().required(fieldRequiredMessage),
});

export const validationSchema = yup.object({
  accountNumber: yup
    .string()
    .default(null)
    .nullable()
    .required(fieldRequiredMessage),
  subAccountNumber: yup.string().default(''),
  advisor: yup.number().nullable().required(fieldRequiredMessage),
  feeSplitTemplate: yup
    .number()
    .nullable()
    .default(null)
    .required(fieldRequiredMessage),
  advices: yup.array(advicesSchema),
  fees: yup
    .array(feesSchema)
    .min(1, 'At least 1 fee has to be added')
    .required(fieldRequiredMessage),
});

export type FormValues = {
  fees: Array<{
    id: number | undefined;
    initial: Nullable<number>;
    amount: Nullable<string>;
    dueDate: Nullable<string>;
  }>;
  accountNumber: Nullable<string>;
  subAccountNumber: string;
  advisor: Nullable<number>;
  feeSplitTemplate: Nullable<number>;
  advices: Array<{
    id: number | undefined;
    isImplemented: boolean;
    description: string;
    type: number;
  }>;
};

export const expectedFeeTypeSelectOptions: SelectOption[] = [
  {
    label: 'Fee From Provider',
    value: 1,
  },
  {
    label: 'Fee From Client',
    value: 2,
  },
  {
    label: 'Commission From Provider',
    value: 3,
  },
];
