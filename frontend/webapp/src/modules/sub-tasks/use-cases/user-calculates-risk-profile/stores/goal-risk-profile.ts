import { get } from 'lodash';
import { StateTree, defineStore } from 'pinia';
import { Nullable } from '@aventur-shared/types/Common';
import { ClientId } from '@aventur-shared/modules/clients';
import {
  RiskAnswersType as RiskAnswers,
  RiskProfileData as RiskProfile,
} from '@aventur-shared/modules/goals';

interface State {
  data: Map<string, RiskProfile>;
}

type Getters = {
  getAnswers: (
    state: State,
  ) => (goalId: number, clientId: ClientId) => Nullable<RiskAnswers>;
  getRiskProfile: (
    state: State,
  ) => (goalId: number, clientId: ClientId) => Nullable<RiskProfile>;
};

interface Actions {
  reset: () => void;
  saveRiskProfile: (
    goalId: number,
    clientId: ClientId,
    profile: Partial<RiskProfile> | null,
  ) => void;
  resetRiskProfile: (goalId: number, clientId: ClientId) => void;
}

export const useStore = defineStore<
  'goal-risk-profile',
  State,
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  Getters,
  Actions
>('goal-risk-profile', {
  state: () => ({
    data: new Map(),
  }),
  getters: {
    getAnswers(state: State) {
      return (goalId: number, clientId: ClientId) =>
        get(state.data.get(JSON.stringify({ goalId, clientId })), 'answers') ||
        null;
    },
    getRiskProfile(state: State) {
      return (goalId: number, clientId: ClientId) =>
        state.data.get(JSON.stringify({ goalId, clientId })) || null;
    },
  },
  actions: {
    reset() {
      this.$reset();
    },
    saveRiskProfile(
      goalId: number,
      clientId: ClientId,
      data: Partial<RiskProfile> | null,
    ) {
      data && this.data.set(JSON.stringify({ goalId, clientId }), data);
    },
    resetRiskProfile(goalId: number, clientId: ClientId) {
      this.data.delete(JSON.stringify({ goalId, clientId }));
    },
  },
  persist: {
    pick: ['data'],
    serializer: {
      deserialize: (value: string) => ({
        data: new Map([...JSON.parse(value)]),
      }),
      serialize: (value: StateTree) =>
        JSON.stringify([...value.data.entries()]),
    },
  },
});
