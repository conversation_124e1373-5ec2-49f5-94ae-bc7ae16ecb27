<template>
  <div class="mb-10">
    <h1 class="my-5 text-base font-normal">{{ question.question }}</h1>
    <RadioGroup
      :key="score"
      v-model="score"
      @update:model-value="emit('update:modelValue', $event)"
    >
      <RadioGroupLabel class="sr-only">Server size</RadioGroupLabel>
      <div class="space-y-2">
        <RadioGroupOption
          v-for="(o, k) in question.answers"
          :key="k"
          v-slot="{ active, checked }"
          as="template"
          :value="k + 1"
        >
          <div
            :class="[
              active
                ? 'ring-primary-900/10 border-transparent ring-1'
                : 'border-gray-300',
              checked
                ? 'bg-primary-100/75 text-primary ring-primary-900 ring-1'
                : 'bg-white',
            ]"
            class="hover:ring-primary-900 hover:ring-offset-primary-900 relative flex cursor-pointer rounded-lg border px-5 py-4 hover:ring-1 focus:outline-none"
            aria-hidden="true"
          >
            <div class="flex w-full items-center justify-between">
              <div class="flex items-center">
                <div class="text-sm leading-6">
                  <RadioGroupLabel
                    as="p"
                    :class="checked ? 'text-primary' : 'text-gray-600'"
                    class="font-medium"
                  >
                    {{ o.answer }}
                  </RadioGroupLabel>
                  <RadioGroupDescription
                    v-if="o.description"
                    as="span"
                    :class="
                      checked ? 'text-primary opacity-60' : 'text-gray-500'
                    "
                    class="inline"
                  >
                    <span>{{ o.description }}</span>
                  </RadioGroupDescription>
                </div>
              </div>
              <div v-show="checked" class="shrink-0 text-white">
                <svg class="size-6" viewBox="0 0 24 24" fill="none">
                  <circle
                    cx="12"
                    cy="12"
                    r="12"
                    fill="#215249"
                    fill-opacity="0.9"
                  />
                  <path
                    d="M7 13l3 3 7-7"
                    stroke="#fff"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
        </RadioGroupOption>
      </div>
    </RadioGroup>
    <slot />
  </div>
</template>

<script setup lang="ts">
  import { ref, useAttrs, watch } from 'vue';
  import {
    RadioGroup,
    RadioGroupDescription,
    RadioGroupLabel,
    RadioGroupOption,
  } from '@headlessui/vue';
  import { TRiskQuestion } from '@aventur-shared/modules/goals';

  const attrs = useAttrs();

  defineProps<{
    question: TRiskQuestion;
  }>();

  const score = ref<number | undefined>(attrs.value as number);
  watch(
    () => attrs.value as number,
    (value: number) => !value && (score.value = undefined),
  );

  const emit = defineEmits(['update:modelValue']);
</script>
