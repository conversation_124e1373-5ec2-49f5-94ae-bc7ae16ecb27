<template>
  <RadioGroup class="mb-10">
    <RadioGroupLabel class="my-4 flex flex-col">
      <span>Recommended Risk</span>
      <span class="text-sm"
        >Your recommended risk level is {{ props.results.recommended_risk }} and
        it has been calculated from your attitude to risk score and your current
        capacity to take risk score.</span
      >
    </RadioGroupLabel>
    <div class="flex flex-row items-center justify-between space-x-4">
      <RadioGroupOption v-for="v in [1, 2, 3, 4, 5]" :key="v" as="template">
        <div
          :class="[
            v === props.results.recommended_risk
              ? 'bg-primary-900 border-transparent p-5 text-white'
              : 'bg-primary-900/10 border-gray-200 p-3 text-gray-400',
            'flex items-center justify-center rounded-md border text-sm font-medium uppercase sm:flex-1',
          ]"
        >
          <RadioGroupLabel as="span" class="text-2xl font-semibold">{{
            v
          }}</RadioGroupLabel>
        </div>
      </RadioGroupOption>
    </div>
  </RadioGroup>
  <div class="flex flex-col items-center justify-between space-y-4">
    <p class="m-auto w-3/4 text-sm">
      <span class="font-bold"
        >Your current risk attitude is {{ props.results.risk_attitude }}.</span
      >
      This is the amount of risk that you might generally be happy with
      regarding your money.
    </p>
    <p class="m-auto w-3/4 text-sm">
      <span class="font-bold"
        >Your current loss tolerance is
        {{ props.results.loss_tolerance }}.</span
      >
      This is the level of risk that you might actually be able to tolerate
      based on your situation. This is likely to change after completing the
      rest of this calculator.
    </p>
  </div>
  <div v-if="!props.isReadOnly" class="flex flex-col items-center">
    <custom-button
      class="btn bg-primary-900 hover:bg-primary-900 mt-16 w-fit border-transparent uppercase text-white hover:opacity-90"
      theme="primary"
      @click.prevent.stop="emit('risk-profile:success')"
      >Continue</custom-button
    >
    <p class="my-5 flex text-center text-sm">
      <a
        class="text-primary-900 cursor-pointer underline hover:no-underline"
        @click.prevent.stop="emit('risk-profile:reset')"
        >Click here</a
      >&nbsp;to reset results and start over
    </p>
  </div>
</template>

<script setup lang="ts">
  import {
    RadioGroup,
    RadioGroupLabel,
    RadioGroupOption,
  } from '@headlessui/vue';
  import { Button as CustomButton } from '@modules/ui';
  import { RiskProfileData } from '@aventur-shared/modules/goals';
  const props = withDefaults(
    defineProps<{
      results: RiskProfileData;
      isReadOnly?: boolean;
    }>(),
    {
      isReadOnly: false,
    },
  );
  const emit = defineEmits<{
    (e: 'risk-profile:reset'): void;
    (e: 'risk-profile:success'): void;
  }>();
</script>
