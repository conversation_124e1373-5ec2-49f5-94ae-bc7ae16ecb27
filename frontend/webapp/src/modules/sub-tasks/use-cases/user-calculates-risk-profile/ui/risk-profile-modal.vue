<template>
  <slide-over :config="{ maxWidth: 'screen-md' }">
    <template #open-button="{ open }">
      <slot name="activator" :open="open" :is-read-only="props.isReadOnly" />
    </template>

    <template #header>
      <box-section>
        <div class="flex flex-col items-start">
          <h1 class="text-lg text-[#7A8796]">Risk Assessment</h1>
          <div class="my-2">
            <dl>
              <dt class="flex font-medium text-[#6DBFC0]">
                <span> {{ formatName(props.client) }} </span>
              </dt>
              <dd class="flex text-white">
                {{ goal.name }}
              </dd>
            </dl>
          </div>
        </div>
      </box-section>
    </template>

    <template #default="{ close }">
      <box-section
        v-if="props.isReadOnly && riskProfile"
        class="m-auto w-full rounded-lg bg-white md:w-[46rem]"
      >
        <div class="flex w-full flex-col content-start 2xl:max-w-2xl">
          <h1 class="mb-[42px] mt-5 text-xl font-normal">Risk Results</h1>
          <risk-results :results="riskProfile" is-read-only />
          <disclosure-section title="Answers" :default-open="false">
            <ul role="list" class="divide-y divide-gray-100">
              <li
                v-for="(q, i) in questions"
                :key="i"
                class="flex flex-wrap items-center justify-between gap-x-6 gap-y-4 py-5 sm:flex-nowrap"
              >
                <div>
                  <p class="text-sm font-semibold leading-6 text-gray-900">
                    {{ q.question }}
                  </p>
                  <div
                    class="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500"
                  >
                    <p>
                      {{ getAnswerByIndex(q, i) }}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
          </disclosure-section>
        </div>
      </box-section>
      <box-section
        v-else
        class="m-auto w-full rounded-lg bg-white md:w-[46rem]"
      >
        <div class="flex w-full flex-col content-start 2xl:max-w-2xl">
          <template v-if="!hasResults">
            <span class="mt-2 flex text-sm text-gray-500"
              >* All fields are required</span
            >
            <form @submit.prevent="submitAnswers">
              <Field
                v-for="(q, i) in questions.slice(0, 1)"
                v-slot="{ field }"
                :key="`q${i}`"
                v-model="answers[`a${i}`]"
                :name="`a${i}`"
                rules="required"
              >
                <risk-question v-bind="field" :question="q" as="radio-group" />
              </Field>
              <template v-if="isRiskAllowed">
                <Field
                  v-for="(q, i) in questions.slice(1)"
                  v-slot="{ field }"
                  :key="`q${i + 1}`"
                  v-model="answers[`a${i + 1}`]"
                  :name="`a${i + 1}`"
                  rules="required"
                >
                  <risk-question v-bind="field" :question="q" />
                </Field>
                <div v-if="Object.keys(errors).length" class="my-10">
                  <div class="rounded-md bg-yellow-50 p-4">
                    <div class="flex">
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">
                          Incorrect or missing answers
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700">
                          <p>
                            Please check the questionnaire form and fill the
                            missing fields. All questions must be answered.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </form>
          </template>
          <template v-else-if="riskProfile">
            <h1 class="mb-[42px] mt-5 text-xl font-normal">Risk Results</h1>
            <risk-results
              :results="riskProfile"
              :is-read-only="isReadOnly"
              @risk-profile:success="handleSuccess(close)"
              @risk-profile:reset="onReset"
            />
          </template>
        </div>
      </box-section>
    </template>
    <template #footer>
      <div v-if="isActionable" class="flex justify-between space-x-3">
        <custom-button
          type="button"
          theme="primary-ghost"
          :disabled="calculatingRisk"
          @on-click="onReset"
        >
          Reset
        </custom-button>
        <custom-button
          type="button"
          theme="primary"
          :disabled="!isRiskAllowed"
          :is-busy="calculatingRisk"
          @on-click="submitAnswers"
          >Submit & Save</custom-button
        >
      </div>
    </template>
  </slide-over>
</template>

<script setup lang="ts">
  import { map, sortBy } from 'lodash';
  import { computed, ref } from 'vue';
  import { useEventBus } from '@vueuse/core';
  import { Field, defineRule, useForm } from 'vee-validate';
  import { required } from '@vee-validate/rules';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { ArrayElement } from '@aventur-shared/types/Common';
  import { formatName } from '@aventur-shared/utils/user';
  import { TaskGoalRequired } from '@aventur-shared/modules/tasks';
  import {
    Goal,
    ProfilerEvent,
    RiskAnswersType,
    RiskProfileData,
    TRiskQuestion,
    profilerEventKey,
  } from '@aventur-shared/modules/goals';
  import { riskProfileQuestionnaire } from '@aventur-shared/modules/refdata/refdata';
  import { calculateRiskProfile } from '@aventur-shared/modules/goals/api/calculate-risk-profile';
  import { BoxSection, Button as CustomButton } from '@modules/ui';
  import SlideOver from '../../../../ui/slide-over/slide-over.vue';
  import {
    RiskQuestion,
    RiskResults,
  } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import { useRiskProfileStore } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import DisclosureSection from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/components/disclosure-section.vue';

  defineRule('required', required);

  const props = defineProps<{
    goal: TaskGoalRequired;
    client: ArrayElement<Goal['clients']>;
    isReadOnly: boolean;
  }>();

  const toast = useToast();
  const { saveRiskProfile, getRiskProfile, resetRiskProfile, getAnswers } =
    useRiskProfileStore();

  const eventBus = useEventBus(profilerEventKey);
  const handleSuccess = (cb: () => void) => {
    eventBus.emit({ name: ProfilerEvent.SUBMIT });
    cb();
  };
  const calculatingRisk = ref<boolean>(false);
  const answers = ref<RiskAnswersType>({
    ...getAnswers(props.goal.id, props.client.id),
  });
  const results = ref<RiskProfileData>();

  const isActionable = computed(() => !hasResults.value && !props.isReadOnly);
  const riskProfile = computed(() =>
    getRiskProfile(props.goal.id, props.client.id),
  );
  const questions: TRiskQuestion[] = riskProfileQuestionnaire;
  const hasAnswers = computed(() => {
    return !!Object.values(answers.value).length;
  });
  const hasResults = computed(() => {
    return !!riskProfile.value?.updated_at;
  });
  const isRiskAllowed = computed(() => {
    return hasAnswers.value && answers.value['a0'] === 1;
  });

  const { errors, handleSubmit, resetForm } = useForm({
    initialValues: {
      answers: {},
    },
  });

  const getAnswerByIndex = (question: TRiskQuestion, i: number) => {
    const profile = { ...riskProfile.value } as RiskProfileData;
    const key = profile.answers[`a${i}`] - 1;
    return question.answers[key].answer;
  };

  const submitAnswers = handleSubmit(async () => {
    saveRiskProfile(props.goal.goalId, props.client.id, {
      answers: { ...answers.value },
    });

    calculatingRisk.value = true;
    try {
      const $answers = map(
        sortBy(
          Object.entries(answers.value),
          (item: [string, number]) => Number(item[0].slice(1)), // convert to corresponding number
        ),
        (item: [string, number]) => item[1],
      );
      results.value = await calculateRiskProfile(
        props.goal.id,
        props.client.id,
        $answers,
      );
      toast.success('The risk profile successfully saved');
      saveRiskProfile(props.goal.id, props.client.id, {
        ...results.value,
      });
      resetForm();
    } catch (e: unknown) {
      toast.error(e as Error);
    } finally {
      calculatingRisk.value = false;
    }
  });
  const onReset = (): void => {
    resetForm();
    answers.value = {};
    results.value = undefined;
    resetRiskProfile(props.goal.id, props.client.id);
  };
</script>
