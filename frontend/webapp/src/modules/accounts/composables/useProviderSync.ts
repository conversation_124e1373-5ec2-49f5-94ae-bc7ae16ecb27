import { ref } from 'vue';
import { useToast } from '@aventur-shared/composables/useToast';
import { getProviderInvestmentConstituents } from '@aventur-shared/modules/accounts/api';

export function useProviderSync() {
  const toast = useToast();
  const loadingConstituents = ref(false);

  const handleProviderSync = async (
    accountId,
    selectedPlanDate,
  ): Promise<
    Array<{
      date: string;
      ISIN: string;
      fund: string;
      value: string;
      fundCharge: string;
      investedPercentage: string;
      weightedCharge: string;
    }>
  > => {
    if (!selectedPlanDate) {
      toast.error('Unable to sync with provider as no plan is selected.');
      return [];
    }

    loadingConstituents.value = true;

    try {
      const providerConstituents = await getProviderInvestmentConstituents(
        accountId,
        selectedPlanDate,
      );

      if (providerConstituents.length === 0) {
        toast.info('Provider has no data for the selected date.');
        return [];
      }

      return providerConstituents.map((constituent) => ({
        date: selectedPlanDate,
        ISIN: constituent.ISIN,
        fund: constituent.fund,
        value: constituent.value.toFixed(2),
        fundCharge: constituent.fundChargePercent ?? '',
        investedPercentage: '',
        weightedCharge: '',
      }));
    } catch (error) {
      toast.error('Failed to sync with provider. Please try again later.');
      return [];
    } finally {
      loadingConstituents.value = false;
    }
  };
  const PROVIDERS = [47, 76, 2]; // Fidelity, Fundment, Transact provider ids

  const canSyncWithProvider = (providerId) => {
    return providerId && PROVIDERS.includes(providerId);
  };

  return {
    loadingConstituents,
    handleProviderSync,
    canSyncWithProvider,
  };
}
