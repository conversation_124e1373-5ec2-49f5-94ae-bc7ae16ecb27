import { computed, onMounted, ref } from 'vue';
import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
import { getPlanInformationList } from '@aventur-shared/modules/accounts';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { sortBy } from 'lodash';
import {
  AllFormValues,
  getInitialValues,
  isInvestment,
  isPension,
} from '@aventur-shared/modules/accounts/models/form-model';
import { SingleAccount } from '@aventur-shared/modules/accounts';

export function usePlanSelector(account: SingleAccount, dataLayout: string) {
  const loadedPlanInfo = ref<boolean>(false);
  const newPlanId = 'NEW';
  const selectedPlanId = ref<number | string>(newPlanId);
  const planInformationSelectOptions = ref<SelectOption[]>([]);
  const planInformation = ref<AllFormValues[]>([]);
  const isNewPlan = computed(() => selectedPlanId.value === newPlanId);

  async function getPlanInfoData() {
    try {
      loadedPlanInfo.value = false;
      await getPlanInformationList(account.id, dataLayout).then((response) => {
        planInformation.value = sortBy(response, [
          function (plan) {
            return Date.parse(plan.dateOfInformation);
          },
        ]).reverse();
      });

      planInformationSelectOptions.value = [
        { value: newPlanId, label: 'New plan' },
        ...planInformation.value.map((plan) => ({
          label: new DateTime(plan.dateOfInformation).formatToView(),
          value: plan.id as number,
        })),
      ];
      selectedPlanId.value = planInformation.value[0]?.id ?? newPlanId;
      loadedPlanInfo.value = true;
    } catch (e) {
      console.log(e);
    }
  }

  onMounted(async () => {
    await getPlanInfoData();
  });

  const planInformationDates = computed(() =>
    planInformation.value.map((plan) => plan.dateOfInformation),
  );

  const currentPlanInformation = computed<AllFormValues>(() => {
    if (
      selectedPlanId.value == newPlanId &&
      planInformation.value.length >= 1
    ) {
      const data = { ...planInformation.value[0] };
      data.id = newPlanId;
      data.dateOfInformation = '';
      if (isInvestment(data) || isPension(data)) {
        data.constituents.forEach((constituent) => {
          constituent.id = null;
        });
      }

      return data;
    }
    const planInfo = planInformation.value.find(
      (plan) => plan.id === selectedPlanId.value,
    );

    return planInfo ? planInfo : getInitialValues(dataLayout, account);
  });

  const getPlanInformationByDate = (info_date) => {
    return planInformation.value.find(
      (plan) => plan.dateOfInformation === info_date,
    );
  };

  return {
    newPlanId,
    isNewPlan,
    selectedPlanId,
    planInformation,
    planInformationSelectOptions,
    getPlanInfoData,
    planInformationDates,
    currentPlanInformation,
    getPlanInformationByDate,
    loadedPlanInfo,
  };
}
