import { watch } from 'vue';
import { useField, useFieldArray } from 'vee-validate';

interface ConstituentValue {
  value: string | number;
  fundCharge: string | number;
  investedPercentage: string | number;
  weightedCharge: string | number;
}

export function useInvestmentConstituentsCalculations() {
  const { value: fundChargesValue } = useField('chargesFigures.chargesFundAMC');
  const { value: valueSum } = useField<number>('fundValue');
  const { fields: constituentsValues } =
    useFieldArray<ConstituentValue>('constituents');

  const calculateValueSum = () => {
    valueSum.value = Number(
      constituentsValues.value
        ?.reduce((acc, { value }) => acc + Number(value.value), 0)
        .toFixed(2),
    );
  };

  watch(
    [constituentsValues, valueSum],
    ([newValue, oldValues]) => {
      calculateValueSum();

      constituentsValues.value?.forEach((line, index) => {
        const investedPercentage =
          (Number(line.value.value) / valueSum.value) * 100 || 0;

        constituentsValues.value[index].value.investedPercentage =
          investedPercentage.toFixed(2);

        const weightedCharge =
          (Number(line.value.fundCharge) / 100) *
            Number(line.value.investedPercentage) || 0;

        constituentsValues.value[index].value.weightedCharge =
          weightedCharge.toFixed(2);
      });

      fundChargesValue.value = constituentsValues.value
        ?.reduce((acc, { value }) => acc + Number(value.weightedCharge), 0)
        .toFixed(2);
    },
    { deep: true },
  );

  return { valueSum };
}
