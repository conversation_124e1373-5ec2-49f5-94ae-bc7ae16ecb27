import { Valuation } from '@aventur-shared/modules/factfind/models/valuation';
import { createAccountValuations } from '@aventur-shared/modules/accounts/api';

type Model = Omit<Valuation, 'id'>;

export default async (accountId: number, model: Model) => {
  try {
    await createAccountValuations(accountId, {
      amount: model.amount.getValue(),
      date: model.date.formatForForm(),
      is_actual: model.type === 'actual' ? true : false,
    });
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    } else {
      throw new Error('Create valuation error');
    }
  }
};
