import { fetchAccounts } from '@aventur-shared/modules/accounts/api';
import {
  AccountProviderId,
  AccountStatusEnum,
} from '@aventur-shared/modules/accounts/models/account';

interface QueryString
  extends Partial<{
    page: string;
    status_id: `${AccountStatusEnum}`;
    provider_id: `${AccountProviderId}`;
  }> {}

interface ActionProps {
  query: QueryString;
}

const prepareQueryString = (
  query: Partial<ActionProps['query']>,
): QueryString => {
  const filterEmptyQueryParts = (queryPart: [string, string | number]) =>
    queryPart[1];
  const queryEntries = Object.entries(query).filter(filterEmptyQueryParts);

  return Object.fromEntries(queryEntries);
};

export const userPreviewsAllAccountsAction = async ({ query }: ActionProps) => {
  try {
    return await fetchAccounts(prepareQueryString(query));
  } catch (e: unknown) {
    throw new Error('Could not fetch accounts.');
  }
};
