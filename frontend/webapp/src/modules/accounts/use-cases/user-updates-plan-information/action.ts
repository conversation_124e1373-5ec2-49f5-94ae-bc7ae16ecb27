import { AccountId } from '@aventur-shared/modules/accounts/types';
import { mapToDto } from '@aventur-shared/modules/accounts/models';
import { default as updatePlanInformation } from './api-request';
import { AllFormValues } from '@aventur-shared/modules/accounts/models/form-model';

export default async (
  accountId: AccountId,
  planId: number,
  model: AllFormValues,
  product_layout: string | null,
) =>
  await updatePlanInformation(
    accountId,
    planId,
    mapToDto(model, product_layout),
  );
