import { default as updateAccount } from './api-request';
import { type SingleAccount } from '@aventur-shared/modules/accounts/models/account';
import { type UpdateAccountModel } from './update-account-model';

export const userUpdatesAccountAction = async (
  accountId: SingleAccount['id'],
  model: UpdateAccountModel,
): Promise<SingleAccount> =>
  await updateAccount(accountId, {
    adviser_id: model.advisorId,
    provider_id: model.provider,
    fee_split_template_id: model.feeSplitTemplateId,
    fee_model: model.feeModel,
    portfolio_model_id: model.portfolioModelId,
    account_status_id: model.status,
    type_id: model.typeId,
    account_number: model.accountNumber,
    sub_account_number: model.subAccountNumber,
    clients: model.clients,
    additional_info: model.additionalInfo,
  });
