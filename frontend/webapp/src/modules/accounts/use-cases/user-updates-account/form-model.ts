import { ObjectSchema, array, number, object, string } from 'yup';
import { UpdateAccountModel } from './update-account-model';
import { Nullable } from '@aventur-shared/types/Common';
import { correctAccountNumber } from '@aventur-shared/utils/form/fields';
import { validationMessages } from '@aventur-shared/utils/form';

export const validationSchema: ObjectSchema<
  Omit<UpdateAccountModel, 'typeId'>
> = object({
  advisorId: number()
    .nullable()
    .required(validationMessages.fieldRequiredMessage),
  feeSplitTemplateId: number()
    .nullable()
    .required(validationMessages.fieldRequiredMessage),
  status: number().nullable().required(validationMessages.fieldRequiredMessage),
  provider: number()
    .nullable()
    .required(validationMessages.fieldRequiredMessage),
  accountNumber: correctAccountNumber.required(
    validationMessages.fieldRequiredMessage,
  ),
  subAccountNumber: correctAccountNumber.defined(),
  clients: array()
    .of(number().defined())
    .min(1, 'At least 1 client has to be selected')
    .required(validationMessages.fieldRequiredMessage),
  additionalInfo: string()
    .default('')
    .max(2000, validationMessages.maxLengthMessage('Additional info', 2000)),
  feeModel: number().nullable().defined(),
  portfolioModelId: number().nullable().defined(),
});

export type FormValues = {
  advisorId: Nullable<number>;
  feeSplitTemplateId: Nullable<number>;
  feeModel: Nullable<number>;
  portfolioModelId: Nullable<number>;
  provider: Nullable<number>;
  accountNumber: Nullable<string>;
  subAccountNumber: Nullable<string>;
  status: Nullable<number>;
  clients: number[];
  additionalInfo: string;
};
