import { apiClient } from '@aventur-shared/services/api';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { type Account } from '@aventur-shared/modules/accounts';
import { InferType, array, boolean, number, object, string } from 'yup';
import { SingleAccount } from '@aventur-shared/modules/accounts/models/account';
import { ClientId } from '@aventur-shared/modules/clients';

type SupportedSections = 'has_plan_information' | 'has_investment_mix';
type ProductLayouts =
  | 'pension_layout'
  | 'protection_layout'
  | 'investment_layout'
  | 'pension_income_paying_layout';

export interface Body {
  account_number: string;
  sub_account_number: string;
  adviser_id: number | null;
  account_status_id: number;
  type_id: number;
  fee_split_template_id: number | null;
  fee_model: number | null;
  portfolio_model_id: number | null;
  provider_id: number;
  clients: number[];
  additional_info: string;
}

const getAccountSchema = object({
  id: number().required(),
  provider_id: number().required(),
  adviser: object({
    id: number().required(),
    first_name: string().required(),
    last_name: string().nullable().defined(),
  }).required(),
  type_id: number().required(),
  fee_split_template: object({
    id: number().required(),
    name: string().required(),
    is_active: boolean().required(),
  }).required(),
  fee_model: number().nullable(),
  portfolio_model_id: number().nullable().defined(),
  clients: array()
    .of(
      object({
        id: number().required(),
        first_name: string().required(),
        last_name: string().defined(),
      }),
    )
    .required(),
  acc_no: string().defined(),
  sub_acc_no: string().defined(),
  created: string().required(),
  status: object({
    id: number().required(),
    name: string().required(),
  }).required(),
  additional_info: string().defined(),
  supported_sections: array()
    .of(
      string()
        .oneOf<SupportedSections>([
          'has_plan_information',
          'has_investment_mix',
        ])
        .defined(),
    )
    .required(),
  product_layout: string()
    .oneOf<ProductLayouts>([
      'pension_layout',
      'protection_layout',
      'investment_layout',
      'pension_income_paying_layout',
    ])
    .defined()
    .nullable(),
});

type AccountDTO = InferType<typeof getAccountSchema>;

export default async (
  accountId: Account['id'],
  body: Body,
): Promise<SingleAccount> => {
  const response = await apiClient.patch<Body, unknown>(
    `/api/internal/v1/accounts/${accountId}`,
    body,
  );

  const accountDTO: AccountDTO = await getAccountSchema.validate(response);

  return {
    id: accountDTO.id,
    providerId: accountDTO.provider_id,
    advisor: {
      id: accountDTO.adviser.id,
      firstName: accountDTO.adviser.first_name,
      lastName: accountDTO.adviser.last_name,
    },
    typeId: accountDTO.type_id,
    feeSplitTemplate: accountDTO.fee_split_template
      ? {
          id: accountDTO.fee_split_template.id,
          name: accountDTO.fee_split_template.name,
          isActive: accountDTO.fee_split_template.is_active,
        }
      : null,
    feeModel: accountDTO.fee_model ?? null,
    portfolioModelId: accountDTO.portfolio_model_id ?? null,
    clients: accountDTO.clients.map((client) => ({
      id: client.id as ClientId,
      firstName: client.first_name,
      lastName: client.last_name,
    })),
    accountNumber: accountDTO.acc_no,
    subAccountNumber: accountDTO.sub_acc_no,
    created: new DateTime(accountDTO.created),
    status: accountDTO.status,
    additionalInfo: accountDTO.additional_info,
    hasPlanInformation: accountDTO.supported_sections.includes(
      'has_plan_information',
    ),
    hasInvestmentMix:
      accountDTO.supported_sections.includes('has_investment_mix'),
    productLayout: accountDTO.product_layout,
  };
};
