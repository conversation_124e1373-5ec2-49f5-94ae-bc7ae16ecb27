import { AccountId } from '@aventur-shared/modules/accounts';
import { default as updateInvestmentConstituentsRequest } from './api-request';

interface Model {
  constituents: Array<{
    id?: number;
    date: string;
    ISIN: string;
    fund: string;
    value: string;
    fundCharge: string;
    investedPercentage: string;
    weightedCharge: string;
  }>;
}

export default async (accountId: AccountId, planId: number, model: Model) => {
  try {
    await updateInvestmentConstituentsRequest(accountId, planId, {
      constituents: model.constituents.map((line) => ({
        id: line.id,
        date: line.date,
        fund_name: line.fund,
        fund_charge: line.fundCharge,
        isin: line.ISIN,
        value: line.value,
      })),
    });
  } catch (e) {
    throw new Error('Could not save investment constituents');
  }
};
