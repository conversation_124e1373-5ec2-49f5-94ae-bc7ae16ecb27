import { apiClient } from '@aventur-shared/services/api';
import { AccountId } from '@aventur-shared/modules/accounts';

interface Body {
  constituents: Array<{
    id?: number;
    date: string;
    isin: string;
    fund_name: string;
    value: string;
    fund_charge: string;
  }>;
}

export default async (accountId: AccountId, planId: number, body: Body) => {
  await apiClient.post<Body, Promise<void>>(
    `/api/v1/holdings/${accountId}/plan_information/${planId}/constituents`,
    body,
  );
};
