import { apiClient } from '@aventur-shared/services/api';
import { AccountId } from '@aventur-shared/modules/accounts';

interface Constituents {
  id?: number;
  date: string;
  isin: string;
  fund_name: string;
  value: string;
  fund_charge: string;
}

interface ChargesFigures {
  charges_plan_amc: number | null;
  charges_fund_amc: number | null;
  charges_dfm_fee: number | null;
  any_additional_pounds_fee_pa: number | null;
  annual_overall_charge_pa_percentage_figures: number | null;
}

interface ChargesProjections {
  account_commencement_date: string | null;
  retirement_date: string | null;
  date_of_projection: string | null;
  retirement_date_on_projections: string | null;
  rate_of_projection: number | null;
  projection_figure_pounds: number | null;
}

interface SafeguardingBenefits {
  guaranteed_annuity_rate_gar: boolean;
  guaranteed_min_pension_gmp: boolean;
  is_tfc_greater_than_25_percent: boolean;
  any_loyalty_bonus: boolean;
  protected_retirement_age: boolean;
}

interface PensionInfo {
  adviser_remuneration_allowable: boolean;
  available_funds_for_switch_on_file: boolean;
  contributing_history_on_file: boolean;
  death_benefits_on_file: boolean;
  discharge_forms_on_file: boolean;
  drawdown_available: boolean;
  life_cover: boolean;
  nominee_flexi_access_drawdown_available: boolean;
  switching_strategy_on_file: boolean;
  ufpls_available: boolean;
  with_profits: boolean;
}

interface InvestmentInfo {
  with_profits: boolean;
  contributing_history_on_file: boolean;
  available_funds_for_switch_on_file: boolean;
  adviser_remuneration_allowable: boolean;
  withdrawal_schedule_on_file: boolean;
  transaction_cgt_report_on_file: boolean;
  wrapper_structure_details_on_file: boolean;
  segmentation_structure_history_on_file: boolean;
}

interface PensionIncomeInfo {
  death_benefits_on_file: boolean;
  details_of_scheme_on_file: boolean;
  details_of_pensionable_service_on_file: boolean;
}

interface ProtectionInfo {
  adviser_remuneration_allowable: boolean;
  insurance_type_details_on_file: boolean;
  details_of_trust_on_file: boolean;
  insurance_cost_details_on_file: boolean;
  details_of_person_insured_on_file: boolean;
  insurance_history_details_on_file: boolean;
  latest_policy_statement_on_file: boolean;
}

interface PensionPlanInformationBody {
  date_of_information: string;
  data_layout: string | null;
  notes: string | null;
  fund_value?: number | null;
  transfer_value?: number | null;
  portfolio_model?: string;
  charges_figures?: ChargesFigures;
  constituents?: Constituents[];
  charges_projections?: ChargesProjections;
  safeguarding_benefits?: SafeguardingBenefits;
  pension_info?: PensionInfo;
}

interface InvestmentPlanInformationBody {
  date_of_information: string;
  data_layout: string | null;
  notes: string | null;
  fund_value?: number | null;
  transfer_value?: number | null;
  portfolio_model?: string;
  charges_figures?: ChargesFigures;
  constituents?: Constituents[];
  investment_info?: InvestmentInfo;
}

interface ProtectionPlanInformationBody {
  date_of_information: string;
  data_layout: string | null;
  notes: string | null;
  protection_info?: ProtectionInfo;
}

interface PensionIncomePlanInformationBody {
  date_of_information: string;
  data_layout: string | null;
  notes: string | null;
  pension_income_paying_info?: PensionIncomeInfo;
}

export default async (
  accountId: AccountId,
  body:
    | PensionPlanInformationBody
    | InvestmentPlanInformationBody
    | ProtectionPlanInformationBody
    | PensionIncomePlanInformationBody,
) => {
  return await apiClient.post<object, Promise<number>>(
    `/api/v2/holdings/${accountId}/plan_information`,
    body,
  );
};
