import { AccountId } from '@aventur-shared/modules/accounts/types';
import { mapToDto } from '@aventur-shared/modules/accounts/models';
import { default as createPlanInformation } from './api-request';
import { AllFormValues } from '@aventur-shared/modules/accounts/models/form-model';

export default async (
  accountId: AccountId,
  model: AllFormValues,
  product_layout,
) => await createPlanInformation(accountId, mapToDto(model, product_layout));
