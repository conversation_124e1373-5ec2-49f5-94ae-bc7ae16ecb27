<template>
  <div class="flex flex-col">
    <form @submit="onSubmit">
      <box class="mb-8">
        <box-section title="Account details" :divider="'bottom'">
          <account-details-fields
            :account="account"
            @advisor-select="handleAdvisorSelect"
          />
        </box-section>
        <box-section title="Fee details" :divider="'bottom'">
          <div class="mt-5 grid grid-cols-1 gap-5 lg:grid-cols-2">
            <select-field
              label="Fee Split Template"
              name="feeSplitTemplateId"
              :show-warning="showInactiveTemplateMessage"
              warning-message="Please replace the template"
              :options="feeSplitTemplateSelectOptions"
              :hide-disabled-options="true"
              :is-busy="unref(setProvidersFeeSplitTemplatesAction.isPending)"
              is-busy-text="Fetching fee split templates.."
              :disabled="
                !values.advisorId ||
                unref(setProvidersFeeSplitTemplatesAction.isPending)
              "
            />
            <select-field
              label="Current Fee Model"
              name="feeModel"
              :options="FeeModelOptions"
              :disabled="!isSuperAdmin"
            />
          </div>
        </box-section>
      </box>
      <div class="text-end">
        <custom-button type="submit" theme="primary"> Save </custom-button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, toRef, unref, watch } from 'vue';
  import { onBeforeRouteLeave } from 'vue-router';
  import { useForm, useIsFormDirty, useIsFormValid } from 'vee-validate';
  import { Box, BoxSection } from '@modules/ui';
  import { Button as CustomButton } from '@modules/ui';
  import { SelectField } from '@aventur-shared/components/form';
  import { useRefData } from '@aventur-shared/stores';
  import { useUserStore } from '@aventur-shared/modules/users';
  import {
    SingleAccount,
    feeModelToSelectOptions,
  } from '@aventur-shared/modules/accounts';
  import { advisorsProvider } from '@aventur-shared/modules/advisors';
  import {
    FeeSplitTemplate,
    useFeeSplitTemplates,
  } from '@aventur-shared/modules/refdata/fee-split-template';
  import { useUserInterfaceAction } from '@aventur-shared/utils/ui/ui-action';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import {
    FormValues,
    validationSchema,
  } from '@modules/accounts/use-cases/user-updates-account';
  import { default as AccountDetailsFields } from './account-details-fields.vue';

  const props = defineProps<{
    account: SingleAccount;
  }>();

  const emit = defineEmits<{
    (e: 'on-update', formValues: FormValues): void;
  }>();

  const account = toRef(props, 'account');
  const advisor = ref<number>();
  const { getHoldingStatuses } = useRefData();
  const { isSuperAdmin } = useUserStore();
  const setProvidersFeeSplitTemplatesAction = useUserInterfaceAction<
    FeeSplitTemplate[]
  >([]);

  const isTemplateInactive = computed(
    () => !account.value.feeSplitTemplate?.isActive,
  );

  const showInactiveTemplateMessage = computed(
    () =>
      isTemplateInactive.value &&
      values.feeSplitTemplateId === account.value.feeSplitTemplate?.id,
  );

  const feeSplitTemplateSelectOptions = computed<SelectOption[]>(() => {
    const activeFeeSplitTemplates: SelectOption[] =
      setProvidersFeeSplitTemplatesAction.data.value.map((template) => ({
        value: template.id,
        label: template.name,
      }));
    if (isTemplateInactive.value) {
      activeFeeSplitTemplates.push({
        label: account.value.feeSplitTemplate?.name as string,
        value: account.value.feeSplitTemplate?.id as number,
        disabled: true,
      });
    }
    return activeFeeSplitTemplates;
  });

  const { handleSubmit, setFieldError, setFieldValue, values, resetForm } =
    useForm<FormValues>({
      validationSchema,
      initialValues: getInitialValues(),
    });
  const isFormDirty = useIsFormDirty();
  const isFormValid = useIsFormValid();

  const { feeSplitTemplates, fetchForAdvisor } = useFeeSplitTemplates();
  const FeeModelOptions = feeModelToSelectOptions();

  watch(advisor, async () => {
    await getAdvisorsFeeSplitTemplates();
  });

  watch(
    () => props.account,
    () => {
      resetForm({
        values: getInitialValues(),
      });
    },
  );

  onMounted(async () => {
    await advisorsProvider().create();
    advisor.value = account.value.advisor?.id;
  });

  onBeforeRouteLeave(() => {
    if (isFormValid.value && isFormDirty.value) {
      return window.confirm(
        `Are you sure you want to leave? Your data won’t be saved.`,
      );
    }
    return true;
  });

  function handleAdvisorSelect(selectedAdvisor: number) {
    advisor.value = selectedAdvisor;
    setFieldValue('feeSplitTemplateId', null);
  }

  async function getAdvisorsFeeSplitTemplates() {
    if (!advisor.value) return;
    setProvidersFeeSplitTemplatesAction.start();
    try {
      await fetchForAdvisor(advisor.value as number);
      setProvidersFeeSplitTemplatesAction.resolved(feeSplitTemplates.value);
    } catch (e) {
      if (e instanceof Error) {
        setProvidersFeeSplitTemplatesAction.rejected(new Error(e.message));
        setFieldError('feeSplitTemplateId', e.message);
      }
    }
  }

  function getInitialValues() {
    return {
      advisorId: account.value.advisor?.id ?? null,
      provider: account.value.providerId,
      feeSplitTemplateId: account.value.feeSplitTemplate?.id ?? null,
      typeId: account.value.typeId,
      status:
        getHoldingStatuses.find(
          (status) => status.id === account.value.status.id,
        )?.id ?? null,
      accountNumber: account.value.accountNumber,
      subAccountNumber: account.value.subAccountNumber,
      clients: account.value.clients.map((client) => client.id),
      additionalInfo: account.value.additionalInfo,
      feeModel: account.value.feeModel,
      portfolioModelId: account.value.portfolioModelId,
    };
  }

  const onSubmit = handleSubmit(async (formValues) => {
    emit('on-update', formValues);
  });
</script>
