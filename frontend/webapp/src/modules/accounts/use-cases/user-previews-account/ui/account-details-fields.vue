<template>
  <div class="mt-5 grid grid-cols-1 gap-5 lg:grid-cols-2">
    <multi-select-field name="clients" label="Clients" :options="clientList" />
    <select-field
      label="Advisor"
      name="advisorId"
      :options="getAdvisorsSelectOptions(activeAdvisors)"
      @on-select="(advisorId: number) => $emit('advisor-select', advisorId)"
    />
    <select-field
      label="Provider"
      name="provider"
      :options="
        getProviders.map((provider) => ({
          label: provider.name,
          value: provider.id,
        }))
      "
    />
    <select-field
      label="Account Type"
      name="typeId"
      :disabled="true"
      :options="
        getProductTypes.map((product) => ({
          label: product.name,
          value: product.id,
        })) || []
      "
    />
    <select-field
      label="Account Status"
      name="status"
      :options="
        getHoldingStatuses.map((s) => ({
          value: s.id,
          label: s.name,
        }))
      "
    />
    <text-field label="Account Number" name="accountNumber" />
    <text-field
      v-if="
        account.typeId &&
        getAccountProducts.map((p) => p.id).includes(account.typeId)
      "
      label="Sub Account Number"
      name="subAccountNumber"
    />
    <text-area-field
      class="lg:col-start-1 lg:col-end-3"
      name="additionalInfo"
      label="Additional Info"
      placeholder="Add additional info"
    />
  </div>
</template>

<script setup lang="ts">
  import { computed, toRef } from 'vue';
  import { formatName } from '@aventur-shared/utils/user';
  import { useRefData } from '@aventur-shared/stores/refdataStore';
  import { useProducts } from '@aventur-shared/composables/useProducts';
  import { useClientStore } from '@aventur-shared/modules/clients';
  import { SingleAccount } from '@aventur-shared/modules/accounts';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';
  import {
    MultiSelectField,
    SelectField,
    TextAreaField,
    TextField,
  } from '@aventur-shared/components/form';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';

  const props = defineProps<{
    account: SingleAccount;
  }>();

  defineEmits<{
    (e: 'advisor-select', advisorId: number): void;
  }>();

  const { getAccountProducts } = useProducts();
  const { activeAdvisors } = advisorsProvider().provide();
  const { getClientId, getProfile: client } = useClientStore();
  const { getProviders, getProductTypes, getHoldingStatuses } = useRefData();
  const { linkedClients } = useLinkedClientList(toRef(client));

  const clientList = computed<SelectOption[]>(() => {
    if (getClientId) {
      return linkedClients.value;
    }

    return props.account.clients.map(($client) => ({
      value: $client.id,
      label: formatName($client),
      disabled: true,
    }));
  });
</script>
