<template>
  <div>
    <select-field
      label="Status"
      name="account-status-filter"
      :value="String(modelValue)"
      :can-clear="true"
      :options="
        getHoldingStatuses.map((status) => ({
          value: String(status.id),
          label: status.name,
        }))
      "
      @on-select="handlePick"
    />
  </div>
</template>

<script setup lang="ts">
  import { useRefData } from '@aventur-shared/stores';
  import { SelectField } from '@aventur-shared/components/form';
  import { AccountStatusEnum } from '@aventur-shared/modules/accounts';

  const emit = defineEmits(['update:modelValue']);
  const props = defineProps<{
    modelValue?: `${AccountStatusEnum}`;
  }>();

  const { getHoldingStatuses } = useRefData();

  const handlePick = (status?: string) => {
    if (String(props.modelValue) === status) return;
    emit('update:modelValue', status);
  };
</script>
