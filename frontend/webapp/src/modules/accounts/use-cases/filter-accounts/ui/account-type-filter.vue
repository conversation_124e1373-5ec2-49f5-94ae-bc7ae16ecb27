<template>
  <div>
    <select-field
      label="Type"
      name="account-type-filter"
      :value="String(modelValue)"
      :can-clear="true"
      :options="accountTypeOptions"
      @on-select="handlePick"
    />
  </div>
</template>

<script setup lang="ts">
  import { useRefData } from '@aventur-shared/stores';
  import { ProductType } from '@/models/refdata';
  import { SelectField } from '@aventur-shared/components/form';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { AccountType } from '@aventur-shared/modules/accounts';

  const emit = defineEmits(['update:modelValue']);
  const props = defineProps<{
    modelValue?: `${ReturnType<AccountType['toNumber']>}`;
  }>();

  const { getProductTypes } = useRefData();

  const accountTypeOptions: SelectOption[] = Object.values(getProductTypes)
    .filter((item: ProductType) =>
      [
        'credit_card',
        'account',
        'mortgage',
        'personal_loan',
        'other_debt',
      ].includes(item.type),
    )
    .map((item: ProductType) => ({
      value: String(item.id),
      label: item.name,
    }));

  const handlePick = (status?: string) => {
    if (String(props.modelValue) === status) return;
    emit('update:modelValue', status);
  };
</script>
