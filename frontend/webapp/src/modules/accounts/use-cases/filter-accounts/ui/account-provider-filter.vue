<template>
  <div>
    <select-field
      label="Provider"
      name="account-advisor-filter"
      :value="modelValue"
      :can-clear="true"
      :options="
        getProviders.map((provider) => ({
          label: provider.name,
          value: provider.id,
        }))
      "
      @on-select="handlePick"
    />
  </div>
</template>

<script setup lang="ts">
  import { useRefData } from '@aventur-shared/stores';
  import { SelectField } from '@aventur-shared/components/form';
  import { storeToRefs } from 'pinia';
  import { AccountProviderId } from '@aventur-shared/modules/accounts/models/account';

  const emit = defineEmits(['update:modelValue']);
  const props = defineProps<{
    modelValue?: `${AccountProviderId}`;
  }>();

  const { getProviders } = storeToRefs(useRefData());

  const handlePick = (status?: number) => {
    if (status && props.modelValue === status.toString()) return;
    emit('update:modelValue', status);
  };
</script>
