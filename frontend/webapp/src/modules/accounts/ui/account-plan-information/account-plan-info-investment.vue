<template>
  <box class="mb-8 rounded-t-none">
    <box-section title="Portfolio Model" :divider="'bottom'">
      <account-plan-info-portfolio-model></account-plan-info-portfolio-model>
    </box-section>
    <box-section title="Investment constituents" :divider="'bottom'">
      <account-plan-info-investment-constituents
        :account="props.account"
        :selected-plan-date="selectedPlanDate"
      >
      </account-plan-info-investment-constituents>
    </box-section>
    <box-section title="Charges" :divider="'bottom'">
      <div class="grid grid-cols-1 gap-5 lg:grid-cols-2">
        <account-charges-figures></account-charges-figures>
      </div>
    </box-section>
    <box-section title="Other information">
      <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
        <radio-group
          label="Is there any With Profits?"
          name="otherInvestmentInfo.withProfits"
          :options="selectOptions"
        />
        <radio-group
          label="Contribution History on file?"
          name="otherInvestmentInfo.contributingHistoryOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="List of available funds to switch into on file?"
          name="otherInvestmentInfo.availableFundsForSwitchOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Adviser remuneration allowable"
          name="otherInvestmentInfo.adviserRemunerationAllowable"
          :options="selectOptions"
        />

        <radio-group
          label="GIA & Bonds - Schedule of Withdrawals on file?"
          name="otherInvestmentInfo.withdrawalScheduleOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="GIA - Copy of latest transactional/CGT report on file?"
          name="otherInvestmentInfo.transactionCGTReportOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Bond - Trust wrapper & structure details on file?"
          name="otherInvestmentInfo.wrapperStructureDetailsOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Bond - Segmentation structure & history on file?"
          name="otherInvestmentInfo.segmentationStructureHistoryOnFile"
          :options="selectOptions"
        />
      </div>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { Box, BoxSection } from '@modules/ui';
  import AccountChargesFigures from '@modules/accounts/ui/account-plan-information/form/account-charges-figures.vue';
  import { SingleAccount } from '@aventur-shared/modules/accounts';
  import AccountPlanInfoInvestmentConstituents from '@modules/accounts/ui/account-plan-information/form/account-plan-info-investment-constituents.vue';
  import { selectOptions } from '@modules/accounts/ui/account-plan-information/options';
  import { RadioGroup } from '@aventur-shared/components/form';
  import AccountPlanInfoPortfolioModel from '@modules/accounts/ui/account-plan-information/account-plan-info-portfolio-model.vue';

  const props = defineProps<{
    account: SingleAccount;
    selectedPlanDate: string | undefined;
  }>();
</script>
