<template>
  <div v-if="loadedPlanInfo" class="flex flex-col">
    <box class="rounded-b-none">
      <box-section :divider="'bottom'">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div class="col-span-2 flex flex-row gap-2">
            <select-field
              class="w-1/2"
              label="Select plan information"
              name="plan-select"
              data-testid="plan-select"
              :model-value="selectedPlanId"
              :options="planInformationSelectOptions"
              :can-deselect="false"
              @on-select="handlePlanSelect"
            />
            <custom-button
              v-if="planInformation.length > 0 && !isNewPlan"
              theme="primary-ghost"
              class="mb-4 self-end"
              @on-click="handlePlanSelect('NEW')"
              >Add new
            </custom-button>
          </div>
        </div>
      </box-section>
    </box>
    <account-plan-info-form
      :key="selectedPlanId"
      :account="account"
      :product-layout="account.productLayout"
      :plan-info="currentPlanInformation"
      @create="createPlan"
      @update="updatePlan"
    ></account-plan-info-form>
  </div>
</template>
<script lang="ts" setup>
  import { Box, BoxSection, Button as CustomButton } from '@modules/ui';
  import { SelectField } from '@aventur-shared/components/form';
  import { SingleAccount } from '@aventur-shared/modules/accounts';
  import { usePlanSelector } from '@modules/accounts/composables/usePlanSelector';
  import AccountPlanInfoForm from '@modules/accounts/ui/account-plan-information/account-plan-info-form.vue';

  const props = defineProps<{
    account: SingleAccount;
  }>();

  const {
    newPlanId,
    isNewPlan,
    selectedPlanId,
    planInformation,
    planInformationSelectOptions,
    loadedPlanInfo,
    currentPlanInformation,
    getPlanInfoData,
  } = usePlanSelector(props.account, props.account.productLayout ?? '');

  const handlePlanSelect = (planId: number | undefined | typeof newPlanId) => {
    if (planId != undefined) {
      selectedPlanId.value = planId;
    } else {
      selectedPlanId.value = newPlanId;
    }
  };
  const createPlan = async (planId: number) => {
    await getPlanInfoData();
    selectedPlanId.value = planId;
  };
  const updatePlan = async () => {
    await getPlanInfoData();
  };
</script>
