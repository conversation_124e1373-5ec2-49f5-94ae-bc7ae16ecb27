<template>
  <box class="mb-8 rounded-t-none">
    <box-section title="Protection" :divider="'bottom'">
      <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
        <radio-group
          label="Death Benefits Information on file?"
          name="otherPensionIncomePaying.deathBenefitsOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Details of scheme name & member section on file?"
          name="otherPensionIncomePaying.detailsOfSchemeOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Details dates of pensionable service on file?"
          name="otherPensionIncomePaying.detailsOfPensionableServiceOnFile"
          :options="selectOptions"
        />
      </div>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { Box, BoxSection } from '@modules/ui';
  import { RadioGroup } from '@aventur-shared/components/form';
  import { selectOptions } from '@modules/accounts/ui/account-plan-information/options';
</script>
