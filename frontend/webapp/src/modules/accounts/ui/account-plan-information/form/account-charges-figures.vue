<template>
  <box-section no-padding-x>
    <h3 class="my-3 font-medium text-gray-500">Using figures</h3>
    <text-field
      label="Cost of Provider %"
      name="chargesFigures.chargesPlanAMC"
    />
    <text-field
      label="Cost of Provider (additional annual fee £ PA)"
      name="chargesFigures.additionalfeePAAmount"
    />
    <text-field label="Cost of Funds %" name="chargesFigures.chargesFundAMC" />
    <text-field
      label="Cost of Investment Management %"
      name="chargesFigures.chargesDFMfee"
    />
    <text-field
      label="Annual Overall Charge PA, %"
      name="chargesFigures.annualOverallChargePAPercentageFigures"
      :disabled="true"
    />
  </box-section>
</template>

<script lang="ts" setup>
  import { debounce, isEqual } from 'lodash';
  import { useField } from 'vee-validate';
  import { TextField } from '@aventur-shared/components/form';
  import { BoxSection } from '@/modules/ui';
  import { watch } from 'vue';
  import { getAnnualOverallChargeUsingFigures } from '@aventur-shared/modules/accounts';
  import { valueToFormattedNumber } from '@aventur-shared/utils/string/numberFormatting';

  const { value: fundValueValue, meta: fundValueMeta } =
    useField<number>('fundValue');
  const { value: chargesPlanAMCValue, meta: chargesPlanAMCMeta } =
    useField<number>('chargesFigures.chargesPlanAMC');
  const { value: additionalfeePAAmountValue, meta: additionalfeePAAmountMeta } =
    useField<number>('chargesFigures.additionalfeePAAmount');
  const { value: chargesFundAMCValue, meta: chargesFundAMCMeta } =
    useField<number>('chargesFigures.chargesFundAMC');
  const { value: chargesDFMfeeValue, meta: chargesDFMfeeMeta } =
    useField<number>('chargesFigures.chargesDFMfee');
  const { value: annualOverallChargePAPercentageFiguresValue } =
    useField<string>('chargesFigures.annualOverallChargePAPercentageFigures');

  watch(
    [
      fundValueValue,
      chargesPlanAMCValue,
      additionalfeePAAmountValue,
      chargesFundAMCValue,
      chargesDFMfeeValue,
    ],
    debounce(async (newVal, oldVal) => {
      if (
        fundValueMeta.valid &&
        chargesPlanAMCMeta.valid &&
        additionalfeePAAmountMeta.valid &&
        chargesFundAMCMeta.valid &&
        chargesDFMfeeMeta.valid &&
        newVal.every((item) => item !== null)
      ) {
        if (!isEqual(newVal, oldVal)) {
          annualOverallChargePAPercentageFiguresValue.value =
            await getAnnualOverallChargeUsingFigures({
              charges_plan_amc: valueToFormattedNumber(
                chargesPlanAMCValue.value,
              ),
              charges_fund_amc: valueToFormattedNumber(
                chargesFundAMCValue.value,
              ),
              charges_dfm_fee: valueToFormattedNumber(chargesDFMfeeValue.value),
              additional_fee_amount: valueToFormattedNumber(
                additionalfeePAAmountValue.value,
              ),
              fund_value: valueToFormattedNumber(fundValueValue.value),
            });
        }
      } else {
        annualOverallChargePAPercentageFiguresValue.value = '';
      }
    }, 500),
  );
</script>
