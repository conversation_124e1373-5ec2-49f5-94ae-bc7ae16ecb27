<template>
  <div ref="componentRef" class="relative">
    <text-field
      v-model="searchQuery"
      class="!mb-0"
      :name="props.name"
      :label="props.label"
      autocomplete="off"
      @keydown.down.prevent="incrementIndex"
      @keydown.up.prevent="decrementIndex"
      @keydown.enter.prevent="selectOption"
      @keydown.esc.prevent="isOpen = false"
      @on-change="handleUserInput($event)"
    />

    <div
      v-if="isOpen"
      id="autocomplete-dropdown"
      class="absolute z-50 mt-1 max-h-[272px] min-w-full max-w-full overflow-y-auto overflow-x-clip rounded-lg border border-gray-200 bg-white shadow-lg sm:max-w-full md:max-w-full lg:max-w-[600%]"
      @mouseenter="selectedIndex = null"
    >
      <ul>
        <li
          v-for="(option, index) in filteredOptions"
          :key="index"
          :class="[
            'flex cursor-pointer items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100',
            selectedIndex === index ? 'bg-gray-100' : '',
          ]"
          @click="selectOptionByClick(option)"
        >
          <div class="flex w-full flex-col gap-1">
            <div class="whitespace-nowrap text-left font-bold">
              {{ option.code }}
            </div>
            <div class="text-left">{{ option.name }}</div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue';
  import { TextField } from '@aventur-shared/components/form';
  import {
    SymbolDetails,
    getSymbolCodeCompletions,
    getSymbolNameCompletions,
  } from '@aventur-shared/modules/accounts';
  import { debounce } from 'lodash';

  const props = defineProps<{
    name: string;
    label: string;
    modelValue: string;
    isIsinField: boolean;
  }>();

  const emit = defineEmits<{
    (e: 'on-symbol-pick', value: SymbolDetails): void;
    (e: 'update:modelValue', value: string): void;
  }>();

  const filteredOptions = ref<SymbolDetails[]>([]);
  const componentRef = ref<HTMLElement | null>(null);
  const isOpen = ref(false);
  const selectedIndex = ref<number | null>(null);

  const searchQuery = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  const handleUserInput = debounce(async (event: any) => {
    const input = event.value;
    const MIN_SEARCH_LENGTH = 3;

    const resetState = () => {
      filteredOptions.value = [];
      isOpen.value = false;
      selectedIndex.value = null;
    };

    if (input.length < MIN_SEARCH_LENGTH) {
      resetState();
      return;
    }

    const matchedSymbols: SymbolDetails[] = await (props.isIsinField
      ? getSymbolCodeCompletions(input, 20)
      : getSymbolNameCompletions(input, 20));

    filteredOptions.value = matchedSymbols.map(({ code, name }) => ({
      code,
      name,
    }));

    isOpen.value = matchedSymbols.length > 0;
  }, 500);

  // Keyboard navigation methods
  const incrementIndex = () => {
    if (selectedIndex.value === null) {
      selectedIndex.value = 0;
    } else if (selectedIndex.value < filteredOptions.value.length - 1) {
      selectedIndex.value++;
    } else {
      selectedIndex.value = 0;
    }
    scrollSelectedIntoView();
  };

  const decrementIndex = () => {
    if (selectedIndex.value === null) {
      selectedIndex.value = filteredOptions.value.length - 1;
    } else if (selectedIndex.value > 0) {
      selectedIndex.value--;
    } else {
      selectedIndex.value = filteredOptions.value.length - 1;
    }
    scrollSelectedIntoView();
  };

  const scrollSelectedIntoView = () => {
    if (selectedIndex.value === null) return;

    nextTick(() => {
      const container = document.getElementById('autocomplete-dropdown');
      const selectedElement = container?.querySelector(
        `li:nth-child(${selectedIndex.value! + 1})`, // This is safe because of check on ln 140
      );

      if (container && selectedElement) {
        const containerRect = container.getBoundingClientRect();
        const selectedRect = selectedElement.getBoundingClientRect();

        if (selectedRect.bottom > containerRect.bottom) {
          container.scrollTop += selectedRect.bottom - containerRect.bottom;
        } else if (selectedRect.top < containerRect.top) {
          container.scrollTop -= containerRect.top - selectedRect.top;
        }
      }
    });
  };

  const selectOption = () => {
    if (
      selectedIndex.value !== null &&
      filteredOptions.value[selectedIndex.value]
    ) {
      const selected = filteredOptions.value[selectedIndex.value];
      searchQuery.value = selected.code;
      isOpen.value = false;
      filteredOptions.value = [];
      selectedIndex.value = null;

      emit('on-symbol-pick', selected);
    }
  };

  const selectOptionByClick = (option: SymbolDetails) => {
    searchQuery.value = option.code;
    isOpen.value = false;
    filteredOptions.value = [];

    emit('on-symbol-pick', option);
  };

  // Close dropdown when clicking outside
  const handleClickOutside = (event: MouseEvent) => {
    if (!componentRef.value || !event.target) return;

    // Check if the click is outside the component
    if (!componentRef.value.contains(event.target as Node)) {
      isOpen.value = false;
    }
  };

  // Add event listener for click outside
  onMounted(() => {
    document.addEventListener('click', handleClickOutside);
  });

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
  });
</script>
