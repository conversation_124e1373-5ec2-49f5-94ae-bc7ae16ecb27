<template>
  <box-section no-padding-x>
    <h3 class="my-3 font-medium text-gray-500">Using projections</h3>
    <date-picker
      label="Commencement Date"
      name="chargesProjections.commencementDate"
    />
    <date-picker
      label="Retirement Date"
      name="chargesProjections.retirementDate"
    />
    <date-picker
      label="Date of projection"
      name="chargesProjections.dateOfProjection"
    />
    <date-picker
      label="Retirement Date on Projection"
      name="chargesProjections.retirementDateOnProjection"
    />
    <text-field
      label="Rate of Projection %"
      name="chargesProjections.rateOfProjection"
    />
    <text-field
      label="Projection Figure £ at Retirement Age"
      name="chargesProjections.projectionFigure"
    />
    <text-field
      label="Annual Overall Charge PA %"
      name="chargesProjections.annualOverallChargePAPercentageProjections"
      :disabled="true"
    />
  </box-section>
</template>

<script lang="ts" setup>
  import { watch } from 'vue';
  import { debounce, isEqual } from 'lodash';
  import { useField } from 'vee-validate';
  import { DatePicker, TextField } from '@aventur-shared/components/form';
  import { BoxSection } from '@/modules/ui';
  import { getAnnualOverallChargeUsingProjections } from '@aventur-shared/modules/accounts';
  import { valueToFormattedNumber } from '@aventur-shared/utils/string/numberFormatting';

  const { value: fundValueValue, meta: fundValueMeta } =
    useField<number>('fundValue');
  const { value: rateOfProjectionValue, meta: rateOfProjectionMeta } =
    useField<number>('chargesProjections.rateOfProjection');
  const { value: dateOfProjectionValue, meta: dateOfProjectionMeta } =
    useField<string>('chargesProjections.dateOfProjection');
  const {
    value: retirementDateOnProjectionValue,
    meta: retirementDateOnProjectionMeta,
  } = useField<string>('chargesProjections.retirementDateOnProjection');
  const { value: projectionFigureValue, meta: projectionFigureMeta } =
    useField<number>('chargesProjections.projectionFigure');
  const { value: annualOverallChargePAPercentageProjectionsValue } = useField<
    string | null
  >('chargesProjections.annualOverallChargePAPercentageProjections');

  watch(
    [
      dateOfProjectionValue,
      retirementDateOnProjectionValue,
      projectionFigureValue,
      rateOfProjectionValue,
      fundValueValue,
    ],
    debounce(async (newVal, oldVal) => {
      if (
        dateOfProjectionMeta.valid &&
        retirementDateOnProjectionMeta.valid &&
        projectionFigureMeta.valid &&
        rateOfProjectionMeta.valid &&
        fundValueMeta.valid &&
        newVal.every((item) => item !== null)
      ) {
        if (!isEqual(newVal, oldVal)) {
          annualOverallChargePAPercentageProjectionsValue.value =
            await getAnnualOverallChargeUsingProjections({
              retirement_date_on_projections:
                retirementDateOnProjectionValue.value,
              date_of_projection: dateOfProjectionValue.value,
              projection_figure_pounds: valueToFormattedNumber(
                projectionFigureValue.value,
              ),
              fund_value: valueToFormattedNumber(fundValueValue.value),
              rate_of_projection: valueToFormattedNumber(
                rateOfProjectionValue.value,
              ),
            });
        }
      } else {
        annualOverallChargePAPercentageProjectionsValue.value = null;
      }
    }, 500),
  );
</script>
