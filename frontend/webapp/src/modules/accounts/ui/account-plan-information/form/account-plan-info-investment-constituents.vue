<template>
  <div class="flex flex-col">
    <custom-button
      v-if="fields.length > 0"
      class="mb-4 self-end"
      theme="primary-ghost"
      data-testid="provider-sync-btn"
      :disabled="loadingConstituents"
      @on-click="removeAll"
    >
      Clear
    </custom-button>
    <custom-button
      v-if="canSyncWithProvider(account.providerId) && fields.length == 0"
      class="mb-4 self-end"
      theme="primary-ghost"
      data-testid="provider-sync-btn"
      :disabled="loadingConstituents"
      @on-click="syncProvider"
    >
      Sync with provider
    </custom-button>

    <LoadingOverlay :is-loading="loadingConstituents">
      <div
        v-for="(field, fieldIdx) in fields"
        :key="field.key"
        class="relative mb-5 grid grid-cols-1 gap-3 whitespace-nowrap rounded-xl border-2 border-gray-200 p-4 lg:grid-cols-7 lg:border-none lg:p-0"
      >
        <TrashIcon
          class="size-6 cursor-pointer place-self-end text-red-400 hover:text-red-800 lg:absolute lg:right-14 lg:top-3"
          :class="{ 'lg:top-9': !fieldIdx }"
          @click="remove(fieldIdx)"
        />
        <symbol-autocomplete-text-box
          :name="`constituents[${fieldIdx}].ISIN`"
          :label="`${!fieldIdx ? 'ISIN' : ''}`"
          :model-value="
            fields[fieldIdx].value.ISIN ? fields[fieldIdx].value.ISIN : ''
          "
          :is-isin-field="true"
          @update:model-value="(value) => (fields[fieldIdx].value.ISIN = value)"
          @on-symbol-pick="(symbol) => handleSymbolPick(symbol, fieldIdx)"
        />
        <symbol-autocomplete-text-box
          :name="`constituents[${fieldIdx}].fund`"
          :label="`${!fieldIdx ? 'Fund' : ''}`"
          :model-value="
            fields[fieldIdx].value.fund ? fields[fieldIdx].value.fund : ''
          "
          :is-isin-field="false"
          @update:model-value="(value) => (fields[fieldIdx].value.fund = value)"
          @on-symbol-pick="(symbol) => handleSymbolPick(symbol, fieldIdx)"
        />
        <text-field
          class="group text-right"
          :label="`${!fieldIdx ? 'Value' : ''}`"
          :name="`constituents[${fieldIdx}].value`"
        />
        <text-field
          class="group text-right"
          :disabled="!fields[fieldIdx].value"
          :label="`${!fieldIdx ? 'Fund charge %' : ''}`"
          :name="`constituents[${fieldIdx}].fundCharge`"
        />
        <text-field
          class="group text-right"
          :label="`${!fieldIdx ? 'Invested %' : ''}`"
          :name="`constituents[${fieldIdx}].investedPercentage`"
          :disabled="true"
        />
        <text-field
          class="group text-right"
          :label="`${!fieldIdx ? 'Cost of Funds (weighted)' : ''}`"
          :name="`constituents[${fieldIdx}].weightedCharge`"
          :disabled="true"
        />
      </div>
    </LoadingOverlay>

    <div>
      <div class="mb-4 flex flex-col items-center">
        <span v-if="!fields.length">No investment constituents added</span>
        <span>
          <custom-button
            data-testid="add-investment-constituents-row"
            theme="primary-ghost"
            @click="addFund"
          >
            Add new row
          </custom-button>
        </span>
      </div>
      <div
        v-show="fields.length"
        class="mb-4 flex flex-col gap-3 whitespace-nowrap py-5 lg:grid lg:grid-cols-4"
      >
        <text-field
          class="group text-right"
          label="Total Value"
          name="fundValue"
          :disabled="true"
        />
        <div class="flex flex-row items-center justify-between">
          <span> Different transfer value </span>
          <av-toggle v-model="differentTransferValue" label="" />
        </div>
        <text-field
          class="group text-right"
          label="Transfer Value"
          name="transferValue"
          :disabled="!differentTransferValue"
        />
        <text-field
          class="group text-right"
          label="Total Cost of Funds (weighted)"
          name="chargesFigures.chargesFundAMC"
          :disabled="true"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { useFieldArray, useSetFieldValue } from 'vee-validate';
  import { ref, watch } from 'vue';
  import { TrashIcon } from '@heroicons/vue/24/outline';
  import { TextField } from '@aventur-shared/components/form';
  import LoadingOverlay from '@aventur-shared/components/LoadingOverlay.vue';
  import { Button as CustomButton } from '@modules/ui';
  import {
    SingleAccount,
    getIsinFundCharge,
  } from '@aventur-shared/modules/accounts';
  import { useProviderSync } from '@modules/accounts/composables/useProviderSync';
  import { useInvestmentConstituentsCalculations } from '@modules/accounts/composables/useInvestmentConstituentsCalculations';
  import AvToggle from '@/components/AvToggle.vue';
  import SymbolAutocompleteTextBox from './symbol-autocomplete-text-box.vue';
  import { SymbolDetails } from '@aventur-shared/modules/accounts/api';

  const props = defineProps<{
    account: SingleAccount;
    selectedPlanDate: string | undefined;
  }>();

  interface ConstituentField {
    ISIN: string;
    fund: string;
    value: string;
    fundCharge: string;
    investedPercentage: string;
    weightedCharge: string;
  }

  const { fields, push, remove } =
    useFieldArray<ConstituentField>('constituents');

  const setTransferValue = useSetFieldValue('transferValue');

  const differentTransferValue = ref(false);

  const { valueSum } = useInvestmentConstituentsCalculations();

  watch([differentTransferValue, valueSum], ([newValue]) => {
    if (!newValue) {
      setTransferValue(valueSum.value);
    } else {
      setTransferValue('0');
    }
  });

  const addFund = () => {
    push({
      fund: '',
      value: '',
      fundCharge: '',
      investedPercentage: '',
      ISIN: '',
      weightedCharge: '',
    });
  };

  const removeAll = () => {
    const noOfConstituents = fields.value.length;
    for (let i = 0; i < noOfConstituents; i++) {
      remove(0);
    }
  };

  const { loadingConstituents, handleProviderSync, canSyncWithProvider } =
    useProviderSync();

  const syncProvider = async () => {
    const result = await handleProviderSync(
      props.account.id,
      props.selectedPlanDate,
    );
    result.forEach((item) => {
      push(item);
    });
  };

  const handleSymbolPick = async (symbol: SymbolDetails, index: number) => {
    fields.value[index].value.ISIN = symbol.code;
    fields.value[index].value.fund = symbol.name;
    try {
      const result = await getIsinFundCharge(symbol.code);
      fields.value[index].value.fundCharge = result !== null ? result : '';
    } catch (e) {
      return;
    }
  };
</script>
