<template>
  <form @submit="onSubmit">
    <box v-show="planInfo.id == 'NEW'" class="rounded-none">
      <box-section :divider="'bottom'">
        <div class="grid grid-cols-3 gap-4">
          <date-picker
            class="col-start-3"
            name="dateOfInformation"
            label="Date of information"
          />
        </div>
      </box-section>
    </box>
    <text-field v-show="false" label="Product Layout" name="productLayout" />
    <component
      :is="productLayoutMap[account.productLayout ?? '']"
      :account="account"
      :selected-plan-date="values.dateOfInformation"
    ></component>
    <text-area-field
      name="notes"
      label="Notes"
      rows="3"
      placeholder="Add note here..."
    />

    <div class="text-end">
      <custom-button type="submit" theme="primary" :disabled="!isFormValid">
        Save
      </custom-button>
    </div>
  </form>
</template>
<script setup lang="ts">
  import {
    DatePicker,
    TextAreaField,
    TextField,
  } from '@aventur-shared/components/form';
  import { Box, BoxSection, Button as CustomButton } from '@modules/ui';
  import { useForm, useIsFormDirty, useIsFormValid } from 'vee-validate';
  import { AllFormValues } from '@aventur-shared/modules/accounts/models/form-model';
  import { validationSchema } from '@modules/accounts/ui/account-plan-information/form-validation';
  import AccountPlanInfoInvestment from '@modules/accounts/ui/account-plan-information/account-plan-info-investment.vue';
  import AccountPlanInfoPension from '@modules/accounts/ui/account-plan-information/account-plan-info-pension.vue';
  import AccountPlanInfoPensionIncome from '@modules/accounts/ui/account-plan-information/account-plan-info-pension-income.vue';
  import AccountPlanInfoProtection from '@modules/accounts/ui/account-plan-information/account-plan-info-protection.vue';
  import { createPlanInformationAction } from '@modules/accounts/use-cases/user-creates-plan-information';
  import { updatePlanInformationAction } from '@modules/accounts/use-cases/user-updates-plan-information';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { SingleAccount } from '@aventur-shared/modules/accounts';
  import { onBeforeRouteLeave } from 'vue-router';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';

  const props = defineProps<{
    account: SingleAccount;
    planInfo: AllFormValues;
  }>();

  const emit = defineEmits<{
    (e: 'create', planId: number): void;
    (e: 'update'): void;
  }>();

  const toast = useToast();
  const { handleSubmit, values } = useForm<AllFormValues>({
    validationSchema: validationSchema[props.account.productLayout ?? ''],
    initialValues: props.planInfo,
  });
  const isFormDirty = useIsFormDirty();
  const isFormValid = useIsFormValid();

  const productLayoutMap = {
    investment_layout: AccountPlanInfoInvestment,
    pension_layout: AccountPlanInfoPension,
    pension_income_paying_layout: AccountPlanInfoPensionIncome,
    protection_layout: AccountPlanInfoProtection,
  };

  const onSubmit = handleSubmit(async (fv: AllFormValues) => {
    props.planInfo.id == 'NEW'
      ? await handleCreateNew(fv)
      : await handleUpdateExisting(fv);
  });

  const handleCreateNew = async (fv: AllFormValues) => {
    await createPlanInformationAction(
      props.account.id,
      fv,
      props.account.productLayout,
    )
      .then((res) => {
        toast.success('Plan information has been created');
        emit('create', res);
      })
      .catch((e) => {
        console.log(e);
        toast.error(e as Error, 'Could not create plan information');
      });
  };

  const handleUpdateExisting = async (fv: AllFormValues) => {
    await updatePlanInformationAction(
      props.account.id,
      props.planInfo.id as number,
      fv,
      props.account.productLayout,
    )
      .then(() => {
        toast.success('Plan information has been updated');
        emit('update');
      })
      .catch((e) => {
        toast.error(e as Error, 'Could not update plan information');
      });
  };

  onBeforeRouteLeave(async () => {
    if (isFormValid.value && isFormDirty.value) {
      const { isAccepted } = await useConfirmation(
        "Are you sure you want to leave? Your data won't be saved.",
      );

      return isAccepted();
    }

    return true;
  });
</script>
<style scoped></style>
