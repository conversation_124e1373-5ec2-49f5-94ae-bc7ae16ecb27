import { array, boolean, mixed, number, object, string } from 'yup';
import { validationMessages } from '@aventur-shared/utils/form';

const percentageField = mixed()
  .nullable()
  .checkIsNumber(validationMessages.invalidNumberMessage)
  .when({
    is: (value: string) => value,
    then: () =>
      number()
        .min(0, 'Value must be higher or equal to 0')
        .max(100, 'Value must be lower or equal to 100')
        .test(
          'maxDigitsAfterDecimal',
          'Number field must have 2 digits after decimal or less',
          (value) => /^\d+(\.\d{1,2})?$/.test(String(value)),
        ),
  });
const numberField = mixed()
  .nullable()
  .checkIsNumber(validationMessages.invalidNumberMessage)
  .when({
    is: (value: string) => value,
    then: () => number().nullable(),
  });

const stringField = string().nullable();
const booleanField = boolean()
  .nullable()
  .transform((value) => (value === '' ? null : value));
const dateField = string()
  .matches(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/, 'Date required')
  .required();
const chargesFiguresField = object({
  chargesPlanAMC: percentageField,
  chargesFundAMC: percentageField,
  chargesDFMfee: percentageField,
  additionalfeePAAmount: numberField,
  annualOverallChargePAPercentageFigures: numberField,
});
const chargesProjectionsField = object({
  commencementDate: stringField,
  retirementDate: stringField,
  dateOfProjection: stringField,
  retirementDateOnProjection: stringField,
  rateOfProjection: percentageField,
  projectionFigure: numberField,
  annualOverallChargePAPercentageProjections: numberField,
});
const safeguardingBenefits = object({
  guaranteedAnnuityRateGAR: booleanField,
  guaranteedMinPensionGMP: booleanField,
  isTFCGreaterThan25Percentage: booleanField,
  anyLoyaltyBonus: booleanField,
  protectedRetirementAge: booleanField,
});
const otherPensionInfoField = object({
  UFPLSAvailable: booleanField,
  dischargeFormsOnFile: booleanField,
  drawdownAvailable: booleanField,
  nomineeFlexiAccessDrawdownAvailable: booleanField,
  withProfits: booleanField,
  switchingStrategyOnFile: booleanField,
  lifeCover: booleanField,
  contributingHistoryOnFile: booleanField,
  deathBenefitsOnFile: booleanField,
  availableFundsForSwitchOnFile: booleanField,
  adviserRemunerationAllowable: booleanField,
});
const protectionInfo = object({
  adviserRemunerationAllowable: booleanField,
  insuranceTypeDetailsOnFile: booleanField,
  detailsOfTrustOnFile: booleanField,
  insuranceCostDetailsOnFile: booleanField,
  detailsOfPersonInsuredOnFile: booleanField,
  insuranceHistoryDetailsOnFile: booleanField,
  latestPolicyStatementOnFile: booleanField,
});
const otherInvestmentInfo = object({
  withProfits: booleanField,
  contributingHistoryOnFile: booleanField,
  availableFundsForSwitchOnFile: booleanField,
  adviserRemunerationAllowable: booleanField,
  withdrawalScheduleOnFile: booleanField,
  transactionCGTReportOnFile: booleanField,
  wrapperStructureDetailsOnFile: booleanField,
  segmentationStructureHistoryOnFile: booleanField,
});

const constituentsField = array().of(
  object({
    ISIN: string().required(validationMessages.fieldRequiredMessage),
    value: number().required(validationMessages.fieldRequiredMessage),
    fund: string().required(validationMessages.fieldRequiredMessage),
    fundCharge: percentageField,
    investedPercentage: percentageField,
    weightedCharge: percentageField,
  }),
);

export const validationSchemaPension = object({
  // Account info fields
  dateOfInformation: dateField,
  fundValue: numberField,
  transferValue: numberField,
  portfolioModel: numberField,
  constituents: constituentsField,
  // Charges fields
  chargesFigures: chargesFiguresField,
  chargesProjections: chargesProjectionsField,
  // Other details
  otherPensionInfo: otherPensionInfoField,
  safeguardingBenefits: safeguardingBenefits,
});
export const validationSchemaInvestment = object({
  // Account info fields
  dateOfInformation: dateField,
  fundValue: numberField,
  transferValue: numberField,
  portfolioModel: numberField,
  constituents: constituentsField,
  // Charges fields
  chargesFigures: chargesFiguresField,
  otherInvestmentInfo: otherInvestmentInfo,
});
export const validationSchemaProtection = object({
  // Account info fields
  dateOfInformation: dateField,
  protectionInfo: protectionInfo,
});
export const validationSchemaPensionIncomingPaying = object({
  // Account info fields
  dateOfInformation: dateField,
});
export const validationSchema = {
  pension_layout: validationSchemaPension,
  investment_layout: validationSchemaInvestment,
  protection_layout: validationSchemaProtection,
  pension_income_paying_layout: validationSchemaPensionIncomingPaying,
};
