<template>
  <select-field
    class="w-full lg:w-1/2"
    :can-deselect="false"
    label="Investment Portfolio"
    name="portfolioModel"
    :groups="true"
    :options="portfolioModelOptions"
    :disabled="!!portfolioModel && !isSuperAdmin"
  />
</template>

<script setup lang="ts">
  import { useRefData } from '@aventur-shared/stores';
  import { useField } from 'vee-validate';
  import { isSuperAdmin } from '@aventur-shared/utils/user';
  import { computed } from 'vue';
  import { groupOptions } from '@aventur-shared/components/form/fields/field-model';
  import { SelectField } from '@aventur-shared/components/form';

  const { getPortfolioModels } = useRefData();

  const { value: portfolioModel } = useField('portfolioModel');

  const portfolioModelOptions = computed(() =>
    groupOptions(getPortfolioModels),
  );
</script>

<style scoped></style>
