<template>
  <box class="mb-8 rounded-t-none">
    <box-section title="Portfolio Model" :divider="'bottom'">
      <account-plan-info-portfolio-model></account-plan-info-portfolio-model>
    </box-section>
    <box-section title="Investment constituents" :divider="'bottom'">
      <account-plan-info-investment-constituents
        :account="props.account"
        :selected-plan-date="selectedPlanDate"
      >
      </account-plan-info-investment-constituents>
    </box-section>

    <box-section title="Charges" :divider="'bottom'">
      <div class="grid grid-cols-1 gap-5 lg:grid-cols-2">
        <account-charges-figures></account-charges-figures>
        <account-charges-projections></account-charges-projections>
      </div>
    </box-section>
    <box-section title="Safeguarded Benefits" :divider="'bottom'">
      <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
        <radio-group
          label="Guaranteed Annuity Rate GAR"
          name="safeguardingBenefits.guaranteedAnnuityRateGAR"
          :options="selectOptions"
        />
        <radio-group
          label="Guaranteed Min Pension GMP"
          name="safeguardingBenefits.guaranteedMinPensionGMP"
          :options="selectOptions"
        />
        <radio-group
          label="Is TFC greater than 25%"
          name="safeguardingBenefits.isTFCGreaterThan25Percentage"
          :options="selectOptions"
        />
        <radio-group
          label="Any Loyalty Bonus"
          name="safeguardingBenefits.anyLoyaltyBonus"
          :options="selectOptions"
        />
        <radio-group
          label="Protected Retirement Age"
          name="safeguardingBenefits.protectedRetirementAge"
          :options="selectOptions"
        />
      </div>
    </box-section>
    <box-section title="Other information">
      <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
        <radio-group
          label="UFPLS available?"
          name="otherPensionInfo.UFPLSAvailable"
          :options="selectOptions"
        />
        <radio-group
          label="Drawdown account Available?"
          name="otherPensionInfo.drawdownAvailable"
          :options="selectOptions"
        />
        <radio-group
          label="Nominee Flexi Access Drawdown Available?"
          name="otherPensionInfo.nomineeFlexiAccessDrawdownAvailable"
          :options="selectOptions"
        />
        <radio-group
          label="Is there any With Profits?"
          name="otherPensionInfo.withProfits"
          :options="selectOptions"
        />
        <radio-group
          label="Discharge Forms on file"
          name="otherPensionInfo.dischargeFormsOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Any lifestyling/auto switch strategy details on file?"
          name="otherPensionInfo.switchingStrategyOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Any Life Cover or additional benefits?"
          name="otherPensionInfo.lifeCover"
          :options="selectOptions"
        />
        <radio-group
          label="Contribution History on file?"
          name="otherPensionInfo.contributingHistoryOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Death benefits information on file?"
          name="otherPensionInfo.deathBenefitsOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="List of available funds to switch into on file?"
          name="otherPensionInfo.availableFundsForSwitchOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Adviser remuneration allowable"
          name="otherPensionInfo.adviserRemunerationAllowable"
          :options="selectOptions"
        />
      </div>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { Box, BoxSection } from '@modules/ui';
  import { RadioGroup } from '@aventur-shared/components/form';
  import AccountChargesFigures from '@modules/accounts/ui/account-plan-information/form/account-charges-figures.vue';
  import AccountChargesProjections from '@modules/accounts/ui/account-plan-information/form/account-charges-projections.vue';
  import { selectOptions } from '@modules/accounts/ui/account-plan-information/options';
  import { SingleAccount } from '@aventur-shared/modules/accounts';
  import AccountPlanInfoInvestmentConstituents from '@modules/accounts/ui/account-plan-information/form/account-plan-info-investment-constituents.vue';
  import AccountPlanInfoPortfolioModel from '@modules/accounts/ui/account-plan-information/account-plan-info-portfolio-model.vue';

  const props = defineProps<{
    account: SingleAccount;
    selectedPlanDate: string | undefined;
  }>();
</script>
