<template>
  <box class="mb-8 rounded-t-none">
    <box-section title="Protection" :divider="'bottom'">
      <div class="grid grid-cols-1 gap-5 md:grid-cols-2">
        <radio-group
          label="Adviser remuneration allowable?"
          name="otherProtectionInfo.adviserRemunerationAllowable"
          :options="selectOptions"
        />
        <radio-group
          label="Details on the type of insurance on file?"
          name="otherProtectionInfo.insuranceTypeDetailsOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Details of Trust on file?"
          name="otherProtectionInfo.detailsOfTrustOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Details Cost & Term on file?"
          name="otherProtectionInfo.insuranceCostDetailsOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Details of Persons insured on file?"
          name="otherProtectionInfo.detailsOfPersonInsuredOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Details History of review/adjustments on file?"
          name="otherProtectionInfo.insuranceHistoryDetailsOnFile"
          :options="selectOptions"
        />
        <radio-group
          label="Latest policy statement on file?"
          name="otherProtectionInfo.latestPolicyStatementOnFile"
          :options="selectOptions"
        />
      </div>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { Box, BoxSection } from '@modules/ui';
  import { RadioGroup } from '@aventur-shared/components/form';
  import { selectOptions } from '@modules/accounts/ui/account-plan-information/options';
</script>
