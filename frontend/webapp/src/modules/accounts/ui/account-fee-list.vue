<template>
  <t-table>
    <t-head>
      <t-head-tr>
        <t-head-th> Statement ID </t-head-th>
        <t-head-th> Statement Date </t-head-th>
        <t-head-th> Type </t-head-th>
        <t-head-th> Gross Amount </t-head-th>
      </t-head-tr>
    </t-head>
    <t-body>
      <t-body-tr v-for="fee in fees" :key="fee.id">
        <t-body-td>
          {{ fee.statementId }}
        </t-body-td>
        <t-body-td>
          {{ fee.paidDate.formatToView() }}
        </t-body-td>
        <t-body-td>
          {{ fee.feeType }}
        </t-body-td>
        <t-body-td>
          {{ formatWithCurrency(fee.gross) }}
        </t-body-td>
      </t-body-tr>
    </t-body>
  </t-table>
</template>

<script setup lang="ts">
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui/table';
  import { formatWithCurrency } from '@aventur-shared/utils/money';
  import { onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { getAccountFees } from '@aventur-shared/modules/accounts/api';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { AccountFeeStatementLine } from '@aventur-shared/modules/accounts/api/get-account-fees';

  const fees = ref<AccountFeeStatementLine[]>([]);
  const route = useRoute();
  const accountId = Number(route.params.accountId);
  const toast = useToast();

  onMounted(async () => {
    try {
      fees.value = await getAccountFees(accountId);
    } catch (e) {
      toast.error("Couldn't fetch fees");
    }
  });
</script>
