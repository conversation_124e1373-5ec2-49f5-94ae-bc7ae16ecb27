<template>
  <page-title class="mb-4">Provider Valuations Upload</page-title>
  <UploadForm
    :is-loading="isLoading"
    @file-submitted="handleFileUpload"
    @selection-changed="resetContext"
  />
  <div v-if="unmatchedRows || missingValuations">
    <Alert
      class="my-3"
      type="warning"
      message="Uh-oh! Unable to proceed with data upload. File validation failed with the following errors."
      data-testid="warning-alert"
    />
    <div v-if="unmatchedRows">
      <Alert
        class="my-3"
        type="error"
        message="The following account details do not match any holdings, or they match multiple holdings."
        data-testid="unmatched-holdings-alert"
      />
      <t-table class="bg-white" wrapper-class="rounded">
        <t-head>
          <t-head-tr>
            <t-head-th
              v-for="(header, index) in unmatchedRows.columns"
              :key="index"
              >{{ header }}</t-head-th
            >
          </t-head-tr>
        </t-head>
        <t-body>
          <t-body-tr
            v-for="(row, rowIndex) in unmatchedRows.data"
            :key="rowIndex"
          >
            <t-body-td v-for="(item, itemIndex) in row" :key="itemIndex">{{
              item
            }}</t-body-td>
          </t-body-tr>
        </t-body>
      </t-table>
    </div>

    <div v-if="missingValuations">
      <Alert
        class="my-3"
        type="error"
        message="The following active holdings for the provider are missing from the upload. Please 
        provide valuations for all active accounts for the provider."
        data-testid="missing-valuations-alert"
      />
      <t-table class="bg-white" wrapper-class="rounded">
        <t-head>
          <t-head-tr>
            <t-head-th
              v-for="(header, index) in missingValuations.columns"
              :key="index"
              >{{ header }}</t-head-th
            >
          </t-head-tr>
        </t-head>
        <t-body>
          <t-body-tr
            v-for="(row, rowIndex) in missingValuations.data"
            :key="rowIndex"
          >
            <t-body-td v-for="(item, itemIndex) in row" :key="itemIndex">{{
              item
            }}</t-body-td>
          </t-body-tr>
        </t-body>
      </t-table>
    </div>
    <Alert
      class="my-3"
      type="info"
      :message="`${rowCount} valuation rows validated successfully. No valuation rows uploaded due to errors.`"
      data-testid="row-count-alert"
    />
  </div>
  <div v-if="success">
    <Alert
      class="my-3"
      type="success"
      :message="`${rowCount} valuation rows validated and uploaded`"
      data-testid="success-alert"
    />
  </div>
</template>
<script setup lang="ts">
  import { ref } from 'vue';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import Alert from '@aventur-shared/components/Alert.vue';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { PageTitle } from '@modules/ui/text';
  import { FileSubmittedEvent, TableDTO } from '../../types/valuation-upload';
  import postUploadValuations from '../../api/post-upload-valuations';
  import { UploadForm } from '.';

  const toast = useToast();
  const { isLoading } = useAPIState();

  const unmatchedRows = ref<TableDTO | null>(null);
  const missingValuations = ref<TableDTO | null>(null);
  const rowCount = ref<number | null>(null);
  const success = ref<boolean>(false);

  const action = async (
    providerId: number,
    valuationDate: Date,
    file: File,
  ) => {
    try {
      rowCount.value = await postUploadValuations(
        providerId,
        valuationDate,
        file,
      );
      success.value = true;
      toast.success(
        'File uploaded successfully. It will take a few minutes for the new data to be synced.',
      );
    } catch (e: any) {
      success.value = false;
      if (e.response && e.response.status === 400) {
        unmatchedRows.value = e.response.data.holding_match_errors;
        missingValuations.value = e.response.data.active_account_check_errors;
        rowCount.value = e.response.data.number_validated;
        toast.error('File upload failed due to data errors');
      } else {
        toast.error(e);
      }
    }
  };

  const handleFileUpload = async (formValues: FileSubmittedEvent) => {
    resetContext();
    await action(
      formValues.providerId,
      formValues.valuationDate,
      formValues.file,
    );
  };

  const resetContext = () => {
    unmatchedRows.value = null;
    missingValuations.value = null;
    rowCount.value = null;
    success.value = false;
  };
</script>
