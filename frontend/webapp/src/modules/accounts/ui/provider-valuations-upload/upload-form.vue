<template>
  <form @submit="onSubmit" @change="onChange">
    <div class="flex flex-col gap-3">
      <div class="flex flex-col justify-start gap-x-3 lg:flex-row">
        <select-field
          label="Provider"
          name="providerId"
          class="flex-1"
          :options="
            getProviders.map((provider) => ({
              value: provider.id,
              label: provider.name,
            }))
          "
          :searchable="true"
          :disabled="isLoading"
          @on-select="onChange"
        />
        <date-picker
          class="flex-1"
          label="Valuation Date"
          name="valuationDate"
          :disabled="isLoading"
        />
      </div>

      <div class="flex flex-row justify-between">
        <div class="flex flex-row items-center justify-start gap-x-3">
          <file-field accept=".xlsx, .xls" @on-file-upload="handleFileUpload">
            <template #button="{ openFileInput }">
              <custom-button
                theme="secondary"
                :disabled="isLoading"
                @on-click="openFileInput"
                >Select File
              </custom-button>
            </template>
          </file-field>
          <label v-if="selectedFile">{{ selectedFile }}</label>
        </div>
        <custom-button
          v-if="formIsValid"
          :disabled="isLoading"
          :is-busy="isLoading"
          type="submit"
          theme="primary"
          data-testid="submit-button"
          >Submit</custom-button
        >
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { useRefData } from '@aventur-shared/stores';
  import { useForm, useIsFormValid } from 'vee-validate';
  import { ref } from 'vue';
  import { date, mixed, number, object } from 'yup';
  import { DatePicker, SelectField } from '@aventur-shared/components/form';
  import { Button as CustomButton } from '@modules/ui';
  import { FileField } from '@aventur-shared/components/form/fields/clean-fields';
  import { Nullable } from '@aventur-shared/types/Common';

  withDefaults(
    defineProps<{
      isLoading?: boolean;
    }>(),
    {
      isLoading: false,
    },
  );

  const emit = defineEmits(['file-submitted', 'selection-changed']);

  const { getProviders } = storeToRefs(useRefData());

  const selectedFile = ref<string | null>(null);

  type FormValues = {
    providerId: Nullable<number>;
    valuationDate: Nullable<Date>;
    file: Nullable<File>;
  };

  const schema = object({
    providerId: number().nullable().required('Provider ID is required'),
    valuationDate: date()
      .nullable()
      .required('Valuation Date is required')
      .max(new Date(), 'Valuation Date cannot be in the future'),
    file: mixed().required('A file is required'),
  });

  const { handleSubmit, setFieldValue } = useForm<FormValues>({
    validationSchema: schema,
    initialValues: {
      providerId: null,
      valuationDate: null,
      file: null,
    },
  });

  const formIsValid = useIsFormValid();

  const handleFileUpload = async (files: FileList) => {
    setFieldValue('file', files[0]);
    selectedFile.value = files[0].name;
  };

  const onSubmit = handleSubmit((formValues: FormValues) => {
    emit('file-submitted', formValues);
  });

  const onChange = () => {
    emit('selection-changed');
  };
</script>
