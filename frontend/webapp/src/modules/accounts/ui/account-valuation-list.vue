<template>
  <div class="flex flex-col">
    <div class="mb-4 text-end">
      <custom-button theme="primary" type="button" @click="openModalForAdd">
        Add valuation
      </custom-button>
    </div>
    <account-valuation-modal
      @on-load="({ modalApi: api }) => (modalApi = api.value)"
    >
      <account-valuation-form
        :amount="currentValuation?.amount.getValue().toString()"
        :date="currentValuation?.date.formatForForm()"
        :type="currentValuation?.type"
        @on-close="handleModalClose"
        @on-submit="
          (fv, originalValuationDate) =>
            handleCreateOrEditValuation(fv, originalValuationDate)
        "
      />
    </account-valuation-modal>
    <t-table class="bg-white" wrapper-class="rounded" style="overflow: visible">
      <t-head>
        <t-head-tr>
          <t-head-th class="w-1/3"> Date </t-head-th>
          <t-head-th class="w-1/3"> Type </t-head-th>
          <t-head-th class="w-1/3"> Amount </t-head-th>
          <t-head-th class="w-1/4"></t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="valuation in valuations"
          :key="valuation.date.formatToView()"
        >
          <t-body-td>
            {{ valuation.date.formatToView() }}
          </t-body-td>
          <t-body-td>
            {{ valuation.type }}
          </t-body-td>
          <t-body-td>
            {{ formatWithCurrency(valuation.amount) }}
          </t-body-td>
          <t-body-td>
            <div class="flex justify-end gap-4">
              <popup-options
                options-wrapper-class="-left-[140px]"
                :options="operations"
                @selected="
                  (option: ValuationItemOperationsOptions[0]) =>
                    handleSelectOption(valuation)(option.value)
                "
              >
                <template #label>
                  <ellipsis-vertical-icon
                    class="inline-block size-5 cursor-pointer"
                  />
                </template>
                <template #option="{ option }">
                  <div
                    class="flex items-center gap-1"
                    :class="{ 'text-red-500': option.value === 'delete' }"
                  >
                    <component
                      :is="
                        option.value === 'edit' ? PencilSquareIcon : TrashIcon
                      "
                      class="size-4"
                    >
                      <title>{{ option.label }}</title>
                    </component>
                    {{ option.label }}
                  </div>
                </template>
              </popup-options>
            </div>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import {
    AccountValuationForm,
    AccountValuationModal,
    FormValues,
  } from './account-valuation';
  import {
    Button as CustomButton,
    ModalAPI,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
  import { computed, onMounted, ref } from 'vue';
  import { Valuation } from '@aventur-shared/modules/factfind/models/valuation';
  import { useRoute } from 'vue-router';
  import { getAccountValuations } from '@aventur-shared/modules/accounts/api';
  import { useToast } from '@aventur-shared/composables/useToast';
  import {
    userAddsValuationAction,
    userDeletesValuationAction,
  } from '@modules/accounts/use-cases';
  import { DateTime } from '@aventur-shared/utils/dateTime';
  import { PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/outline';
  import { PopupOptions } from '@modules/ui';
  import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';

  type ValuationItemOperations = 'edit' | 'delete';
  type ValuationItemOperationsOptions = Array<{
    label: string;
    value: ValuationItemOperations;
  }>;
  const operations = computed<ValuationItemOperationsOptions>(() => [
    { label: 'Edit', value: 'edit' },
    { label: 'Delete', value: 'delete' },
  ]);

  const valuations = ref<Valuation[]>([]);
  const currentValuation = ref<Valuation>();
  const route = useRoute();
  const accountId = Number(route.params.accountId);
  const toast = useToast();
  const modalApi = ref<ModalAPI>();

  onMounted(async () => {
    try {
      valuations.value = await getAccountValuations(accountId);
    } catch (e) {
      toast.error("Couldn't fetch valuations");
    }
  });

  const handleSelectOption = (valuation: Valuation) => {
    return (option: ValuationItemOperations) => {
      switch (option) {
        case 'edit':
          openModalForEdit(valuation);
          break;
        case 'delete':
          handleDeleteValuation(valuation.date.formatForForm());
          break;
      }
    };
  };

  const handleCreateOrEditValuation = async (
    fv: FormValues,
    originalValuationDate?: string,
  ) => {
    try {
      await userAddsValuationAction(accountId, {
        amount: new Money(Number(fv.amount)),
        date: new DateTime(fv.date),
        type: fv.type,
      });
      if (originalValuationDate !== undefined) {
        // Edit of existing valuation with a date change
        await userDeletesValuationAction(accountId, originalValuationDate);
      }
      valuations.value = await getAccountValuations(accountId);
      toast.success('Valuation has been saved');
      handleModalClose();
    } catch (e: unknown) {
      toast.error('Error saving valuation');
    }
  };

  const handleDeleteValuation = async (valuationDate: string) => {
    const { isAccepted } = await useConfirmation(
      `Delete the valuation for ${new DateTime(valuationDate).formatToView()}?`,
    );
    if (isAccepted()) {
      try {
        await userDeletesValuationAction(accountId, valuationDate);
        valuations.value = await getAccountValuations(accountId);
        toast.success('Valuation has been deleted');
      } catch (e: unknown) {
        toast.error('Error deleting valuation');
      }
    }
  };

  const openModalForAdd = () => {
    currentValuation.value = undefined;
    handleModalOpen();
  };

  const openModalForEdit = (valuation: Valuation) => {
    currentValuation.value = valuation;
    handleModalOpen();
  };

  const handleModalOpen = () => {
    modalApi.value?.open();
  };

  const handleModalClose = () => {
    modalApi.value?.close();
  };
</script>
