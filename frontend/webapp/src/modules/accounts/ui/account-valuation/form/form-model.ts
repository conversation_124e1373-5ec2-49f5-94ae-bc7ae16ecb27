import { DateTime } from 'luxon';
import { date, mixed, number, object, string } from 'yup';

import {
  fieldRequiredMessage,
  invalidNumberMessage,
  noFutureDateMessage,
} from '@aventur-shared/utils/form/validation-messages';
import { ValuationType } from '@aventur-shared/modules/factfind/types';
//

export interface FormValues {
  id?: number;
  amount: string;
  type: ValuationType;
  date: string;
}

export const validationSchema = object({
  amount: mixed()
    .checkIsNumber(invalidNumberMessage)
    .when({
      is: (value: string) => value,
      then: () =>
        number()
          .positive('Value cannot be negative')
          .max(99000000, 'Value must be lower than 99.000.000'),
    })
    .transform((value) => value || null)
    .required(fieldRequiredMessage),
  date: string()
    .transform((value) => (DateTime.isDateTime(value) ? value : undefined))
    .when({
      is: (value: string) => value,
      then: () => date().max(DateTime.now(), noFutureDateMessage),
    })
    .required(fieldRequiredMessage),
  type: string().required(fieldRequiredMessage),
});
