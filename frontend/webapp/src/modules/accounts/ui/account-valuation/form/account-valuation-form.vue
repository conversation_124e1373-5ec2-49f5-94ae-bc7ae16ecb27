<template>
  <form @submit.prevent="onSubmit">
    <text-field data-testid="amount-input" label="Amount" name="amount" />
    <date-picker data-testid="date-input" label="Valuation date" name="date" />
    <multi-state-switch
      data-testid="type-input-actual"
      label=""
      name="type"
      :options="[
        {
          label: 'Estimate',
          value: 'estimate',
        },
        {
          label: 'Actual',
          value: 'actual',
        },
      ]"
    />

    <div class="flex flex-col gap-1">
      <custom-button
        type="submit"
        theme="primary"
        :is-busy="APIState.isLoading"
        >{{ props.amount ? 'Update' : 'Add' }}</custom-button
      >
      <custom-button
        type="button"
        theme="gray-ghost"
        @click.prevent="$emit('on-close')"
      >
        Close
      </custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { Button as CustomButton } from '@modules/ui';
  import { useForm } from 'vee-validate';
  import {
    DatePicker,
    MultiStateSwitch,
    TextField,
  } from '@aventur-shared/components/form';
  import { type FormValues, validationSchema } from './form-model';
  import { useAPIState } from '@aventur-shared/composables/useAPIState';
  import { ValuationType } from '@aventur-shared/modules/factfind/types';

  const props = defineProps<{
    amount?: string;
    date?: string;
    type?: ValuationType;
  }>();

  const emit = defineEmits<{
    (
      e: 'on-submit',
      formValues: FormValues,
      originalValuationDate?: string,
    ): void;
    (e: 'on-close'): void;
  }>();

  const { handleSubmit } = useForm<FormValues>({
    initialValues: {
      amount: props.amount ? props.amount : '',
      date: props.date ? props.date : '',
      type: props.type ? props.type : 'estimate',
    },
    validationSchema,
  });

  const onSubmit = handleSubmit((formValues: FormValues) => {
    if (props.date !== undefined && formValues.date !== props.date) {
      emit('on-submit', formValues, props.date);
    } else {
      emit('on-submit', formValues);
    }
  });

  const { APIState } = useAPIState();
</script>
