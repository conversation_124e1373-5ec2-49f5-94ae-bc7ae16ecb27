<template>
  <div class="flex flex-col">
    <page-title class="mb-4">Account List</page-title>

    <filters-panel
      title="Account filters"
      :filters="filters"
      :text-search="true"
      text-search-hint="Search by client name, email or account number"
      @filters:apply="handleUpdateFilters"
      @filters:reset="handleResetFilters"
    >
      <div
        class="grid w-full grid-cols-1 items-center justify-items-stretch gap-4 sm:grid-cols-2 lg:grid-cols-3"
      >
        <account-provider-filter v-model="filters.provider_id" />
        <account-type-filter v-model="filters.type_id" />
        <account-status-filter v-model="filters.status_id" />
      </div>
    </filters-panel>

    <table-with-pagination
      :pagination="{
        activePage: unref(tableService.page),
        totalItems: unref(tableService.config.totalItems),
        perPage: tableService.config.perPage,
      }"
      :show-results="true"
      @on-page-change="handlePageChange"
    >
      <t-table class="bg-white" wrapper-class="rounded">
        <t-head>
          <t-head-tr>
            <t-head-th> ID </t-head-th>
            <t-head-th> Provider </t-head-th>
            <t-head-th> Type </t-head-th>
            <t-head-th> Client(s) </t-head-th>
            <t-head-th> Acc no. </t-head-th>
            <t-head-th> Sub acc no. </t-head-th>
            <t-head-th> Created </t-head-th>
            <t-head-th> Status </t-head-th>
          </t-head-tr>
        </t-head>
        <t-body>
          <t-body-tr v-for="account in accounts.items" :key="account.id">
            <t-body-td>
              {{ account.id }}
            </t-body-td>
            <t-body-td>
              <router-link
                class="border-b border-dotted border-b-black hover:border-solid"
                :to="{
                  name: 'account-details-index',
                  params: { accountId: account.id },
                }"
              >
                {{ account.providerName }}
              </router-link>
            </t-body-td>
            <t-body-td>
              {{ account.type }}
            </t-body-td>
            <t-body-td>
              <ul>
                <li
                  v-for="client in account.clients"
                  :key="client.id"
                  class="whitespace-nowrap"
                >
                  <router-link
                    class="border-b border-dotted border-b-black hover:border-solid"
                    :to="{ name: 'client-overview', params: { id: client.id } }"
                  >
                    {{ formatName(client) }}
                  </router-link>
                </li>
              </ul>
            </t-body-td>
            <t-body-td>
              {{ account.accountNumber }}
            </t-body-td>
            <t-body-td> {{ account.subAccountNumber }} </t-body-td>
            <t-body-td class="whitespace-nowrap">
              {{ account.created.formatWithShortName() }}
            </t-body-td>
            <t-body-td>
              {{ account.status.name }}
            </t-body-td>
          </t-body-tr>
        </t-body>
      </t-table>
    </table-with-pagination>
  </div>
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { onMounted, ref, unref, watch } from 'vue';
  import { formatName } from '@aventur-shared/utils/user';
  import { AccountList } from '@aventur-shared/modules/accounts';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui/table';
  import { PageTitle, TableWithPagination } from '@modules/ui';
  import FiltersPanel from '@modules/ui/filters-panel/filters-panel.vue';
  import {
    AccountProviderFilter,
    AccountStatusFilter,
    AccountTypeFilter,
    type Filters,
  } from '@modules/accounts/use-cases/filter-accounts';
  import { useTableService } from '@aventur-shared/utils/table/use-table-service';
  import { userPreviewsAllAccountsAction } from '@modules/accounts/use-cases';
  import { useFilterStore } from '@modules/ui/filters-panel/filters-store';

  const route = useRoute();
  const { getLastFilter } = useFilterStore();

  const accounts = ref<AccountList>({ items: [], totalItems: 0 });

  const { filters, ...tableService } = useTableService<Filters>('accounts', {
    initialPage: 1,
    perPage: 10,
    totalItems: accounts.value?.totalItems || 0,
  });

  watch(accounts, () => {
    tableService.setTotalItems(accounts.value?.totalItems || 0);
  });

  onMounted(async () => {
    handleUpdateFilters(getLastFilter(route.name) ?? filters.value);
  });

  const handlePageChange = async (page: number): Promise<void> => {
    tableService
      .changePage(page)
      .apply()
      .then(() => {
        fetchAccounts();
      });
  };

  const handleUpdateFilters = (filters: Partial<Filters>) => {
    tableService.changePage(1).addFilter(filters).apply().then(fetchAccounts);
  };

  const handleResetFilters = () => {
    tableService
      .resetFilters(['provider_id', 'type_id', 'status_id', 'text'])
      .changePage(1)
      .apply()
      .then(fetchAccounts);
  };

  const fetchAccounts = () => {
    return userPreviewsAllAccountsAction({
      query: {
        page: tableService.page.value.toString(),
        ...filters.value,
      },
    }).then((list: AccountList) => {
      accounts.value = list;
    });
  };
</script>
