import { apiClient } from '@aventur-shared/services/api';

export default async (
  providerId: number,
  valuationDate: Date,
  file: File,
): Promise<number> => {
  const formData = new FormData();
  formData.append('upload', file);

  return await apiClient.post<FormData, number>(
    `/api/v1/holdings/upload-valuations?provider_id=${providerId}&valuation_date=${valuationDate}`,
    formData,
  );
};
