<template>
  <box>
    <box-section class="!px-6 !pb-0">
      <div class="flex flex-row items-center justify-items-start gap-4">
        <div class="bg-primary rounded-md p-3">
          <currency-pound-icon class="size-6 text-white" />
        </div>
        <p class="text-lg font-medium text-gray-500">Adviser Fee Forecast</p>
      </div>
    </box-section>
    <box-section class="!px-6">
      <t-table data-testid="fee-forecast-table">
        <t-head>
          <t-head-tr>
            <t-head-th
              v-for="(header, index) in tableData.columns"
              :key="index"
              :class="index === 0 ? 'text-left' : 'text-right'"
              >{{ header }}</t-head-th
            >
          </t-head-tr>
        </t-head>
        <t-body>
          <t-body-tr v-for="(row, rowIndex) in tableData.data" :key="rowIndex">
            <t-body-td
              v-for="(item, itemIndex) in row"
              :key="itemIndex"
              :class="itemIndex === 0 ? 'text-left' : 'text-right'"
              >{{ item }}</t-body-td
            >
          </t-body-tr>
        </t-body>
      </t-table>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { CurrencyPoundIcon } from '@heroicons/vue/24/outline';
  import {
    Box,
    BoxSection,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { getFeeForecast } from '@aventur-shared/modules/advisors/api';

  interface Table {
    columns: string[];
    data: any[][];
  }

  const tableData = ref<Table>({ columns: [], data: [] });

  onMounted(async () => {
    tableData.value = await getFeeForecast();
  });
</script>
