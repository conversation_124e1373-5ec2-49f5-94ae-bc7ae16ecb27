<template>
  <div>
    <page-title class="mb-4">Administrators</page-title>

    <div class="flex justify-between">
      <custom-button
        class="mb-2 ml-auto"
        theme="primary"
        type="button"
        @on-click="handleAddModalOpen"
        >Add new</custom-button
      >
      <slide-over
        :config="{ outsideClickClose: false, maxWidth: '7xl' }"
        @use-api="useSlideOverApi"
      >
        <administrator-modal-content
          :form-state="formState"
          @on-save-and-close="handleSaveAndClose"
          @on-cancel-and-close="slideOverApi?.close()"
        />
      </slide-over>
    </div>

    <table-with-pagination
      :pagination="{
        activePage: unref(tableService.page),
        totalItems: unref(tableService.config.totalItems),
        perPage: tableService.config.perPage,
      }"
      :show-results="true"
      @on-page-change="changePage"
    >
      <t-table class="bg-white" wrapper-class="rounded">
        <t-head>
          <table-head-tr>
            <table-head-th>ID</table-head-th>
            <table-head-th>Email address</table-head-th>
            <table-head-th>Full name</table-head-th>
            <table-head-th>Roles</table-head-th>
            <table-head-th>Status</table-head-th>
            <table-head-th></table-head-th>
          </table-head-tr>
        </t-head>

        <t-body>
          <t-body-tr v-for="advisor in advisors.items" :key="advisor.id">
            <t-body-td>{{ advisor.id }}</t-body-td>
            <t-body-td>{{ advisor.email }}</t-body-td>
            <t-body-td>{{
              formatName({
                firstName: advisor.firstName || '',
                lastName: advisor.lastName,
              })
            }}</t-body-td>
            <t-body-td>{{ getRolesDisplay(advisor) }}</t-body-td>
            <t-body-td> {{ advisor.status }} </t-body-td>
            <t-body-td class="text-center">
              <popup-options
                options-wrapper-class="-ml-10 -mt-8 xl:fixed absolute"
                :options="[{ label: 'Edit', value: 'edit' }]"
                @selected="() => handleEditModalOpen(advisor)"
              >
                <template #label>
                  <ellipsis-vertical-icon class="inline-block size-4" />
                </template>
                <template #option="{ option }">
                  <span class="hover:cursor-pointer">{{ option.label }}</span>
                </template>
              </popup-options>
            </t-body-td>
          </t-body-tr>
        </t-body>
      </t-table>
    </table-with-pagination>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, unref, watch } from 'vue';
  import type { ModalAPI } from '@modules/ui';
  import {
    Button as CustomButton,
    PopupOptions,
    SlideOver,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    TTable,
    TableWithPagination,
  } from '@modules/ui';
  import TableHeadTr from '@modules/ui/table/table-head-tr.vue';
  import TableHeadTh from '@modules/ui/table/table-head-th.vue';
  import { PageTitle } from '@modules/ui/text';
  import { useTableService } from '@aventur-shared/utils/table/use-table-service';
  import { Advisor, AdvisorList } from '@aventur-shared/modules/advisors';
  import { formatName } from '@aventur-shared/utils/user';
  import { getAdvisorsAction } from '@modules/advisors/use-cases/user-previews-all-advisors';
  import { AdministratorModalContent } from '@modules/advisors/ui/administrator-modal';
  import { action as createAction } from '@modules/advisors/use-cases/user-creates-advisor';
  import { action as updateAction } from '@modules/advisors/use-cases/user-updates-advisor';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { FormValues } from './administrator-modal/form/form-model';
  import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
  import { UniqueEmailError } from '@aventur-shared/modules/advisors/api';
  import { useAdvisorsStore } from '@aventur-shared/modules/advisors';
  import { DateTime } from '@aventur-shared/utils/dateTime';

  const advisors = ref<AdvisorList>({
    items: [],
    totalItems: 0,
  });
  const slideOverApi = ref<ModalAPI>();
  const formState = ref<{
    type: 'add' | 'edit';
    fv?: FormValues;
    errors?: Partial<Record<keyof FormValues, string>>;
  }>({
    type: 'add',
  });

  const toast = useToast();
  const tableService = useTableService('advisors', {
    initialPage: 1,
    perPage: 10,
    totalItems: advisors.value.totalItems,
  });

  const { fetchActiveAdvisors, getAdvisorsRoles } = useAdvisorsStore();

  watch(advisors, () => {
    tableService.setTotalItems(advisors.value.totalItems);
  });

  onMounted(() => {
    fetchAdvisors();
  });

  const getRolesDisplay = (advisor: AdvisorList['items'][0]) => {
    return advisor.roles
      .map(
        (role) =>
          getAdvisorsRoles.find((advisorRole) => advisorRole.value === role)
            ?.label,
      )
      .join(', ');
  };

  const handleAddModalOpen = () => {
    formState.value = {
      type: 'add',
      fv: {
        roles: [],
        email: '',
        firstName: '',
        lastName: '',
        status: 'Active',
        reachedCompetentAdviserStatus: false,
        reachedCompetentAdviserStatusDate: null,
      },
    };
    slideOverApi.value?.open();
  };

  const handleEditModalOpen = (advisor: Advisor) => {
    formState.value = {
      type: 'edit',
      fv: {
        id: advisor.id,
        email: advisor.email,
        firstName: advisor.firstName ?? '',
        lastName: advisor.lastName ?? '',
        roles: advisor.roles,
        status: advisor.status,
        reachedCompetentAdviserStatus: advisor.reachedCompetentAdviserStatus,
        reachedCompetentAdviserStatusDate:
          advisor.reachedCompetentAdviserStatusDate
            ? advisor.reachedCompetentAdviserStatusDate?.formatForForm()
            : null,
      },
    };
    slideOverApi.value?.open();
  };

  const useSlideOverApi = (api: any) => {
    slideOverApi.value = api;
  };

  const changePage = (page: number) => {
    tableService
      .changePage(page)
      .apply()
      .then(() => {
        fetchAdvisors();
      });
  };

  const handleSaveAndClose = (
    type: (typeof formState)['value']['type'],
    fv: FormValues,
  ) => {
    if (type === 'add') {
      handleAddAdvisor(fv);
    } else {
      handleEditAdvisor(fv);
    }
  };

  const handleAddAdvisor = async (fv: FormValues) => {
    try {
      await createAction({
        email: fv.email,
        firstName: fv.firstName,
        lastName: fv.lastName,
        roles: fv.roles,
        status: fv.status,
        reachedCompetentAdviserStatus: fv.reachedCompetentAdviserStatus,
        reachedCompetentAdviserStatusDate: fv.reachedCompetentAdviserStatusDate
          ? new DateTime(fv.reachedCompetentAdviserStatusDate)
          : null,
      });
      await fetchAdvisors();
      await fetchActiveAdvisors({
        force: true,
      });
      toast.success('Advisor has been added');
      slideOverApi.value?.close();
    } catch (e: unknown) {
      if (e instanceof UniqueEmailError) {
        formState.value.errors = {
          email: e.message,
        };
      } else {
        toast.error('Create advisor error.');
      }
    }
  };

  const handleEditAdvisor = async (fv: FormValues) => {
    try {
      const updatedAdvisor = await updateAction({
        id: fv.id as number,
        email: fv.email,
        firstName: fv.firstName,
        lastName: fv.lastName,
        roles: fv.roles,
        status: fv.status,
        reachedCompetentAdviserStatus: fv.reachedCompetentAdviserStatus,
        reachedCompetentAdviserStatusDate: fv.reachedCompetentAdviserStatusDate
          ? new DateTime(fv.reachedCompetentAdviserStatusDate)
          : null,
      });
      const ind = advisors.value.items.findIndex(
        (advisor) => advisor.id === updatedAdvisor.id,
      );
      advisors.value.items[ind] = updatedAdvisor;
      slideOverApi.value?.close();
      await fetchActiveAdvisors({
        force: true,
      });
      toast.success('Advisor has been updated');
    } catch {
      toast.error('Could not update an advisor');
    }
  };

  const fetchAdvisors = () => {
    return getAdvisorsAction({
      query: {
        page: tableService.page.value.toString(),
      },
    }).then((response) => {
      advisors.value = response;
    });
  };
</script>
