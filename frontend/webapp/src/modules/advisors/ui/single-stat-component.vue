<template>
  <div
    class="relative overflow-hidden rounded-lg bg-white px-4 pb-12 pt-5 shadow sm:px-6 sm:pt-6"
  >
    <dt>
      <div class="bg-primary absolute rounded-md p-3">
        <component
          :is="props.icon"
          class="size-6 text-white"
          aria-hidden="true"
        />
      </div>
      <p class="ml-16 truncate text-sm font-medium text-gray-500">
        {{ props.title }}
      </p>
    </dt>
    <dd class="ml-16 flex items-baseline pb-6 sm:pb-7">
      <p class="text-2xl font-semibold text-gray-900">
        {{ stat_value }}
      </p>
      <div class="absolute inset-x-0 bottom-0 bg-gray-50 p-4 sm:px-6">
        <div class="text-sm">
          <router-link
            :to="props.link"
            class="text-primary hover:text-primary font-medium"
          >
            View all<span class="sr-only"> {{ props.title }} stats</span>
          </router-link>
        </div>
      </div>
    </dd>
  </div>
</template>

<script setup lang="ts">
  import { StatisticType } from '@aventur-shared/modules/advisors/api';
  import { useStatistic } from '@aventur-shared/modules/advisors';

  const props = defineProps<{
    title: string;
    statName: StatisticType;
    icon: any;
    link: string;
  }>();

  const { stat_value } = useStatistic(props.statName);
</script>
