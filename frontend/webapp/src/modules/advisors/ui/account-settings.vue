<template>
  <page-title class="mb-4">Account Settings</page-title>
  <Box>
    <BoxSection divider="bottom">
      <BoxTitle>
        <p>Multi-factor Authentication (MFA)</p>
        <span class="text-sm font-normal text-gray-500"
          >Enable MFA for advanced account security</span
        >
      </BoxTitle>
    </BoxSection>

    <BoxSection no-padding-y>
      <ul role="list" class="divide-y divide-gray-100">
        <li
          v-for="(config, type) in authMFATypes"
          :key="type"
          class="flex items-center justify-between gap-x-6 py-5"
        >
          <div class="min-w-0">
            <div class="flex flex-col items-start gap-x-3">
              <p
                class="text-sm font-medium"
                :class="config.configurable ? 'text-gray-900' : 'text-gray-400'"
              >
                {{ type }}
              </p>
              <span class="text-secondary text-xs" v-if="type === 'EMAIL'">{{
                user.email
              }}</span>
            </div>
          </div>
          <div class="flex flex-none items-center gap-x-4">
            <input
              type="checkbox"
              class="text-primary focus:ring-primary size-4 hover:cursor-pointer"
              :class="{
                '!border-gray-300 !bg-gray-300/80': !config.configurable,
              }"
              :disabled="!config.configurable"
              :checked="config.enabled"
              @change="
                (checked) =>
                  updateMFASettings(
                    type,
                    !!(checked.target as HTMLInputElement)?.checked,
                  )
              "
            />
          </div>
        </li>
      </ul>
    </BoxSection>
  </Box>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { lowerCase } from 'lodash';
  import { fetchMFAPreference, updateMFAPreference } from '@aws-amplify/auth';

  import { useUserStore } from '@aventur-shared/modules/users';
  import { useToast } from '@aventur-shared/composables/useToast';

  import { Box, BoxSection, BoxTitle, PageTitle } from '@modules/ui';
  //

  const toast = useToast();

  type AuthMFAType = 'SMS' | 'TOTP' | 'EMAIL';
  type AuthMFAConfiguration = {
    enabled: boolean;
    configurable: boolean;
  };

  const authMFATypes = computed<Record<AuthMFAType, AuthMFAConfiguration>>(
    () => ({
      EMAIL: {
        enabled: enabledMfaOptions.value.includes('EMAIL'),
        configurable: true,
      },
      SMS: {
        enabled: enabledMfaOptions.value.includes('SMS'),
        configurable: false,
      },
      TOTP: {
        enabled: enabledMfaOptions.value.includes('TOTP'),
        configurable: false,
      },
    }),
  );

  const { user, isClient } = useUserStore();
  const enabledMfaOptions = ref<AuthMFAType[]>([]);

  const updateMFASettings = async (type: AuthMFAType, enabled: boolean) => {
    try {
      await updateMFAPreference({
        [lowerCase(String(type))]: enabled ? 'ENABLED' : 'DISABLED',
      });
      toast.success('Successfully updated MFA settings');
    } catch (e) {
      toast.error(e as Error);
    }
  };

  onMounted(async () => {
    if (!isClient) {
      try {
        const { enabled } = await fetchMFAPreference();
        enabledMfaOptions.value = enabled ?? [];
      } catch {
        //
      }
    }
  });
</script>
