import { array, boolean, object, string } from 'yup';
import { validationMessages } from '@aventur-shared/utils/form';
import {
  AdvisorId,
  AdvisorRoleEnum,
  AdvisorStatus,
} from '@aventur-shared/modules/advisors';
import { invalidCharactersRegex } from '@aventur-shared/utils/form/fields';
import { Nullable } from '@aventur-shared/types/Common';

export interface FormValues {
  id?: AdvisorId;
  email: string;
  status: AdvisorStatus;
  firstName: string;
  lastName: string;
  reachedCompetentAdviserStatus: boolean;
  reachedCompetentAdviserStatusDate: Nullable<string>;
  roles: AdvisorRoleEnum[];
}

export const validationSchema = object({
  email: string()
    .required(validationMessages.fieldRequiredMessage)
    .email(validationMessages.invalidEmailMessage),
  status: string().required(validationMessages.fieldRequiredMessage),
  firstName: string()
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: validationMessages.invalidCharacterMessage,
    })
    .required(validationMessages.fieldRequiredMessage),
  lastName: string()
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: validationMessages.invalidCharacterMessage,
    })
    .required(validationMessages.fieldRequiredMessage),
  reachedCompetentAdviserStatus: boolean().nullable(),
  reachedCompetentAdviserStatusDate: string()
    .nullable()
    .when(['reachedCompetentAdviserStatus'], {
      is: (value: string) => value,
      then: (schema) =>
        schema.required(validationMessages.fieldRequiredMessage),
    }),
  roles: array()
    .of(
      string<AdvisorRoleEnum>()
        .oneOf([
          AdvisorRoleEnum.Adviser,
          AdvisorRoleEnum.CaseManagement,
          AdvisorRoleEnum.Client,
          AdvisorRoleEnum.Compliance,
          AdvisorRoleEnum.Introducer,
          AdvisorRoleEnum.Paraplanner,
          AdvisorRoleEnum.RelationshipManager,
          AdvisorRoleEnum.SuperAdmin,
        ])
        .defined(),
    )
    .min(1, 'At least 1 role has to be selected')
    .required(validationMessages.fieldRequiredMessage),
});
