<template>
  <form class="flex h-full flex-col text-start" @submit="onSubmit">
    <box-section divider="bottom">
      <div class="grid w-4/5 grid-cols-1 gap-4 md:grid-cols-2">
        <text-field label="Email" name="email" :disabled="isEditForm" />
        <multi-state-switch
          class="order-first justify-self-start md:order-none md:justify-self-end"
          label=""
          name="status"
          :options="[
            {
              label: 'Active',
              value: 'Active',
            },
            {
              label: 'Inactive',
              value: 'Inactive',
            },
          ]"
        />
        <text-field label="First Name" name="firstName" />
        <text-field label="Last Name" name="lastName" />

        <div :class="classObject">
          <multi-select-field
            label="Roles"
            name="roles"
            data-testid="roles"
            :options="getAdvisorsRoles"
            :searchable="true"
          />
          <checkbox-field
            v-show="showCompetentAdviserFields"
            data-testid="reachedCompetentAdviserStatus"
            label="Reached competent adviser status"
            name="reachedCompetentAdviserStatus"
            :checked-value="true"
          />
          <date-picker
            v-show="showCompetentAdviserFields"
            data-testid="reachedCompetentAdviserStatusDate"
            name="reachedCompetentAdviserStatusDate"
            label="Date of reaching competent adviser status"
          />
        </div>
      </div>
    </box-section>
    <box-section class="mt-auto flex flex-row gap-2">
      <custom-button type="submit" theme="primary">Save & close</custom-button>
      <custom-button
        type="button"
        theme="gray-ghost"
        @on-click="handleCancelAndClose"
        >Cancel & close</custom-button
      >
    </box-section>
  </form>
</template>

<script setup lang="ts">
  import { BoxSection, Button as CustomButton } from '@modules/ui';
  import {
    CheckboxField,
    DatePicker,
    MultiSelectField,
    MultiStateSwitch,
    TextField,
  } from '@aventur-shared/components/form';
  import { storeToRefs } from 'pinia';
  import { useForm, useIsFormDirty, useIsFormValid } from 'vee-validate';
  import { type FormValues, validationSchema } from './form-model';
  import { useAdvisorsStore } from '@aventur-shared/modules/advisors';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { computed, watch } from 'vue';
  import { AdvisorRoleEnum } from '@aventur-shared/modules/advisors';

  const props = defineProps<{
    formState: {
      type: 'add' | 'edit';
      fv?: FormValues;
      errors?: Partial<Record<keyof FormValues, string>>;
    };
  }>();

  const emit = defineEmits(['on-save-and-close', 'on-cancel-and-close']);

  const { getAdvisorsRoles } = storeToRefs(useAdvisorsStore());
  const { handleSubmit, setErrors, values, setFieldValue } =
    useForm<FormValues>({
      validationSchema,
      initialValues: props.formState.fv,
    });

  const isFormValid = useIsFormValid();
  const isFormDirty = useIsFormDirty();
  const isEditForm = computed(() => props.formState.type === 'edit');
  const showCompetentAdviserFields = computed(() =>
    values.roles.includes(AdvisorRoleEnum.Adviser),
  );

  const classObject = computed(() => ({
    'flex flex-col gap-4 p-4 rounded-md bg-gray-100 transition-all duration-300':
      showCompetentAdviserFields.value,
    'flex flex-col gap-4 transition-all duration-300':
      !showCompetentAdviserFields.value,
  }));

  watch(
    () => props.formState.errors,
    () => {
      setErrors(props.formState.errors ?? {});
    },
  );

  watch(
    () => values.roles,
    () => {
      if (!showCompetentAdviserFields.value) {
        setFieldValue('reachedCompetentAdviserStatus', false);
        setFieldValue('reachedCompetentAdviserStatusDate', null);
      }
    },
  );

  const onSubmit = handleSubmit((fv) => {
    emit('on-save-and-close', props.formState.type, fv);
  });

  const handleCancelAndClose = async () => {
    if (isFormValid.value && isFormDirty.value) {
      const { onAccept } = await useConfirmation(
        `Are you sure you want to leave? Your data won't be saved.`,
      );

      return onAccept(() => emit('on-cancel-and-close'));
    }
    emit('on-cancel-and-close');
  };
</script>
