<template>
  <box class="h-full shadow-none">
    <box-section
      divider="bottom"
      class="-my-6 text-start"
      title="User detail"
    />
    <box-section no-padding-x class="h-full">
      <administrator-form
        :form-state="formState"
        @on-save-and-close="(type, fv) => $emit('on-save-and-close', type, fv)"
        @on-cancel-and-close="$emit('on-cancel-and-close')"
      />
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { Box, BoxSection } from '@modules/ui';
  import { AdministratorForm } from './form';
  import { FormValues } from './form/form-model';

  defineProps<{
    formState: {
      type: 'add' | 'edit';
      fv?: FormValues;
      errors?: Partial<Record<keyof FormValues, string>>;
    };
  }>();

  defineEmits(['on-save-and-close', 'on-cancel-and-close']);
</script>
