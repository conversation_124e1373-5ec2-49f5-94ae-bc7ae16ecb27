import { getAdvisors } from '@aventur-shared/modules/advisors/api';

interface ActionProps {
  query: Partial<{
    page: string;
  }>;
}

interface QueryString
  extends Partial<{
    page: string;
  }> {}

const prepareQueryString = (
  query: Partial<ActionProps['query']>,
): QueryString => {
  const filterEmptyQueryParts = (queryPart: [string, string | number]) =>
    queryPart[1];
  const queryEntries = Object.entries({
    ...query,
  }).filter(filterEmptyQueryParts);

  return Object.fromEntries(queryEntries);
};

export const getAdvisorsAction = async ({ query }: ActionProps) => {
  try {
    const advisors = await getAdvisors(prepareQueryString(query));

    return advisors;
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    }
    throw new Error('Something went wrong during advisors fetching.');
  }
};
