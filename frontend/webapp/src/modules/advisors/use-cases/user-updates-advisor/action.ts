import { IDateTime } from '@aventur-shared/utils/dateTime';
import { Nullable } from '@aventur-shared/types/Common';
import { updateAdvisorRequest } from '@aventur-shared/modules/advisors/api';
import {
  Advisor,
  AdvisorId,
  AdvisorRoleEnum,
  AdvisorStatus,
} from '@aventur-shared/modules/advisors';

type Model = {
  id: AdvisorId;
  email: string;
  firstName: string;
  lastName: string;
  roles: AdvisorRoleEnum[];
  status: AdvisorStatus;
  reachedCompetentAdviserStatus: boolean;
  reachedCompetentAdviserStatusDate: Nullable<IDateTime>;
};

export default async (model: Model): Promise<Advisor> => {
  try {
    return await updateAdvisorRequest(model.id, {
      first_name: model.firstName,
      last_name: model.lastName,
      status: model.status,
      reached_competent_adviser_status: model.reachedCompetentAdviserStatus,
      reached_competent_adviser_date: model.reachedCompetentAdviserStatusDate
        ? model.reachedCompetentAdviserStatusDate?.formatForForm()
        : null,
      roles: model.roles,
    });
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    } else {
      throw new Error('Update advisor error');
    }
  }
};
