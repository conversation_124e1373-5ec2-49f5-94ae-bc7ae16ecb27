import { IDateTime } from '@aventur-shared/utils/dateTime';
import { Nullable } from '@aventur-shared/types/Common';
import { createAdvisorRequest } from '@aventur-shared/modules/advisors/api';
import {
  AdvisorRoleEnum,
  AdvisorStatus,
} from '@aventur-shared/modules/advisors';

type Model = {
  email: string;
  firstName: string;
  lastName: string;
  roles: AdvisorRoleEnum[];
  status: AdvisorStatus;
  reachedCompetentAdviserStatus: boolean;
  reachedCompetentAdviserStatusDate: Nullable<IDateTime>;
};

export default async (model: Model) => {
  try {
    await createAdvisorRequest({
      email: model.email,
      first_name: model.firstName,
      last_name: model.lastName,
      roles: model.roles,
      status: model.status,
      reached_competent_adviser_status: model.reachedCompetentAdviserStatus,
      reached_competent_adviser_date: model.reachedCompetentAdviserStatusDate
        ? model.reachedCompetentAdviserStatusDate?.formatForForm()
        : null,
    });
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    } else {
      throw new Error('Create advisor error');
    }
  }
};
