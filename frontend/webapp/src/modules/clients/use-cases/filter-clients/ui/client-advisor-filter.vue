<template>
  <div>
    <select-field
      label="Adviser"
      name="client-advisor-filter"
      :value="modelValue"
      :options="selectOptions"
      :can-clear="true"
      @on-select="(advisorId) => $emit('update:modelValue', advisorId)"
    />
  </div>
</template>

<script setup lang="ts">
  import { SelectField } from '@aventur-shared/components/form';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';
  import { onMounted } from 'vue';
  import { AdvisorId } from '@aventur-shared/modules/advisors';

  defineEmits(['update:modelValue']);
  defineProps<{
    modelValue?: AdvisorId;
  }>();

  const { activeAdvisors } = advisorsProvider().provide();
  const selectOptions = getAdvisorsSelectOptions(activeAdvisors.value);

  onMounted(async () => {
    await advisorsProvider().create();
  });
</script>
