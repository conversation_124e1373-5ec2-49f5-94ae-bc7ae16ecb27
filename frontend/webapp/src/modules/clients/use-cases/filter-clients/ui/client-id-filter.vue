<template>
  <div>
    <text-field
      label="Client ID"
      name="client-id-filter"
      :model-value="String(modelValue || '')"
      @on-change="(id) => $emit('update:modelValue', id)"
    />
  </div>
</template>

<script setup lang="ts">
  import { TextField } from '@aventur-shared/components/form';
  import { ClientId } from '@aventur-shared/modules/clients';

  defineEmits(['update:modelValue']);
  defineProps<{
    modelValue?: `${ClientId}`;
  }>();
</script>
