<template>
  <div>
    <select-field
      label="Status"
      name="client-status-filter"
      :value="modelValue"
      :options="selectOptions"
      :can-clear="true"
      @on-select="(status) => $emit('update:modelValue', status)"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { SelectField } from '@aventur-shared/components/form';
  import { ClientStatuses } from '@aventur-shared/modules/clients/models';
  import { getClientStatusSelectOptions } from '@modules/clients';
  import { useRefData } from '@aventur-shared/stores';

  const { getClientStatuses: clientStatusOptions } = storeToRefs(useRefData());
  const selectOptions = getClientStatusSelectOptions(clientStatusOptions.value);

  defineEmits(['update:modelValue']);
  defineProps<{
    modelValue?: ClientStatuses['id'];
  }>();
</script>
