<template>
  <div>
    <select-field
      label="Type"
      name="client-type-filter"
      :value="modelValue"
      :options="selectOptions"
      :can-clear="true"
      :searchable="false"
      @on-select="(status) => $emit('update:modelValue', status)"
    />
  </div>
</template>

<script setup lang="ts">
  import { SelectField } from '@aventur-shared/components/form';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { ClientTypeEnum } from '@aventur-shared/modules/clients/models';

  const selectOptions: SelectOption[] = [
    {
      label: 'Individual',
      value: ClientTypeEnum.Individual,
    },
    {
      label: 'Corporate',
      value: ClientTypeEnum.Corporate,
    },
    {
      label: 'Trust',
      value: ClientTypeEnum.Trust,
    },
  ];

  defineEmits(['update:modelValue']);
  defineProps<{
    modelValue?: ClientTypeEnum;
  }>();
</script>
