// import { toDTO } from '@modules/clients/api';
import getClients from '@aventur-shared/modules/clients/api/client/get-clients';
import { type Filters } from './filters';

interface ActionProps {
  query: QueryString;
}

interface QueryString
  extends Partial<{
    page: number;
    text: string;
    client_id: Filters['client_id'];
    type_id: Filters['type_id'];
    status_id: Filters['status_id'];
    advisor_id: Filters['advisor_id'];
  }> {}

const prepareQueryString = (
  query: Partial<ActionProps['query']>,
): QueryString => {
  const queryParams = {
    page: query.page,
    results_per_page: 10,
    type_id: Number(query.type_id),
    status_id: query.status_id ? Number(query.status_id) : undefined,
    client_id: query.client_id ? Number(query.client_id) : undefined,
    advisor_id: query.advisor_id ? Number(query.advisor_id) : undefined,
    text: query.text || '',
  };
  const filterEmptyQueryParts = (
    queryPart: [string, string | number | undefined],
  ) => queryPart[1];
  const queryEntries = Object.entries({
    ...queryParams,
  }).filter(filterEmptyQueryParts);

  return Object.fromEntries(queryEntries);
};

export const getClientsAction = async ({ query }: ActionProps) => {
  try {
    return await getClients(prepareQueryString(query));
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    }
    throw new Error('Something went wrong during case fetching.');
  }
};
