import { ClientId } from '@aventur-shared/modules/clients/types';
import { putClient } from '@aventur-shared/modules/clients/api';
import { type EditClientModel } from './edit-client-model';

export const editClientAction = async (
  editClientModel: EditClientModel,
): Promise<{ clientId: ClientId }> | never => {
  await putClient(editClientModel.id, editClientModel);

  return { clientId: editClientModel.id };
};
