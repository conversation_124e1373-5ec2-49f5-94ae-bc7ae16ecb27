import { Client } from '@aventur-shared/modules/clients';

export interface EditClientModel
  extends Pick<
    Client,
    | 'id'
    | 'title'
    | 'firstName'
    | 'lastName'
    | 'dateOfBirth'
    | 'email'
    | 'noEmailReasonId'
    | 'phoneNumber'
    | 'mobileNumber'
    | 'clientSource'
    | 'clientType'
    | 'clientStatus'
    | 'linkedClients'
    | 'clientAgreementId'
    | 'privacyNoticeId'
  > {
  clientOwner: Client['advisor']['id'];
}
