import { Client } from '@aventur-shared/modules/clients/models';

export interface AddNewClientModel
  extends Pick<
    Client,
    | 'title'
    | 'firstName'
    | 'lastName'
    | 'dateOfBirth'
    | 'phoneNumber'
    | 'mobileNumber'
    | 'clientSource'
    | 'clientType'
    | 'clientStatus'
    | 'noEmailReasonId'
    | 'linkedClients'
  > {
  clientOwner: Client['advisor']['id'];
  email: string | null;
}
