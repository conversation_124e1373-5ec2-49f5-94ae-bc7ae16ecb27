import { ClientId } from '@aventur-shared/modules/clients/types';
import {
  FormFieldsError,
  postClient,
} from '@aventur-shared/modules/clients/api/client/post-client';
import { AddNewClientModel } from './add-new-client-model';
import { AddNewClientFormError } from './add-new-client-error';

export const addNewClientAction = async (
  addNewClientModel: AddNewClientModel,
): Promise<{ newClientId: ClientId }> | never => {
  try {
    const postClientResponse = await postClient(addNewClientModel);

    return { newClientId: postClientResponse };
  } catch (e: unknown) {
    if (e instanceof FormFieldsError) {
      throw new AddNewClientFormError(e.formErrors);
    }

    throw new Error('Could not save client.');
  }
};
