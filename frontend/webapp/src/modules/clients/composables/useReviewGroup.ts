import { Ref, computed, ref, toValue } from 'vue';
import {
  AnnualReview,
  Frequency,
  QuarterlyReview,
  Review,
  ReviewFrequency,
  SemiAnnualReview,
} from '@aventur-shared/modules/clients/models/review-frequency';
import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

export class MonthsOptionsForFrequencyProvider {
  private reviews: Map<ReviewFrequency, Review> = new Map<
    ReviewFrequency,
    Review
  >([]);

  provide(frequency: ReviewFrequency): SelectOption[] {
    const review = this.getReview(frequency);
    return review.getMonths().map(([monthNames, monthNumber]) => {
      return {
        label: monthNames.join('/'),
        value: monthNumber,
      };
    });
  }

  private getReview(frequency: ReviewFrequency): Review {
    if (frequency === 'Annual') {
      this.reviews.set('Annual', new AnnualReview());
    } else if (frequency === 'SemiAnnual') {
      this.reviews.set('SemiAnnual', new SemiAnnualReview());
    } else if (frequency === 'Quarterly') {
      this.reviews.set('Quarterly', new QuarterlyReview());
    }

    return this.reviews.get(frequency) as Review;
  }
}

const reviewFrequencies = <ReviewFrequency[]>[
  'Annual',
  'SemiAnnual',
  'Quarterly',
];

export const reviewFrequencyOptions: SelectOption[] = reviewFrequencies.map(
  (frequency) => ({
    label: new Frequency(frequency).toString(),
    value: frequency,
  }),
);

export function useReviewSetupOptions(reviewFrequency) {
  const review_frequency_options = reviewFrequencyOptions;
  const reviewMonthsOptionsProvider = ref(
    new MonthsOptionsForFrequencyProvider(),
  );

  const review_month_options: Ref<SelectOption[]> = computed(() => {
    return toValue(reviewFrequency)
      ? reviewMonthsOptionsProvider.value.provide(toValue(reviewFrequency))
      : [];
  });

  return { review_frequency_options, review_month_options };
}
