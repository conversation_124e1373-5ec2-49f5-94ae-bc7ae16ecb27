import * as yup from 'yup';
import { validationMessages } from '@aventur-shared/utils/form';
import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
import {
  ClientLink,
  ClientStatus,
  ClientTypeEnum,
} from '@aventur-shared/modules/clients';

yup.addMethod(yup.array, 'uniqueValueCheck', function (field, message) {
  return this.test('unique', message, function (tcasts) {
    const array = tcasts || [];
    const uniqueData = Array.from(
      new Set(
        array.map((row) => {
          return row[field];
        }),
      ),
    );
    const isUnique = array.length === uniqueData.length;
    if (isUnique) {
      return true;
    }
    const index = array.findIndex((row, i) => {
      return row[field] !== uniqueData[i];
    });
    if (array[index] && array[index][field] === '') {
      return true;
    }

    return this.createError({
      path: `${this.path}[${index}].${field}`,
      message,
    });
  });
});

const relationSchema = yup.object().shape({
  relatedClientId: yup
    .number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .required(validationMessages.fieldRequiredMessage)
    .test('unique', 'This Client is already linked', function (this: any, val) {
      if (!val) return true;
      const linkedClients = this.from[1].value.linkedClients as ClientLink[];
      const ids = linkedClients
        .filter((link) => link)
        .map((link) => link.linkedClientId);
      return ids.filter((id) => id === val).length === 1;
    }),
  relationshipType: yup
    .number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .required(validationMessages.fieldRequiredMessage),
});

export const validationSchema = yup.object().shape({
  clientType: yup.number().required(validationMessages.fieldRequiredMessage),
  clientStatus: yup.number().required(validationMessages.fieldRequiredMessage),
  title: yup.number().transform((val) => val || undefined),
  firstName: yup.string().required(validationMessages.fieldRequiredMessage),
  lastName: yup.string().required(validationMessages.fieldRequiredMessage),
  dateOfBirth: yup.string().nullable().default(''),
  email: yup
    .string()
    .nullable()
    .default(null)
    .email(validationMessages.invalidEmailMessage)
    .transform((value) => value || null)
    .test(
      'email-or-reason',
      validationMessages.emailOrReasonMessage,
      function (value) {
        const { noEmailReason } = this.parent;
        if (value && noEmailReason) {
          return this.createError({
            message: validationMessages.emailAndReasonMessage,
          });
        }
        return !!(value || noEmailReason);
      },
    ),
  noEmailReason: yup
    .string()
    .nullable()
    .test(
      'reason-or-email',
      validationMessages.reasonOrEmailMessage,
      function (value) {
        const { email } = this.parent;
        if (email && value) {
          return this.createError({
            message: validationMessages.emailAndReasonMessage,
          });
        }
        return !!(email || value);
      },
    ),
  phoneNumber: yup
    .string()
    .nullable()
    .default('')
    .checkIsCorrectPhoneNumber('Number should be correct'),
  mobileNumber: yup
    .string()
    .nullable()
    .default('')
    .checkIsCorrectPhoneNumber('Number should be correct'),
  clientSource: yup
    .number()
    .nullable()
    .required(validationMessages.fieldRequiredMessage),
  clientOwner: yup
    .number()
    .nullable()
    .required(validationMessages.fieldRequiredMessage),
  relations: yup.array().of(relationSchema),
});

export const clientTypeOptions: SelectOption[] = [
  {
    label: 'Individual',
    value: ClientTypeEnum.Individual,
  },
  {
    label: 'Corporate',
    value: ClientTypeEnum.Corporate,
  },
  {
    label: 'Trust',
    value: ClientTypeEnum.Trust,
  },
];

export type FormValues = Partial<{
  clientStatus: ClientStatus;
  title: number;
  firstName: string;
  lastName: string | null;
  dateOfBirth: string | null;
  email: string | null;
  noEmailReason: number | null;
  phoneNumber: string | null;
  mobileNumber: string | null;
  clientSource: number;
  clientOwner: number;
  linkedClients: Partial<ClientLink>[];
  clientType: ClientTypeEnum;
  marketingId: number | null;
  privacyNoticeId: number | null;
  clientAgreementId: number | null;
}>;

export const initialValues: FormValues = {
  clientType: ClientTypeEnum.Individual,
  clientStatus: undefined,
  clientOwner: undefined,
  clientSource: undefined,
  dateOfBirth: undefined,
  email: undefined,
  noEmailReason: undefined,
  firstName: undefined,
  lastName: undefined,
  phoneNumber: undefined,
  mobileNumber: undefined,
  title: undefined,
  linkedClients: [],
};

export const toBeClientLink = (toBeLink: any): toBeLink is ClientLink => {
  return 'linkTypeId' in toBeLink && 'linkedClientId' in toBeLink;
};
