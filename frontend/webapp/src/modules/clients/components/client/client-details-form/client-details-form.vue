<template>
  <div class="flex flex-col xl:w-3/4">
    <form @submit="onSubmit">
      <box class="mb-8">
        <box-section divider="bottom">
          <h2 class="text-base font-bold text-gray-400">{{ props.title }}</h2>
        </box-section>
        <box-section>
          <div class="grid xl:grid-cols-2 xl:gap-4">
            <radio-group
              :options="clientTypeOptions"
              name="clientType"
              label="Select the client type:"
              :is-required="true"
            />
            <select-field
              :options="getClientStatusSelectOptions(getClientStatuses)"
              name="clientStatus"
              label="Client status"
              :is-required="true"
            />
          </div>

          <div class="grid xl:grid-cols-5 xl:gap-4">
            <div>
              <select-field
                name="title"
                autocomplete="honorific-prefix"
                label="Title"
                :options="
                  getTitles.map((t) => ({ label: t.name, value: t.id }))
                "
              />
            </div>

            <div class="xl:col-span-2">
              <text-field
                name="firstName"
                autocomplete="given-name"
                label="First name"
                :is-required="true"
              />
            </div>

            <div class="xl:col-span-2">
              <text-field
                name="lastName"
                autocomplete="given-name"
                label="Last name"
                :is-required="true"
              />
            </div>
          </div>

          <div class="grid xl:grid-cols-4">
            <date-picker name="dateOfBirth" label="Date of Birth" />
          </div>
        </box-section>
      </box>

      <box class="mb-8">
        <box-section divider="bottom">
          <h2 class="text-base font-bold text-gray-400">Contact Details</h2>
        </box-section>
        <box-section>
          <div class="grid xl:grid-cols-2 xl:gap-x-4">
            <email-field
              name="email"
              autocomplete="given-name"
              label="Email address"
            />
            <select-field
              label="No email address available?"
              name="noEmailReason"
              placeholder="Select a reason for no email address"
              :options="reasons.map((r) => ({ label: r.reason, value: r.id }))"
              :can-clear="true"
              :searchable="false"
            />
          </div>
          <Alert class="mb-4 text-sm" type="warning">
            <p>
              Changing email will reset sign-in details. Client will be logged
              off to login using new email address and set a new password.
              Removing email address will disable client access.
            </p>
          </Alert>
          <div class="grid xl:grid-cols-2 xl:gap-x-4">
            <text-field
              name="phoneNumber"
              autocomplete="given-name"
              label="Phone number"
            />
            <text-field
              name="mobileNumber"
              autocomplete="given-name"
              label="Mobile number"
            />
          </div>

          <div>
            <Alert
              v-if="props.formValues?.marketingId"
              type="success"
              class="text-sm"
            >
              Marketing contact configured. Click
              <router-link
                :to="`/clients/${clientId}/factfind/marketing`"
                class="whitespace-nowrap underline hover:cursor-pointer hover:text-blue-600 hover:no-underline"
                >here
              </router-link>
              to manage marketing preferences.
            </Alert>
            <Alert v-else type="info" class="text-sm">
              Missing marketing contact. Save client details to update
              configuration.
            </Alert>
          </div>
        </box-section>
      </box>

      <box class="mb-8">
        <box-section divider="bottom">
          <h2 class="text-base font-bold text-gray-400">Aventur Setup</h2>
        </box-section>
        <box-section>
          <div class="grid xl:grid-cols-2 xl:gap-4">
            <select-field
              name="clientSource"
              label="Client source"
              :options="clientSourcesOptions"
              :is-required="true"
              :groups="true"
              :disabled="isClientSourceSelected && !isSuperAdmin"
            />
            <select-field
              name="clientOwner"
              label="Client owner"
              :options="getAdvisorsSelectOptions(activeAdvisors)"
              :is-required="true"
              :is-busy="isBusy"
              is-busy-text="Loading..."
            />
            <select-field
              name="clientAgreementId"
              label="Client agreement"
              :searchable="false"
              :options="clientAgreementOptions"
            />
            <select-field
              name="privacyNoticeId"
              label="Privacy Notice"
              :searchable="false"
              :options="privacyNoticeOptions"
            />
          </div>
        </box-section>
      </box>

      <box class="mb-8">
        <box-section divider="bottom">
          <h2 class="text-base font-bold text-gray-400">
            Links to Exiting Clients
          </h2>
        </box-section>
        <box-section>
          <FieldArray
            v-slot="{ fields, push, remove }: any"
            name="linkedClients"
          >
            <div v-for="(field, idx) in fields" :key="field.key" class="mb-4">
              <div
                class="flex items-center justify-center gap-4 border-b border-none border-gray-300 pb-4 xl:pb-0"
              >
                <select-field
                  class="flex-1"
                  :name="`linkedClients[${idx}].linkedClientId`"
                  autocomplete="honorific-prefix"
                  label="Client"
                  :options="
                    linkFieldOptions.map((o: SelectOption) => ({
                      ...o,
                      disabled: isAlreadySelected(fields)(o),
                    }))
                  "
                  :searchable="true"
                  no-results-text="Client not found."
                  :hide-disabled-options="true"
                />
                <select-field
                  class="flex-1"
                  :name="`linkedClients[${idx}].linkTypeId`"
                  autocomplete="honorific-prefix"
                  label="Connection Type"
                  :options="linkTypesOptions"
                />
                <div class="mt-4">
                  <button
                    type="button"
                    class="text-gray-400 hover:text-gray-700"
                    @click.stop="remove(idx)"
                  >
                    <XMarkIcon class="size-5" />
                  </button>
                </div>
              </div>
            </div>
            <custom-button
              v-if="linkFieldOptions.length !== fields?.length"
              theme="secondary"
              type="button"
              @on-click="
                push({
                  linkTypeId: undefined,
                  linkedClientId: undefined,
                })
              "
            >
              Add a New Link
            </custom-button>
          </FieldArray>
        </box-section>
      </box>

      <div class="text-end">
        <custom-button type="submit" :is-busy="isSubmitBusy" theme="primary">
          {{ clientId ? 'Save' : 'Save & add client +' }}
        </custom-button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
  import { onBeforeRouteLeave } from 'vue-router';
  import { Ref, computed, onMounted, ref, watch } from 'vue';
  import {
    FieldArray,
    useForm,
    useIsFormDirty,
    useIsFormValid,
  } from 'vee-validate';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import { useRefData } from '@aventur-shared/stores';
  import {
    DatePicker,
    EmailField,
    RadioGroup,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import {
    SelectOption,
    SelectOptionGroup,
    groupOptions,
  } from '@aventur-shared/components/form/fields/field-model';
  import { ClientId, ClientLink } from '@aventur-shared/modules/clients';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { getActiveClients } from '@aventur-shared/modules/refdata';
  import { useNoEmailReasons } from '@/refdata/no-email-reasons/use-no-email-reasons';
  import { Alert, Box, BoxSection, Button as CustomButton } from '@modules/ui';
  import { getClientStatusSelectOptions } from '@modules/clients';
  import {
    FormValues,
    clientTypeOptions,
    validationSchema,
  } from './form-model';
  import {
    clientAgreementToSelectOptions,
    privacyNoticeToSelectOptions,
  } from '@modules/clients/components/client/client-details-form/index';

  const toast = useToast();
  const { isSuperAdmin } = useUserStore();
  const { reasons } = useNoEmailReasons();
  const {
    getTitles,
    getRelationshipTypes,
    getClientSources,
    getClientStatuses,
  } = useRefData();

  const props = defineProps<{
    formValues?: FormValues;
    title: string;
    clientId?: ClientId;
    formErrors?: any;
    isSubmitBusy: boolean;
  }>();
  const emits = defineEmits<{
    (e: 'onFormSubmit', formValues: FormValues): void;
  }>();

  const { handleSubmit, setErrors, resetForm } = useForm<FormValues>({
    validationSchema,
    initialValues: props.formValues,
  });

  watch(
    () => props.formErrors,
    () => {
      setErrors(props.formErrors);
    },
  );

  watch(
    () => props.formValues,
    () => {
      resetForm({ values: props.formValues });
    },
  );

  const isFormDirty = useIsFormDirty();
  const isFormValid = useIsFormValid();

  const linkFieldOptions = ref<SelectOption[]>([]);
  const linkTypesOptions = computed(() =>
    getRelationshipTypes.map((item) => ({
      label: item.name,
      value: item.id,
    })),
  );
  const clientSourcesOptions = computed<SelectOptionGroup[]>(() =>
    groupOptions(getClientSources),
  );
  const isClientSourceSelected = computed(
    () => !!props.formValues?.clientSource,
  );

  const clientAgreementOptions = clientAgreementToSelectOptions();
  const privacyNoticeOptions = privacyNoticeToSelectOptions();

  const { activeAdvisors, isBusy } = advisorsProvider().provide();

  onMounted(async () => {
    await advisorsProvider().create();
    const [clients] = await Promise.all([getActiveClients()]);
    linkFieldOptions.value = clients
      .filter((client) => client.id !== props.clientId)
      .map((client) => ({
        value: client.id,
        label: `${client.firstName} ${client.lastName}`,
      }));
  });

  onBeforeRouteLeave(async () => {
    if (isFormValid.value && isFormDirty.value) {
      const { isAccepted } = await useConfirmation(
        `Are you sure you want to leave? Your data won’t be saved.`,
      );

      return isAccepted();
    }

    return true;
  });

  const onSubmit = handleSubmit(
    async (formValues) => {
      setErrors({});

      emits('onFormSubmit', validationSchema.cast(formValues) as FormValues);
      resetForm({
        values: formValues,
      });
    },
    () => {
      toast.error('Please fill in the required fields.');
    },
  );

  const isAlreadySelected = (fields: Array<Ref<ClientLink>>) => {
    const selectedIds = fields.map(
      (field) => field.value && field.value.linkedClientId,
    );
    return (option: SelectOption) => selectedIds.includes(Number(option.value));
  };
</script>
