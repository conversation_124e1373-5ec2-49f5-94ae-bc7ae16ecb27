<template>
  <box-section
    :divider="'bottom'"
    :class="linkItem ? 'cursor-pointer' : undefined"
    @click.stop="linkItem && $emit('link-item-click', linkItem.link)"
  >
    <template #default>
      <div
        v-if="linkItem"
        class="flex items-center justify-between"
        :class="linkItem.statusValue ? '-my-3' : ''"
      >
        <div class="flex flex-col">
          <span data-testid="item-title" class="font-medium">{{
            linkItem.title
          }}</span>
          <div
            v-if="linkItem.statusValue"
            data-testid="optional-status"
            class="flex flex-row items-center gap-2"
          >
            <check-circle-icon
              v-if="linkItem.statusValue.status === Status.Good"
              class="size-5"
              style="fill: green"
            />
            <question-mark-circle-icon
              v-else-if="linkItem.statusValue.status === Status.Medium"
              class="size-5"
              style="fill: orange"
            />
            <exclamation-circle-icon v-else class="size-5" style="fill: red" />
            <p data-testid="status-description" class="text-sm">
              {{ linkItem.statusValue.description }}
            </p>
          </div>
        </div>
        <span data-testid="chevron-icon" class="text-primary">
          <chevron-right-icon class="size-5" />
        </span>
      </div>
      <slot />
    </template>
  </box-section>
</template>

<script setup lang="ts">
  import {
    CheckCircleIcon,
    ChevronRightIcon,
    ExclamationCircleIcon,
    QuestionMarkCircleIcon,
  } from '@heroicons/vue/24/solid';
  import { BoxSection } from '@modules/ui';
  import {
    Status,
    StatusLinkItem,
  } from '@modules/clients/components/client/personal-profile-status/types';

  withDefaults(
    defineProps<{
      linkItem?: StatusLinkItem;
    }>(),
    {
      linkItem: undefined,
    },
  );

  defineEmits<{
    (e: 'link-item-click', link: string): void;
  }>();
</script>
