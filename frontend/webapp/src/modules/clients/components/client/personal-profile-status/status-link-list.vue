<template>
  <box>
    <template v-for="linkItem in props.links" :key="linkItem.title">
      <status-link-list-item
        :link-item="linkItem"
        @link-item-click="handleClick"
      />
    </template>
    <status-link-list-item>
      <client-access />
    </status-link-list-item>
  </box>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import { Box } from '@modules/ui';
  import { StatusLinkListProps } from '@modules/clients/components/client/personal-profile-status/types';
  import StatusLinkListItem from '@modules/clients/components/client/personal-profile-status/status-link-list-item.vue';
  import { ClientAccess } from '@modules/clients/components/client';

  const router = useRouter();

  const props = defineProps<StatusLinkListProps>();

  const handleClick = (link: string) => {
    router.push({ name: link });
  };
</script>
