<template>
  <div>
    <h2 class="text-2xl font-semibold">
      {{ viewModel.formatClientLabel(props.client) }}
    </h2>
    <span class="pr-3 text-base text-gray-500">Client status</span>
    <span class="text-base font-bold">{{
      getStatusNameById(client.clientStatus)
    }}</span>
  </div>
</template>

<script setup lang="ts">
  import useClientStatus from '@aventur-shared/composables/useClientStatus';
  import { ClientProps } from './types';
  import * as viewModel from './viewModel';

  const props = defineProps<{ client: ClientProps }>();
  const { getStatusNameById } = useClientStatus();
</script>
