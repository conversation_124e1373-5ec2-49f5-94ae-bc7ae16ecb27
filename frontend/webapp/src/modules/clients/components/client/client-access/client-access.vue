<template>
  <custom-button
    class="text-primary-900 !m-0 !p-0"
    :class="{ 'cursor-not-allowed hover:no-underline': isDisabled }"
    type="button"
    theme="text-like"
    :is-busy="inProgress"
    :disabled="isDisabled"
    :title="!client?.email ? 'Missing client email' : null"
    @on-click="handleClick"
  >
    {{ caption }}
  </custom-button>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useConfirmation } from '@aventur-shared/composables/useConfirmation';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useUserAbility } from '@aventur-shared/modules/users';
  import { useClientStore } from '@aventur-shared/modules/clients';
  import { setClientAccess } from '@aventur-shared/modules/clients/api';
  import { Button as CustomButton } from '@modules/ui';

  const toast = useToast();
  const ability = useUserAbility();
  const { getProfile: client } = storeToRefs(useClientStore());

  const inProgress = ref(false);
  const isDisabled = computed(
    () =>
      !(
        client.value.email &&
        client.value.accessEnabled !== null &&
        ability.can('set_client_access_api_internal_v1_clients', 'all')
      ),
  );
  const caption = computed(() =>
    client.value.accessEnabled
      ? 'Disable Client Access'
      : 'Enable Client Access',
  );

  const toggleClientAccess = async (enabled: boolean) => {
    try {
      inProgress.value = true;
      await setClientAccess(client.value.id, enabled);
      client.value.accessEnabled = enabled;
      toast.success('Client access request successful.');
    } catch (e) {
      toast.error(e as Error);
    } finally {
      inProgress.value = false;
    }
  };

  const handleClick = async () => {
    if (!client.value.accessEnabled) {
      const { onAccept } = await useConfirmation(
        `A new temporary password will be sent to ${client.value.email}. Continue?`,
      );
      return onAccept(
        async () => await toggleClientAccess(!client.value.accessEnabled),
      );
    }
    if (client.value.accessEnabled) {
      const { onAccept } = await useConfirmation(
        `Revoke client access for ${client.value.email}?`,
      );
      return onAccept(
        async () => await toggleClientAccess(!client.value.accessEnabled),
      );
    }
  };
</script>
