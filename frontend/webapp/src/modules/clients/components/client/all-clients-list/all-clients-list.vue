<template>
  <t-table class="bg-white" wrapper-class="rounded">
    <t-head>
      <t-head-tr>
        <t-head-th> ID </t-head-th>
        <t-head-th> Name </t-head-th>
        <t-head-th> Email </t-head-th>
        <t-head-th> Type </t-head-th>
        <t-head-th> Next review </t-head-th>
        <t-head-th> Owner </t-head-th>
        <t-head-th> Status </t-head-th>
        <t-head-th> Acc </t-head-th>
        <t-head-th> Links </t-head-th>
      </t-head-tr>
    </t-head>
    <t-body>
      <t-body-tr v-for="person in clients" :key="person.id">
        <t-body-td>
          {{ person.id }}
        </t-body-td>
        <t-body-td>
          <router-link
            class="border-b border-dotted border-b-black hover:border-solid"
            :to="{ name: 'client-overview', params: { id: person.id } }"
          >
            {{ formatName(person) }}
          </router-link>
        </t-body-td>
        <t-body-td>
          {{ person.email }}
        </t-body-td>
        <t-body-td>
          {{ formatClientType(person.clientTypeId) }}
        </t-body-td>
        <t-body-td>
          {{ nextReviewLabel(person) }}
        </t-body-td>
        <t-body-td>
          <template v-if="person.advisor">
            {{
              formatName({
                firstName: person.advisor.firstName || '',
                lastName: person.advisor.lastName || '',
              })
            }}
          </template>
          <template v-else> New client </template>
        </t-body-td>
        <t-body-td>
          {{ getStatusNameById(person.clientStatusId) }}
        </t-body-td>
        <t-body-td>
          {{ person.accountsCount }}
        </t-body-td>
        <t-body-td>
          {{ person.linksCount }}
        </t-body-td>
      </t-body-tr>
    </t-body>
  </t-table>
</template>
<script setup lang="ts">
  import { formatName } from '@aventur-shared/utils/user';
  import { ClientList } from '@aventur-shared/modules/clients/models';
  import { formatClientType } from '@aventur-shared/modules/clients/models';
  import useClientStatus from '@aventur-shared/composables/useClientStatus';
  import {
    Frequency,
    Month,
  } from '@aventur-shared/modules/clients/models/review-frequency';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui/table';

  function nextReviewLabel(client: ClientList['clients'][0]) {
    const frequency = client.reviewFrequency
      ? new Frequency(client.reviewFrequency)
      : null;
    const month = client.reviewMonth ? new Month(client.reviewMonth) : null;
    return frequency && month ? `${frequency}, ${month}` : '-';
  }

  defineProps<{
    clients: ClientList['clients'];
  }>();

  const { getStatusNameById } = useClientStatus();
</script>
