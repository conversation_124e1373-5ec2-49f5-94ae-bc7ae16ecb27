<template>
  <box>
    <box-section divider="bottom" class="flex justify-between">
      <h3 class="text-base font-semibold text-gray-900">Personal details</h3>
      <span class="text-primary">
        <router-link :to="`/clients/${client.id}/edit`">Edit</router-link>
      </span>
    </box-section>
    <box-section>
      <dl>
        <div
          v-for="detail in getDetails(props.client)"
          :key="detail.title"
          class="py-4"
        >
          <dt class="text-sm text-gray-500">{{ detail.title }}</dt>
          <dd
            class="mt-1 text-base font-medium text-gray-900 sm:col-span-2 sm:mt-0"
          >
            <template v-if="detail.type === 'text'">{{
              toValue(detail.value)
            }}</template>
            <template v-if="detail.type === 'link'">
              <a :href="detail.href" class="text-secondary break-words"
                >{{ toValue(detail.value) }}
                <ArrowTopRightOnSquareIcon
                  class="text-secondary inline size-5"
                />
              </a>
            </template>
            <template v-if="detail.type === 'list'">
              <ul>
                <li
                  v-for="(item, itemIndex) in detail.items"
                  :key="`item-${itemIndex}`"
                >
                  <a :href="item.href" class="text-secondary break-words"
                    >{{ toValue(item.value) }}
                  </a>
                </li>
              </ul>
            </template>
          </dd>
        </div>
      </dl>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { toValue } from 'vue';
  import { ArrowTopRightOnSquareIcon } from '@heroicons/vue/20/solid';
  import { Client } from '@aventur-shared/modules/clients';
  import { Box, BoxSection } from '@modules/ui';
  import { getDetails } from './view-model';

  const props = defineProps<{ client: Client }>();
</script>
