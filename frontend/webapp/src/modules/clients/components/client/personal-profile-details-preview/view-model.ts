import { computed } from 'vue';
import { find, get } from 'lodash';

import { Client } from '@aventur-shared/modules/clients';
import { formatWithMonthName } from '@aventur-shared/utils/dateTime';
import { formatName } from '@aventur-shared/utils/user';
import { useRefData } from '@aventur-shared/stores';
import { ViewDetail } from '@aventur-shared/types/ViewDetail';
import { ClientLink } from '@aventur-shared/modules/clients/models';
import { formatAddressForView } from '@aventur-shared/services/address';

import { useNoEmailReasons } from '@/refdata/no-email-reasons/use-no-email-reasons';
//

export const formatAdvisorLabel = (advisor?: Client['advisor']): string => {
  const firstName = advisor?.firstName || '';
  const lastName = advisor?.lastName || '';
  return formatName({ firstName, lastName });
};

export const formatClientLink = (link: ClientLink): string => {
  const { getRelationshipTypes } = useRefData();
  const relationName = getRelationshipTypes.find(
    (element) => element.id == link.linkTypeId,
  )?.name;
  const relationNameFormatted = relationName ? `(${relationName})` : '';
  return `${formatName(link)} ${relationNameFormatted}`;
};

export const getDetails = (client: Client): ViewDetail[] => {
  const { reasons: noEmailReasons } = useNoEmailReasons();
  return [
    {
      title: 'Advisor',
      value: formatAdvisorLabel(client.advisor),
      type: 'text',
    },
    {
      title: 'Mobile number',
      value: client.mobileNumber || '',
      type: 'text',
    },
    {
      title: 'Phone number',
      value: client.phoneNumber || '',
      type: 'text',
    },
    {
      title: 'Email address',
      value: computed(
        () =>
          client.email ||
          'N/A - ' +
            get(find(noEmailReasons, { id: client.noEmailReasonId }), 'reason'),
      ),
      type: client.email ? 'link' : 'text',
      href: `mailto:${client.email}`,
    },
    {
      title: 'Primary Address',
      value: formatAddressForView(
        client.addresses.filter((address) => address.isPrimary)[0] || {},
      ),
      type: 'text',
    },
    {
      title: 'Date of birth',
      value: client.dateOfBirth ? formatWithMonthName(client.dateOfBirth) : '',
      type: 'text',
    },
    {
      title: 'Linked client',
      items: client.linkedClients.map((link) => ({
        href: `/clients/${link.linkedClientId}`,
        title: '',
        type: 'link',
        value: formatClientLink(link),
      })),
      type: 'list',
    },
  ];
};
