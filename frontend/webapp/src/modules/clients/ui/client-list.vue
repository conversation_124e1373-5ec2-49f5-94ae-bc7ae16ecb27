<template>
  <div class="flex flex-col">
    <div class="mb-4 text-end">
      <button
        type="button"
        class="bg-primary rounded-md p-2 text-white"
        @click="handleAddNewClientClick"
      >
        Add Client
      </button>
    </div>

    <filters-panel
      ref="filtersPanel"
      title="Client filters"
      :filters="filters"
      :text-search="true"
      text-search-hint="Search by client first name, last name or email"
      @filters:reset="handleResetFilters"
      @filters:apply="handleUpdateFilters"
    >
      <div
        class="grid w-full grid-cols-1 items-center justify-items-stretch gap-4 sm:grid-cols-2 lg:grid-cols-3"
      >
        <client-id-filter v-model="filters.client_id" />
        <client-advisor-filter v-model="filters.advisor_id" />
        <client-type-filter v-model="filters.type_id" />
        <client-status-filter v-model="filters.status_id" />
      </div>
    </filters-panel>

    <table-with-pagination
      :pagination="{
        perPage: tableService.config.perPage,
        activePage: unref(tableService.page),
        totalItems: unref(tableService.config.totalItems),
      }"
      :show-results="true"
      @on-page-change="changePage"
    >
      <all-clients-list v-if="!error" :clients="clients" />
    </table-with-pagination>
    <div v-if="error">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, unref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useClientListStore } from '@aventur-shared/modules/clients';
  import { AllClientsList } from '@modules/clients/components/client';
  import FiltersPanel from '@modules/ui/filters-panel/filters-panel.vue';
  import {
    ClientAdvisorFilter,
    ClientIdFilter,
    ClientStatusFilter,
    ClientTypeFilter,
    type Filters,
    getClientsAction,
  } from '@modules/clients/use-cases/filter-clients';
  import TableWithPagination from '@modules/ui/table-with-pagination/table-with-pagination.vue';
  import { useTableService } from '@aventur-shared/utils/table/use-table-service';
  import { useFilterStore } from '@modules/ui/filters-panel/filters-store';

  const router = useRouter();
  const route = useRoute();
  const { getLastFilter } = useFilterStore();

  const { clients, totalClientCount, error } = storeToRefs(
    useClientListStore.useStore(),
  );
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const activePage = ref(1);
  const handleAddNewClientClick = () => {
    router.push({ name: 'add-new-client' });
  };

  const { userId, isAdvisor } = useUserStore();

  const { filters, ...tableService } = useTableService<Filters>('clients', {
    initialPage: 1,
    perPage: 10,
    totalItems: clients.value.length,
    filter: {
      advisor_id: undefined,
    },
  });

  watch(clients, () => {
    tableService.setTotalItems(totalClientCount.value || 0);
  });

  onMounted(() => {
    handleUpdateFilters(
      getLastFilter(route.name) ??
        (isAdvisor ? { advisor_id: Number(userId) } : filters.value),
    );
  });

  const changePage = (page: number) => {
    tableService
      .changePage(page)
      .apply()
      .then(() => fetchClients());
  };

  const handleUpdateFilters = (filters: Partial<Filters>) => {
    tableService
      .changePage(1)
      .addFilter(filters)
      .apply()
      .then(() => fetchClients());
  };

  const handleResetFilters = () => {
    tableService
      .resetFilters(['type_id', 'status_id', 'advisor_id', 'client_id', 'text'])
      .changePage(1)
      .addFilter(isAdvisor ? { advisor_id: Number(userId) } : {})
      .apply()
      .then(() => fetchClients());
  };

  const fetchClients = async () => {
    return getClientsAction({
      query: {
        page: tableService.page.value,
        ...filters.value,
      },
    }).then((response) => {
      clients.value = response.clients;
      totalClientCount.value = response.totalClientCount;
    });
  };
</script>
