import { apiClient } from '@aventur-shared/services/api';
import { InferType, array, number, object, string } from 'yup';

const getClientsSchema = array()
  .of(
    object({
      id: number().required(),
      first_name: string().required(),
      last_name: string().nullable(),
    }),
  )
  .required();

type ClientsDTO = InferType<typeof getClientsSchema>;

export default async (providerId: number) => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/internal/v1/query/get-clients-related-to-holding`,
    { provider_id: providerId },
  );

  const clientsDTO: ClientsDTO = await getClientsSchema.validate(response);

  return clientsDTO.map((client) => ({
    id: client.id,
    firstName: client.first_name,
    lastName: client.last_name ?? null,
  }));
};
