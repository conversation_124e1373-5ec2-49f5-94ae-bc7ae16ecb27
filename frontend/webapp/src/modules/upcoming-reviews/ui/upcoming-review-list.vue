<template>
  <page-title class="mb-4">Upcoming Reviews</page-title>

  <filters-panel
    title="Upcoming reviews filters"
    :filters="filters"
    :text-search="true"
    text-search-hint="Search by client first name, last name or email"
    @filters:reset="handleResetFilters"
    @filters:apply="handleUpdateFilters"
  >
    <div
      class="grid w-full grid-cols-1 items-center justify-items-stretch gap-4 sm:grid-cols-2 lg:grid-cols-3"
    >
      <client-owner-filter v-model="filters.client_owner_id" />
      <case-adviser-filter v-model="filters.case_adviser_id" />
      <case-status-filter v-model="filters.case_status" />
    </div>
    <div class="flex flex-row justify-start py-2">
      <time-limited-filter v-model="filters.time_limited" />
    </div>
  </filters-panel>

  <table-with-pagination
    class="pt-8"
    :pagination="{
      activePage: unref(tableService.page),
      totalItems: unref(tableService.config.totalItems),
      perPage: tableService.config.perPage,
    }"
    :show-results="true"
    @on-page-change="changePage"
  >
    <t-table class="bg-white" wrapper-class="rounded">
      <t-head>
        <t-head-tr>
          <t-head-th>Client(s)</t-head-th>
          <t-head-th>Review Date</t-head-th>
          <t-head-th>Review Type</t-head-th>
          <t-head-th>Client Adviser</t-head-th>
          <t-head-th>Case Status</t-head-th>
          <t-head-th>Case Adviser</t-head-th>
          <t-head-th>&nbsp;</t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="upcomingReview in upcomingReviews"
          :key="upcomingReview.id"
        >
          <t-body-td>
            <p
              v-for="client in upcomingReview.clients"
              :key="client.id"
              class="w-fit max-w-52 truncate border-b border-dotted border-b-black hover:border-solid"
            >
              <router-link
                :to="{
                  name: 'client-overview',
                  params: { id: client.id },
                }"
              >
                {{ `${client.firstName} ${client.lastName}` }}
              </router-link>
            </p>
          </t-body-td>
          <t-body-td>
            {{
              formatReviewDate(
                upcomingReview.reviewMonth,
                upcomingReview.reviewYear,
              )
            }}
          </t-body-td>
          <t-body-td> {{ upcomingReview.reviewType.toLabel() }} </t-body-td>
          <t-body-td>{{
            formatAdviser(upcomingReview.clientOwnerId)
          }}</t-body-td>
          <t-body-td>
            {{
              upcomingReview.caseData
                ? formatCaseStatus(upcomingReview.caseData.status)
                : '---'
            }}
          </t-body-td>
          <t-body-td>
            {{
              upcomingReview.caseData
                ? formatAdviser(upcomingReview.caseData.adviserId)
                : '---'
            }}
          </t-body-td>
          <t-body-td>
            <div v-if="!upcomingReview.caseData">
              <create-case-modal
                :passed-in-review-slot-id="upcomingReview.id"
                :passed-in-adviser-id="upcomingReview.clientOwnerId"
                :passed-in-case-type="upcomingReview.reviewType"
                :clients="upcomingReview.clients"
                :open-button-title="'Create\r\ncase'"
                :button-class="''"
              />
            </div>
            <div v-else>
              <button
                type="button"
                class="bg-secondary rounded-md px-5 py-2 text-white"
                style="width: 80%"
                @click="
                  handleViewCaseClick(
                    upcomingReview.clients[0].id,
                    upcomingReview.caseData.id as CaseId,
                  )
                "
              >
                View Case
              </button>
            </div>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </table-with-pagination>
</template>

<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router';
  import { onMounted, ref, unref, watch } from 'vue';
  import { useTableService } from '@aventur-shared/utils/table/use-table-service';
  import { UpcomingReview } from '@aventur-shared/modules/upcoming-reviews/models/upcoming-reviews';
  import { advisorsProvider } from '@aventur-shared/modules/advisors';
  import { CaseId } from '@aventur-shared/modules/cases';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui/table';
  import { TableWithPagination } from '@modules/ui';
  import FiltersPanel from '@modules/ui/filters-panel/filters-panel.vue';
  import {
    CaseAdviserFilter,
    CaseStatusFilter,
    ClientOwnerFilter,
    type Filters,
    TimeLimitedFilter,
  } from '@modules/upcoming-reviews/use-cases/filter-upcoming-reviews';
  import PageTitle from '@modules/ui/text/page-title.vue';
  import { getUpcomingReviewsAction } from '@modules/upcoming-reviews/use-cases/view-upcoming-reviews/action';
  import CreateCaseModal from '@modules/clients-cases/use-cases/user-creates-case/ui/create-case-modal/create-case-modal.vue';
  import { useFilterStore } from '@modules/ui/filters-panel/filters-store';

  const router = useRouter();
  const route = useRoute();
  const { getLastFilter } = useFilterStore();

  const upcomingReviews = ref<UpcomingReview[]>([]);
  const totalReviews = ref<number>(0);
  const { activeAdvisors } = advisorsProvider().provide();

  const { filters, ...tableService } = useTableService<Filters>(
    'upcomingReviews',
    {
      initialPage: 1,
      perPage: 10,
      totalItems: totalReviews.value || 0,
    },
  );

  onMounted(() => {
    handleUpdateFilters(getLastFilter(route.name) ?? filters.value);
  });

  watch(totalReviews, () => {
    tableService.setTotalItems(totalReviews.value || 0);
  });

  const fetchUpcomingReviews = async () => {
    const response = await getUpcomingReviewsAction({
      query: {
        page: tableService.page.value,
        ...filters.value,
      },
    });
    totalReviews.value = response.totalReviews;
    upcomingReviews.value = response.upcomingReviews;
  };

  const changePage = (page: number) => {
    tableService
      .changePage(page)
      .apply()
      .then(() => {
        fetchUpcomingReviews();
      });
  };

  const handleViewCaseClick = async (clientId: number, caseId: CaseId) => {
    await router.push({
      name: 'client-case',
      params: {
        id: clientId,
        caseId: caseId,
      },
    });
  };

  const handleUpdateFilters = (filters: Partial<Filters>) => {
    tableService
      .changePage(1)
      .addFilter(filters)
      .apply()
      .then(() => fetchUpcomingReviews());
  };

  const handleResetFilters = () => {
    tableService
      .resetFilters([
        'client_owner_id',
        'case_adviser_id',
        'case_status',
        'text',
      ])
      .changePage(1)
      .apply()
      .then(() => fetchUpcomingReviews());
  };

  const formatReviewDate = (month: number, year: number): string => {
    const date = new Date(year, month - 1);
    const options: Intl.DateTimeFormatOptions = {
      month: 'long',
      year: 'numeric',
    };
    return date.toLocaleString('default', options);
  };

  const formatCaseStatus = (caseStatusId: number): string => {
    switch (caseStatusId) {
      case 1:
        return 'Open';
      case 2:
        return 'Completed';
      case 3:
        return 'Cancelled';
      default:
        return 'Open';
    }
  };

  const formatAdviser = (adviserId: number): string => {
    const adviser = activeAdvisors.value.find(
      (adviser) => adviser.id === adviserId,
    );
    return `${adviser?.firstName} ${adviser?.lastName}`;
  };
</script>
