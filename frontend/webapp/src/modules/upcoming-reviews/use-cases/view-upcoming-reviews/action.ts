import { getUpcomingReviews } from '@aventur-shared/modules/upcoming-reviews/api';
import { Advisor } from '@aventur-shared/modules/advisors';
import { CaseStatusEnum } from '@aventur-shared/modules/cases';

interface ActionProps {
  query: QueryString;
}

interface QueryString
  extends Partial<{
    client_owner_id: number;
    case_adviser_id: Advisor['id'];
    case_status: CaseStatusEnum;
    time_limited: string;
    text: string;
    page: number;
    results_per_page: number;
  }> {}

const prepareQueryString = (
  query: Partial<ActionProps['query']>,
): QueryString => {
  const queryParams = {
    page: query.page,
    results_per_page: 10,
    client_owner_id: Number(query.client_owner_id),
    case_adviser_id: Number(query.case_adviser_id),
    case_status:
      Number(query.case_status) === 4 ? undefined : Number(query.case_status),
    time_limited: query.time_limited !== 'false',
    open_slots_only: Number(query.case_status) === 4,
    text: query.text || '',
  };

  const filterEmptyQueryParts = (
    queryPart: [string, string | number | boolean | undefined],
  ) => queryPart[1] || queryPart[1] === false;
  const queryEntries = Object.entries({
    ...queryParams,
  }).filter(filterEmptyQueryParts);

  return Object.fromEntries(queryEntries);
};

export const getUpcomingReviewsAction = async ({ query }: ActionProps) => {
  try {
    return await getUpcomingReviews(prepareQueryString(query));
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    }
    throw new Error('Something went wrong during upcoming review fetching.');
  }
};
