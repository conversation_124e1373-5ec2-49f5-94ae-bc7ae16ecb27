<template>
  <div>
    <select-field
      label="Case Status"
      name="case-status-filter"
      :value="modelValue"
      :can-clear="true"
      :searchable="false"
      :options="selectOptions"
      @on-select="handlePick"
    />
  </div>
</template>

<script setup lang="ts">
  import { SelectField } from '@aventur-shared/components/form';
  import { CaseStatusEnum } from '@aventur-shared/modules/cases';

  const emit = defineEmits(['update:modelValue']);
  const props = defineProps<{
    modelValue?: `${CaseStatusEnum}`;
  }>();

  const selectOptions = [
    {
      label: 'Open',
      value: 1,
    },
    {
      label: 'Completed',
      value: 2,
    },
    {
      label: 'Cancelled',
      value: 3,
    },
    {
      label: 'No Case Assigned',
      value: 4,
    },
  ];

  const handlePick = (status: number) => {
    if (Number(props.modelValue) === status) return;
    emit('update:modelValue', status);
  };
</script>
