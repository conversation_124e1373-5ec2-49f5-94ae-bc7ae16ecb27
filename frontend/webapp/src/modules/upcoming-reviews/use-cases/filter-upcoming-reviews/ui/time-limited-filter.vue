<template>
  <SwitchGroup as="div" class="flex items-center">
    <Switch
      v-model="timeLimited"
      :class="[
        timeLimited ? 'bg-primary' : 'bg-gray-200',
        'focus:ring-primary relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2',
      ]"
    >
      <span
        aria-hidden="true"
        :class="[
          timeLimited ? 'translate-x-5' : 'translate-x-0',
          'pointer-events-none inline-block size-5 rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
        ]"
      />
    </Switch>
    <SwitchLabel as="span" class="ml-3">
      <span class="text-sm font-medium text-gray-900"
        >Limit to next 3 months only</span
      >
    </SwitchLabel>
  </SwitchGroup>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { Switch, SwitchGroup, SwitchLabel } from '@headlessui/vue';

  const props = withDefaults(
    defineProps<{
      modelValue?: string;
    }>(),
    {
      modelValue: 'true',
    },
  );

  const timeLimited = ref(/true/.test(props.modelValue));

  const emit = defineEmits(['update:modelValue']);

  watch(timeLimited, () => {
    emit('update:modelValue', timeLimited.value?.toString());
  });
</script>
