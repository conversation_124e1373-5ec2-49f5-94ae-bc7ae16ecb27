<template>
  <select-field
    label="Client Adviser"
    name="client-advisor-filter"
    :value="modelValue"
    :can-clear="true"
    :options="getAdvisorsSelectOptions(activeAdvisors)"
    @on-select="handlePick"
  />
</template>

<script setup lang="ts">
  import { SelectField } from '@aventur-shared/components/form';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';
  import { onMounted } from 'vue';

  const emit = defineEmits(['update:modelValue']);
  const props = defineProps<{
    modelValue?: number;
  }>();

  const { activeAdvisors } = advisorsProvider().provide();

  onMounted(async () => {
    await advisorsProvider().create();
  });

  const handlePick = (owner: number) => {
    if (props.modelValue === owner) return;
    emit('update:modelValue', owner);
  };
</script>
