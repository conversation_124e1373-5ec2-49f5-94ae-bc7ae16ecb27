import { array, boolean, object, string } from 'yup';
import { Nullable } from '@aventur-shared/types/Common';
import * as validationMessages from '@aventur-shared/utils/form/validation-messages';
import { IFrequency } from '@aventur-shared/modules/factfind/models';

export interface ExpenditureFormValues {
  id: Nullable<number>;
  typeGroup: Nullable<number>;
  type: Nullable<number>;
  description: Nullable<string>;
  frequency: Nullable<ReturnType<IFrequency['toValue']>>;
  amount: Nullable<string>;
  isEssential: boolean;
}

export const expendituresValidationSchema = object({
  expenditures: array().of(
    object({
      description: string().nullable(),
      amount: string()
        .checkIsNumber(validationMessages.invalidNumberMessage)
        .nullable(),
      frequency: string().when(
        'amount',
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore It works
        ([amount]: [amount: string], schema) => {
          if (`${amount}`.trim() === '' || +amount === 0) {
            return schema.nullable();
          }

          return schema.required(validationMessages.fieldRequiredMessage);
        },
      ),
      isEssential: boolean(),
    }),
  ),
});
