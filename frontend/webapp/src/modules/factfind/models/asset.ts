import { filter } from 'lodash';

import { ListElement } from '@aventur-shared/utils/list';
import { useProducts } from '@aventur-shared/composables/useProducts';
import { Product } from '@aventur-shared/modules/refdata/types/Product';
import {
  Account,
  Asset,
  CompanyShares,
  CryptoCurrency,
  DefinedBenefitPension,
  OtherAsset,
  Property,
} from '@aventur-shared/modules/factfind/types/Asset';
//

export type AssetListItem = ListElement<Asset>;

export class AssetTypeGuard {
  static isAssetProduct = function (
    asset: Asset,
    products: Product[],
  ): boolean {
    return products.map((_) => _.id).includes(asset.typeId);
  };

  static isAccount = (asset: Asset): asset is Account => {
    const { getAccountProducts } = useProducts();
    return this.isAssetProduct(asset, getAccountProducts.value);
  };

  static isProperty = (asset: Asset): asset is Property => {
    const { getPropertyProducts } = useProducts();
    return this.isAssetProduct(asset, getPropertyProducts.value);
  };

  static isCompanyShares = (asset: Asset): asset is CompanyShares => {
    const { getCompanySharesProducts } = useProducts();
    return this.isAssetProduct(asset, getCompanySharesProducts.value);
  };

  static isCryptoCurrency = (asset: Asset): asset is CryptoCurrency => {
    const { getCryptoCurrencyProducts } = useProducts();
    return this.isAssetProduct(asset, getCryptoCurrencyProducts.value);
  };

  static isOtherAsset = (asset: Asset): asset is OtherAsset => {
    const { getOtherAssetProducts } = useProducts();
    return this.isAssetProduct(asset, getOtherAssetProducts.value);
  };

  static isProtectionProduct = (asset: Asset): asset is Account => {
    const {
      getTermPolicyProducts,
      getIndemnityPolicyProducts,
      getWholeOfLifePolicyProducts,
      getIncomeProtectionPolicyProducts,
    } = useProducts();

    const protectionProducts = [
      ...getTermPolicyProducts.value,
      ...getIndemnityPolicyProducts.value,
      ...getWholeOfLifePolicyProducts.value,
      ...getIncomeProtectionPolicyProducts.value,
    ];
    return this.isAssetProduct(asset, protectionProducts);
  };

  static isDefinedBenefitPension = (
    asset: Asset,
  ): asset is DefinedBenefitPension => {
    const { getDefinedBenefitPensionProducts } = useProducts();
    return this.isAssetProduct(asset, getDefinedBenefitPensionProducts.value);
  };
}

export const formatPropertyAssetAddress = (property: Property) => {
  return filter([
    property.addressLineOne,
    property.addressLineTwo,
    property.city,
    property.postCode,
  ]).join(', ');
};
