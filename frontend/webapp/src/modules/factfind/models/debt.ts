import { ListElement } from '@aventur-shared/utils/list';
import { useProducts } from '@aventur-shared/composables/useProducts';
import { Product } from '@aventur-shared/modules/refdata/types/Product';
import {
  CreditCard,
  Debt,
  Mortgage,
  OtherDebt,
  PersonalLoan,
} from '@aventur-shared/modules/factfind/types/Debt';
//

export type DebtListItem = ListElement<Debt>;

export class DebtTypeGuard {
  static isDebtProduct = function (debt: Debt, products: Product[]): boolean {
    return products.map((_) => _.id).includes(debt.typeId);
  };

  static isMortgage = (debt: Debt): debt is Mortgage => {
    const { getMortgageProducts } = useProducts();
    return this.isDebtProduct(debt, getMortgageProducts.value);
  };

  static isPersonalLoan = (debt: Debt): debt is PersonalLoan => {
    const { getPersonalLoanProducts } = useProducts();
    return this.isDebtProduct(debt, getPersonalLoanProducts.value);
  };

  static isCreditCard = (debt: Debt): debt is CreditCard => {
    const { getCreditCardProducts } = useProducts();
    return this.isDebtProduct(debt, getCreditCardProducts.value);
  };

  static isOtherDebt = (debt: Debt): debt is OtherDebt => {
    const { getOtherDebtProducts } = useProducts();
    return this.isDebtProduct(debt, getOtherDebtProducts.value);
  };
}
