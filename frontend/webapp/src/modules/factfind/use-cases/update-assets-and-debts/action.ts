import { filter } from 'lodash';
import { InferType, array, mixed, number, object, string } from 'yup';
import { postAssetsAndDebts } from '@aventur-shared/modules/factfind/api';
import { ClientId } from '@aventur-shared/modules/clients';
import { Asset, Debt } from '@aventur-shared/modules/factfind/types';
import { dtoToAssetMapper } from '@aventur-shared/modules/factfind/utils/mappers/dtoToAssetMapper';
import { dtoToDebtMapper } from '@aventur-shared/modules/factfind/utils/mappers/dtoToDebtMapper';

const getAssetsAndDebtsSchema = object({
  assets: array()
    .of(
      object({
        id: number().required(),
        client_ids: array().of(mixed<ClientId>().required()).required(),
        group_id: number().required(),
        type_id: number().required(),
        status_id: number().required(),
        current_quantity: number().required(),
        attributes: object({}),
      }),
    )
    .required(),
  debts: array()
    .of(
      object({
        id: number().required(),
        client_ids: array().of(number().required()).required(),
        group_id: number().required(),
        type_id: number().required(),
        status_id: number().required(),
        current_quantity: number().required(),
        provider_id: number().required(),
        account_number: string().min(0).defined(),
        attributes: object({}),
      }),
    )
    .required(),
});

type AssetsAndDebtsDTO = InferType<typeof getAssetsAndDebtsSchema>;

export default async (clientId: ClientId, assets: Asset[], debts: Debt[]) => {
  const predicate = (item: Asset | Debt) => !item.id || item.hasQuantity;
  const response = await postAssetsAndDebts(
    clientId,
    filter(assets, predicate),
    filter(debts, predicate),
  );

  const assetsAndDebtsDTO: AssetsAndDebtsDTO =
    await getAssetsAndDebtsSchema.validate(response);

  const [domainAssets, domainDebts] = await Promise.all([
    Promise.all(
      assetsAndDebtsDTO.assets.map((asset) => dtoToAssetMapper(asset)),
    ),
    Promise.all(assetsAndDebtsDTO.debts.map((debt) => dtoToDebtMapper(debt))),
  ]);

  return {
    assets: domainAssets,
    debts: domainDebts,
  };
};
