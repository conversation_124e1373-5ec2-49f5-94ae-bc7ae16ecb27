import { GoalId } from '@aventur-shared/modules/goals';
import { ClientId } from '@aventur-shared/modules/clients';
import patchFactFindGoalObjectives from '@aventur-shared/modules/factfind/api/goals/patch-client-goal-objectives';

export default async (
  goalId: GoalId,
  clientId: ClientId,
  goalObjectives: string,
): Promise<void> =>
  await patchFactFindGoalObjectives(clientId, goalId, {
    goal_objectives: goalObjectives,
  });
