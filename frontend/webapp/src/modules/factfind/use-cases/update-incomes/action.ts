import { ClientId } from '@aventur-shared/modules/clients';
import { getIncomes, postIncomes } from '@aventur-shared/modules/factfind/api';
import { type Income } from '@aventur-shared/modules/factfind/models';

export default async (
  clientId: ClientId,
  incomes: Income[],
): Promise<Income[]> => {
  try {
    await postIncomes(clientId, incomes);
    return getIncomes(clientId);
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    } else {
      throw new Error('Update client incomes error.');
    }
  }
};
