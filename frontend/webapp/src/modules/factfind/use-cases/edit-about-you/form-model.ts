import * as validationMessages from '@aventur-shared/utils/form/validation-messages';
import { array, boolean, date, mixed, number, object, string } from 'yup';
import { DeepNullable, Nullable } from '@aventur-shared/types/Common';
import {
  fieldRequiredMessage,
  invalidCharacterMessage,
  invalidNumberMessage,
} from '@aventur-shared/utils/form/validation-messages';
import { invalidCharactersRegex } from '@aventur-shared/utils/form/fields';

export interface FamilyMembersFormValues {
  id: number;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  relationshipType: string;
}

export interface FurtherInformationsFormValues {
  previousFinancialAdvice: boolean;
  previousInvestmentExperience: string;
  ethicalInvestments: boolean;
  religiousRestrictions: boolean;
  isVulnerablePerson: boolean;
  insuranceNumber: string;
  will: string;
  powerOfAttorney: string;
  creditHistory: string;
  employmentStatus: string;
}

export interface Address {
  id: number;
  line1: string;
  line2: string;
  line3: string;
  line4: string;
  city: string;
  countryId: number;
  postCode: string;
  moveInDate: string;
  moveOutDate: string;
  isPrimaryAddress: boolean;
}

interface PersonalDetailsFormValues {
  titleId: number;
  firstName: string;
  lastName: string;
  genderId: number;
  nationalityId: number;
  birthCountryId: number;
  primaryCountryId: number;
  secondaryCountryId: number;
  dateOfBirth: string;
  maritalStatusId: number;
}

export type FormAddress = {
  [K in keyof Address]: Nullable<Address[K]>;
};

export interface RetirementDetailsFormValues {
  retirementAge: number;
  monthlyRetirementIncomeRequired: number;
  statePension: boolean;
  alreadyRetired: boolean;
}

export interface FormValues {
  personalDetails: DeepNullable<PersonalDetailsFormValues>;
  contactDetails: {
    email: Nullable<string>;
    phoneNumber: Nullable<string>;
    mobileNumber: Nullable<string>;
    address: Array<FormAddress>;
  };
  familyMembers: DeepNullable<FamilyMembersFormValues>[];
  furtherInformations: DeepNullable<FurtherInformationsFormValues>;
  retirementDetails: DeepNullable<RetirementDetailsFormValues>;
}

export const addressValidationSchema = object({
  id: number().nullable(),
  line1: string()
    .required(fieldRequiredMessage)
    .matches(invalidCharactersRegex, {
      message: invalidCharacterMessage,
    }),
  line2: string().nullable().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  line3: string().nullable().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  line4: string().nullable().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  city: string().nullable().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  countryId: number().required(fieldRequiredMessage),
  postCode: string()
    .required(fieldRequiredMessage)
    .matches(invalidCharactersRegex, {
      message: invalidCharacterMessage,
    }),
  moveInDate: string()
    .nullable()
    .transform((value) => (value === '' ? null : value)),
  moveOutDate: string()
    .nullable()
    .transform((value) => (value === '' ? null : value)),
  isPrimaryAddress: boolean().nullable(),
});

export const contactDetailsValidationSchema = object({
  email: string().nullable().email(validationMessages.invalidEmailMessage),
  phoneNumber: string()
    .nullable()
    .checkIsCorrectPhoneNumber('Please provide correct number'),
  mobileNumber: string()
    .nullable()
    .checkIsCorrectPhoneNumber('Please provide correct number'),
  address: array()
    .of(addressValidationSchema)
    .test(
      'has-primary',
      'Exactly one primary address must be specified.',
      (value) =>
        !value?.length ||
        value?.filter((address) => address.isPrimaryAddress).length === 1,
    ),
});

export const personalDetailsValidationSchema = object({
  titleId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value)),
  firstName: string()
    .nullable()
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: invalidCharacterMessage,
    })
    .required(validationMessages.fieldRequiredMessage),
  lastName: string()
    .nullable()
    .matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: invalidCharacterMessage,
    })
    .required(validationMessages.fieldRequiredMessage),
  genderId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value)),
  nationalityId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value)),
  birthCountryId: number().required(validationMessages.fieldRequiredMessage),
  primaryCountryId: number().required(validationMessages.fieldRequiredMessage),
  secondaryCountryId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value)),
  dateOfBirth: date()
    .nullable()
    .transform((value) => {
      return value instanceof Date && !isNaN(value as unknown as number)
        ? value
        : null;
    }),
  maritalStatusId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value)),
});

export const familyMemberValidationSchema = object({
  firstName: string().nullable().required().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  lastName: string().nullable().required().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: invalidCharacterMessage,
  }),
  dateOfBirth: string()
    .nullable()
    .transform((value) => (value === '' ? null : value)),
  relationshipType: string()
    .nullable()
    .required()
    .transform((value) => (value === '' ? null : value)),
});

const furtherInformationsValidationSchema = object({
  previousFinancialAdvice: boolean().nullable(),
  previousInvestmentExperience: string()
    .nullable()
    .transform((value) => value || null),
  ethicalInvestments: boolean().nullable(),
  religiousRestrictions: boolean().nullable(),
  isVulnerablePerson: boolean().nullable(),
  insuranceNumber: string()
    .nullable()
    .transform((value) => value || null),
  will: string()
    .nullable()
    .transform((value) => value || null),
  powerOfAttorney: string()
    .nullable()
    .transform((value) => value || null),
  creditHistory: string()
    .nullable()
    .transform((value) => value || null),
  employmentStatus: string()
    .nullable()
    .transform((value) => value || null),
});

export const retirementDetailsValidationSchema = object({
  retirementAge: mixed()
    .nullable()
    .checkIsNumber(invalidNumberMessage)
    .when({
      is: (value: number) => value,
      then: () =>
        number()
          .min(0, 'Value must be higher than 0')
          .max(150, 'Value must be lower than 150'),
    })
    .transform((value) => value || null),
  monthlyRetirementIncomeRequired: mixed()
    .nullable()
    .checkIsNumber(invalidNumberMessage)
    .when({
      is: (value: number) => value,
      then: () =>
        number()
          .min(0, 'Value must be higher than 0')
          .max(20000, 'Value must be lower than 20000'),
    })
    .transform((value) => value || null),
  statePension: boolean().nullable(),
  alreadyRetired: boolean().nullable(),
});

export const validationSchema = object({
  personalDetails: personalDetailsValidationSchema,
  contactDetails: contactDetailsValidationSchema,
  familyMembers: array().of(familyMemberValidationSchema),
  furtherInformations: furtherInformationsValidationSchema,
  retirementDetails: retirementDetailsValidationSchema,
});
