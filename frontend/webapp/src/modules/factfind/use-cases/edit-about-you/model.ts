import { ArrayElement } from '@aventur-shared/types/Common';
import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';
import {
  type DateLike,
  IDateTime,
  factory as dateTimeFactory,
} from '@aventur-shared/utils/dateTime';

import { FormValues } from './form-model';
//

const asDateTime = (value: DateLike | null) =>
  value ? dateTimeFactory(value) : null;

const addressToDomain = (
  item: ArrayElement<FormValues['contactDetails']['address']>,
) => ({
  id: item.id ?? undefined,
  addressLineOne: item.line1 as string,
  addressLineTwo: item.line2,
  addressLineThree: item.line3,
  addressLineFour: item.line4,
  postCode: item.postCode as string,
  city: item.city,
  countryId: item.countryId as number,
  isPrimary: item.isPrimaryAddress,
  moveInDate: asDateTime(item.moveInDate),
  moveOutDate: asDateTime(item.moveOutDate),
});

const addressToFormValues = (
  item: ArrayElement<AboutYou['contactDetails']['addresses']>,
) => ({
  id: item.id ?? null,
  city: item.city,
  countryId: item.countryId,
  isPrimaryAddress: item.isPrimary,
  line1: item.addressLineOne,
  line2: item.addressLineTwo,
  line3: item.addressLineThree,
  line4: item.addressLineFour,
  postCode: item.postCode,
  moveInDate: item.moveInDate?.formatForForm() || '',
  moveOutDate: item.moveOutDate?.formatForForm() || '',
});

const familyMemberToDomain = (
  item: ArrayElement<FormValues['familyMembers']>,
) => ({
  id: item.id,
  firstName: item.firstName,
  lastName: item.lastName,
  dateOfBirth: asDateTime(item.dateOfBirth),
  relationshipType: item.relationshipType,
});

const familyMemberToFormValues = (
  item: ArrayElement<AboutYou['familyMembers']>,
) => ({
  id: item.id,
  firstName: item.firstName,
  lastName: item.lastName,
  dateOfBirth: item.dateOfBirth?.formatForForm() || '',
  relationshipType: item.relationshipType,
});

export const mapFormValuesToAboutYouDomain = (
  formValues: FormValues,
): AboutYou => {
  return {
    personalDetails: {
      ...formValues.personalDetails,
      dateOfBirth: asDateTime(formValues.personalDetails.dateOfBirth),
    },
    contactDetails: {
      email: formValues.contactDetails.email,
      phoneNumber: formValues.contactDetails.phoneNumber,
      mobileNumber: formValues.contactDetails.mobileNumber,
      addresses: formValues.contactDetails.address.map(addressToDomain),
    },
    familyMembers: formValues.familyMembers.map(familyMemberToDomain),
    furtherInformations: {
      creditHistory: formValues.furtherInformations.creditHistory,
      employmentStatus: formValues.furtherInformations.employmentStatus,
      ethicalInvestments: formValues.furtherInformations.ethicalInvestments,
      insuranceNumber: formValues.furtherInformations.insuranceNumber || null,
      powerOfAttorney: formValues.furtherInformations.powerOfAttorney,
      previousFinancialAdvice:
        formValues.furtherInformations.previousFinancialAdvice,
      previousInvestmentExperience:
        formValues.furtherInformations.previousInvestmentExperience,
      religiousRestrictions:
        formValues.furtherInformations.religiousRestrictions,
      isVulnerablePerson: formValues.furtherInformations.isVulnerablePerson,
      will: formValues.furtherInformations.will,
    },
    retirementDetails: {
      monthlyRetirementIncomeRequired:
        formValues.retirementDetails.monthlyRetirementIncomeRequired || null,
      retirementAge: formValues.retirementDetails.retirementAge || null,
      alreadyRetired: formValues.retirementDetails.alreadyRetired,
      statePension: formValues.retirementDetails.statePension,
    },
  };
};

export const mapAboutYouDomainToFormValues = (domain: AboutYou): FormValues => {
  return {
    personalDetails: {
      firstName: domain.personalDetails.firstName,
      lastName: domain.personalDetails.lastName,
      dateOfBirth:
        (domain.personalDetails.dateOfBirth as IDateTime)?.formatForForm() ||
        null,
      titleId: domain.personalDetails.titleId,
      genderId: domain.personalDetails.genderId,
      maritalStatusId: domain.personalDetails.maritalStatusId,
      nationalityId: domain.personalDetails.nationalityId,
      birthCountryId: domain.personalDetails.birthCountryId,
      primaryCountryId: domain.personalDetails.primaryCountryId,
      secondaryCountryId: domain.personalDetails.secondaryCountryId,
    },
    contactDetails: {
      email: domain.contactDetails.email,
      phoneNumber: domain.contactDetails.phoneNumber,
      mobileNumber: domain.contactDetails.mobileNumber,
      address: domain.contactDetails.addresses.map(addressToFormValues),
    },
    familyMembers: domain.familyMembers.map(familyMemberToFormValues),
    furtherInformations: domain.furtherInformations,
    retirementDetails: domain.retirementDetails,
  };
};
