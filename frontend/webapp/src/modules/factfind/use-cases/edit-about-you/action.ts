import { ClientId } from '@aventur-shared/modules/clients';
import { AboutYou } from '@aventur-shared/modules/factfind/types/AboutYou';
import { postClientAboutYou } from '@aventur-shared/modules/factfind/api/';
//

export default async (clientId: ClientId, model: AboutYou): Promise<void> => {
  try {
    await postClientAboutYou(clientId, model);
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    } else {
      throw new Error('Update client about you error.');
    }
  }
};
