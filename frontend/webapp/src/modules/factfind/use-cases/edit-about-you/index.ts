export { default as PersonalDetailsSection } from './ui/personal-details.vue';
export { default as ContactDetailsSection } from './ui/contact-details.vue';
export { default as FamilyMembersSection } from './ui/family-members.vue';
export { default as FurtherInformationsSection } from './ui/further-informations.vue';
export { default as RetirementDetailsSection } from './ui/retirement-details.vue';
export { type FormValues, validationSchema } from './form-model';
export { default as action } from './action';
export {
  mapAboutYouDomainToFormValues,
  mapFormValuesToAboutYouDomain,
} from './model';
