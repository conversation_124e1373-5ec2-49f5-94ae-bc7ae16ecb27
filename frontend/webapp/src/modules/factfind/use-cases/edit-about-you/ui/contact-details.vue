<template>
  <div>
    <text-field
      label="Email address"
      name="contactDetails.email"
      placeholder="No email address"
      :is-readonly="true"
    />
    <text-field label="Mobile number" name="contactDetails.mobileNumber" />
    <text-field label="Phone number" name="contactDetails.phoneNumber" />
  </div>
  <div>
    <h1 class="py-5 text-base font-semibold text-gray-900">
      You address {{ fields.length > 0 ? `(${fields.length} added)` : '' }}
    </h1>
    <div class="pb-4">
      <template v-if="fields.length > 0">
        <ul class="flex flex-col gap-1">
          <li
            v-for="(field, idx) in fields"
            :key="field.key"
            class="hover:-mx-2 hover:rounded-lg hover:bg-gray-50 hover:px-2"
          >
            <created-address-row
              :address="field.value as Address"
              @on-edit="(addressId) => editAddressRequested(addressId, idx)"
              @on-delete="() => remove(idx)"
            />
          </li>
          <li
            class="-mx-2 rounded-lg border border-dashed border-gray-200 bg-gray-50 text-center"
          >
            <span>
              <custom-button
                class="w-full"
                theme="text-like"
                @on-click="() => addAddressModalApi?.open()"
                >+ Add new address</custom-button
              >
            </span>
          </li>
        </ul>

        <modal
          :config="{
            outsideClickClose: false,
          }"
          @use-api="
            (api) => {
              editAddressModalApi = api;
            }
          "
        >
          <template #default>
            <box>
              <box-section
                title="Edit address"
                divider="bottom"
                class="flex justify-between"
              />
              <box-section>
                <address-form
                  :form-values="
                    editedAddressIndex !== null
                      ? fields[editedAddressIndex].value
                      : createEmptyAddress()
                  "
                  type="EDIT"
                  @on-submit="handleEditAddress"
                  @on-close="editAddressModalApi?.close"
                />
              </box-section>
            </box>
          </template>
        </modal>
      </template>
      <template v-else>
        <div>
          <span>No address added</span>
          <span>
            <custom-button
              theme="text-like"
              @on-click="() => addAddressModalApi?.open()"
              >Add first</custom-button
            >
          </span>
        </div>
      </template>
    </div>
    <modal
      :config="{
        outsideClickClose: false,
      }"
      @use-api="(api) => (addAddressModalApi = api)"
    >
      <template #default>
        <box>
          <box-section
            title="Add address"
            divider="bottom"
            class="flex justify-between"
          />
          <box-section>
            <address-form
              :form-values="createEmptyAddress()"
              type="ADD"
              @on-close="addAddressModalApi?.close"
              @on-submit="(address) => handleAddAddress(address)"
            />
          </box-section>
        </box>
      </template>
    </modal>
  </div>
</template>

<script setup lang="ts">
  import { useFieldArray } from 'vee-validate';
  import { TextField } from '@aventur-shared/components/form';
  import { Box, BoxSection, Button as CustomButton, Modal } from '@modules/ui';
  import { ref } from 'vue';
  import { ModalAPI } from '@modules/ui/types';
  import {
    type Address,
    type FormAddress,
  } from '@modules/factfind/use-cases/edit-about-you/form-model';
  import AddressForm from './address/address-form.vue';
  import CreatedAddressRow from './address/created-address-row.vue';

  const editedAddressIndex = ref<number | null>(null);
  const editAddressModalApi = ref<ModalAPI>();
  const addAddressModalApi = ref<ModalAPI>();

  const { fields, push, remove, update } = useFieldArray<FormAddress>(
    'contactDetails.address',
  );

  const createEmptyAddress = (): FormAddress => ({
    id: null,
    isPrimaryAddress: null,
    line1: null,
    line2: null,
    line3: null,
    line4: null,
    city: null,
    countryId: null,
    moveInDate: null,
    moveOutDate: null,
    postCode: null,
  });

  const findAddressIndex = (addressId: number | null): number | null => {
    if (addressId === null) return addressId;
    else {
      const indexByAddressId = fields.value.findIndex(
        (existingAddress) => existingAddress.value.id === addressId,
      );
      return indexByAddressId !== -1 ? indexByAddressId : null;
    }
  };

  const editAddressRequested = (
    addressId: FormAddress['id'],
    index: number,
  ) => {
    editedAddressIndex.value = findAddressIndex(addressId) ?? index;
    editAddressModalApi.value?.open();
  };

  const handleAddAddress = (address: FormAddress) => {
    push(address);
    addAddressModalApi.value?.close();
  };

  const handleEditAddress = (address: FormAddress) => {
    if (editedAddressIndex.value === null) return;

    update(editedAddressIndex.value, address);
    editedAddressIndex.value = null;
    editAddressModalApi.value?.close();
  };
</script>
