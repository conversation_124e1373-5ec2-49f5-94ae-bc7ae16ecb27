<template>
  <div class="pb-4">
    <family-members-list
      :members="props.members"
      @on-remove="handleMemberRemove"
      @on-edit="({ index, values }) => handleMemberEdit(index, values)"
    />
    <modal
      :config="{
        outsideClickClose: false,
      }"
    >
      <template #open-button="modalApi">
        <div
          v-if="fields.length > 0"
          class="rounded-lg border border-dashed border-gray-200 bg-gray-50 text-center"
        >
          <span>
            <custom-button
              class="w-full"
              theme="text-like"
              @on-click="() => modalApi?.open()"
              >+ Add new member</custom-button
            >
          </span>
        </div>
        <div v-else>
          <span>No members added</span>
          <span>
            <custom-button theme="text-like" @on-click="() => modalApi?.open()"
              >Add first</custom-button
            >
          </span>
        </div>
      </template>

      <template #default="modalApi">
        <box class="text-left">
          <box-section
            title="Add family member"
            divider="bottom"
            class="flex justify-between"
          >
          </box-section>
          <box-section>
            <family-members-add-form
              @on-close="modalApi.close"
              @on-submit="(formValues) => handleMemberAdd(modalApi)(formValues)"
            />
          </box-section>
        </box>
      </template>
    </modal>
  </div>
</template>

<script setup lang="ts">
  import {
    Box,
    BoxSection,
    Button as CustomButton,
    Modal,
    ModalAPI,
  } from '@modules/ui';
  import { default as FamilyMembersAddForm } from './family-members/add-form.vue';
  import { default as FamilyMembersList } from './family-members/list.vue';
  import { FormValues } from '@modules/factfind/use-cases/edit-about-you';
  import { useFieldArray } from 'vee-validate';

  const props = defineProps<{
    members: FormValues['familyMembers'];
  }>();

  const { remove, push, update, fields } =
    useFieldArray<FormValues['familyMembers'][0]>('familyMembers');

  const handleMemberAdd =
    (modalApi: ModalAPI) => (formValues: FormValues['familyMembers'][0]) => {
      push(formValues);
      modalApi.close();
    };

  const handleMemberEdit = (
    index: number,
    formValues: FormValues['familyMembers'][0],
  ) => {
    update(index, {
      id: formValues.id,
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      dateOfBirth: formValues.dateOfBirth,
      relationshipType: formValues.relationshipType,
    });
  };

  const handleMemberRemove = (index: number) => {
    remove(index);
  };
</script>
