<template>
  <form @submit="onSubmit">
    <text-field label="First name" name="firstName" />
    <text-field label="Last name" name="lastName" />
    <date-picker label="Date of birth" name="dateOfBirth" />
    <select-field
      label="Relationship"
      name="relationshipType"
      :options="relationshipTypeSelectOptions"
      :searchable="true"
      :can-clear="true"
    />
    <div class="flex justify-between">
      <custom-button type="button" theme="gray-ghost" @click="emit('on-close')"
        >Close
      </custom-button>
      <custom-button type="submit" theme="primary">Save</custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { useForm } from 'vee-validate';
  import { Button as CustomButton } from '@modules/ui';
  import {
    DatePicker,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { relationshipTypeSelectOptions } from '@aventur-shared/modules/factfind/models';
  import { FormValues } from '@modules/factfind/use-cases/edit-about-you';
  import { familyMemberValidationSchema as validationSchema } from '@modules/factfind/use-cases/edit-about-you/form-model';

  type FamilyMemberFormValues = FormValues['familyMembers'][0];

  const emit = defineEmits<{
    (e: 'on-submit', item: FormValues['familyMembers'][0]): void;
    (e: 'on-close'): void;
  }>();

  const { handleSubmit } = useForm<FamilyMemberFormValues>({
    validationSchema,
    initialValues: {
      id: null,
      firstName: null,
      lastName: null,
      dateOfBirth: null,
      relationshipType: null,
    },
  });
  const onSubmit = handleSubmit((formValues: FamilyMemberFormValues) => {
    emit('on-submit', {
      id: null,
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      dateOfBirth: formValues.dateOfBirth,
      relationshipType: formValues.relationshipType,
    });
  });
</script>
