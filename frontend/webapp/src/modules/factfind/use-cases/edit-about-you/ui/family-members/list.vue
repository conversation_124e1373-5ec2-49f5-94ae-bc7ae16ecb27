<template>
  <t-table
    v-if="members && members.length > 0"
    class="border-separate border-spacing-y-2"
  >
    <t-head>
      <t-head-tr>
        <t-head-th>First name</t-head-th>
        <t-head-th>Last name</t-head-th>
        <t-head-th>Date of birth</t-head-th>
        <t-head-th>Relationship</t-head-th>
        <t-head-th></t-head-th>
      </t-head-tr>
    </t-head>
    <t-body>
      <modal
        :config="{
          outsideClickClose: false,
        }"
      >
        <template #open-button="modalApi">
          <t-body-tr
            v-for="(member, index) in members"
            :key="index"
            class="hover:bg-[#F4F5F7]"
          >
            <t-body-td>
              {{ createValue(member.firstName) }}
            </t-body-td>
            <t-body-td>
              {{ createValue(member.lastName) }}
            </t-body-td>
            <t-body-td>
              {{
                member.dateOfBirth
                  ? formatDateStringForView(member.dateOfBirth)
                  : '-'
              }}
            </t-body-td>
            <t-body-td>
              {{ createValue(member.relationshipType) }}
            </t-body-td>
            <t-body-td class="whitespace-nowrap">
              <div class="flex items-center justify-end py-2">
                <button
                  type="button"
                  class="px-2 text-gray-400 hover:text-gray-700"
                  @click.stop="handleModalOpen(modalApi)(index)"
                >
                  <PencilSquareIcon class="size-5" />
                </button>
                <button
                  type="button"
                  class="px-2 text-gray-400 hover:text-gray-700"
                  @click.stop="$emit('on-remove', index)"
                >
                  <XMarkIcon class="size-5" />
                </button>
              </div>
            </t-body-td>
          </t-body-tr>
        </template>
        <template #default="modalApi">
          <box class="text-left">
            <box-section
              title="Edit family member"
              divider="bottom"
              class="flex justify-between"
            >
            </box-section>
            <box-section>
              <family-members-edit-form
                v-if="selectedIndex !== undefined"
                :index="selectedIndex"
                :values="members[selectedIndex]"
                @on-close="modalApi.close"
                @on-edit="
                  ({ index, values }) =>
                    handleMemberEdit(modalApi)(index, values)
                "
              />
            </box-section>
          </box>
        </template>
      </modal>
    </t-body>
  </t-table>
</template>

<script setup lang="ts">
  import {
    Box,
    BoxSection,
    Modal,
    ModalAPI,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { createValue } from '@aventur-shared/utils/table/create-value';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import { FormValues } from '@modules/factfind/use-cases/edit-about-you';
  import { default as FamilyMembersEditForm } from './edit-form.vue';
  import { formatDateStringForView } from '@aventur-shared/utils/dateTime';
  import { ref } from 'vue';
  import { PencilSquareIcon } from '@heroicons/vue/24/solid';

  defineProps<{
    members: FormValues['familyMembers'];
  }>();
  const emit = defineEmits(['on-edit', 'on-remove']);

  const selectedIndex = ref<number>();

  const handleModalOpen = (modalApi: ModalAPI) => (index: number) => {
    selectedIndex.value = index;
    modalApi.open();
  };

  const handleMemberEdit =
    (modalApi: ModalAPI) =>
    (index: number, values: FormValues['familyMembers'][0]) => {
      emit('on-edit', { index, values });
      modalApi.close();
    };
</script>
