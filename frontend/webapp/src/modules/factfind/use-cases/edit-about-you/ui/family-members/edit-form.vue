<template>
  <form @submit="onSubmit">
    <text-field label="First name" name="firstName" />
    <text-field label="Last name" name="lastName" />
    <date-picker label="Date of birth" name="dateOfBirth" />
    <select-field
      label="Relationship"
      name="relationshipType"
      :options="relationshipTypeSelectOptions"
      :searchable="true"
      :can-clear="true"
    />
    <div class="flex justify-between">
      <custom-button type="button" theme="gray-ghost" @click="emit('on-close')"
        >Close
      </custom-button>
      <custom-button type="submit" theme="primary">Save</custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { useForm } from 'vee-validate';
  import { Button as CustomButton } from '@modules/ui';
  import {
    DatePicker,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { relationshipTypeSelectOptions } from '@aventur-shared/modules/factfind/models';
  import { FormValues } from '@modules/factfind/use-cases/edit-about-you';
  import { familyMemberValidationSchema as validationSchema } from '@modules/factfind/use-cases/edit-about-you/form-model';

  type FamilyMemberFormValues = FormValues['familyMembers'][0];

  const props = defineProps<{
    index: number;
    values: FamilyMemberFormValues;
  }>();
  const emit = defineEmits<{
    (
      e: 'on-edit',
      item: {
        index: number;
        values: FormValues['familyMembers'][0];
      },
    ): void;
    (e: 'on-close'): void;
  }>();

  const { handleSubmit } = useForm<FamilyMemberFormValues>({
    validationSchema,
    initialValues: props.values,
  });
  const onSubmit = handleSubmit((formValues: FamilyMemberFormValues) => {
    emit('on-edit', {
      index: props.index,
      values: formValues,
    });
  });
</script>
