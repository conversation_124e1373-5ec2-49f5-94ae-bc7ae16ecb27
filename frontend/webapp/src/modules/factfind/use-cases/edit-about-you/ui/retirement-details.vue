<template>
  <text-field label="Retirement age" name="retirementDetails.retirementAge" />
  <text-field
    label="Income required in retirement per month"
    name="retirementDetails.monthlyRetirementIncomeRequired"
  />
  <radio-group
    :options="[
      { label: 'Yes', value: true },
      { label: 'No', value: false },
    ]"
    label="Will you qualify for full state pension at retirement age?"
    name="retirementDetails.statePension"
  />
  <radio-group
    :options="[
      { label: 'Yes', value: true },
      { label: 'No', value: false },
    ]"
    label="Are you already retired?"
    name="retirementDetails.alreadyRetired"
  />
</template>

<script setup lang="ts">
  import { RadioGroup, TextField } from '@aventur-shared/components/form';
</script>
