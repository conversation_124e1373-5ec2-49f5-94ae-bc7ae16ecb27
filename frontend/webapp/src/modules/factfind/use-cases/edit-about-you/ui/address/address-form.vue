<template>
  <form @submit.prevent="onSubmit">
    <text-field label="Address Line 1" name="line1" />
    <text-field label="Address Line 2" name="line2" />
    <text-field label="Address Line 3" name="line3" />
    <text-field label="Address Line 4" name="line4" />
    <text-field label="Postcode" name="postCode" />
    <text-field label="City" name="city" />
    <select-field
      label="Country"
      name="countryId"
      :value="props.formValues?.countryId || defaultCountryId"
      :options="countries"
      :searchable="true"
    />
    <date-picker label="Move in date" name="moveInDate" />
    <date-picker label="Move out date" name="moveOutDate" />
    <checkbox-field
      label="Is primary address"
      name="isPrimaryAddress"
      :checked-value="true"
    />
    <div class="flex justify-between">
      <custom-button type="button" theme="gray-ghost" @click="emit('on-close')"
        >Close
      </custom-button>
      <custom-button type="submit" theme="primary">Save</custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { sortBy } from 'lodash';
  import { useForm } from 'vee-validate';
  import {
    CheckboxField,
    DatePicker,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { useRefData } from '@aventur-shared/stores';
  import { Button as CustomButton } from '@modules/ui';
  import {
    FormAddress,
    FormValues,
    addressValidationSchema,
  } from '../../form-model';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

  const props = defineProps<{
    formValues: FormValues['contactDetails']['address'][0];
    type: 'EDIT' | 'ADD';
  }>();
  const emit = defineEmits<{
    (e: 'on-submit', address: FormAddress): void;
    (e: 'on-close'): void;
  }>();

  const { getCountries, getCountryByCode } = useRefData();
  const defaultCountryId: number | undefined =
    props.type == 'ADD' ? getCountryByCode('GBR')?.id : undefined;
  const countries = sortBy(
    getCountries.map((item) => ({
      label: item.name,
      value: item.id,
    })),
    'label',
  ) as Array<SelectOption>;

  const { handleSubmit } = useForm({
    validationSchema: addressValidationSchema,
    initialValues: props.formValues,
  });

  const onSubmit = handleSubmit(
    (formValues: FormValues['contactDetails']['address'][0]) => {
      emit('on-submit', formValues);
    },
  );
</script>
