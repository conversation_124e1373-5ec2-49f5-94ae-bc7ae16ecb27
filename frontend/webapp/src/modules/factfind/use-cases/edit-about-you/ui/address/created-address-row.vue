<template>
  <div class="flex items-center justify-between">
    <span class="flex flex-1 items-center gap-x-1 py-2.5"
      >{{
        formatAddressForView({
          addressLineOne: address.line1,
          addressLineTwo: address.line2,
          addressLineThree: address.line3,
          addressLineFour: address.line4,
          city: address.city,
          countryId: address.countryId,
          postCode: address.postCode,
        })
      }}
      <span
        v-if="address.isPrimaryAddress"
        class="text-primary inline-flex text-xs"
      >
        <StarIcon v-tippy="'Primary address'" class="size-4" />
      </span>
      <span v-if="!address.id" class="text-xs text-red-400"> (new) </span>
    </span>
    <span class="flex items-center justify-end py-2">
      <button
        class="px-2 text-gray-400 hover:text-gray-700"
        @click.prevent="() => $emit('on-edit', address.id)"
      >
        <PencilSquareIcon class="size-5" />
      </button>
      <button
        class="px-2 text-gray-400 hover:text-gray-700"
        @click.prevent="() => $emit('on-delete', address)"
      >
        <XMarkIcon class="size-5" />
      </button>
    </span>
  </div>
</template>

<script lang="ts" setup>
  import {
    PencilSquareIcon,
    StarIcon,
    XMarkIcon,
  } from '@heroicons/vue/24/solid';

  import { formatAddressForView } from '@aventur-shared/services/address';

  import { Address } from '../../form-model';
  //

  defineProps<{ address: Address }>();
  defineEmits(['on-edit', 'on-delete']);
</script>
