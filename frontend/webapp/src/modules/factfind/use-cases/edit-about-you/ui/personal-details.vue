<template>
  <div>
    <text-field label="First name" name="personalDetails.firstName" />
    <text-field label="Last name" name="personalDetails.lastName" />
    <date-picker label="Date of birth" name="personalDetails.dateOfBirth" />
  </div>
  <div>
    <select-field
      label="Title"
      name="personalDetails.titleId"
      :options="
        getTitles.map((title) => ({
          label: title.name,
          value: title.id,
        }))
      "
      :searchable="true"
      :can-clear="true"
    />
    <select-field
      label="Gender"
      name="personalDetails.genderId"
      :options="
        getGenders.map((g) => ({
          label: g.name,
          value: g.id,
        }))
      "
      :searchable="true"
      :can-clear="true"
    />
    <select-field
      label="Marital status"
      name="personalDetails.maritalStatusId"
      :options="
        getMaritalStatuses.map((ms) => ({
          label: ms.name,
          value: ms.id,
        }))
      "
      :searchable="true"
      :can-clear="true"
    />
  </div>
  <div
    class="grid border-dashed border-gray-100 lg:col-span-2 lg:grid-cols-2 lg:gap-6 lg:border-t lg:pt-5"
  >
    <div>
      <select-field
        label="Nationality"
        name="personalDetails.nationalityId"
        :options="
          getNationalities.map((n) => ({
            label: n.name,
            value: n.id,
          }))
        "
        :searchable="true"
        :can-clear="true"
      />
      <select-field
        label="Country of birth"
        name="personalDetails.birthCountryId"
        :options="countries"
        :searchable="true"
      />
    </div>
    <div>
      <select-field
        label="Country of citizenship / first passport"
        name="personalDetails.primaryCountryId"
        :options="countries"
        :searchable="true"
      />
      <select-field
        label="Dual citizenship (if any)"
        name="personalDetails.secondaryCountryId"
        :options="countries"
        :searchable="true"
        :can-clear="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRefData } from '@aventur-shared/stores';
  import {
    DatePicker,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { sortBy } from 'lodash';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

  const {
    getNationalities,
    getGenders,
    getMaritalStatuses,
    getTitles,
    getCountries,
  } = useRefData();

  const countries = sortBy(
    getCountries.map((item) => ({
      label: item.name,
      value: item.id,
    })),
    'label',
  ) as Array<SelectOption>;
</script>
