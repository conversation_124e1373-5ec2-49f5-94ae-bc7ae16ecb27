<template>
  <div>
    <radio-group
      :options="yesNoSelectOptions"
      label="Have you experienced financial advice before?"
      name="furtherInformations.previousFinancialAdvice"
    />
    <radio-group
      label="Do you have previous investment experience?"
      name="furtherInformations.previousInvestmentExperience"
      :options="previousInvestmentExperienceSelectOptions"
    />
    <radio-group
      :options="yesNoSelectOptions"
      label="Do you wish to consider ethical investments?"
      name="furtherInformations.ethicalInvestments"
    />
    <radio-group
      :options="yesNoSelectOptions"
      label="Does your religious views restrict your investment options?"
      name="furtherInformations.religiousRestrictions"
    />
    <radio-group
      :options="yesNoSelectOptions"
      label="Are you a vulnerable person?"
      name="furtherInformations.isVulnerablePerson"
    />
  </div>
  <div>
    <radio-group
      :options="yesNoNeedsUpdatingSelectOptions"
      label="Do you have a will in place?"
      name="furtherInformations.will"
    />
    <radio-group
      :options="yesNoNeedsUpdatingSelectOptions"
      label="Power of attorney in place?"
      name="furtherInformations.powerOfAttorney"
    />
    <radio-group
      :options="creditHistorySelectOptions"
      label="Credit history?"
      name="furtherInformations.creditHistory"
    />
    <select-field
      label="Employment status"
      name="furtherInformations.employmentStatus"
      :options="employmentStatusSelectOptions"
      :searchable="true"
      :can-clear="true"
    />
    <text-field
      label="National Insurance number"
      name="furtherInformations.insuranceNumber"
    />
  </div>
</template>

<script setup lang="ts">
  import {
    RadioGroup,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import {
    creditHistorySelectOptions,
    employmentStatusSelectOptions,
    previousInvestmentExperienceSelectOptions,
    yesNoNeedsUpdatingSelectOptions,
    yesNoSelectOptions,
  } from '@aventur-shared/constants';
</script>
