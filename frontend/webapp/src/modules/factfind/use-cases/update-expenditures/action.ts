import { ClientId } from '@aventur-shared/modules/clients';
import { Expenditure } from '@aventur-shared/modules/factfind/models';
import {
  getExpenditures,
  postExpenditures,
} from '@aventur-shared/modules/factfind/api';

export default async (
  clientId: ClientId,
  expenditures: Expenditure[],
): Promise<Expenditure[]> => {
  try {
    await postExpenditures(clientId, expenditures);
    return getExpenditures(clientId);
  } catch (e: unknown) {
    if (e instanceof Error) {
      throw e;
    } else {
      throw new Error('Update client expenditures error.');
    }
  }
};
