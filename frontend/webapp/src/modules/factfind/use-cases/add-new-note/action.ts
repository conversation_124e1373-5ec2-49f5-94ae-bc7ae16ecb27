import { FactfindNote } from '@aventur-shared/modules/clients/models';
import { ClientId } from '@aventur-shared/modules/clients';
import { default as postNote } from './api-request';

export const action = async (
  clientId: ClientId,
  content: FactfindNote['content'],
): Promise<FactfindNote> => {
  try {
    const note = await postNote(clientId, content);
    return note;
  } catch (e) {
    if (typeof e === 'string') throw new Error(e);
    else {
      throw new Error('Could not create note.');
    }
  }
};
