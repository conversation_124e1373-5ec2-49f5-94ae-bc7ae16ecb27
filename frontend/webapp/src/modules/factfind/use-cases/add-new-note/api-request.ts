import { number, object, string } from 'yup';
import { apiClient } from '@aventur-shared/services/api';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { ClientId } from '@aventur-shared/modules/clients';
import { FactfindNote } from '@aventur-shared/modules/clients/models';

interface Body {
  content: string;
}

const postNoteResponseValidationSchema = object({
  id: number().required(),
  created_by: object({
    id: number().required(),
    first_name: string().defined().min(0),
    last_name: string().defined().min(0),
  }).required(),
  content: string().required(),
  created_datetime: string().required(),
});

export default async (
  clientId: ClientId,
  content: string,
): Promise<FactfindNote> => {
  const response = await apiClient.post<Body, Promise<unknown>>(
    `/api/internal/v1/clients/${clientId}/notes`,
    {
      content,
    },
  );
  try {
    const noteDTO = await postNoteResponseValidationSchema.validate(response);

    return {
      id: noteDTO.id,
      author: {
        id: noteDTO.created_by.id,
        firstName: noteDTO.created_by.first_name,
        lastName: noteDTO.created_by.last_name,
      },
      content: noteDTO.content,
      created: new DateTime(noteDTO.created_datetime),
    };
  } catch (e) {
    if (e instanceof Error) throw new Error(e.message);
    else {
      throw new Error('Could not fetch notes.');
    }
  }
};
