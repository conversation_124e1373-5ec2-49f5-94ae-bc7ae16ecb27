import { EventBusKey, useEventBus } from '@vueuse/core';
import { onUnmounted } from 'vue';

const SplitViewEventKey: EventBusKey<{ name: SplitViewEventType }> =
  Symbol('split-view');

export const SplitViewEvent = {
  UPDATE: 'linked-client-updated',
} as const;

export type SplitViewEventType =
  (typeof SplitViewEvent)[keyof typeof SplitViewEvent];

export const splitViewEventBus =
  useEventBus<SplitViewEventType>(SplitViewEventKey);

export const useSplitView = (listener: (e: SplitViewEventType) => void) => {
  const eventBus = useEventBus<SplitViewEventType>(SplitViewEventKey);

  eventBus.on(listener);
  onUnmounted(() => splitViewEventBus.off(listener));

  return {
    eventBus,
  };
};
