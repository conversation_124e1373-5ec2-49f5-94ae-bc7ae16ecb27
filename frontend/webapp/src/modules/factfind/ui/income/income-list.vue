<template>
  <div class="grid gap-4">
    <income-item
      v-for="income in incomes"
      :key="income.key"
      :income="income"
      @on-edit="handleEditIncomeRequest(income.key)"
      @on-remove="$emit('on-income-remove', income.key)"
    />
  </div>
  <dashed-border-button class="mt-4" @on-click="addIncomeModalApi?.open"
    >+Add income</dashed-border-button
  >
  <modal
    :config="{
      outsideClickClose: false,
    }"
    @use-api="(api) => (addIncomeModalApi = api)"
  >
    <template #default>
      <modal-form-content
        title="
          Add Income"
        @on-close="addIncomeModalApi?.close"
      >
        <income-form
          :form-values="createEmptyIncome()"
          type="ADD"
          @on-submit="handleAddIncome"
        />
      </modal-form-content>
    </template>
  </modal>
  <modal
    :config="{
      outsideClickClose: false,
      initialFocus: initialFocusRef,
    }"
    @use-api="(api) => (editIncomeModalApi = api)"
  >
    <template #default>
      <modal-form-content
        :title="'Edit Income'"
        @on-close="editIncomeModalApi?.close"
      >
        <income-form
          :form-values="getIncomeToEdit()"
          type="EDIT"
          @on-submit="(income) => handleEditIncome(income as IncomeListItem)"
          @on-load="handleIncomeFormLoad"
        />
      </modal-form-content>
    </template>
  </modal>
</template>

<script setup lang="ts">
  import { Ref, ref } from 'vue';
  import { Modal, ModalAPI } from '@modules/ui';
  import { default as IncomeItem } from './income-item.vue';
  import { default as IncomeForm } from './form/form.vue';
  import { default as ModalFormContent } from './form/modal-form-content.vue';
  import {
    type Income,
    type IncomeListItem,
  } from '@aventur-shared/modules/factfind/models';
  import { DashedBorderButton } from '@modules/factfind/ui';

  const emit = defineEmits<{
    (e: 'on-income-add', income: Income): void;
    (e: 'on-income-edit', income: IncomeListItem, key: string): void;
    (e: 'on-income-remove', key: string): void;
  }>();

  const props = defineProps<{
    incomes: Array<IncomeListItem>;
  }>();

  const initialFocusRef = ref<HTMLElement | null>(null);

  const addIncomeModalApi = ref<ModalAPI>();
  const editIncomeModalApi = ref<ModalAPI>();
  const editedIncomeKey = ref<string | null>(null);

  const handleIncomeFormLoad = ({
    focusRef,
  }: {
    focusRef: Ref<HTMLElement | null>;
  }) => {
    initialFocusRef.value = focusRef.value;
  };

  const getIncomeToEdit = () => {
    const foundIncome = props.incomes.find(
      (item) => item.key === editedIncomeKey.value,
    );
    if (!foundIncome) return createEmptyIncome();

    return {
      id: foundIncome.id,
      typeGroup: foundIncome.typeGroup,
      type: foundIncome.type,
      description: foundIncome.description,
      frequency: foundIncome.frequency.toValue(),
      amount: String(foundIncome.amount.getValue()),
    };
  };

  const handleAddIncome = (income: Income) => {
    emit('on-income-add', income);
    addIncomeModalApi.value?.close();
  };

  const handleEditIncomeRequest = (key: string) => {
    editedIncomeKey.value = key;
    editIncomeModalApi.value?.open();
  };

  const handleEditIncome = (income: IncomeListItem) => {
    emit('on-income-edit', income, editedIncomeKey.value as string);
    editedIncomeKey.value = null;
    editIncomeModalApi.value?.close();
  };

  const createEmptyIncome = () => {
    return {
      id: null,
      type: null,
      typeGroup: null,
      description: null,
      frequency: null,
      amount: null,
    };
  };
</script>

<style lang="postcss" scoped>
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    grid-auto-rows: minmax(250px, 1fr);
  }
</style>
