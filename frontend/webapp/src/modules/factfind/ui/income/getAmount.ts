import { IMoney, Money } from '@aventur-shared/utils/money';
import { IFrequency } from '@aventur-shared/modules/factfind/models/frequency';

export function getAnnualAmount(amount: IMoney, frequency: IFrequency): IMoney {
  return frequency.calculateAmount(amount);
}

export function getMonthlyAmount(
  amount: IMoney,
  frequency: IFrequency,
): IMoney {
  return new Money(frequency.calculateAmount(amount).getValue() / 12);
}
