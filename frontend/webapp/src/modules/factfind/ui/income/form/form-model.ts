import { mixed, number, object, string } from 'yup';
import { Nullable } from '@aventur-shared/types/Common';
import { validationMessages } from '@aventur-shared/utils/form';
import { IFrequency } from '@aventur-shared/modules/factfind/models/frequency';
import { invalidCharactersRegex } from '@aventur-shared/utils/form/fields';

export interface FormValues {
  id: Nullable<number>;
  typeGroup: Nullable<number>;
  type: Nullable<number>;
  description: Nullable<string>;
  frequency: Nullable<ReturnType<IFrequency['toValue']>>;
  amount: Nullable<string>;
}

export const validationSchema = object({
  typeGroup: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .required(validationMessages.fieldRequiredMessage),
  type: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .required(validationMessages.fieldRequiredMessage),
  description: string().nullable().matches(invalidCharactersRegex, {
    excludeEmptyString: true,
    message: validationMessages.invalidCharacterMessage,
  }),
  frequency: string()
    .nullable()
    .required(validationMessages.fieldRequiredMessage),
  amount: mixed()
    .checkIsNumber(validationMessages.invalidNumberMessage)
    .when({
      is: (value: string) => value,
      then: () =>
        number()
          .min(0, 'Value must be higher than 0')
          .max(1000000000000, 'Value must be lower than *************'),
    })
    .transform((value) => value || null)
    .required(validationMessages.fieldRequiredMessage),
});
