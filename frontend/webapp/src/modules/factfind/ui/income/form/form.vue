<template>
  <form @submit.prevent="onSubmit">
    <select-field
      label="Type Group"
      name="typeGroup"
      :options="cashFlow.getIncomeGroupsOptions()"
      @on-select="handleGroupSelect"
    />
    <select-field
      label="Type"
      name="type"
      :options="typeSelectOptions"
      :disabled="!values.typeGroup"
    />
    <text-field label="Description" name="description" />
    <select-field
      label="Frequency"
      name="frequency"
      :options="frequencySelectOptions"
    />
    <text-field label="Amount" name="amount" />

    <custom-button
      ref="submitButtonRef"
      type="submit"
      theme="primary"
      class="w-full"
      >{{ type === 'ADD' ? 'Add' : 'Edit' }} Income</custom-button
    >
  </form>
</template>

<script setup lang="ts">
  import { useForm } from 'vee-validate';
  import { Ref, computed, onMounted, ref } from 'vue';

  import { Money } from '@aventur-shared/utils/money';
  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import { type Income } from '@aventur-shared/modules/factfind/models/income';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { SelectField, TextField } from '@aventur-shared/components/form';
  import {
    Frequency,
    FrequencyEnum,
    frequencySelectOptions,
  } from '@aventur-shared/modules/factfind/models/frequency';

  import { Button as CustomButton } from '@modules/ui';
  import { type FormValues, validationSchema } from './form-model';

  const cashFlow = useCashFlow();
  const submitButtonRef = ref<HTMLElement | null>(null);

  const emit = defineEmits<{
    (e: 'on-submit', income: Income): void;
    (e: 'on-load', { focusRef }: { focusRef: Ref<HTMLElement | null> }): void;
  }>();

  const props = defineProps<{
    formValues: FormValues;
    type: 'ADD' | 'EDIT';
  }>();

  onMounted(() => {
    emit('on-load', { focusRef: submitButtonRef });
  });

  const { handleSubmit, setFieldValue, values } = useForm<FormValues>({
    initialValues: props.formValues,
    validationSchema,
  });

  const getTypeSelectOptions = (groupId: number) => {
    return groupId ? cashFlow.getIncomeGroupTypesOptions(groupId) : [];
  };

  const typeSelectOptions = computed<SelectOption[]>(() => {
    return values.typeGroup ? getTypeSelectOptions(values.typeGroup) : [];
  });

  const handleGroupSelect = () => {
    setFieldValue('type', null);
  };

  const onSubmit = handleSubmit((formValues: FormValues) => {
    emit('on-submit', {
      id: formValues.id,
      typeGroup: formValues.typeGroup as number,
      type: formValues.type as number,
      description: formValues.description as string,
      frequency: new Frequency(formValues.frequency as FrequencyEnum),
      amount: new Money(Number(formValues.amount)),
    });
  });
</script>
