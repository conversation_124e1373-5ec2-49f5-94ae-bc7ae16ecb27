<template>
  <box class="text-left">
    <box-section :title="title" divider="bottom" class="flex justify-between" />
    <box-section class="py-2" no-padding-y>
      <slot />
    </box-section>
    <box-section no-padding-y class="pb-4">
      <custom-button
        theme="gray-ghost"
        class="w-full"
        @on-click="$emit('on-close')"
        >Cancel & close</custom-button
      ></box-section
    >
  </box>
</template>
<script setup lang="ts">
  import { Box, BoxSection, Button as CustomButton } from '@modules/ui';

  defineProps<{ title?: string }>();
  defineEmits(['on-close']);
</script>
