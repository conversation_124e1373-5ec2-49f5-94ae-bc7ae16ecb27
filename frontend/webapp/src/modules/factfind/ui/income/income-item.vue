<template>
  <box class="flex min-w-[400px] flex-col justify-start">
    <box-section :title="incomeGroupName" class="relative">
      <p class="break-all">{{ incomeTypeName }}</p>
      <div v-if="!isActionDisabled" class="absolute right-6 top-4">
        <popup-options
          options-wrapper-class="-left-[100px]"
          :options="operations"
          @selected="
            (option: IncomeItemOperationsOptions[0]) =>
              handleSelectOption(option.value)
          "
        >
          <template #label>
            <ellipsis-vertical-icon
              class="inline-block size-5 cursor-pointer"
            />
          </template>

          <template #option="{ option }">
            <span>{{ option.label }}</span>
          </template>
        </popup-options>
      </div>
    </box-section>
    <box-section class="pt-0">
      <p class="text-[#7A8796]">Annual</p>
      <p class="break-all">
        {{ formatWithCurrency(annualAmount) }}
      </p>
    </box-section>
    <box-section class="pt-0">
      <p class="text-[#7A8796]">Monthly</p>
      <p class="break-all">
        {{ formatWithCurrency(monthlyAmount) }}
      </p>
    </box-section>
  </box>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';

  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import { formatWithCurrency } from '@aventur-shared/utils/money';
  import { type Income } from '@aventur-shared/modules/factfind/models';

  import { Box, BoxSection, PopupOptions } from '@modules/ui';
  import { getAnnualAmount, getMonthlyAmount } from './getAmount';
  //

  type IncomeItemOperations = 'edit' | 'remove';
  type IncomeItemOperationsOptions = Array<{
    label: string;
    value: IncomeItemOperations;
  }>;

  const props = withDefaults(
    defineProps<{
      income: Income;
      isActionDisabled?: boolean;
    }>(),
    {
      isActionDisabled: false,
    },
  );

  const emit = defineEmits<{
    (e: 'on-add'): void;
    (e: 'on-edit'): void;
    (e: 'on-remove'): void;
  }>();

  const { getIncomeByTypeId } = useCashFlow();

  const annualAmount = computed(() => {
    return getAnnualAmount(props.income.amount, props.income.frequency);
  });

  const monthlyAmount = computed(() => {
    return getMonthlyAmount(props.income.amount, props.income.frequency);
  });

  const incomeGroupName = computed(() => {
    return getIncomeByTypeId(props.income.type)?.group_name ?? 'N/A';
  });

  const incomeTypeName = computed(() => {
    return getIncomeByTypeId(props.income.type)?.name ?? 'N/A';
  });

  const operations: IncomeItemOperationsOptions = [
    {
      label: 'Edit',
      value: 'edit',
    },
    {
      label: 'Remove',
      value: 'remove',
    },
  ];

  const handleSelectOption = (option: IncomeItemOperations) => {
    switch (option) {
      case 'edit': {
        emit('on-edit');
        break;
      }
      case 'remove': {
        emit('on-remove');
        break;
      }
    }
  };
</script>
