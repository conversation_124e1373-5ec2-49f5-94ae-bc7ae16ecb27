import { IMoney, formatWithCurrency } from '@aventur-shared/utils/money';
import { useActiveClients, useRefData } from '@aventur-shared/stores';
import { moveElement } from '@aventur-shared/utils/list';
import { formatName } from '@aventur-shared/utils/user';
import { useUserStore } from '@aventur-shared/modules/users';
import { useClientStore } from '@aventur-shared/modules/clients';
import { Advisor, useAdvisorsStore } from '@aventur-shared/modules/advisors';
import { extractClientsFromClientProfile } from '@aventur-shared/modules/clients/models';
import {
  CreditCard,
  Debt,
  Mortgage,
  OtherDebt,
  PersonalLoan,
} from '@aventur-shared/modules/factfind/types/Debt';
import { Property } from '@aventur-shared/modules/factfind/types/Asset';

import { formatPropertyAssetAddress } from '@modules/factfind/models/asset';

const formatAdvisorName = (advisor: Advisor | null) => {
  if (!advisor) return '';
  return formatName({
    firstName: advisor?.firstName || '',
    lastName: advisor?.lastName || '',
  });
};

const formatFalsyValue = <T>(
  val?: T | null,
  valFmtFunc?: (val: T) => string,
) => {
  if (!val) return '';
  if (!valFmtFunc) return val;
  return valFmtFunc(val);
};

const getClientsFormatted = (clientIds: Debt['clientIds']) => {
  const { isClient } = useUserStore();
  const { getClientId, getProfile } = useClientStore();
  const { list: activeClients } = useActiveClients();
  const list = !isClient
    ? activeClients
    : extractClientsFromClientProfile(getProfile);

  const orderedClientIds = moveElement(
    clientIds,
    clientIds.indexOf(getClientId),
    0,
  );

  return orderedClientIds
    .map((id) => {
      const client = list.find((client) => client.id === id);
      if (!client) return '';
      return formatName(client);
    })
    .join(', ');
};

const formatPercentageValue = (value: number) => `${value}%`;

export const useDebtDescription = () => {
  const { getProviderByID } = useRefData();
  const { getAdvisorById } = useAdvisorsStore();

  function buildMortgageDescription(debt: Mortgage, collateral?: Property) {
    const advisor = debt.advisorId ? getAdvisorById(debt.advisorId) : null;

    return [
      ['Clients', getClientsFormatted(debt.clientIds)],
      [
        'Secured against',
        formatFalsyValue<Property>(collateral, formatPropertyAssetAddress),
      ],
      ['Provider', getProviderByID(debt.providerId).name],
      ['Account no', debt.accountNumber],
      ['Mortgage end date', debt.mortgageEndDate?.formatToView() || ''],
      [
        'Mortgage product end date',
        debt.mortgageProductEndDate?.formatToView() || '',
      ],
      [
        'Interest rate',
        formatFalsyValue(debt.interestRate, formatPercentageValue),
      ],
      [
        'Monthly payment',
        formatFalsyValue<IMoney>(debt.monthlyPayment, formatWithCurrency),
      ],
      ['Adviser', formatAdvisorName(advisor)],
    ];
  }

  function buildPersonalLoanDescription(
    debt: PersonalLoan,
    collateral?: Property,
  ) {
    const advisor = debt.advisorId ? getAdvisorById(debt.advisorId) : null;

    return [
      ['Clients', getClientsFormatted(debt.clientIds)],
      [
        'Secured against',
        formatFalsyValue<Property>(collateral, formatPropertyAssetAddress),
      ],
      ['Provider', getProviderByID(debt.providerId).name],
      ['Account no', debt.accountNumber],
      [
        'Loan end date',
        debt.loanEndDate ? debt.loanEndDate.formatToView() : '',
      ],
      [
        'Loan product end date',
        debt.loanProductEndDate ? debt.loanProductEndDate.formatToView() : '',
      ],
      [
        'Interest rate',
        formatFalsyValue(debt.interestRate, formatPercentageValue),
      ],
      [
        'Monthly payment',
        formatFalsyValue<IMoney>(debt.monthlyPayment, formatWithCurrency),
      ],
      ['Adviser', formatFalsyValue(advisor, formatAdvisorName)],
    ];
  }

  function buildCreditCardDescription(debt: CreditCard) {
    const advisor = debt.advisorId ? getAdvisorById(debt.advisorId) : null;

    return [
      ['Clients', getClientsFormatted(debt.clientIds)],
      ['Provider', getProviderByID(debt.providerId).name],
      ['Account no', debt.accountNumber],
      [
        'Interest rate',
        formatFalsyValue(debt.interestRate, formatPercentageValue),
      ],
      [
        'Interest rate end date',
        debt.interestRateEndDate ? debt.interestRateEndDate.formatToView() : '',
      ],
      [
        'Monthly payment',
        formatFalsyValue<IMoney>(debt.monthlyPayment, formatWithCurrency),
      ],
      ['Adviser', formatFalsyValue(advisor, formatAdvisorName)],
    ];
  }

  function buildOtherDebtDescription(debt: OtherDebt) {
    const advisor = debt.advisorId ? getAdvisorById(debt.advisorId) : null;

    return [
      ['Clients', getClientsFormatted(debt.clientIds)],
      ['Provider', getProviderByID(debt.providerId).name],
      ['Account no', debt.accountNumber],
      [
        'Interest rate',
        formatFalsyValue(debt.interestRate, formatPercentageValue),
      ],
      [
        'Interest rate end date',
        debt.interestRateEndDate ? debt.interestRateEndDate.formatToView() : '',
      ],
      [
        'Monthly payment',
        formatFalsyValue<IMoney>(debt.monthlyPayment, formatWithCurrency),
      ],
      ['Adviser', formatFalsyValue(advisor, formatAdvisorName)],
    ];
  }

  return {
    buildMortgageDescription,
    buildPersonalLoanDescription,
    buildCreditCardDescription,
    buildOtherDebtDescription,
  };
};
