<template>
  <box-section title="Your Debts" no-padding data-testid="debts"
    ><div class="flex items-center justify-between">
      <span class="text-sm text-gray-500"
        >Mortgages, loans and credit cards</span
      >
      <toggle
        label="Include Inactive"
        :model-value="includeInactive"
        :label-class="['!text-gray-500']"
        :disabled="disableToggleInactive"
        @update:model-value="(value) => (includeInactive = value)"
      >
        <template #label="{ label }">
          <div class="flex flex-col items-start justify-center -space-y-1">
            <span>{{ label }}</span>
            <span class="text-xs text-gray-500"
              >Showing {{ items.length }} of {{ debtList.length }}</span
            >
          </div>
        </template>
      </toggle>
    </div>
  </box-section>
  <box-section no-padding-x>
    <debts-list
      v-if="items.length"
      :debts="items"
      :is-action-disabled="isActionDisabled"
      :property-assets="propertyAssets"
      @on-remove="handleRemoveDebt"
      @on-edit="handleEditDebtRequest"
      @on-add-valuation="handleAddValuation"
    />
    <dashed-border-button
      class="mt-4"
      :disabled="isActionDisabled"
      @on-click="modalApis.addDebt?.open"
      >+ Add debt
    </dashed-border-button>
    <modal
      :config="{
        outsideClickClose: false,
        initialFocus: initialFocusRef,
      }"
      @use-api="(api) => (modalApis.addDebt = api)"
    >
      <template #default="{ close }">
        <box>
          <box-section
            title="Add Debt"
            divider="bottom"
            class="flex justify-between"
          />
          <box-section>
            <debt-form
              :client="client"
              :property-assets="propertyAssets"
              type="ADD"
              @on-submit="(...args) => handleAddDebt(...args)"
              @on-load="handleDebtFormLoad"
              @on-cancel="close"
            />
          </box-section>
        </box>
      </template>
    </modal>
    <modal
      :config="{
        outsideClickClose: false,
        initialFocus: initialFocusRef,
      }"
      @use-api="
        (api) => {
          modalApis.editDebt = api;
        }
      "
    >
      <template #default="{ close }">
        <box>
          <box-section
            title="Edit Debt"
            divider="bottom"
            class="flex justify-between"
          />
          <box-section>
            <debt-form
              :client="client"
              :property-assets="propertyAssets"
              :debt="getDebtToEdit()"
              type="EDIT"
              @on-submit="(debt) => handleEditDebt(debt)"
              @on-load="handleDebtFormLoad"
              @on-cancel="close"
            />
          </box-section>
        </box>
      </template>
    </modal>

    <valuation-modal
      @on-load="({ modalApi }) => (modalApis.addValuation = modalApi)"
    >
      <add-valuation
        :last-valuation-info="unref(valuationProcess.lastValuation)"
        @on-add="handleOnValuationAdd"
        @on-load="handleAddValuationLoad"
        @on-close="handleCloseAddingValuation"
      />
    </valuation-modal>
  </box-section>
</template>
<script setup lang="ts">
  import { Ref, ref, unref } from 'vue';

  import { Client } from '@aventur-shared/modules/clients';
  import Toggle from '@aventur-shared/components/Toggle.vue';
  import { useValuationProcess } from '@aventur-shared/composables/useValuation';
  import { useAssetsDebtsFiltering } from '@aventur-shared/modules/factfind/composables';
  import { Debt } from '@aventur-shared/modules/factfind';
  import { type DebtListItem } from '@modules/factfind/models/debt';
  import { Property } from '@aventur-shared/modules/factfind/types/Asset';

  import { DashedBorderButton } from '@modules/factfind/ui';
  import { Box, BoxSection, Modal, ModalAPI } from '@modules/ui';
  import { AddValuation, ValuationModal } from '@modules/factfind/ui/valuation';
  import { default as DebtsList } from './list.vue';
  import { default as DebtForm } from './form/form.vue';
  //

  const emit = defineEmits<{
    (e: 'on-debt-add', debt: Debt): void;
    (e: 'on-debt-edit', debt: DebtListItem): void;
    (e: 'on-debt-remove', debt: DebtListItem): void;
  }>();

  const props = defineProps<{
    client: Client;
    debtList: DebtListItem[];
    isActionDisabled: boolean;
    propertyAssets: Property[];
  }>();

  const initialFocusRef = ref<HTMLElement | null>(null);
  const editedDebtKey = ref<string | null>(null);
  const valuationProcess = useValuationProcess<DebtListItem>({
    onAddNew: (item: DebtListItem) => {
      emit('on-debt-add', item);
    },
    onAddToExisting: (item: DebtListItem) => {
      emit('on-debt-edit', item);
    },
  });

  const { items, includeInactive, disableToggleInactive } =
    useAssetsDebtsFiltering(
      () => props.debtList,
      (item: DebtListItem) => item.hasQuantity || !item.id,
    );

  const modalApis = ref<{
    editDebt: ModalAPI | undefined;
    addDebt: ModalAPI | undefined;
    addValuation: ModalAPI | undefined;
  }>({
    editDebt: undefined,
    addDebt: undefined,
    addValuation: undefined,
  });

  const handleDebtFormLoad = ({
    focusRef,
  }: {
    focusRef: Ref<HTMLElement | null>;
  }) => {
    initialFocusRef.value = focusRef.value;
  };

  const getDebtToEdit = () =>
    props.debtList.find((item) => item.key === editedDebtKey.value);

  const handleAddDebt = (debt: Debt, shouldAddValuation: boolean) => {
    modalApis.value.addDebt?.close();

    if (shouldAddValuation) {
      valuationProcess
        .addingValuationToNewElement({ ...debt, key: '' })
        .then(modalApis.value.addValuation?.open);
    } else {
      emit('on-debt-add', debt);
    }
  };

  const handleRemoveDebt = (debt: DebtListItem) => {
    emit('on-debt-remove', debt);
  };

  const handleEditDebtRequest = ({ key }: DebtListItem) => {
    editedDebtKey.value = key;
    modalApis.value.editDebt?.open();
  };

  const handleEditDebt = (debt: Debt) => {
    emit('on-debt-edit', { ...debt, key: editedDebtKey.value } as DebtListItem);
    editedDebtKey.value = null;
    modalApis.value.editDebt?.close();
  };

  function handleAddValuationLoad({
    focusRef,
  }: {
    focusRef: Ref<HTMLElement | null>;
  }) {
    initialFocusRef.value = focusRef.value;
  }

  function handleAddValuation(debt: DebtListItem) {
    valuationProcess
      .addingValuationToExistingItem(debt)
      .then(modalApis.value.addValuation?.open);
  }

  function handleCloseAddingValuation() {
    valuationProcess.cancel().then(modalApis.value?.addValuation?.close);
  }

  function handleOnValuationAdd(valuation: Debt['valuation']) {
    valuationProcess
      .addValuation(valuation)
      .then(modalApis.value.addValuation?.close);
  }
</script>
