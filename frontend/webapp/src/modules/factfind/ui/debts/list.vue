<template>
  <div class="grid gap-4">
    <box
      v-for="debt in debts"
      :key="debt.key"
      class="flex min-h-[250px] shrink grow basis-[400px] flex-col justify-start"
    >
      <list-row class="h-full">
        <list-item>
          <div v-if="!isActionDisabled" class="-mb-7 self-end">
            <popup-options
              options-wrapper-class="-left-[120px]"
              :options="getItemOperations(debt)"
              @selected="
                (option: DebtItemOperationsOptions[0]) =>
                  handleSelectOption(debt)(option.value)
              "
            >
              <template #label>
                <ellipsis-vertical-icon
                  class="inline-block size-5 cursor-pointer"
                />
              </template>
              <template #option="{ option }">
                <span>{{ option.label }}</span>
              </template>
            </popup-options>
          </div>
          <div class="flex flex-col">
            <p class="whitespace-nowrap text-base font-semibold text-gray-900">
              {{ productGroupName(debt) }}
              <template v-if="debt.id"
                ><span
                  v-if="displayDebtStatus(debt)"
                  class="text-xs font-normal text-gray-400"
                >
                  &middot; {{ getStatusName(debt.statusId) }}
                </span></template
              >
              <template v-else>
                <span class="text-xs font-normal text-teal-600">
                  &middot; New
                </span>
              </template>
            </p>
            <p class="text-base text-gray-500">
              {{ productTypeName(debt) }}
            </p>
          </div>
        </list-item>
        <list-item class="flex grow flex-row items-start justify-between p-5">
          <div class="flex flex-col">
            <template
              v-for="([name, value], index) in getDebtDescription(debt)"
              :key="index"
            >
              <p v-if="value" class="flex flex-row text-sm">
                <span class="min-w-[200px] text-gray-500">{{ name }}:</span>
                <span>{{ value }}</span>
              </p>
            </template>
          </div>
          <div
            class="self-end whitespace-nowrap text-right text-xl font-medium text-red-400"
          >
            <template v-if="debt.valuation">
              {{ formatValuationToMoney(debt.valuation) }}
              <span class="flex place-content-end text-xs text-gray-400">{{
                debt.valuation ? debt.valuation.date.formatToView() : null
              }}</span>
            </template>
            <template v-else>-</template>
          </div>
        </list-item>
      </list-row>
    </box>
  </div>
</template>

<script setup lang="ts">
  import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { formatToMoney as formatValuationToMoney } from '@aventur-shared/modules/factfind/models/valuation';
  import { useProductData } from '@aventur-shared/modules/factfind/models/product';
  import { getStatusName } from '@aventur-shared/modules/accounts/models/account';
  import { Property } from '@aventur-shared/modules/factfind/types/Asset';
  import { Debt } from '@aventur-shared/modules/factfind';

  import { Box, PopupOptions } from '@modules/ui';
  import { ListItem, ListRow } from '@modules/factfind/ui/list/';
  import {
    type DebtListItem,
    DebtTypeGuard,
  } from '@modules/factfind/models/debt';
  import { useDebtDescription } from './view-model';

  const { isClient } = useUserStore();
  const { productGroupName, productTypeName } = useProductData();

  type DebtItemOperations = 'edit' | 'remove' | 'add-valuation';
  type DebtItemOperationsOptions = Array<{
    label: string;
    value: DebtItemOperations;
    disabled?: boolean;
  }>;

  const emit = defineEmits<{
    (e: 'on-edit', debt: DebtListItem): void;
    (e: 'on-remove', debt: DebtListItem): void;
    (e: 'on-add-valuation', debt: DebtListItem): void;
  }>();

  const props = defineProps<{
    debts: DebtListItem[];
    isActionDisabled: boolean;
    propertyAssets: Property[];
  }>();

  const {
    buildMortgageDescription,
    buildPersonalLoanDescription,
    buildCreditCardDescription,
    buildOtherDebtDescription,
  } = useDebtDescription();

  const getItemOperations = (item: DebtListItem): DebtItemOperationsOptions => [
    {
      label: 'Edit',
      value: 'edit',
      disabled: props.isActionDisabled,
    },
    {
      label: 'Remove',
      value: 'remove',
      disabled: props.isActionDisabled || !item.hasQuantity,
    },
    {
      label: 'Add valuation',
      value: 'add-valuation',
      disabled: props.isActionDisabled,
    },
  ];

  const displayDebtStatus = (debt: DebtListItem) => !isClient;

  const handleSelectOption = (debt: DebtListItem) => {
    return (option: DebtItemOperations) => {
      switch (option) {
        case 'edit': {
          emit('on-edit', debt);
          break;
        }
        case 'remove': {
          emit('on-remove', debt);
          break;
        }
        case 'add-valuation': {
          emit('on-add-valuation', debt);
          break;
        }
      }
    };
  };

  const getDebtDescription = (debt: Debt) => {
    if (DebtTypeGuard.isMortgage(debt)) {
      const linkedAsset = props.propertyAssets.find(
        (asset) => asset.id === debt.securedAgainstAddressId,
      );
      return buildMortgageDescription(debt, linkedAsset);
    }

    if (DebtTypeGuard.isPersonalLoan(debt)) {
      const linkedAsset = props.propertyAssets.find(
        (asset) => asset.id === debt.securedAgainstAddressId,
      );
      return buildPersonalLoanDescription(debt, linkedAsset);
    }

    if (DebtTypeGuard.isCreditCard(debt)) {
      return buildCreditCardDescription(debt);
    }

    if (DebtTypeGuard.isOtherDebt(debt)) {
      return buildOtherDebtDescription(debt);
    }
    return '-';
  };
</script>

<style lang="postcss" scoped>
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    grid-auto-rows: minmax(250px, 1fr);
  }
</style>
