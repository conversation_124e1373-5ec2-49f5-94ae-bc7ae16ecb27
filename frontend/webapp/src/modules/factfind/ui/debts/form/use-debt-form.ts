import { computed, ref } from 'vue';

import { useClientStore } from '@aventur-shared/modules/clients';
import { Debt } from '@aventur-shared/modules/factfind';

import { DebtFormModelFactory, FormValues } from './form-model';
//

export const useDebtForm = (fv?: FormValues) => {
  const { getProfile } = useClientStore();
  const groupId = ref<FormValues['groupId']>(fv?.groupId || null);
  const typeId = ref<FormValues['typeId'] | null>(fv?.typeId || null);
  const clientIds = fv?.clientIds.length ? fv.clientIds : [getProfile.id];

  const formModel = computed(() =>
    DebtFormModelFactory.create(clientIds, groupId.value, typeId.value, fv),
  );
  const initialValues = computed(() => formModel.value.getInitialValues());
  const validationSchema = computed(() =>
    formModel.value.getValidationSchema(),
  );

  const handleGroupChange = (selectedGroup: Debt['groupId']) => {
    groupId.value = selectedGroup;
    typeId.value = null;
  };

  const setTypeValue = (selectedType: FormValues['typeId']) =>
    (typeId.value = selectedType);

  return {
    initialValues,
    validationSchema,
    handleGroupChange,
    setTypeValue,
  };
};
