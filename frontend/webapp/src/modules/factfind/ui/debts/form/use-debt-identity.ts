import { Debt } from '@aventur-shared/modules/factfind';
import { useProducts } from '@aventur-shared/composables/useProducts';
import { Product } from '@aventur-shared/modules/refdata/types/Product';

export const useDebtIdentity = () => {
  function debtInProducts(
    debtTypeId: Debt['typeId'],
    products: Product[],
  ): boolean {
    return products.map(($p) => $p.id).includes(debtTypeId);
  }
  const {
    getMortgageProducts,
    getPersonalLoanProducts,
    getCreditCardProducts,
    getOtherDebtProducts,
  } = useProducts();

  const isMortgageType = (typeId: Debt['typeId']) =>
    debtInProducts(typeId, getMortgageProducts.value);

  const isPersonalLoanType = (typeId: Debt['typeId']) =>
    debtInProducts(typeId, getPersonalLoanProducts.value);

  const isCreditCardType = (typeId: Debt['typeId']) =>
    debtInProducts(typeId, getCreditCardProducts.value);

  const isOtherDebtType = (typeId: Debt['typeId']) =>
    debtInProducts(typeId, getOtherDebtProducts.value);

  return {
    isMortgageType,
    isPersonalLoanType,
    isCreditCardType,
    isOtherDebtType,
  };
};
