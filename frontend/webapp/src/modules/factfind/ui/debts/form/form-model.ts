import { boolean, mixed, number, object, string } from 'yup';
import { validationMessages } from '@aventur-shared/utils/form';
import { Nullable } from '@aventur-shared/types/Common';
import { useDebtIdentity } from './use-debt-identity';
import { correctAccountNumber } from '@aventur-shared/utils/form/fields';
import { ClientId } from '@aventur-shared/modules/clients';
import { array } from 'yup';

export interface InitialFormValues {
  id: Nullable<number>;
  clientIds: ClientId[];
  groupId: Nullable<number>;
  typeId: Nullable<number>;
  statusId?: number;
  hasQuantity: boolean;
}
interface BaseFormValues extends InitialFormValues {
  providerId: Nullable<number>;
  accountNumber: Nullable<string>;
}

export interface MortgageFormValues extends BaseFormValues {
  securedAgainstAddressId: Nullable<number>;
  mortgageEndDate: Nullable<string>;
  interestRate: Nullable<number>;
  mortgageProductEndDate: Nullable<string>;
  monthlyPayment: Nullable<number>;
  advisorId: Nullable<number>;
}

export interface PersonalLoanFormValues extends BaseFormValues {
  securedAgainstAddressId: Nullable<number>;
  loanEndDate: Nullable<string>;
  interestRate: Nullable<number>;
  loanProductEndDate: Nullable<string>;
  monthlyPayment: Nullable<number>;
  advisorId: Nullable<number>;
}

export interface CreditCardFormValues extends BaseFormValues {
  interestRate: Nullable<number>;
  interestRateEndDate: Nullable<string>;
  monthlyPayment: Nullable<number>;
  advisorId: Nullable<number>;
}

export interface OtherDebtFormValues extends BaseFormValues {
  interestRate: Nullable<number>;
  interestRateEndDate: Nullable<string>;
  monthlyPayment: Nullable<number>;
  advisorId: Nullable<number>;
}

export type FormValues =
  | MortgageFormValues
  | PersonalLoanFormValues
  | CreditCardFormValues
  | OtherDebtFormValues;

const initialSchema = object({
  clientIds: array().of(number().required()).required(),
  groupId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .required(validationMessages.fieldRequiredMessage),
  typeId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .required(validationMessages.fieldRequiredMessage),
});

const baseSchema = initialSchema.concat(
  object({
    providerId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value))
      .required(validationMessages.fieldRequiredMessage),
    accountNumber: correctAccountNumber
      .nullable()
      .required(validationMessages.fieldRequiredMessage),
  }),
);

const mortgageSchema = baseSchema.concat(
  object({
    securedAgainstAddressId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value)),
    mortgageEndDate: string()
      .defined()
      .nullable()
      .transform((value) => (value ? value : null)),
    interestRate: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100, 'Value must be lower than 100'),
      })
      .transform((value) => value || null),
    mortgageProductEndDate: string()
      .defined()
      .nullable()
      .transform((value) => (value ? value : null)),
    monthlyPayment: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100000, 'Value must be lower than 100.000'),
      }),
    advisorId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value)),
  }),
);

const personalLoanSchema = baseSchema.concat(
  object({
    securedAgainstAddressId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value)),
    loanEndDate: string()
      .defined()
      .nullable()
      .transform((value) => (value ? value : null)),
    interestRate: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100, 'Value must be lower than 100'),
      })
      .transform((value) => value || null),
    loanProductEndDate: string()
      .defined()
      .nullable()
      .transform((value) => (value ? value : null)),
    monthlyPayment: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100000, 'Value must be lower than 100.000'),
      })
      .transform((value) => value || null),
    advisorId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value)),
  }),
);

const creditCardSchema = baseSchema.concat(
  object({
    interestRate: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100, 'Value must be lower than 100'),
      })
      .transform((value) => value || null),
    interestRateEndDate: string()
      .defined()
      .nullable()
      .transform((value) => (value ? value : null)),
    monthlyPayment: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100000, 'Value must be lower than 100.000'),
      })
      .transform((value) => value || null),
    advisorId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value)),
  }),
);

const otherDebtSchema = baseSchema.concat(
  object({
    interestRate: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100, 'Value must be lower than 100'),
      })
      .transform((value) => value || null),
    interestRateEndDate: string()
      .defined()
      .nullable()
      .transform((value) => (value ? value : null)),
    monthlyPayment: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100000, 'Value must be lower than 100.000'),
      })
      .transform((value) => value || null),
    advisorId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value)),
  }),
);

class InitialFormModel {
  private clientIds: ClientId[];
  private groupId: number | null;
  private typeId: number | null;

  constructor(
    clientIds: ClientId[],
    groupId: number | null,
    typeId: number | null,
  ) {
    this.clientIds = clientIds;
    this.groupId = groupId;
    this.typeId = typeId;
  }

  getValidationSchema() {
    return initialSchema;
  }
  getInitialValues() {
    return {
      id: null,
      hasQuantity: true,
      clientIds: this.clientIds,
      groupId: this.groupId ?? null,
      typeId: this.typeId ?? null,
    };
  }
}
class MortgageFormModel {
  private clientIds: ClientId[];
  private groupId: number;
  private typeId: number;
  private fv?: FormValues;

  constructor(
    clientIds: ClientId[],
    groupId: number,
    typeId: number,
    fv?: FormValues,
  ) {
    this.clientIds = clientIds;
    this.groupId = groupId;
    this.typeId = typeId;
    this.fv = fv;
  }

  getValidationSchema() {
    return mortgageSchema;
  }
  getInitialValues() {
    if (this.fv) return this.fv;
    return {
      id: null,
      hasQuantity: true,
      clientIds: this.clientIds,
      groupId: this.groupId,
      typeId: this.typeId,
      securedAgainstAddressId: null,
      providerId: null,
      accountNumber: null,
      mortgageEndDate: null,
      interestRate: null,
      mortgageProductEndDate: null,
      monthlyPayment: null,
      advisorId: null,
    };
  }
}

class PersonalLoanFormModel {
  private clientIds: ClientId[];
  private groupId: number;
  private typeId: number;
  private fv?: FormValues;

  constructor(
    clientIds: ClientId[],
    groupId: number,
    typeId: number,
    fv?: FormValues,
  ) {
    this.clientIds = clientIds;
    this.groupId = groupId;
    this.typeId = typeId;
    this.fv = fv;
  }

  getValidationSchema() {
    return personalLoanSchema;
  }
  getInitialValues() {
    if (this.fv) return this.fv;
    return {
      id: null,
      hasQuantity: true,
      clientIds: this.clientIds,
      groupId: this.groupId,
      typeId: this.typeId,
      securedAgainstAddressId: null,
      providerId: null,
      accountNumber: null,
      loanEndDate: null,
      interestRate: null,
      loanProductEndDate: null,
      monthlyPayment: null,
      advisorId: null,
    };
  }
}

class CreditCardFormModel {
  private clientIds: ClientId[];
  private groupId: number;
  private typeId: number;
  private fv?: FormValues;

  constructor(
    clientIds: ClientId[],
    groupId: number,
    typeId: number,
    fv?: FormValues,
  ) {
    this.clientIds = clientIds;
    this.groupId = groupId;
    this.typeId = typeId;
    this.fv = fv;
  }

  getValidationSchema() {
    return creditCardSchema;
  }
  getInitialValues() {
    if (this.fv) return this.fv;
    return {
      id: null,
      hasQuantity: true,
      clientIds: this.clientIds,
      groupId: this.groupId,
      typeId: this.typeId,
      providerId: null,
      accountNumber: null,
      interestRate: null,
      interestRateEndDate: null,
      monthlyPayment: null,
      advisorId: null,
    };
  }
}

class OtherDebtFormModel {
  private clientIds: ClientId[];
  private groupId: number;
  private typeId: number;
  private fv?: FormValues;

  constructor(
    clientIds: ClientId[],
    groupId: number,
    typeId: number,
    fv?: FormValues,
  ) {
    this.clientIds = clientIds;
    this.groupId = groupId;
    this.typeId = typeId;
    this.fv = fv;
  }

  getValidationSchema() {
    return otherDebtSchema;
  }
  getInitialValues() {
    if (this.fv) return this.fv;
    return {
      id: null,
      hasQuantity: true,
      clientIds: this.clientIds,
      groupId: this.groupId,
      typeId: this.typeId,
      providerId: null,
      accountNumber: null,
      interestRate: null,
      interestRateEndDate: null,
      monthlyPayment: null,
      advisorId: null,
    };
  }
}

export class DebtFormModelFactory {
  static create(
    clientIds: ClientId[],
    groupId: FormValues['groupId'],
    typeId: FormValues['typeId'],
    fv?: FormValues,
  ) {
    const {
      isMortgageType,
      isPersonalLoanType,
      isCreditCardType,
      isOtherDebtType,
    } = useDebtIdentity();

    if (groupId && typeId) {
      if (isMortgageType(typeId))
        return new MortgageFormModel(clientIds, groupId, typeId, fv);
      if (isPersonalLoanType(typeId))
        return new PersonalLoanFormModel(clientIds, groupId, typeId, fv);
      if (isCreditCardType(typeId))
        return new CreditCardFormModel(clientIds, groupId, typeId, fv);
      if (isOtherDebtType(typeId))
        return new OtherDebtFormModel(clientIds, groupId, typeId, fv);
    }

    return new InitialFormModel(clientIds, groupId, typeId);
  }
}
