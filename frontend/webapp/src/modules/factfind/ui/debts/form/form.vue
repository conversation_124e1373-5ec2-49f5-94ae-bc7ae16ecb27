<template>
  <form @submit.prevent="onSubmit">
    <select-field
      label="Group"
      name="groupId"
      :options="groups"
      :searchable="true"
      :disabled="isEditForm"
      @on-select="handleGroupSelect"
    />
    <select-field
      label="Type"
      name="typeId"
      :options="productTypeSelectOptions"
      :searchable="true"
      :disabled="!values.groupId || isEditForm"
      @on-select="(val) => setTypeValue(val)"
    />

    <template v-if="values.typeId">
      <multi-select-field
        name="clientIds"
        label="Clients"
        :options="linkedClients"
        :hint="
          isClient
            ? 'Contact your advisor to link clients to your profile.'
            : undefined
        "
      />
    </template>
    <template v-if="values.typeId && isMortgageType(values.typeId)">
      <select-field
        label="Secured against address"
        name="securedAgainstAddressId"
        :options="securedAgainstAddressSelectOptions()"
        :searchable="true"
        :can-clear="true"
      />
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <date-picker label="Mortgage end date" name="mortgageEndDate" />
      <text-field label="Interest rate" name="interestRate" />
      <date-picker
        label="Mortgage product end date"
        name="mortgageProductEndDate"
      />
      <text-field label="Monthly payment, £" name="monthlyPayment" />
      <select-field
        v-if="!isClient"
        label="Adviser"
        name="advisorId"
        :options="getAdvisorsSelectOptions(activeAdvisors)"
        :searchable="true"
        :can-clear="true"
      />
    </template>

    <template v-if="values.typeId && isPersonalLoanType(values.typeId)">
      <select-field
        label="Secured against address"
        name="securedAgainstAddressId"
        :options="securedAgainstAddressSelectOptions()"
        :searchable="true"
        :can-clear="true"
      />
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <date-picker label="Loan end date" name="loanEndDate" />
      <text-field label="Interest rate" name="interestRate" />
      <date-picker label="Loan product end date" name="loanProductEndDate" />
      <text-field label="Monthly payment, £" name="monthlyPayment" />
      <select-field
        v-if="!isClient"
        label="Adviser"
        name="advisorId"
        :options="getAdvisorsSelectOptions(activeAdvisors)"
        :searchable="true"
        :can-clear="true"
      />
    </template>

    <template v-if="values.typeId && isCreditCardType(values.typeId)">
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <text-field label="Interest rate" name="interestRate" />
      <date-picker label="Interest rate end date" name="interestRateEndDate" />
      <text-field label="Monthly payment, £" name="monthlyPayment" />
      <select-field
        v-if="!isClient"
        label="Adviser"
        name="advisorId"
        :options="getAdvisorsSelectOptions(activeAdvisors)"
        :searchable="true"
        :can-clear="true"
      />
    </template>

    <template v-if="values.typeId && isOtherDebtType(values.typeId)">
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <text-field label="Interest rate" name="interestRate" />
      <date-picker label="Interest rate end date" name="interestRateEndDate" />
      <text-field label="Monthly payment, £" name="monthlyPayment" />
      <select-field
        v-if="!isClient"
        label="Adviser"
        name="advisorId"
        :options="getAdvisorsSelectOptions(activeAdvisors)"
        :searchable="true"
        :can-clear="true"
      />
    </template>
    <template v-if="type === 'ADD'">
      <div class="flex flex-col items-start justify-between py-2">
        <valuation-toggle
          label="Add valuation"
          @on-toggle="(val) => (shouldAddValuation = val)"
        />
      </div>
    </template>
    <div class="flex flex-col items-center justify-between gap-y-2">
      <custom-button
        ref="submitButtonRef"
        type="submit"
        theme="primary"
        class="w-full"
      >
        {{ type === 'ADD' ? 'Add' : 'Edit' }} Debt
      </custom-button>
      <custom-button
        theme="gray-ghost"
        class="w-full"
        @click="emit('on-cancel')"
      >
        Cancel & close
      </custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { Ref, onMounted, ref, toRef, watch } from 'vue';
  import { useForm } from 'vee-validate';

  import {
    DatePicker,
    MultiSelectField,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { useRefData } from '@aventur-shared/stores';
  import { Client } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { Debt } from '@aventur-shared/modules/factfind';
  import { Property } from '@aventur-shared/modules/factfind/types/Asset';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';

  import { Button as CustomButton } from '@modules/ui';
  import { formatPropertyAssetAddress } from '@/modules/factfind/models/asset';
  import { ValuationToggle } from '@modules/factfind/ui/valuation';
  import { DebtListItem } from '@/modules/factfind/models/debt';
  import { useDebtForm } from './use-debt-form';
  import { useDebtIdentity } from './use-debt-identity';
  import { FormValues, InitialFormValues } from './form-model';
  import { domainToFormValues, formValuesToDomain } from './mapper';

  const emit = defineEmits<{
    (e: 'on-submit', debt: Debt, shouldAddValuation: boolean): void;
    (e: 'on-load', { focusRef }: { focusRef: Ref<HTMLElement | null> }): void;
    (e: 'on-cancel'): void;
  }>();

  const props = defineProps<{
    client: Client;
    debt?: DebtListItem;
    propertyAssets: Property[];
    type: 'ADD' | 'EDIT';
  }>();

  const submitButtonRef = ref<HTMLElement | null>(null);

  const isEditForm = props.type === 'EDIT';

  const { isClient } = useUserStore();
  const { linkedClients } = useLinkedClientList(toRef(props, 'client'));
  const { getProviders, getDebts, getProductsByGroupId } = useRefData();

  const { activeAdvisors } = advisorsProvider().provide();
  const shouldAddValuation = ref(false);

  const {
    isMortgageType,
    isPersonalLoanType,
    isCreditCardType,
    isOtherDebtType,
  } = useDebtIdentity();

  const { initialValues, validationSchema, handleGroupChange, setTypeValue } =
    useDebtForm(props.debt && domainToFormValues(props.debt));

  const { handleSubmit, values, resetForm } = useForm<
    FormValues | InitialFormValues
  >({
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    initialValues,
    validationSchema,
  });

  const groups = getDebts.map((product) => ({
    label: product.name,
    value: product.id,
  })) as Array<SelectOption>;

  const providers = getProviders.map((provider) => ({
    label: provider.name,
    value: provider.id,
  })) as Array<SelectOption>;

  onMounted(async () => {
    await advisorsProvider().create();
    emit('on-load', { focusRef: submitButtonRef });
  });

  const productTypeSelectOptions = ref<SelectOption[]>(
    props.debt ? getProductTypeSelectOptions(props.debt.groupId) : [],
  );

  const securedAgainstAddressSelectOptions = (): SelectOption[] => {
    return props.propertyAssets.map((asset) => ({
      label: formatPropertyAssetAddress(asset),
      value: asset.id as number,
    }));
  };

  watch(validationSchema, () => {
    resetForm({
      values: initialValues.value,
    });
  });

  const handleGroupSelect = (selectedOptionValue: number) => {
    handleGroupChange(selectedOptionValue);
    productTypeSelectOptions.value =
      getProductTypeSelectOptions(selectedOptionValue);
  };

  function getProductTypeSelectOptions(groupId: number): SelectOption[] {
    return getProductsByGroupId(groupId).map((type) => ({
      label: type.name,
      value: type.id,
    }));
  }

  const onSubmit = handleSubmit((formValues) => {
    emit(
      'on-submit',
      {
        ...formValuesToDomain(
          validationSchema.value.cast(formValues) as FormValues,
        ),
        id: props.debt?.id ?? null,
      },
      shouldAddValuation.value,
    );
  });
</script>
