<template>
  <modal
    :config="{
      initialFocus: initialFocusRef,
    }"
    @use-api="
      (api) => {
        modalApi = api;
      }
    "
  >
    <template #default>
      <box>
        <box-section divider="bottom" title="Valuation" />
        <box-section><slot /></box-section>
      </box>
    </template>
  </modal>
</template>

<script setup lang="ts">
  import { Box, BoxSection, Modal, ModalAPI } from '@modules/ui';
  import { onMounted, ref } from 'vue';

  const emit = defineEmits(['on-load']);

  const initialFocusRef = ref<HTMLElement | null>(null);
  const modalApi = ref<ModalAPI>();

  onMounted(() => {
    emit('on-load', { modalApi });
  });
</script>
