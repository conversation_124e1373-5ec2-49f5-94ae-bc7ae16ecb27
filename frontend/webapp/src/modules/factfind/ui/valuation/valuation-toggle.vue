<template>
  <span class="flex items-center gap-2">
    <clean-checkbox-input
      id="toggleValue"
      name="toggleValue"
      :model-value="toggleValue"
      :checked="toggleValue"
      @change="handleChange"
    />
    <label for="toggleValue">{{ label }}</label>
  </span>
</template>

<script setup lang="ts">
  import { CleanCheckboxInput } from '@aventur-shared/components/form/fields/clean-fields';
  import { ref } from 'vue';

  const emit = defineEmits(['on-toggle']);
  defineProps<{ label: string }>();

  const toggleValue = ref(false);

  function handleChange(event: InputEvent) {
    toggleValue.value = (event.target as HTMLInputElement).checked;
    emit('on-toggle', toggleValue.value);
  }
</script>
