<template>
  <form @submit.prevent="onSubmit">
    <text-field label="Valuation, £" name="amount" autocomplete="off" />
    <date-picker label="Valuation date" name="date" />
    <multi-state-switch
      label=""
      name="valuationType"
      :options="[
        {
          label: 'Estimate',
          value: 'estimate',
        },
        {
          label: 'Actual',
          value: 'actual',
        },
      ]"
    />

    <div class="flex flex-col gap-1">
      <custom-button ref="submitButtonRef" type="submit" theme="primary">{{
        saveButtonLabel
      }}</custom-button>
      <custom-button
        type="button"
        theme="gray-ghost"
        @click.prevent="$emit('on-close')"
      >
        Close
      </custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { DateTime } from 'luxon';
  import { useForm } from 'vee-validate';
  import { Ref, onMounted, ref } from 'vue';
  import { date, mixed, number, object, string } from 'yup';

  import {
    DatePicker,
    MultiStateSwitch,
    TextField,
  } from '@aventur-shared/components/form';
  import {
    fieldRequiredMessage,
    invalidNumberMessage,
    noFutureDateMessage,
  } from '@aventur-shared/utils/form/validation-messages';

  import { Button as CustomButton } from '@modules/ui';
  import { ValuationFormModel } from '@modules/factfind/ui/valuation/valuation-form.model';
  //

  const validationSchema = object({
    amount: mixed()
      .checkIsNumber(invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: (schema) =>
          number()
            .positive('Value cannot be negative')
            .max(99000000, 'Value must be lower than 99.000.000'),
      })
      .transform((value) => value || null)
      .required(fieldRequiredMessage),
    date: string()
      .transform((value) => (DateTime.isDateTime(value) ? value : undefined))
      .when({
        is: (value: string) => value,
        then: () => date().max(DateTime.now(), noFutureDateMessage),
      })
      .required(fieldRequiredMessage),
    valuationType: string().required(fieldRequiredMessage),
  });

  defineProps<{
    saveButtonLabel: string;
  }>();
  const emit = defineEmits<{
    (e: 'on-load', focusRef: { focusRef: Ref<HTMLElement | null> }): void;
    (e: 'on-submit', formValues: ValuationFormModel): void;
    (e: 'on-close'): void;
  }>();

  const { handleSubmit } = useForm<ValuationFormModel>({
    initialValues: {
      amount: 0,
      date: '',
      valuationType: 'estimate',
    },
    validationSchema,
  });
  const submitButtonRef = ref<HTMLElement | null>(null);

  onMounted(() => {
    emit('on-load', { focusRef: submitButtonRef });
  });

  const onSubmit = handleSubmit(function (formValues: ValuationFormModel) {
    emit('on-submit', formValues);
  });
</script>
