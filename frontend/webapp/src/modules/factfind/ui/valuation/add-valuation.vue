<template>
  <box-section v-if="lastValuationInfo" no-padding>
    <p
      class="bg-primary-100 text-primary -mt-2 mb-2 rounded-md px-2 py-1 text-sm"
    >
      {{ lastValuationInfo }}
    </p>
  </box-section>

  <valuation-form
    save-button-label="Add valuation"
    @on-submit="handleSubmit"
    @on-load="(args) => $emit('on-load', args)"
    @on-close="(...args) => $emit('on-close')"
  />
</template>

<script setup lang="ts">
  import { Ref } from 'vue';

  import { DateTime } from '@aventur-shared/utils/dateTime';
  import { Money } from '@aventur-shared/utils/money';
  import { Asset } from '@aventur-shared/modules/factfind';

  import BoxSection from '@modules/ui/box/box-section.vue';
  import { ValuationFormModel } from '@modules/factfind/ui/valuation/valuation-form.model';
  import ValuationForm from './valuation-form.vue';
  //

  const emit = defineEmits<{
    (e: 'on-add', asset: Asset['valuation']): void;
    (e: 'on-load', focusRef: { focusRef: Ref<HTMLElement | null> }): void;
    (e: 'on-close'): void;
  }>();

  defineProps<{ lastValuationInfo: string }>();

  function handleSubmit(formValues: ValuationFormModel) {
    emit('on-add', {
      amount: new Money(+formValues.amount),
      date: new DateTime(formValues.date),
      type: formValues.valuationType,
    });
  }
</script>
