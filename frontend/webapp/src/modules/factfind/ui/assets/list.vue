<template>
  <div class="grid gap-4">
    <box
      v-for="asset in assets"
      :key="asset.key"
      class="flex min-h-[250px] min-w-[400px] flex-col justify-start"
    >
      <list-row class="h-full">
        <list-item>
          <div v-if="!isActionDisabled" class="-mb-7 self-end">
            <popup-options
              options-wrapper-class="-left-[120px]"
              :options="getItemOperations(asset)"
              @selected="
                (option: AssetItemOperationsOptions[0]) =>
                  handleSelectOption(asset)(option.value)
              "
            >
              <template #label>
                <ellipsis-vertical-icon
                  class="inline-block size-5 cursor-pointer"
                />
              </template>
              <template #option="{ option }">
                <span>{{ option.label }}</span>
              </template>
            </popup-options>
          </div>
          <div class="flex flex-col">
            <p class="whitespace-nowrap text-base font-semibold text-gray-900">
              {{ productGroupName(asset) }}
              <template v-if="asset.id">
                <span
                  v-if="displayAssetStatus(asset)"
                  class="text-xs font-normal text-gray-400"
                >
                  &middot; {{ getStatusName(asset.statusId) }}
                </span>
              </template>
              <template v-else>
                <span class="text-xs font-normal text-teal-600">
                  &middot; New
                </span>
              </template>
            </p>
            <p class="text-base text-gray-500">
              {{ productTypeName(asset) }}
            </p>
          </div>
        </list-item>
        <list-item class="flex grow flex-row items-start justify-between p-5">
          <div class="flex flex-col">
            <template
              v-for="([name, value], index) in AssetDescriptionBuilder.build(
                asset,
              )"
              :key="index"
            >
              <p v-if="value" class="flex flex-row text-sm">
                <span class="min-w-[200px] text-gray-500">{{ name }}:</span>
                <span>{{ value }}</span>
              </p>
            </template>
          </div>
          <div class="text-primary self-end text-right text-xl font-medium">
            <template v-if="asset.valuation">
              {{ formatValuationToMoney(asset.valuation) }}
              <span class="flex place-content-end text-xs text-gray-400">{{
                asset.valuation ? asset.valuation.date.formatToView() : null
              }}</span>
            </template>
            <template v-else>-</template>
          </div>
        </list-item>
      </list-row>
    </box>
  </div>
</template>

<script setup lang="ts">
  import { EllipsisVerticalIcon } from '@heroicons/vue/20/solid';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { getStatusName } from '@aventur-shared/modules/accounts/models/account';
  import { formatToMoney as formatValuationToMoney } from '@aventur-shared/modules/factfind/models/valuation';
  import { useProductData } from '@aventur-shared/modules/factfind/models/product';
  import { Box, PopupOptions } from '@modules/ui';
  import { ListItem, ListRow } from '@modules/factfind/ui/list/';
  import { type AssetListItem } from '@modules/factfind/models/asset';
  import { AssetDescriptionBuilder } from '@modules/factfind/ui/assets/view-model';
  import { useAssetIdentity } from '@modules/factfind/ui/assets/form/use-asset-identity';

  const { isClient } = useUserStore();
  const { isPropertyType } = useAssetIdentity();
  const { productGroupName, productTypeName } = useProductData();
  type AssetItemOperations = 'edit' | 'remove' | 'add-valuation';
  type AssetItemOperationsOptions = Array<{
    label: string;
    value: AssetItemOperations;
    disabled?: boolean;
  }>;

  const emit = defineEmits<{
    (e: 'on-edit', asset: AssetListItem): void;
    (e: 'on-remove', asset: AssetListItem): void;
    (e: 'on-add-valuation', asset: AssetListItem): void;
  }>();

  const props = defineProps<{
    assets: AssetListItem[];
    isActionDisabled: boolean;
  }>();
  const getItemOperations = (
    item: AssetListItem,
  ): AssetItemOperationsOptions => [
    {
      label: 'Edit',
      value: 'edit',
      disabled: props.isActionDisabled,
    },
    {
      label: 'Remove',
      value: 'remove',
      disabled: !!item.id && (props.isActionDisabled || !item.hasQuantity),
    },
    {
      label: 'Add valuation',
      value: 'add-valuation',
      disabled: props.isActionDisabled,
    },
  ];

  const displayAssetStatus = (asset: AssetListItem) =>
    !isClient && !isPropertyType(asset.typeId);

  const handleSelectOption = (asset: AssetListItem) => {
    return (option: AssetItemOperations) => {
      switch (option) {
        case 'edit': {
          emit('on-edit', asset);
          break;
        }
        case 'remove': {
          emit('on-remove', asset);
          break;
        }
        case 'add-valuation': {
          emit('on-add-valuation', asset);
          break;
        }
      }
    };
  };
</script>

<style lang="postcss" scoped>
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    grid-auto-rows: minmax(250px, 1fr);
  }
</style>
