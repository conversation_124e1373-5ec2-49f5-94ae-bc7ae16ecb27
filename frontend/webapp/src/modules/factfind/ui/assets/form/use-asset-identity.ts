import { Asset } from '@aventur-shared/modules/factfind';
import { useProducts } from '@aventur-shared/composables/useProducts';
import { Product } from '@aventur-shared/modules/refdata/types/Product';

export const useAssetIdentity = () => {
  function assetInProducts(
    assetTypeId: Asset['typeId'],
    products: Product[],
  ): boolean {
    return products.map(($p) => $p.id).includes(assetTypeId);
  }
  const {
    getAccountProducts,
    getPropertyProducts,
    getCompanySharesProducts,
    getCryptoCurrencyProducts,
    getOtherAssetProducts,
    getTermPolicyProducts,
    getIndemnityPolicyProducts,
    getWholeOfLifePolicyProducts,
    getIncomeProtectionPolicyProducts,
    getDefinedBenefitPensionProducts,
  } = useProducts();

  const isAccountType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getAccountProducts.value);

  const isPropertyType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getPropertyProducts.value);

  const isCompanySharesType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getCompanySharesProducts.value);

  const isCryptoCurrencyType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getCryptoCurrencyProducts.value);

  const isOtherAssetType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getOtherAssetProducts.value);

  const isTermPolicyType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getTermPolicyProducts.value);

  const isIndemnityPolicyType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getIndemnityPolicyProducts.value);

  const isWholeOfLifePolicyType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getWholeOfLifePolicyProducts.value);

  const isIncomeProtectionPolicyType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getIncomeProtectionPolicyProducts.value);

  const isDefinedBenefitPensionType = (typeId: Asset['typeId']) =>
    assetInProducts(typeId, getDefinedBenefitPensionProducts.value);

  return {
    isAccountType,
    isPropertyType,
    isCompanySharesType,
    isCryptoCurrencyType,
    isOtherAssetType,
    isTermPolicyType,
    isIndemnityPolicyType,
    isWholeOfLifePolicyType,
    isIncomeProtectionPolicyType,
    isDefinedBenefitPensionType,
  };
};
