<template>
  <form @submit.prevent="onSubmit">
    <select-field
      label="Group"
      name="groupId"
      :options="groups"
      :searchable="true"
      :disabled="isEditForm"
      @on-select="handleGroupSelect"
    />
    <select-field
      label="Type"
      name="typeId"
      :options="productTypeSelectOptions"
      :searchable="true"
      :disabled="!values.groupId || isEditForm"
      @on-select="handleTypeSelect"
    />
    <template v-if="values.typeId">
      <multi-select-field
        name="clientIds"
        label="Clients"
        :options="linkedClients"
        :hint="
          isClient
            ? 'Contact your advisor to link clients to your profile.'
            : undefined
        "
      />
    </template>
    <template v-if="values.typeId && isAccountType(values.typeId)">
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field
        label="Account Number"
        name="accountNumber"
        autocomplete="off"
      />
      <text-field
        label="Sub Account Number"
        name="subAccountNumber"
        autocomplete="off"
      />
      <div class="mt-7 border-t border-dashed pt-4">
        <div class="grid grid-cols-2 grid-rows-1 gap-4">
          <select-field
            label="Contribution/Withdrawal"
            name="monthlyPaymentDirection"
            :options="[
              { label: 'Contribution', value: 'contribution' },
              { label: 'Withdrawal', value: 'withdrawal' },
            ]"
          />
          <text-field
            label="Amount, £"
            name="monthlyPaymentAmount"
            autocomplete="off"
          />
        </div>
        <select-field
          label="Estimated Risk"
          name="riskLevel"
          :options="
            getRiskLevels.map((risk_level) => ({
              label: risk_level.name,
              value: risk_level.id,
            }))
          "
        />
      </div>
    </template>
    <template v-if="values.typeId && isPropertyType(values.typeId)">
      <text-field label="Address Line 1" name="addressLineOne" />
      <text-field label="Address Line 2" name="addressLineTwo" />
      <text-field label="City" name="city" />
      <text-field label="Postcode" name="postCode" />
      <select-field
        label="Country"
        name="countryId"
        :options="countries"
        :searchable="true"
      />
      <select-field
        label="Owner"
        name="owner"
        :options="owners"
        :can-clear="true"
      />
    </template>
    <template v-if="values.typeId && isCompanySharesType(values.typeId)">
      <text-field label="Name of Company" name="nameOfCompany" />
    </template>
    <template v-if="values.typeId && isCryptoCurrencyType(values.typeId)">
      <text-field label="Name of Currency" name="nameOfCurrency" />
      <text-field label="Number of coins" name="numberOfCoins" />
    </template>
    <template v-if="values.typeId && isOtherAssetType(values.typeId)">
      <text-field label="Name of Asset" name="nameOfAsset" />
    </template>
    <template v-if="values.typeId && isTermPolicyType(values.typeId)">
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <text-field label="Sub Account Number" name="subAccountNumber" />
      <text-field label="Cover Amount, £" name="coverAmount" />
      <date-picker label="Policy End Date" name="policyEndDate" />
    </template>
    <template v-if="values.typeId && isIndemnityPolicyType(values.typeId)">
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <text-field label="Sub Account Number" name="subAccountNumber" />
      <date-picker label="Policy End Date" name="policyEndDate" />
    </template>
    <template v-if="values.typeId && isWholeOfLifePolicyType(values.typeId)">
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <text-field label="Sub Account Number" name="subAccountNumber" />
      <text-field label="Cover Amount, £" name="coverAmount" />
    </template>
    <template
      v-if="values.typeId && isIncomeProtectionPolicyType(values.typeId)"
    >
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <text-field label="Sub Account Number" name="subAccountNumber" />
      <text-field label="Monthly Benefit, £" name="monthlyBenefit" />
      <text-field label="Deferred Period, weeks" name="deferredWeeks" />
      <date-picker label="Policy End Date" name="policyEndDate" />
    </template>
    <template
      v-if="values.typeId && isDefinedBenefitPensionType(values.typeId)"
    >
      <select-field
        label="Provider"
        name="providerId"
        :options="providers"
        :searchable="true"
      />
      <text-field label="Account Number" name="accountNumber" />
      <text-field label="Sub Account Number" name="subAccountNumber" />
      <checkbox-field
        label="Is the Pension Index Linked?"
        name="indexLinked"
        :checkedValue="true"
      />
      <checkbox-field
        label="Does the Pension Have Survivor Benefits?"
        name="survivorBenefits"
        :checkedValue="true"
      />
      <checkbox-field
        label="Is the Client Still Working In This Job?"
        name="isCurrentJob"
        :checkedValue="true"
      />
      <template v-if="values.isCurrentJob">
        <text-field
          label="Estimated Annual Income at Retirement, £"
          name="estimatedAnnualIncomeAtRetirement"
        />
        <text-field
          label="Scheme Normal Retirement Age"
          name="schemeNormalRetirementAge"
        />
      </template>
      <template v-else>
        <text-field label="Accrual Rate" name="accrualRate" />
        <text-field
          label="Predicted Final Salary, £"
          name="predictedFinalSalary"
        />
        <text-field
          label="Predicted Years of Service at Retirement"
          name="predictedYearsOfServiceAtRetirement"
        />
      </template>
    </template>
    <template v-if="type === 'ADD'">
      <div class="flex flex-col items-start justify-between py-2">
        <valuation-toggle
          label="Add valuation"
          @on-toggle="(val) => (shouldAddValuation = val)"
        />
      </div>
    </template>
    <div class="flex flex-col items-center justify-between gap-y-2">
      <custom-button
        ref="submitButtonRef"
        type="submit"
        theme="primary"
        class="w-full"
        >{{ type === 'ADD' ? 'Add' : 'Edit' }} Asset
      </custom-button>
      <custom-button
        theme="gray-ghost"
        class="w-full"
        @click="emit('on-cancel')"
        >Cancel & close
      </custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { sortBy } from 'lodash';
  import { Ref, onMounted, ref, toRef } from 'vue';
  import { useForm } from 'vee-validate';
  import {
    CheckboxField,
    DatePicker,
    MultiSelectField,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { Button as CustomButton } from '@modules/ui';
  import { useRefData } from '@aventur-shared/stores';
  import { Client } from '@aventur-shared/modules/clients';
  import { useUserStore } from '@aventur-shared/modules/users';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { Asset } from '@aventur-shared/modules/factfind';

  import { type AssetListItem } from '@/modules/factfind/models/asset';
  import { useAssetForm } from './use-asset-form';
  import { useAssetIdentity } from './use-asset-identity';
  import { ownerValueLabelMap } from '@modules/factfind/ui/assets/view-model';
  import { ValuationToggle } from '@modules/factfind/ui/valuation';

  const emit = defineEmits<{
    (
      e: 'on-submit',
      asset: Asset | AssetListItem,
      shouldAddValuation: boolean,
    ): void;
    (e: 'on-load', { focusRef }: { focusRef: Ref<HTMLElement | null> }): void;
    (e: 'on-cancel'): void;
  }>();

  const props = defineProps<{
    client: Client;
    asset?: AssetListItem;
    type: 'ADD' | 'EDIT';
  }>();

  const submitButtonRef = ref<HTMLElement | null>(null);
  const shouldAddValuation = ref(false);
  const isEditForm = props.type === 'EDIT';

  const { isClient } = useUserStore();
  const { linkedClients } = useLinkedClientList(toRef(props, 'client'));
  const {
    getProviders,
    getAssets,
    getProductsByGroupId,
    getCountries,
    getRiskLevels,
  } = useRefData();

  const {
    isAccountType,
    isPropertyType,
    isCompanySharesType,
    isCryptoCurrencyType,
    isOtherAssetType,
    isTermPolicyType,
    isIndemnityPolicyType,
    isWholeOfLifePolicyType,
    isIncomeProtectionPolicyType,
    isDefinedBenefitPensionType,
  } = useAssetIdentity();

  const { initialValues, validationSchema, handleGroupChange, setTypeValue } =
    useAssetForm(props.asset);

  const { handleSubmit, values, resetForm } = useForm<
    (typeof initialValues)['value'] & {
      isCurrentJob?: boolean;
    }
  >({
    validationSchema,
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    initialValues,
  });

  const groups = getAssets.map((product) => ({
    label: product.name,
    value: product.id,
  })) as Array<SelectOption>;

  const providers = getProviders.map((provider) => ({
    label: provider.name,
    value: provider.id,
  })) as Array<SelectOption>;

  const countries = sortBy(
    getCountries.map((item) => ({
      label: item.name,
      value: item.id,
    })),
    'label',
  ) as Array<SelectOption>;

  const owners = [...ownerValueLabelMap].map(([value, label]) => ({
    label,
    value,
  })) as Array<SelectOption>;

  onMounted(async () => {
    emit('on-load', { focusRef: submitButtonRef });
  });

  const productTypeSelectOptions = ref<SelectOption[]>(
    props.asset ? getProductTypeSelectOptions(props.asset.groupId) : [],
  );

  const handleGroupSelect = (selectedOptionValue: number) => {
    handleGroupChange(selectedOptionValue);
    resetForm({
      values: initialValues.value,
    });
    productTypeSelectOptions.value =
      getProductTypeSelectOptions(selectedOptionValue);
  };

  const handleTypeSelect = (selectedOptionValue: number) => {
    setTypeValue(selectedOptionValue);
    resetForm({
      values: initialValues.value,
    });
  };

  function getProductTypeSelectOptions(groupId: number): SelectOption[] {
    return getProductsByGroupId(groupId).map((type) => ({
      label: type.name,
      value: type.id,
    }));
  }

  const onSubmit = handleSubmit((formValues) => {
    emit(
      'on-submit',
      validationSchema.value.cast(formValues) as Asset | AssetListItem,
      shouldAddValuation.value,
    );
  });
</script>
