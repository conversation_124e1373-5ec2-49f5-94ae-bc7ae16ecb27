import {
  ObjectSchema,
  array,
  boolean,
  mixed,
  number,
  object,
  string,
} from 'yup';

import { useRefData } from '@aventur-shared/stores';
import { ClientId } from '@aventur-shared/modules/clients';
import { PaymentDirectionType } from '@aventur-shared/modules/accounts';
import { validationMessages } from '@aventur-shared/utils/form';
import {
  correctAccountNumber,
  invalidCharactersRegex,
} from '@aventur-shared/utils/form/fields';
import {
  Account,
  Asset,
  CompanyShares,
  CryptoCurrency,
  DefinedBenefitPension,
  OtherAsset,
  Property,
} from '@aventur-shared/modules/factfind/types/Asset';

import { useAssetIdentity } from './use-asset-identity';
//

const baseSchema = object({
  clientIds: array().of(number().required()).required(),
  groupId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .required(validationMessages.fieldRequiredMessage),
  typeId: number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .required(validationMessages.fieldRequiredMessage),
});

const baseAccountSchema = baseSchema.concat(
  object({
    providerId: number()
      .nullable()
      .transform((value) => (isNaN(value) ? null : value))
      .required(validationMessages.fieldRequiredMessage),
    accountNumber: correctAccountNumber
      .nullable()
      .required(validationMessages.fieldRequiredMessage),
    subAccountNumber: correctAccountNumber.default(''),
  }),
);

const accountSchema = baseAccountSchema.concat(
  object({
    monthlyPaymentDirection: mixed<PaymentDirectionType>().defined().nullable(),
    monthlyPaymentAmount: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(100000, 'Value must be lower than 100.000'),
      }),
    riskLevel: number().defined().nullable(),
  }),
);

const termPolicySchema = baseAccountSchema.concat(
  object({
    coverAmount: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () => number().min(0, 'Value must be higher or equal to 0'),
      }),
    policyEndDate: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .test('future-date', validationMessages.futureDateMessage, (value) => {
        if (typeof value === 'string') {
          const date = new Date(value);
          return !isNaN(date.getTime()) && date > new Date();
        }
        return false;
      }),
  }),
);

const indemnityPolicySchema = baseAccountSchema.concat(
  object({
    policyEndDate: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .test('future-date', validationMessages.futureDateMessage, (value) => {
        if (typeof value === 'string') {
          const date = new Date(value);
          return !isNaN(date.getTime()) && date > new Date();
        }
        return false;
      }),
  }),
);

const wholeOfLifePolicySchema = baseAccountSchema.concat(
  object({
    coverAmount: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () => number().min(0, 'Value must be higher or equal to 0'),
      }),
  }),
);

const incomeProtectionPolicySchema = baseAccountSchema.concat(
  object({
    monthlyBenefit: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () => number().min(0, 'Value must be higher or equal to 0'),
      }),
    deferredWeeks: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () => number().min(0, 'Value must be higher or equal to 0'),
      }),
    policyEndDate: mixed()
      .required(validationMessages.fieldRequiredMessage)
      .test('future-date', validationMessages.futureDateMessage, (value) => {
        if (typeof value === 'string') {
          const date = new Date(value);
          return !isNaN(date.getTime()) && date > new Date();
        }
        return false;
      }),
  }),
);

const definedBenefitPensionSchema = baseAccountSchema.concat(
  object({
    indexLinked: boolean().required(validationMessages.fieldRequiredMessage),
    survivorBenefits: boolean().required(
      validationMessages.fieldRequiredMessage,
    ),
    isCurrentJob: boolean().required(validationMessages.fieldRequiredMessage),
    estimatedAnnualIncomeAtRetirement: mixed().when('isCurrentJob', {
      is: true,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
    schemeNormalRetirementAge: mixed().when('isCurrentJob', {
      is: true,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
    accrualRate: mixed().when('isCurrentJob', {
      is: false,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
    predictedFinalSalary: mixed().when('isCurrentJob', {
      is: false,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
    predictedYearsOfServiceAtRetirement: mixed().when('isCurrentJob', {
      is: false,
      then: () =>
        mixed()
          .required(validationMessages.fieldRequiredMessage)
          .checkIsNumber(validationMessages.invalidNumberMessage)
          .when({
            is: (value: string) => value,
            then: () => number().min(0, 'Value must be higher or equal to 0'),
          }),
      otherwise: () => mixed().nullable(),
    }),
  }),
);

const propertySchema = baseSchema.concat(
  object({
    addressLineOne: string().matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: validationMessages.invalidCharacterMessage,
    }),
    addressLineTwo: string().matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: validationMessages.invalidCharacterMessage,
    }),
    city: string().matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: validationMessages.invalidCharacterMessage,
    }),
    postCode: string().matches(invalidCharactersRegex, {
      excludeEmptyString: true,
      message: validationMessages.invalidCharacterMessage,
    }),
    countryId: number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required(validationMessages.fieldRequiredMessage),
    owner: string().transform(($n, $o) => $o ?? ''),
  }),
);

const companySharesSchema = baseSchema.concat(
  object({
    nameOfCompany: string().required(validationMessages.fieldRequiredMessage),
  }),
);

const cryptoCurrencySchema = baseSchema.concat(
  object({
    nameOfCurrency: string().required(validationMessages.fieldRequiredMessage),
    numberOfCoins: mixed()
      .nullable()
      .checkIsNumber(validationMessages.invalidNumberMessage)
      .when({
        is: (value: string) => value,
        then: () =>
          number()
            .min(0, 'Value must be higher or equal to 0')
            .max(1000000, 'Value must be lower than 1.000.000'),
      }),
  }),
);

const otherAssetSchema = baseSchema.concat(
  object({
    nameOfAsset: string().required(validationMessages.fieldRequiredMessage),
  }),
);

abstract class BaseFormModel<T extends Asset = Asset> {
  protected clientIds: ClientId[];
  protected groupId: number | null;
  protected typeId: number | null;
  protected asset?: T;

  constructor(
    clientIds: ClientId[],
    groupId: number | null,
    typeId: number | null,
    asset?: T,
  ) {
    this.clientIds = clientIds;
    this.groupId = groupId;
    this.typeId = typeId;
    this.asset = asset;
  }

  abstract getValidationSchema(): ObjectSchema<any>;

  getInitialValues(): Partial<T> {
    if (this.asset) return this.asset;
    return {
      id: null,
      clientIds: this.clientIds,
      groupId: this.groupId,
      typeId: this.typeId,
      valuation: null,
    } as Partial<T>;
  }
}

class AccountFormModel extends BaseFormModel<Account> {
  getValidationSchema() {
    return accountSchema;
  }

  getInitialValues(): Partial<Account> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      accountNumber: undefined,
      providerId: undefined,
      subAccountNumber: '',
    };
  }
}

class TermPolicyFormModel extends BaseFormModel<Account> {
  getValidationSchema() {
    return termPolicySchema;
  }

  getInitialValues(): Partial<Account> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      accountNumber: undefined,
      providerId: undefined,
      subAccountNumber: '',
      coverAmount: undefined,
      policyEndDate: undefined,
    };
  }
}

class IndemnityPolicyFormModel extends BaseFormModel<Account> {
  getValidationSchema() {
    return indemnityPolicySchema;
  }

  getInitialValues(): Partial<Account> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      accountNumber: undefined,
      providerId: undefined,
      subAccountNumber: '',
      policyEndDate: undefined,
    };
  }
}

class WholeOfLifePolicyFormModel extends BaseFormModel<Account> {
  getValidationSchema() {
    return wholeOfLifePolicySchema;
  }

  getInitialValues(): Partial<Account> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      accountNumber: undefined,
      providerId: undefined,
      subAccountNumber: '',
      coverAmount: undefined,
    };
  }
}

class IncomeProtectionPolicyFormModel extends BaseFormModel<Account> {
  getValidationSchema() {
    return incomeProtectionPolicySchema;
  }

  getInitialValues(): Partial<Account> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      accountNumber: undefined,
      providerId: undefined,
      subAccountNumber: '',
      monthlyBenefit: undefined,
      deferredWeeks: undefined,
      policyEndDate: undefined,
    };
  }
}

class DefinedBenefitPensionFormModel extends BaseFormModel<DefinedBenefitPension> {
  getValidationSchema() {
    return definedBenefitPensionSchema;
  }

  getInitialValues(): Partial<DefinedBenefitPension> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      accountNumber: undefined,
      providerId: undefined,
      subAccountNumber: '',
      isCurrentJob: false,
      indexLinked: false,
      survivorBenefits: false,
      estimatedAnnualIncomeAtRetirement: undefined,
      schemeNormalRetirementAge: undefined,
      accrualRate: undefined,
      predictedFinalSalary: undefined,
      predictedYearsOfServiceAtRetirement: undefined,
    };
  }
}

class PropertyFormModel extends BaseFormModel<Property> {
  getValidationSchema() {
    return propertySchema;
  }

  getInitialValues(): Partial<Property> {
    const { getCountryByCode } = useRefData();
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      addressLineOne: '',
      addressLineTwo: '',
      city: '',
      countryId: getCountryByCode('GBR')?.id,
      postCode: '',
      owner: '',
    };
  }
}

class CompanySharesFormModel extends BaseFormModel<CompanyShares> {
  getValidationSchema() {
    return companySharesSchema;
  }

  getInitialValues(): Partial<CompanyShares> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      nameOfCompany: '',
    };
  }
}

class CryptoCurrencyFormModel extends BaseFormModel<CryptoCurrency> {
  getValidationSchema() {
    return cryptoCurrencySchema;
  }

  getInitialValues(): Partial<CryptoCurrency> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      nameOfCurrency: '',
      numberOfCoins: null,
    };
  }
}

class OtherAssetFormModel extends BaseFormModel<OtherAsset> {
  getValidationSchema() {
    return otherAssetSchema;
  }

  getInitialValues(): Partial<OtherAsset> {
    if (this.asset) return this.asset;
    return {
      ...super.getInitialValues(),
      nameOfAsset: '',
    };
  }
}

class InitialFormModel extends BaseFormModel {
  constructor(clientIds: ClientId[], groupId?: number, typeId?: number) {
    super(clientIds, groupId ?? null, typeId ?? null);
  }

  getValidationSchema() {
    return baseSchema;
  }
}

export class AssetFormModelFactory {
  static create(
    clientIds: ClientId[],
    groupId?: Asset['groupId'],
    typeId?: Asset['typeId'],
    asset?: Asset,
  ) {
    const {
      isAccountType,
      isPropertyType,
      isCompanySharesType,
      isCryptoCurrencyType,
      isOtherAssetType,
      isTermPolicyType,
      isIndemnityPolicyType,
      isWholeOfLifePolicyType,
      isIncomeProtectionPolicyType,
      isDefinedBenefitPensionType,
    } = useAssetIdentity();

    if (groupId && typeId) {
      if (isAccountType(typeId))
        return new AccountFormModel(
          clientIds,
          groupId,
          typeId,
          asset as Account,
        );
      if (isPropertyType(typeId))
        return new PropertyFormModel(
          clientIds,
          groupId,
          typeId,
          asset as Property,
        );
      if (isCompanySharesType(typeId))
        return new CompanySharesFormModel(
          clientIds,
          groupId,
          typeId,
          asset as CompanyShares,
        );
      if (isCryptoCurrencyType(typeId))
        return new CryptoCurrencyFormModel(
          clientIds,
          groupId,
          typeId,
          asset as CryptoCurrency,
        );
      if (isOtherAssetType(typeId))
        return new OtherAssetFormModel(
          clientIds,
          groupId,
          typeId,
          asset as OtherAsset,
        );
      if (isTermPolicyType(typeId))
        return new TermPolicyFormModel(
          clientIds,
          groupId,
          typeId,
          asset as Account,
        );
      if (isIndemnityPolicyType(typeId))
        return new IndemnityPolicyFormModel(
          clientIds,
          groupId,
          typeId,
          asset as Account,
        );
      if (isWholeOfLifePolicyType(typeId))
        return new WholeOfLifePolicyFormModel(
          clientIds,
          groupId,
          typeId,
          asset as Account,
        );
      if (isIncomeProtectionPolicyType(typeId))
        return new IncomeProtectionPolicyFormModel(
          clientIds,
          groupId,
          typeId,
          asset as Account,
        );
      if (isDefinedBenefitPensionType(typeId))
        return new DefinedBenefitPensionFormModel(
          clientIds,
          groupId,
          typeId,
          asset as DefinedBenefitPension,
        );
    }

    return new InitialFormModel(clientIds, groupId, typeId);
  }
}
