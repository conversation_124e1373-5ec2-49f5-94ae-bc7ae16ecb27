<template>
  <box-section title="Your Assets" no-padding data-testid="assets"
    ><div class="flex items-center justify-between">
      <span class="text-sm text-gray-500"
        >Property, investments & other assets</span
      >
      <toggle
        label="Include Inactive"
        :model-value="includeInactive"
        :label-class="['!text-gray-500']"
        :disabled="disableToggleInactive"
        @update:model-value="(value) => (includeInactive = value)"
      >
        <template #label="{ label }">
          <div class="flex flex-col items-start justify-center -space-y-1">
            <span>{{ label }}</span>
            <span class="text-xs text-gray-500"
              >Showing {{ items.length }} of {{ assetList.length }}</span
            >
          </div>
        </template>
      </toggle>
    </div>
  </box-section>
  <box-section no-padding-x>
    <assets-list
      v-if="items.length"
      :is-action-disabled="isActionDisabled"
      :assets="items"
      @on-remove="(asset) => handleRemoveAsset(asset)"
      @on-edit="(asset) => handleEditAssetRequest(asset)"
      @on-add-valuation="(asset) => handleAddValuation(asset)"
    />
    <dashed-border-button
      class="mt-4"
      :disabled="isActionDisabled"
      @on-click="modalApis?.addAsset?.open"
      >+ Add asset
    </dashed-border-button>
    <modal
      :config="{
        outsideClickClose: false,
        initialFocus: initialFocusRef,
      }"
      @use-api="(api) => (modalApis.addAsset = api)"
    >
      <template #default="{ close }">
        <box>
          <box-section
            title="Add Asset"
            divider="bottom"
            class="flex justify-between"
          />
          <box-section>
            <asset-form
              type="ADD"
              :client="client"
              @on-submit="(...args) => handleAddAsset(...args)"
              @on-load="handleAssetFormLoad"
              @on-cancel="close"
            />
          </box-section>
        </box>
      </template>
    </modal>
    <modal
      :config="{
        outsideClickClose: false,
        initialFocus: initialFocusRef,
      }"
      @use-api="
        (api) => {
          modalApis.editAsset = api;
        }
      "
    >
      <template #default="{ close }">
        <box>
          <box-section
            title="Edit Asset"
            divider="bottom"
            class="flex justify-between"
          />
          <box-section>
            <asset-form
              type="EDIT"
              :client="client"
              :asset="getAssetToEdit()"
              @on-submit="(asset) => handleEditAsset(asset as AssetListItem)"
              @on-load="handleAssetFormLoad"
              @on-cancel="close"
            />
          </box-section>
        </box>
      </template>
    </modal>
    <valuation-modal
      @on-load="({ modalApi }) => (modalApis.addValuation = modalApi)"
    >
      <add-valuation
        :last-valuation-info="unref(valuationProcess.lastValuation)"
        @on-add="handleOnValuationAdd"
        @on-load="handleAddValuationLoad"
        @on-close="handleCloseAddingValuation"
      />
    </valuation-modal>
  </box-section>
</template>
<script setup lang="ts">
  import { Ref, ref, unref } from 'vue';
  import Toggle from '@aventur-shared/components/Toggle.vue';
  import { Client } from '@aventur-shared/modules/clients';
  import { useValuationProcess } from '@aventur-shared/composables/useValuation';
  import { useAssetsDebtsFiltering } from '@aventur-shared/modules/factfind/composables';
  import { Asset } from '@aventur-shared/modules/factfind';

  import { Box, BoxSection, Modal, ModalAPI } from '@modules/ui';
  import { DashedBorderButton } from '@modules/factfind/ui';
  import { type AssetListItem } from '@modules/factfind/models/asset';
  import { AddValuation, ValuationModal } from '@modules/factfind/ui/valuation';
  import { default as AssetsList } from './list.vue';
  import { default as AssetForm } from './form/form.vue';

  const emit = defineEmits<{
    (e: 'on-asset-add', asset: Asset): void;
    (e: 'on-asset-edit', asset: AssetListItem): void;
    (e: 'on-asset-remove', asset: AssetListItem): void;
  }>();

  const props = defineProps<{
    client: Client;
    assetList: AssetListItem[];
    isActionDisabled: boolean;
  }>();

  const initialFocusRef = ref<HTMLElement | null>(null);
  const modalApis = ref<{
    editAsset: ModalAPI | undefined;
    addAsset: ModalAPI | undefined;
    addValuation: ModalAPI | undefined;
  }>({
    editAsset: undefined,
    addAsset: undefined,
    addValuation: undefined,
  });
  const editedAssetKey = ref<string | null>(null);
  const valuationProcess = useValuationProcess<AssetListItem>({
    onAddNew: (item) => {
      emit('on-asset-add', item);
    },
    onAddToExisting: (item) => {
      emit('on-asset-edit', item);
    },
  });

  const { items, includeInactive, disableToggleInactive } =
    useAssetsDebtsFiltering(
      () => props.assetList,
      (item: AssetListItem) => item.hasQuantity || !item.id,
    );

  const handleAssetFormLoad = ({
    focusRef,
  }: {
    focusRef: Ref<HTMLElement | null>;
  }) => {
    initialFocusRef.value = focusRef.value;
  };

  function handleAddValuationLoad({
    focusRef,
  }: {
    focusRef: Ref<HTMLElement | null>;
  }) {
    initialFocusRef.value = focusRef.value;
  }

  const getAssetToEdit = () =>
    props.assetList.find((item) => item.key === editedAssetKey.value);

  const handleAddAsset = (asset: Asset, shouldAddValuation: boolean) => {
    modalApis.value?.addAsset?.close();

    if (shouldAddValuation) {
      valuationProcess
        .addingValuationToNewElement({ ...asset, key: '' })
        .then(modalApis.value?.addValuation?.open);
    } else {
      emit('on-asset-add', asset);
    }
  };

  const handleRemoveAsset = (asset: AssetListItem) => {
    emit('on-asset-remove', asset);
  };

  const handleEditAssetRequest = (asset: AssetListItem) => {
    editedAssetKey.value = asset.key;
    modalApis.value?.editAsset?.open();
  };

  const handleEditAsset = (asset: AssetListItem) => {
    emit('on-asset-edit', asset);
    editedAssetKey.value = null;
    modalApis.value?.editAsset?.close();
  };

  function handleAddValuation(asset: AssetListItem) {
    valuationProcess
      .addingValuationToExistingItem(asset)
      .then(modalApis.value?.addValuation?.open);
  }

  function handleCloseAddingValuation() {
    valuationProcess.cancel().then(modalApis.value?.addValuation?.close);
  }

  function handleOnValuationAdd(valuation: Asset['valuation']) {
    valuationProcess
      .addValuation(valuation)
      .then(modalApis.value.addValuation?.close);
  }
</script>
