import { moveElement } from '@aventur-shared/utils/list';
import { formatName } from '@aventur-shared/utils/user';
import { useUserStore } from '@aventur-shared/modules/users';
import { useClientStore } from '@aventur-shared/modules/clients';
import { extractClientsFromClientProfile } from '@aventur-shared/modules/clients/models';
import { useActiveClients, useRefData } from '@aventur-shared/stores';
import {
  Account,
  Asset,
  CompanyShares,
  CryptoCurrency,
  DefinedBenefitPension,
  OtherAsset,
  Property,
} from '@aventur-shared/modules/factfind/types/Asset';

import {
  AssetTypeGuard,
  formatPropertyAssetAddress,
} from '@modules/factfind/models/asset';

export const ownerValueLabelMap = new Map<Property['owner'], string>([
  ['joint', 'Joint'],
  ['sole', 'Sole'],
  ['tenants_in_common', 'Tenants in common'],
]);

const getClientsFormatted = (clientIds: Asset['clientIds']) => {
  const { isClient } = useUserStore();
  const { getClientId, getProfile } = useClientStore();
  const { list: activeClients } = useActiveClients();
  const list = !isClient
    ? activeClients
    : extractClientsFromClientProfile(getProfile);

  const orderedClientIds = moveElement(
    clientIds,
    clientIds.indexOf(getClientId),
    0,
  );

  return orderedClientIds
    .map((id) => {
      const client = list.find((client) => client.id === id);
      if (!client) return '';
      return formatName(client);
    })
    .join(', ');
};

export class AssetDescriptionBuilder {
  static build(asset: Asset) {
    if (
      AssetTypeGuard.isAccount(asset) ||
      AssetTypeGuard.isProtectionProduct(asset) ||
      AssetTypeGuard.isDefinedBenefitPension(asset)
    ) {
      return this.buildAccountDescription(asset);
    }
    if (AssetTypeGuard.isProperty(asset)) {
      return this.buildPropertyDescription(asset);
    }
    if (AssetTypeGuard.isCompanyShares(asset)) {
      return this.buildCompanySharesDescription(asset);
    }
    if (AssetTypeGuard.isCryptoCurrency(asset)) {
      return this.buildCryptoCurrencyDescription(asset);
    }
    if (AssetTypeGuard.isOtherAsset(asset)) {
      return this.buildOtherAssetDescription(asset);
    }
    return '-';
  }

  static buildAccountDescription(asset: Account | DefinedBenefitPension) {
    const { getProviderByID } = useRefData();

    return [
      ['Clients', getClientsFormatted(asset.clientIds)],
      ['Provider', getProviderByID(asset.providerId).name],
      ['Account no', asset.accountNumber],
      ['Sub account no', asset.subAccountNumber],
    ];
  }

  static buildPropertyDescription(asset: Property) {
    const { getCountryById } = useRefData();
    return [
      ['Clients', getClientsFormatted(asset.clientIds)],
      ['Address', formatPropertyAssetAddress(asset)],
      ['Country', asset.countryId ? getCountryById(asset.countryId)?.name : ''],
      ['Ownership', ownerValueLabelMap.get(asset.owner) ?? ''],
    ];
  }

  static buildCompanySharesDescription(asset: CompanyShares) {
    return [
      ['Clients', getClientsFormatted(asset.clientIds)],
      ['Company name', asset.nameOfCompany],
    ];
  }

  static buildCryptoCurrencyDescription(asset: CryptoCurrency) {
    return [
      ['Clients', getClientsFormatted(asset.clientIds)],
      ['Currency', asset.nameOfCurrency],
      ['Number of coins', asset.numberOfCoins ?? ''],
    ];
  }

  static buildOtherAssetDescription(asset: OtherAsset) {
    return [['Clients', getClientsFormatted(asset.clientIds)]];
  }
}
