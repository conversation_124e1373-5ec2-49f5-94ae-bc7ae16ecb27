<template>
  <Switch
    label=""
    :[`data-client-${clientId}-subscription-${subscription.id}`]="true"
    :disabled="subscription.compulsory"
    v-model="value"
    @update:model-value="$emit('on-update', value, subscription)"
    class="cursor-pointer bg-gray-400 disabled:cursor-not-allowed disabled:opacity-60"
    :class="[
      subscription.status == SubscriptionStatus.Active
        ? 'bg-primary'
        : 'bg-gray-300',
      'focus:ring-primary relative inline-flex h-6 w-11 shrink-0 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2',
    ]"
  >
    <span
      aria-hidden="true"
      :class="[
        subscription.status == SubscriptionStatus.Active
          ? 'translate-x-5'
          : 'translate-x-0',
        subscription.compulsory ? 'bg-gray-100' : 'bg-white',
        'pointer-events-none inline-block size-5 rounded-full shadow ring-0 transition duration-200 ease-in-out',
      ]"
    />
  </Switch>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Switch } from '@headlessui/vue';

  import { ClientId } from '@aventur-shared/modules/clients';
  import {
    type ContactSubscription,
    SubscriptionStatus,
  } from '@aventur-shared/modules/factfind';
  //

  const { subscription, clientId } = defineProps<{
    clientId: ClientId;
    subscription: ContactSubscription;
  }>();

  const value = ref(subscription.status == SubscriptionStatus.Active);
</script>
