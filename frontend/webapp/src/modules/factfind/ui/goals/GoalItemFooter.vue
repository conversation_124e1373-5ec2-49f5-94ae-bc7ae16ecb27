<template>
  <div
    data-testid="goal-item-footer"
    class="flex flex-col items-start justify-start pb-3 text-xs text-gray-400 lg:flex-row lg:gap-4"
  >
    <p>
      Clients:&nbsp;<span class="text-primary">{{
        goal.clientIds.length
      }}</span>
    </p>
    <p>
      Open cases:&nbsp;<span class="text-primary">{{ goal.cases.length }}</span>
    </p>
    <p>
      Linked assets:&nbsp;<span class="text-primary">{{
        goal.linkedHoldings.length
      }}</span>
    </p>
    <p>
      Risk profile:&nbsp;<span class="text-primary">{{
        goal.riskProfile ? '&#x2713;' : '-'
      }}</span>
    </p>
    <p>
      Cash forecast:&nbsp;<span class="text-primary">{{
        goal.cashForecast ? '&#x2713;' : '-'
      }}</span>
    </p>
  </div>
</template>

<script setup lang="ts">
  import { ArrayElement } from '@aventur-shared/types/Common';

  import { FormValues } from '@modules/factfind/ui/goals/form-model';

  defineProps<{
    goal: ArrayElement<FormValues['defaultGoals']>;
  }>();
</script>
