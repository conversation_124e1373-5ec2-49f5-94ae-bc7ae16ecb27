<template>
  <Search
    v-model:search-value="searchValue"
    name="client-holdings-list"
    :options="accountOptions"
    placeholder="Search assets to link to the goal"
    @select-option="($event) => handleSelection($event)"
  >
    <template #top>
      <span class="text-secondary text-sm font-semibold"
        >Available Assets ({{ accountOptions.length }})</span
      >
    </template>
    <template #option="{ option }">
      <div class="flex items-center px-4 py-1 hover:bg-gray-200">
        <clean-checkbox-input
          name="select-account"
          :model-value="(option as any as GoalLinkedHolding).id"
          :checked="
            selectedHoldings.has(Number((option as GoalLinkedHolding).id))
          "
          @click.stop
          @change="handleSelection"
        />
        <div class="ml-4">
          <div class="flex flex-wrap items-center">
            <span>{{ (option as any as GoalLinkedHolding).providerName }}</span>
            <span class="mx-2">-</span>
            <span>{{
              (option as any as GoalLinkedHolding).productTypeName
            }}</span>
          </div>
          <div class="flex flex-wrap items-center text-sm text-gray-500">
            <span>{{
              (option as any as GoalLinkedHolding).accountNumber
            }}</span>
          </div>
        </div>
      </div>
    </template>
    <template #bottom="{ actions }">
      <div class="flex gap-2">
        <custom-button
          theme="primary"
          class="w-full"
          data-testid="link-button"
          :disabled="selectedHoldings.size === 0"
          @click="
            () => {
              handleLinkHoldings().then(actions.close);
            }
          "
          ><template v-if="selectedHoldings.size"
            >Link {{ selectedHoldings.size }} selected</template
          ><template v-else
            >Please select asset(s) to add</template
          ></custom-button
        >
        <custom-button
          theme="primary-ghost"
          @click="() => cancelAddingAccounts().then(actions.close)"
          >Cancel</custom-button
        >
      </div>
    </template>
  </Search>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';

  import { GoalLinkedHolding } from '@aventur-shared/modules/clients/models';
  import { CleanCheckboxInput } from '@aventur-shared/components/form/fields/clean-fields';

  import { Button as CustomButton, Search } from '@modules/ui';
  //

  const { holdings } = defineProps<{
    holdings: GoalLinkedHolding[];
  }>();

  const emit = defineEmits(['holdingsSelected']);

  const searchValue = ref('');
  const selectedHoldings = ref(new Set<number>());

  const accountOptions = computed(() => getAssetOptions(holdings));

  const handleSelection = (event: Event) => {
    const holdingId = (event.target as HTMLInputElement).id;
    if (selectedHoldings.value.has(Number(holdingId))) {
      selectedHoldings.value.delete(Number(holdingId));
    } else {
      selectedHoldings.value.add(Number(holdingId));
    }
  };

  const handleLinkHoldings = async () => {
    const holdingIds = [...selectedHoldings.value.values()];
    selectedHoldings.value = new Set<number>();
    emit('holdingsSelected', holdingIds);
  };

  const cancelAddingAccounts = async () => {
    selectedHoldings.value = new Set<number>();
  };

  const getAssetOptions = (
    holdings: GoalLinkedHolding[],
  ): GoalLinkedHolding[] => {
    return searchOptions(holdings)(searchValue.value);
  };

  const searchOptions =
    (options: GoalLinkedHolding[]) => (searchValue: string) => {
      return options.filter((opt) => {
        return [
          opt.accountNumber.toLowerCase(),
          opt.providerName.toLowerCase(),
          opt.productTypeName.toLowerCase(),
        ].some((haystack) => haystack?.includes(searchValue.toLowerCase()));
      });
    };
</script>
