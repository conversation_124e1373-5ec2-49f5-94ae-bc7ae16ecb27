<template>
  <Box class="flex flex-col justify-start !bg-gray-50">
    <div class="flex items-center justify-start p-2">
      <GoalItemHeader :goal="goal" />
      <div class="flex grow"></div>
      <GoalItemMenu
        :goal="goal"
        :field-index="fieldIndex"
        class="mx-3 mt-1"
        @item-clicked="handleMenuClick"
      />
    </div>
    <BoxSection class="flex grow flex-col !px-5 !py-0">
      <GoalObjectives
        :key="goal.id"
        :client-id="client.id"
        :goal="goal"
        @focus="handleMenuClick('objectives')"
      />
      <GoalItemFooter :goal="goal" />
    </BoxSection>
    <GoalLinkedAssetsModal
      :goal="goal"
      :client="client"
      :holdings="activeHoldings"
      :pre-selected-holdings="
        new Set(goal.linkedHoldings.map((holding) => holding.id))
      "
      @goal-updated="handleGoalHoldingsUpdated"
    />
    <GoalRiskProfileModal :goal="goal" :client="client" />
    <GoalForecasterModal :goal="goal" :client="client" />
    <GoalObjectivesModal
      :key="goal.id"
      :goal="goal"
      :client="client"
      @goal-updated="handleGoalObjectivesUpdated"
    />
    <EditGoalModal
      :group="group"
      :client="client"
      :goal="goal"
      @on-edit="handleGoalUpdated"
    />
  </Box>
</template>

<script setup lang="ts">
  import { partial, zip } from 'lodash';
  import { inject, onUnmounted, watch } from 'vue';

  import { ArrayElement } from '@aventur-shared/types/Common';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { Client, ClientGoal } from '@aventur-shared/modules/clients/models';
  import { ClientId } from '@aventur-shared/modules/clients';
  import { GoalId } from '@aventur-shared/modules/goals';
  import { Goal } from '@aventur-shared/stores/refdataStore';
  import { deleteClientGoals } from '@aventur-shared/modules/factfind';

  import { Box, BoxSection } from '@modules/ui';
  import { useRiskProfileStore } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import { useForecasterStore } from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan';
  import { FormValues } from '@modules/factfind/ui/goals/form-model';
  import GoalLinkedAssetsModal from './modals/goal-linked-assets-modal.vue';
  import GoalRiskProfileModal from './modals/goal-risk-profile-modal.vue';
  import GoalForecasterModal from './modals/goal-forecaster-modal.vue';
  import GoalObjectivesModal from './modals/goal-objectives-modal.vue';
  import EditGoalModal from './modals/EditGoalModal.vue';
  import GoalObjectives from './GoalObjectives.vue';
  import GoalItemMenu from './GoalItemMenu.vue';
  import GoalItemHeader from './GoalItemHeader.vue';
  import GoalItemFooter from './GoalItemFooter.vue';

  const toast = useToast();
  const { saveRiskProfile, resetRiskProfile } = useRiskProfileStore();
  const { saveForecasterData, resetForecasterData } = useForecasterStore();

  const group = inject('group') as Goal;

  const props = defineProps<{
    client: Client;
    fieldIndex: number;
    goal: ArrayElement<FormValues['defaultGoals']>;
    activeHoldings: ClientGoal['linkedHoldings'];
  }>();

  const emit = defineEmits<{
    (e: 'goalUpdated', goalId: GoalId): void;
    (e: 'goalDeleted', goalId: GoalId): void;
    (e: 'holdingsUpdated', goalId: GoalId, holdingIds: number[]): void;
    (e: 'objectivesUpdated', goalId: GoalId, objectives: string): void;
    // TODO: Remove - probably never fired
    (
      e: 'riskProfileUpdated',
      goalId: GoalId,
      data: ClientGoal['riskProfile'],
    ): void;
    // TODO: Remove - probably never fired
    (
      e: 'cashForecastUpdated',
      goalId: GoalId,
      data: ClientGoal['cashForecast'],
    ): void;
  }>();

  const handleGoalHoldingsUpdated = async (
    goalId: GoalId,
    holdingIds: number[],
  ) => {
    try {
      emit('holdingsUpdated', goalId, holdingIds);
    } catch {
      toast.error(`Couldn't update goal holdings.`);
    }
  };
  const handleGoalObjectivesUpdated = async (
    goalId: GoalId,
    objectives: string,
  ) => {
    try {
      emit('objectivesUpdated', goalId, objectives);
    } catch {
      toast.error(`Couldn't update goal objectives.`);
    }
  };

  const handleMenuClick = (
    e:
      | 'link-assets'
      | 'objectives'
      | 'risk-profile'
      | 'forecaster'
      | 'delete'
      | 'edit',
  ) => {
    switch (e) {
      case 'edit':
        (
          document.getElementById(
            `openEditModal:${props.client.id}:${props.goal.id}`,
          ) as HTMLElement
        ).click();
        break;
      case 'link-assets':
        (
          document.getElementById(
            `openLinkAssetsModal:${props.client.id}:${props.goal.id}`,
          ) as HTMLElement
        ).click();
        break;
      case 'objectives':
        (
          document.getElementById(
            `openObjectivesModal:${props.client.id}:${props.goal.id}`,
          ) as HTMLElement
        ).click();
        break;
      case 'risk-profile':
        (
          document.getElementById(
            `openRiskProfileModal:${props.client.id}:${props.goal.id}`,
          ) as HTMLElement
        ).click();
        break;
      case 'forecaster':
        (
          document.getElementById(
            `openForecasterModal:${props.client.id}:${props.goal.id}`,
          ) as HTMLElement
        ).click();
        break;
      case 'delete':
        handleGoalDeleted(props.client.id, props.goal.id);
        break;
    }
  };

  const handleGoalUpdated = async (goalId: GoalId) => {
    try {
      emit('goalUpdated', goalId);
    } catch (e) {
      toast.error(e as Error);
    }
  };

  const handleGoalDeleted = async (clientId: ClientId, goalId: GoalId) => {
    try {
      await deleteClientGoals(clientId, goalId);
      emit('goalDeleted', goalId);
    } catch (e) {
      toast.error(e as Error);
    }
  };

  watch(
    [() => props.goal.riskProfile, () => props.goal.cashForecast],
    (goal) => {
      const [riskProfile, cashForecast] = goal;
      zip(
        [
          partial(
            riskProfile ? saveRiskProfile : resetRiskProfile,
            props.goal.id,
            props.client.id,
          ),
          partial(
            cashForecast ? saveForecasterData : resetForecasterData,
            props.goal.id,
            props.client.id,
          ),
        ],
        [() => riskProfile, () => cashForecast],
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
      ).forEach(([func, data], ..._) => func!(data!()));
    },
    { immediate: true },
  );

  onUnmounted(() => {
    resetRiskProfile(props.goal.goalTypeId, props.client.id);
    resetForecasterData(props.goal.goalTypeId, props.client.id);
  });
</script>
