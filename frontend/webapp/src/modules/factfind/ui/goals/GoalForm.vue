<template>
  <form @submit.prevent>
    <fieldset>
      <SelectField
        v-if="mode === 'create'"
        label="Goal type"
        name="goalTypeId"
        :options="goalTypes"
        :searchable="true"
        disabled
        @on-select="handleTypeChange"
      />
      <TextField
        label="Goal name"
        name="goalName"
        ref="goalNameField"
        :hint="fieldsHints.goalName"
      />
      <MultiSelectField
        label="Clients"
        name="clientIds"
        :options="linkedClients"
        :hint="fieldsHints.clientIds"
        @on-select="handleClientSelect"
      />
      <TextField
        name="attributes.targetAmount"
        :label="fieldsLabels['targetAmount']"
        :hint="fieldsHints.targetAmount"
      />
      <DatePicker
        name="attributes.targetDate"
        :label="fieldsLabels['targetDate']"
        :hint="fieldsHints.targetDate"
      />
    </fieldset>
    <div class="flex flex-col items-center justify-between gap-y-2">
      <custom-button
        ref="submitButtonRef"
        type="submit"
        theme="primary"
        class="w-full"
        @click="emit('on-submit')"
        >{{ mode === 'create' ? 'Create' : 'Save' }}
      </custom-button>
      <custom-button
        theme="gray-ghost"
        class="w-full"
        @click="emit('on-cancel')"
        >Cancel & close
      </custom-button>
    </div>
  </form>
</template>

<script setup lang="ts">
  import { computed, onBeforeMount } from 'vue';
  import { useFormValues, useResetForm } from 'vee-validate';

  import {
    CheckboxField,
    DatePicker,
    MultiSelectField,
    SelectField,
    TextField,
  } from '@aventur-shared/components/form';
  import { Client } from '@aventur-shared/modules/clients';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';
  import { Goal, useRefData } from '@aventur-shared/stores/refdataStore';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';

  import { Button as CustomButton } from '@modules/ui';
  import { useGoalForm } from '../goals/use-goal-form';

  const { getGoals } = useRefData();

  const goalTypes = getGoals.map((goal: any) => ({
    label: goal.name,
    value: goal.id,
  }));

  const emit = defineEmits<{
    (e: 'on-submit'): void;
    (e: 'on-cancel'): void;
  }>();

  const props = defineProps<{
    group: Goal;
    client: Client;
    goal?: ClientGoal;
  }>();

  const mode = computed<'edit' | 'create'>(() =>
    props.goal ? 'edit' : 'create',
  );

  const values = useFormValues();
  const resetForm = useResetForm();

  const { fieldsLabels, fieldsHints } = useGoalForm(props.group.id, props.goal);

  const { linkedClients } = useLinkedClientList(props.client);

  const handleTypeChange = () => {
    //
  };

  const handleClientSelect = () => {
    //
  };

  onBeforeMount(() => {
    resetForm({
      values: values.value,
    });
  });
</script>
