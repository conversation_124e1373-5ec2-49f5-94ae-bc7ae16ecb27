<template>
  <div class="flex flex-col">
    <div class="mt-1">
      <textarea
        :value="goal.objectives"
        rows="4"
        :name="`goal${goal.id}-objectives`"
        class="focus:ring-primary-600 block max-h-20 min-h-20 w-full overflow-y-scroll rounded-md border-0 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6"
        placeholder="Click to update the objectives"
        readonly
        @focus="$emit('focus')"
      />
      <div class="flex min-h-4 items-center justify-end text-xs text-gray-400">
        <Clipboard :source="goal.objectives" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ClientId } from '@aventur-shared/modules/clients';
  import { ArrayElement } from '@aventur-shared/types/Common';
  import Clipboard from '@aventur-shared/components/Clipboard.vue';
  import { FormValues } from '@modules/factfind/ui/goals/form-model';

  defineProps<{
    clientId: ClientId;
    goal: ArrayElement<FormValues['defaultGoals']>;
  }>();

  defineEmits<{
    (e: 'focus'): () => void;
  }>();
</script>
