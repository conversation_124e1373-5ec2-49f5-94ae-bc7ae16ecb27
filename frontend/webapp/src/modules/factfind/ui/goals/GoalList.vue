<template>
  <div class="flex flex-wrap gap-4">
    <GoalTypeGroup
      v-for="group in groups"
      :group-id="group.id"
      :goals="goals"
      :client="client"
      :key="group.id"
      @goal-created="emit('goalsUpdated')"
    >
      <template #default="{ items }">
        <div class="flex flex-col gap-4">
          <template v-for="(goal, fieldIdx) in items" :key="goal.id">
            <GoalItem
              :field-index="fieldIdx"
              :goal="goal"
              :client="client"
              :active-holdings="activeHoldings"
              @goal-updated="handleGoalUpdated"
              @goal-deleted="handleGoalDeleted"
              @holdings-updated="handleGoalHoldingsUpdated"
              @objectives-updated="handleGoalObjectivesUpdated"
              @risk-profile-updated="handleGoalRiskProfileUpdated"
              @cash-forecast-updated="handleGoalCashForecastUpdated"
            />
          </template>
        </div>
      </template>
    </GoalTypeGroup>
  </div>
</template>

<script setup lang="ts">
  import { type Ref, inject } from 'vue';
  import { useFieldArray } from 'vee-validate';

  import { ArrayElement } from '@aventur-shared/types/Common';
  import { Goal } from '@aventur-shared/stores/refdataStore';
  import { GoalId } from '@aventur-shared/modules/goals';
  import { Client } from '@aventur-shared/modules/clients';
  import {
    ClientGoal,
    GoalLinkedHolding,
  } from '@aventur-shared/modules/clients/models';

  import GoalTypeGroup from './GoalTypeGroup.vue';
  import GoalItem from './GoalItem.vue';
  import { FormValues } from './form-model';

  interface GoalHoldingsMap {
    [key: number]: GoalLinkedHolding;
  }

  const allHoldings = inject('allHoldings') as Ref<GoalHoldingsMap>;

  const emit = defineEmits<{
    (e: 'goalsUpdated'): void;
  }>();

  const props = defineProps<{
    groups: Goal[];
    client: Client;
    goals: FormValues['defaultGoals'];
    activeHoldings: GoalLinkedHolding[];
  }>();

  const { fields } = useFieldArray<ArrayElement<FormValues['defaultGoals']>>(
    `${props.client.id}.defaultGoals`,
  );

  const handleGoalDeleted = () => {
    emit('goalsUpdated');
  };

  const handleGoalUpdated = () => {
    emit('goalsUpdated');
  };

  const handleGoalHoldingsUpdated = (goalId: GoalId, holdingIds: number[]) => {
    fields.value.forEach((item) => {
      if (item.value.id === goalId) {
        item.value.linkedHoldings = holdingIds.map(
          (holdingId) => allHoldings.value[holdingId],
        );
      }
    });
    handleGoalUpdated();
  };

  const handleGoalObjectivesUpdated = (goalId: GoalId, objectives: string) => {
    fields.value.forEach((item) => {
      if (item.value.id === goalId) {
        item.value.objectives = objectives;
      }
    });
    handleGoalUpdated();
  };

  const handleGoalRiskProfileUpdated = (
    goalId: GoalId,
    riskProfile: ClientGoal['riskProfile'],
  ) => {
    fields.value.forEach((item) => {
      if (item.value.id === goalId) {
        item.value.riskProfile = riskProfile;
      }
    });
    // handleGoalUpdated();
  };

  const handleGoalCashForecastUpdated = (
    goalId: GoalId,
    cashForecast: ClientGoal['cashForecast'],
  ) => {
    fields.value.forEach((item) => {
      if (item.value.id === goalId) {
        item.value.cashForecast = cashForecast;
      }
    });
    // handleGoalUpdated();
  };
</script>
