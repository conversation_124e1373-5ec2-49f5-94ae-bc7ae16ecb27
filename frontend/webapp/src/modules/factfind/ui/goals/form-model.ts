import { array, mixed, number, object, string } from 'yup';

import { validationMessages } from '@aventur-shared/utils/form';
import { ClientId } from '@aventur-shared/modules/clients';
import { ClientGoal } from '@aventur-shared/modules/clients/models';
import { nonAlphaNumericRegex } from '@aventur-shared/utils/form/fields';
//

export interface FormValues {
  defaultGoals: ClientGoal[];
}

export const defaultAttributesValidationSchema = object({
  targetAmount: mixed()
    .checkIsNumber(validationMessages.invalidNumberMessage)
    .when({
      is: (value: string) => !isNaN(Number(value)),
      then: () =>
        number()
          .min(100, 'Value must be higher or equal to 100')
          .max(100_000_000, 'Value must be lower than 100.000.000'),
    })
    .required(validationMessages.fieldRequiredMessage),
  targetDate: string().required(validationMessages.fieldRequiredMessage),
});

export const defaultGoalValidationSchema = object({
  goalTypeId: number().required(validationMessages.fieldRequiredMessage),
  goalName: string()
    .max(50, validationMessages.maxLengthMessage('Field', 50))
    .matches(nonAlphaNumericRegex, {
      excludeEmptyString: true,
      message: validationMessages.alphaNumericMessage,
    })
    .required(validationMessages.fieldRequiredMessage),
  clientIds: array()
    .of(mixed<ClientId>().required())
    .required(validationMessages.fieldRequiredMessage),
});

export const validationSchema = object({
  defaultGoals: array().of(
    defaultGoalValidationSchema.concat(
      object({
        attributes: defaultAttributesValidationSchema.required(),
      }),
    ),
  ),
});

// Extend (.concat()) schemas to tune the requirements per goal
export const EmergencyFundAttributesValidationSchema =
  defaultAttributesValidationSchema;
export const PropertyOwnershipAttributesValidationSchema =
  defaultAttributesValidationSchema;
export const ProtectYourselfAndFamilyAttributesValidationSchema =
  defaultAttributesValidationSchema;
export const RetirementAttributesValidationSchema =
  defaultAttributesValidationSchema;
export const EstatePlanningAndWillsAttributesValidationSchema =
  defaultAttributesValidationSchema;
export const InvestForChildrenAttributesValidationSchema =
  defaultAttributesValidationSchema;
export const InvestForSchoolFeesAttributesValidationSchema =
  defaultAttributesValidationSchema;
export const TravelPlanningAttributesValidationSchema =
  defaultAttributesValidationSchema;
export const BuildWealthAttributesValidationSchema =
  defaultAttributesValidationSchema;
