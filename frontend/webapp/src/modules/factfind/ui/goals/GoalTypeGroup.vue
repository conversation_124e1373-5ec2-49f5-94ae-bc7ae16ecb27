<template>
  <div class="w-full">
    <div class="rounded-lg bg-white p-4">
      <Disclosure v-slot="{ open }">
        <DisclosureButton
          :data-group-id="groupId"
          class="flex w-full items-center justify-between rounded p-2 text-left text-sm font-medium"
          :class="
            hasGoals
              ? 'hover:bg-gray-50 focus:outline-none focus-visible:ring focus-visible:ring-gray-100'
              : null
          "
        >
          <div class="flex flex-col items-start p-2">
            <div class="flex items-center justify-start">
              <h1 class="text-base font-semibold text-gray-900">
                {{ group.name }}
              </h1>
              <div>
                <QuestionMarkCircleIcon
                  v-tippy="`${group.description}`"
                  class="ml-1 size-5 text-gray-600"
                  aria-hidden="true"
                />
              </div>
            </div>
            <div>
              <span
                :class="hasGoals ? 'text-primary-700' : 'text-gray-400'"
                data-group-total-items
              >
                {{ countText(totalItems, 'goal') }}
              </span>
            </div>
          </div>
          <ChevronUpIcon
            :class="open ? 'rotate-180 transform' : ''"
            class="size-5 text-gray-500"
          />
        </DisclosureButton>
        <DisclosurePanel class="px-4 pb-2 pt-4 text-sm text-gray-500">
          <slot :items="groupItems" :disabled="!totalItems" />
          <AddGoalModal
            :group="group"
            :client="client"
            @on-add="$emit('goalCreated')"
          />
        </DisclosurePanel>
      </Disclosure>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, provide } from 'vue';
  import {
    Disclosure,
    DisclosureButton,
    DisclosurePanel,
  } from '@headlessui/vue';
  import { ChevronUpIcon } from '@heroicons/vue/20/solid';
  import { QuestionMarkCircleIcon } from '@heroicons/vue/20/solid';

  import { Goal, useRefData } from '@aventur-shared/stores/refdataStore';
  import { Client } from '@aventur-shared/modules/clients';
  import { countText } from '@aventur-shared/utils/ui/count-text';
  import { FormValues } from '@modules/factfind/ui/goals/form-model';
  import AddGoalModal from '@modules/factfind/ui/goals/modals/AddGoalModal.vue';

  const props = defineProps<{
    groupId: Goal['id'];
    goals: FormValues['defaultGoals'];
    client: Client;
  }>();

  const { getGoalById } = useRefData();

  const group = computed<Goal>(() => getGoalById(props.groupId) as Goal);

  provide('group', group);

  const groupItems = computed<typeof props.goals>(() =>
    Object.values(props.goals).filter(
      (goal) => goal.goalTypeId === props.groupId,
    ),
  );

  const totalItems = computed<number>(() => groupItems.value.length);
  const hasGoals = computed<boolean>(() => totalItems.value > 0);
</script>
