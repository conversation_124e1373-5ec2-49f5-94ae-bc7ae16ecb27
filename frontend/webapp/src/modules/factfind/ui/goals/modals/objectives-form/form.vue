<template>
  <div class="flex flex-col items-center justify-start gap-y-6">
    <div v-if="goalObjectivesExamples.length" class="w-full">
      <h4 class="font-medium leading-7 text-gray-800">
        {{ getGoalById(goal.goalTypeId)!.name }} goal objective examples:
      </h4>
      <p
        v-for="(str, index) in goalObjectivesExamples"
        :key="index"
        class="my-4 text-sm text-gray-600"
      >
        {{ str }}
      </p>
    </div>
    <div class="flex w-full flex-col">
      <label
        :for="`goal${goal.id}-objectives`"
        class="flex items-center justify-start font-medium text-gray-600"
      >
        Objectives
      </label>
      <div class="mt-1">
        <textarea
          ref="objectivesValueRef"
          :value="objectivesValue"
          rows="16"
          :name="`goal${goal.id}-objectives`"
          class="focus:ring-primary-600 block max-h-[400px] min-h-[400px] w-full rounded-md border-0 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6"
          :placeholder="`Between ${MIN_COUNT} and ${MAX_COUNT} characters`"
          @input="handleInput"
        />
        <p class="flex min-h-4 items-center justify-end text-xs">
          <span
            :class="charCount > MAX_COUNT ? 'text-red-600' : 'text-gray-400'"
          >
            {{ charCount }} of 1000 characters
          </span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { useFocus } from '@vueuse/core';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';
  import { useRefData } from '@aventur-shared/stores/refdataStore';
  import { MAX_COUNT, MIN_COUNT } from './constants';

  const { getGoalById } = useRefData();

  const props = defineProps<{
    goal: ClientGoal;
  }>();

  const emit = defineEmits<{
    (e: 'update:modelValue', str: string): void;
  }>();

  const goalObjectivesExamples = computed(
    () => getGoalById(props.goal.goalTypeId)?.objectivesExamples ?? [],
  );

  const objectivesValue = ref(props.goal.objectives || '');
  const charCount = ref<number>(objectivesValue.value.length);

  const handleInput = (e) => {
    objectivesValue.value = e.target.value;
    charCount.value = objectivesValue.value.length;
    emit('update:modelValue', objectivesValue.value);
  };

  const objectivesValueRef = ref();
  onMounted(() => {
    useFocus(objectivesValueRef, { initialValue: true });
  });
</script>
