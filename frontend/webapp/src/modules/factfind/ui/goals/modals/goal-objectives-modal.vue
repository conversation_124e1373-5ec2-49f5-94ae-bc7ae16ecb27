<template>
  <slide-over :config="{ maxWidth: 'screen-md' }">
    <template #open-button="{ open }">
      <custom-button
        :id="`openObjectivesModal:${client.id}:${goal.id}`"
        theme="custom"
        class="hidden"
        data-testid="goal-item-objectives"
        @on-click="open"
      />
    </template>

    <template #header>
      <box-section data-testid="goal-detail-modal">
        <div class="flex flex-col items-start">
          <h1 class="text-lg text-white">{{ goal.name }}</h1>
          <div class="my-2">
            <dl>
              <dt class="text-primary-400 flex font-medium">Goal Objectives</dt>
              <dd class="flex text-gray-400">
                {{ formatName(props.client) }}
              </dd>
            </dl>
          </div>
        </div>
      </box-section>
    </template>

    <template #default>
      <box-section class="m-auto w-full rounded-lg bg-white md:w-[46rem]">
        <objectives-form v-model="objectivesValue" :goal="goal" />
      </box-section>
    </template>
    <template #footer="{ close }">
      <div class="flex justify-between space-x-3">
        <custom-button
          type="button"
          theme="primary-ghost"
          @on-click="handleClose(close)"
        >
          Close
        </custom-button>
        <custom-button
          theme="primary"
          data-testid="done-button"
          :disabled="!hasValidLength"
          @on-click="handleDone(close)"
          >Save</custom-button
        >
      </div>
    </template>
  </slide-over>
</template>

<script setup lang="ts">
  import { inRange } from 'lodash';
  import { computed, nextTick, ref, watch } from 'vue';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { ArrayElement } from '@aventur-shared/types/Common';
  import { formatName } from '@aventur-shared/utils/user';
  import { Goal, GoalId } from '@aventur-shared/modules/goals';
  import { ClientGoal } from '@aventur-shared/modules/clients/models/goal';

  import { BoxSection, Button as CustomButton, SlideOver } from '@modules/ui';
  import { action } from '@modules/factfind/use-cases/update-goal-objectives';
  import { MAX_COUNT, MIN_COUNT, ObjectivesForm } from './objectives-form';

  const toast = useToast();

  const props = defineProps<{
    goal: ClientGoal;
    client: ArrayElement<Goal['clients']>;
  }>();

  const emit = defineEmits<{
    (event: 'goalUpdated', id: GoalId, objectives: string): void;
  }>();

  const objectivesValue = ref(props.goal.objectives || '');
  const charCount = ref<number>(objectivesValue.value.length);

  const hasValidLength = computed(() =>
    inRange(charCount.value, MIN_COUNT, MAX_COUNT + 1),
  );

  watch(
    () => props.goal,
    (goal) => (objectivesValue.value = goal.objectives || ''),
  );

  watch(objectivesValue, (value) => (charCount.value = value.length));

  const handleClose = (close: () => void) => {
    objectivesValue.value = props.goal.objectives || '';
    close();
  };

  const handleDone = async (close: () => void) => {
    if (!hasValidLength.value) return;
    try {
      await action(props.goal.id, props.client.id, objectivesValue.value);
      await nextTick(() =>
        emit('goalUpdated', props.goal.id, objectivesValue.value),
      );
      close();
    } catch (e) {
      toast.error(e as Error);
    }
  };
</script>
