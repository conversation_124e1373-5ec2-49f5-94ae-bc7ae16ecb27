<template>
  <Modal
    :config="{
      isOpened: isOpen,
      outsideClickClose: true,
      initialFocus: initialFocusRef,
    }"
    @use-api="setModalAPI"
  >
    <template #open-button="{ open }">
      <button
        :id="`openEditModal:${client.id}:${goal.id}`"
        class="hidden"
        @click="open"
      ></button>
    </template>
    <template #default>
      <Box>
        <BoxSection divider="bottom" class="flex justify-between">
          <BoxTitle>
            {{ goal.name }}
            <p class="text-sm font-normal text-gray-500">{{ group.name }}</p>
          </BoxTitle>
        </BoxSection>
        <BoxSection>
          <GoalForm
            :group="group"
            :client="client"
            :goal="goal"
            @on-submit="onSubmit"
            @on-cancel="onClose"
          />
        </BoxSection>
      </Box>
    </template>
  </Modal>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { useForm } from 'vee-validate';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { Client } from '@aventur-shared/modules/clients';
  import { Goal } from '@aventur-shared/stores/refdataStore';
  import { GoalId } from '@aventur-shared/modules/goals';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';
  import { ClientGoalAttrsMapper } from '@aventur-shared/modules/clients/utils';
  import patchClientGoal from '@aventur-shared/modules/factfind/api/goals/patch-client-goal';

  import { Box, BoxSection, BoxTitle, Modal, ModalAPI } from '@modules/ui';
  import GoalForm from '../GoalForm.vue';
  import { FormValues, useGoalForm } from '../use-goal-form';

  const toast = useToast();

  const initialFocusRef = ref<HTMLElement | null>(null);

  const emit = defineEmits<{
    (e: 'on-edit', goalId: GoalId): void;
  }>();

  const props = defineProps<{
    group: Goal;
    client: Client;
    goal: ClientGoal;
    isOpen?: boolean;
  }>();

  const modalAPI: ModalAPI = {} as ModalAPI;
  const setModalAPI = (api: ModalAPI) => {
    modalAPI.open = api.open;
    modalAPI.close = api.close;
  };

  const { validationSchema, initialValues } = useGoalForm(
    props.group.id,
    props.goal,
  );

  const { handleSubmit, resetForm } = useForm<FormValues>({
    validationSchema,
    initialValues,
  });

  const mapAttrs = (goalTypeId: number, attrs: FormValues['attributes']) =>
    ClientGoalAttrsMapper.toDTO(goalTypeId, attrs);

  const onSubmit = handleSubmit(async (formValues) => {
    const editClientGoalDTO = {
      id: props.goal.id,
      goal_name: formValues.goalName,
      client_ids: formValues.clientIds,
      goal_attributes: mapAttrs(formValues.goalTypeId, formValues.attributes),
    };

    try {
      await patchClientGoal(props.client.id, props.goal.id, editClientGoalDTO);
      emit('on-edit', props.goal.id);
      modalAPI.close();
    } catch (e) {
      toast.error(e as Error);
    }
  });

  const onClose = () => {
    modalAPI.close();
  };

  watch(
    () => props.goal,
    (goal) =>
      resetForm({
        values: goal,
      }),
  );
</script>
