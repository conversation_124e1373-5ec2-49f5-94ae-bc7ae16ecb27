<template>
  <slide-over :config="{ maxWidth: 'screen-xl' }">
    <template #open-button="{ open }">
      <custom-button
        :id="`openForecasterModal:${client.id}:${goal.id}`"
        theme="custom"
        class="hidden"
        data-testid="goal-item-cash-forecast"
        @on-click="open"
      />
    </template>

    <template #header>
      <box-section data-testid="goal-detail-modal">
        <div class="flex flex-col items-start">
          <h1 class="text-lg text-white">{{ goal.name }}</h1>
          <div class="my-2">
            <dl>
              <dt class="text-primary-400 flex font-medium">Cash Forecast</dt>
              <dd class="flex text-gray-400">
                {{ formatName(client) }}
              </dd>
            </dl>
          </div>
        </div>
      </box-section>
    </template>

    <template #default>
      <box-section class="w-full rounded-lg bg-white">
        <div class="flex w-full flex-col content-start">
          <ForecasterResults
            :goal-id="props.goal.id as GoalId"
            :forecaster-data="goal.cashForecast"
            :client-id="props.client.id"
          />
        </div>
      </box-section>
    </template>

    <template #footer="{ close }">
      <div class="flex justify-between space-x-3">
        <button
          type="button"
          class="text-primary hover:bg-primary-100 rounded-md bg-white px-5 py-2 transition-colors focus:outline focus:outline-gray-100"
          @click="close"
        >
          Close
        </button>
      </div>
    </template>
  </slide-over>
</template>

<script setup lang="ts">
  import { defineRule } from 'vee-validate';
  import { required } from '@vee-validate/rules';

  import { formatName } from '@aventur-shared/utils/user';
  import { ArrayElement } from '@aventur-shared/types/Common';
  import { Goal, GoalId } from '@aventur-shared/modules/goals';
  import { ClientGoal } from '@aventur-shared/modules/clients/models/goal';

  import { BoxSection, Button as CustomButton } from '@modules/ui';
  import SlideOver from '@modules/ui/slide-over/slide-over.vue';
  import ForecasterResults from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/forecaster-results.vue';

  defineRule('required', required);

  const props = defineProps<{
    goal: ClientGoal;
    client: ArrayElement<Goal['clients']>;
  }>();
</script>
