<template>
  <Modal
    :config="{
      outsideClickClose: true,
      initialFocus: initialFocusRef,
    }"
    @use-api="setModalAPI"
  >
    <template #open-button="{ open }">
      <DashedBorderButton
        class="mt-4 bg-gray-50 hover:bg-gray-100"
        @click="open"
      >
        Add a new {{ lowerCase(group.name) }} goal
      </DashedBorderButton>
    </template>
    <template #default>
      <Box>
        <BoxSection
          title="Add Goal"
          divider="bottom"
          class="flex justify-between"
        />
        <BoxSection>
          <GoalForm
            :group="group"
            :client="client"
            @on-submit="onSubmit"
            @on-cancel="onClose"
          />
        </BoxSection>
      </Box>
    </template>
  </Modal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { lowerCase, merge } from 'lodash';
  import { useForm } from 'vee-validate';

  import { useToast } from '@aventur-shared/composables/useToast';
  import { Client } from '@aventur-shared/modules/clients';
  import { Goal } from '@aventur-shared/stores/refdataStore';
  import { postClientGoals } from '@aventur-shared/modules/factfind';
  import { ClientGoalAttrsMapper } from '@aventur-shared/modules/clients/utils';

  import { Box, BoxSection, Modal, ModalAPI } from '@modules/ui';
  import { DashedBorderButton } from '@modules/factfind/ui';

  import GoalForm from '../GoalForm.vue';
  import { FormValues, useGoalForm } from '../use-goal-form';

  const toast = useToast();

  const initialFocusRef = ref<HTMLElement | null>(null);

  const emit = defineEmits<{
    (e: 'on-add'): void;
  }>();

  const props = defineProps<{
    group: Goal;
    client: Client;
  }>();

  const modalAPI: ModalAPI = {} as ModalAPI;
  const setModalAPI = (api: ModalAPI) => {
    modalAPI.open = api.open;
    modalAPI.close = api.close;
  };

  const { validationSchema, initialValues } = useGoalForm(props.group.id);

  const { handleSubmit } = useForm<FormValues>({
    validationSchema,
    initialValues: merge(
      {
        clientIds: [props.client.id],
      },
      initialValues,
    ),
  });

  const mapAttrs = (goalTypeId: number, attrs: FormValues['attributes']) =>
    ClientGoalAttrsMapper.toDTO(goalTypeId, attrs);

  const onSubmit = handleSubmit(async (formValues) => {
    const createClientGoalDTO = {
      goal_type_id: formValues.goalTypeId,
      goal_name: formValues.goalName,
      client_ids: formValues.clientIds,
      goal_attributes: mapAttrs(formValues.goalTypeId, formValues.attributes),
    };

    try {
      await postClientGoals(props.client.id, createClientGoalDTO);
      emit('on-add');
      modalAPI.close();
    } catch (e) {
      toast.error(e as Error);
    }
  });

  const onClose = () => {
    modalAPI.close();
  };
</script>
