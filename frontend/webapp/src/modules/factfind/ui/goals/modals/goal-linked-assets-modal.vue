<template>
  <slide-over :config="{ maxWidth: 'screen-md' }">
    <template #open-button="{ open }">
      <custom-button
        :id="`openLinkAssetsModal:${client.id}:${goal.id}`"
        theme="custom"
        class="hidden"
        data-testid="goal-item-link-assets"
        @on-click="open"
      />
    </template>

    <template #header>
      <box-section data-testid="goal-detail-modal">
        <h1 class="text-lg text-white">{{ goal.name }}</h1>
        <div class="my-2">
          <dl>
            <dt class="text-primary-400 flex font-medium">Related Assets</dt>
            <dd class="flex text-gray-400">
              {{ formatName(props.client) }}
            </dd>
          </dl>
        </div>
      </box-section>
    </template>

    <template #default>
      <div class="flex flex-col p-4">
        <t-table>
          <t-head>
            <t-head-tr>
              <t-head-th>Account Type</t-head-th>
              <t-head-th>Provider</t-head-th>
              <t-head-th>Account Number</t-head-th>
              <t-head-th></t-head-th>
            </t-head-tr>
          </t-head>
          <t-body>
            <t-body-tr v-if="selectedHoldings.length === 0">
              <t-body-td colspan="4"><span>No linked assets</span></t-body-td>
            </t-body-tr>
            <t-body-tr
              v-for="selectedHolding in selectedHoldings"
              :key="selectedHolding.id"
            >
              <t-body-td>{{ selectedHolding.productTypeName }}</t-body-td>
              <t-body-td>{{ selectedHolding.providerName }}</t-body-td>
              <t-body-td>{{ selectedHolding.accountNumber }}</t-body-td>
              <t-body-td
                ><custom-button
                  class="w-full"
                  theme="custom"
                  data-testid="unlink-button"
                  @click="
                    () => {
                      handleUnlink(selectedHolding.id);
                    }
                  "
                >
                  <XMarkIcon
                    class="size-5 text-gray-400 hover:text-gray-600" /></custom-button
              ></t-body-td>
            </t-body-tr>
          </t-body>
        </t-table>
        <div class="flex flex-row items-center px-8 pt-2">
          <span>Link Assets</span>
          <account-selector
            class="grow pl-4"
            :holdings="availableHoldings"
            @holdings-selected="handleHoldingsSelected"
          />
        </div>
      </div>
    </template>
    <template #footer="{ close }">
      <div class="flex justify-between space-x-3">
        <custom-button type="button" theme="primary-ghost" @on-click="close">
          Close
        </custom-button>
        <custom-button
          theme="primary"
          data-testid="done-button"
          @on-click="
            () => {
              handleDone();
              close();
            }
          "
          >Save</custom-button
        >
      </div>
    </template>
  </slide-over>
</template>

<script setup lang="ts">
  import { Ref, onMounted, ref, watch } from 'vue';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import { formatName } from '@aventur-shared/utils/user';
  import { useToast } from '@aventur-shared/composables/useToast';
  import {
    Client,
    ClientGoal,
    GoalLinkedHolding,
  } from '@aventur-shared/modules/clients/models';
  import {
    BoxSection,
    Button as CustomButton,
    SlideOver,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import { action } from '@modules/factfind/use-cases/link-holdings';
  import AccountSelector from '../account-selector.vue';

  const toast = useToast();

  const props = withDefaults(
    defineProps<{
      goal: ClientGoal;
      client: Client;
      holdings: GoalLinkedHolding[];
      preSelectedHoldings?: Set<number>;
    }>(),
    {
      preSelectedHoldings: () => new Set() as Set<number>,
    },
  );

  const emit = defineEmits(['goalUpdated']);

  interface GoalHoldingsMap {
    [key: number]: GoalLinkedHolding;
  }

  const allHoldings: Ref<GoalHoldingsMap> = ref({});
  const selectedHoldings: Ref<GoalLinkedHolding[]> = ref([]);
  const availableHoldings: Ref<GoalLinkedHolding[]> = ref([]);

  const setupSelection = () => {
    allHoldings.value = {};
    selectedHoldings.value = [];
    availableHoldings.value = [];
    props.holdings.forEach((holding: GoalLinkedHolding) => {
      allHoldings.value[holding.id] = holding;
      if (!props.preSelectedHoldings.has(Number(holding.id))) {
        availableHoldings.value.push(holding);
      } else {
        selectedHoldings.value.push(holding);
      }
    });
  };

  watch(
    () => [props.holdings, props.preSelectedHoldings],
    () => {
      setupSelection();
    },
  );

  onMounted(async () => {
    setupSelection();
  });

  const handleHoldingsSelected = (holdingIds: number[]) => {
    const currentSelection = selectedHoldings.value.map(
      (holding) => holding.id,
    );
    const allSelected = [...holdingIds, ...currentSelection];
    selectedHoldings.value = allSelected.map(
      (holdingId) => allHoldings.value[holdingId],
    );

    const mask: Set<number> = new Set(allSelected);
    availableHoldings.value = Object.keys(allHoldings.value)
      .filter((holdingId) => !mask.has(Number(holdingId)))
      .map((holdingId) => allHoldings.value[holdingId]);
  };

  const handleUnlink = (holdingId: number) => {
    availableHoldings.value.push(allHoldings.value[holdingId]);
    selectedHoldings.value = selectedHoldings.value.filter(
      (holding) => holding.id !== holdingId,
    );
  };

  const handleDone = async () => {
    const selectedHoldingIds = selectedHoldings.value.map(
      (holding) => holding.id,
    );
    try {
      await action(props.goal.id, props.client.id, selectedHoldingIds);
      emit('goalUpdated', props.goal.id, selectedHoldingIds);
    } catch (e) {
      setupSelection();
      toast.error(e as Error);
    }
  };
</script>
