<template>
  <slide-over :config="{ maxWidth: 'screen-md' }">
    <template #open-button="{ open }">
      <custom-button
        :id="`openRiskProfileModal:${client.id}:${goal.id}`"
        theme="custom"
        class="hidden"
        data-testid="goal-item-risk-profile"
        @on-click="open"
      />
    </template>

    <template #header>
      <box-section data-testid="goal-detail-modal">
        <div class="flex flex-col items-start">
          <h1 class="text-lg text-white">{{ goal.name }}</h1>
          <div class="my-2">
            <dl>
              <dt class="text-primary-400 flex font-medium">Risk Profile</dt>
              <dd class="flex text-gray-400">
                {{ formatName(props.client) }}
              </dd>
            </dl>
          </div>
        </div>
      </box-section>
    </template>

    <template #default>
      <box-section
        v-if="riskProfile"
        class="m-auto w-full rounded-lg bg-white md:w-[46rem]"
      >
        <div class="flex w-full flex-col content-start 2xl:max-w-2xl">
          <h1 class="mb-[42px] mt-5 text-xl font-normal">Risk Results</h1>
          <risk-results :results="riskProfile" :is-read-only="true" />
          <disclosure-section title="Answers" :default-open="false">
            <ul role="list" class="divide-y divide-gray-100">
              <li
                v-for="(q, i) in questions"
                :key="i"
                class="flex flex-wrap items-center justify-between gap-x-6 gap-y-4 py-5 sm:flex-nowrap"
              >
                <div>
                  <p class="text-sm font-semibold leading-6 text-gray-900">
                    {{ q.question }}
                  </p>
                  <div
                    class="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500"
                  >
                    <p>
                      {{ getAnswerByIndex(q, i) }}
                    </p>
                  </div>
                </div>
              </li>
            </ul>
          </disclosure-section>
        </div>
      </box-section>
      <box-section
        v-else
        class="m-auto w-full rounded-lg bg-white md:w-[46rem]"
      >
        <div class="flex w-full flex-col content-start 2xl:max-w-2xl">
          <template v-if="riskProfile">
            <h1 class="mb-[42px] mt-5 text-xl font-normal">Risk Results</h1>
            <risk-results :results="riskProfile" :is-read-only="true" />
          </template>
        </div>
      </box-section>
    </template>
    <template #footer="{ close }">
      <div class="flex justify-between space-x-3">
        <custom-button type="button" theme="primary-ghost" @on-click="close">
          Close
        </custom-button>
      </div>
    </template>
  </slide-over>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { defineRule } from 'vee-validate';
  import { required } from '@vee-validate/rules';
  import { ArrayElement } from '@aventur-shared/types/Common';
  import { formatName } from '@aventur-shared/utils/user';
  import {
    Goal,
    RiskProfileData,
    TRiskQuestion,
  } from '@aventur-shared/modules/goals';
  import { ClientGoal } from '@aventur-shared/modules/clients/models/goal';
  import { riskProfileQuestionnaire } from '@aventur-shared/modules/refdata/refdata';
  import { BoxSection, Button as CustomButton, SlideOver } from '@modules/ui';
  import { RiskResults } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import { useRiskProfileStore } from '@modules/sub-tasks/use-cases/user-calculates-risk-profile';
  import DisclosureSection from '@modules/sub-tasks/use-cases/user-generates-forecaster-plan/ui/components/disclosure-section.vue';

  defineRule('required', required);
  const props = defineProps<{
    goal: ClientGoal;
    client: ArrayElement<Goal['clients']>;
  }>();

  const { getRiskProfile } = useRiskProfileStore();

  const riskProfile = computed(() =>
    getRiskProfile(props.goal.id, props.client.id),
  );
  const questions: TRiskQuestion[] = riskProfileQuestionnaire;

  const getAnswerByIndex = (question: TRiskQuestion, i: number) => {
    const profile = { ...riskProfile.value } as RiskProfileData;
    const key = profile.answers[`a${i}`] - 1;
    return question.answers[key].answer;
  };
</script>
