<template>
  <Menu as="div" class="relative">
    <div>
      <MenuButton
        class="focus:ring-primary flex max-w-xs items-center rounded-full bg-transparent text-sm focus:outline-none focus:ring-2 focus:ring-offset-2"
        data-testid="goal-item-menu"
      >
        <span class="sr-only">Open goal menu</span>
        <EllipsisVerticalIcon class="size-5 text-gray-600" />
      </MenuButton>
    </div>
    <transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100"
      leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100"
      leave-to-class="transform opacity-0 scale-95"
    >
      <MenuItems
        class="absolute right-0 z-10 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none"
      >
        <div class="px-4 py-3">
          <p class="text-sm font-semibold text-gray-900">Configure Goal</p>
        </div>
        <div class="py-1">
          <MenuItem
            v-for="option in options.slice(0, 3)"
            :key="option.name"
            v-slot="{ active, disabled }"
            :disabled="option.disabled"
            @click.prevent="$emit('item-clicked', option.slug)"
          >
            <span
              :class="[
                active ? 'cursor-pointer bg-gray-100' : '',
                disabled ? 'cursor-not-allowed text-gray-500/50' : '',
                'flex items-center justify-start px-4 py-2 text-sm text-gray-500',
              ]"
            >
              {{ option.name }}
            </span>
          </MenuItem>
        </div>
        <div class="py-1">
          <MenuItem
            v-for="option in options.slice(3, -1)"
            :key="option.name"
            v-slot="{ active, disabled }"
            :disabled="option.disabled"
            @click.prevent="$emit('item-clicked', option.slug)"
          >
            <span
              :class="[
                active ? 'cursor-pointer bg-gray-100' : '',
                disabled ? 'cursor-not-allowed text-gray-500/50' : '',
                'flex items-center justify-start px-4 py-2 text-sm text-gray-500',
              ]"
            >
              {{ option.name }}
            </span>
          </MenuItem>
        </div>
        <div class="py-1">
          <MenuItem
            v-for="option in options.slice(-1)"
            :key="option.name"
            v-slot="{ active, disabled }"
            :disabled="option.disabled"
            @click.prevent="$emit('item-clicked', option.slug)"
          >
            <span
              :class="[
                active ? 'cursor-pointer bg-gray-100' : '',
                disabled ? 'cursor-not-allowed text-red-500/50' : '',
                'flex items-center justify-start px-4 py-2 text-sm text-red-500',
              ]"
            >
              {{ option.name }}
            </span>
          </MenuItem>
        </div>
      </MenuItems>
    </transition>
  </Menu>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
  import { EllipsisVerticalIcon } from '@heroicons/vue/24/solid';
  import { ClientGoal } from '@aventur-shared/modules/clients/models';

  type EventArg =
    | 'link-assets'
    | 'objectives'
    | 'risk-profile'
    | 'forecaster'
    | 'delete'
    | 'edit';

  type GoalItemMenuOption = {
    name: string;
    slug: EventArg;
    disabled: boolean;
  };

  const props = defineProps<{
    fieldIndex: number;
    goal: ClientGoal;
  }>();

  const options = computed<GoalItemMenuOption[]>(() => [
    {
      name: 'Edit goal',
      slug: 'edit',
      disabled: false,
    },
    {
      name: 'Link assets',
      slug: 'link-assets',
      disabled: false,
    },
    {
      name: 'Update objectives',
      slug: 'objectives',
      disabled: false,
    },
    {
      name: 'View risk profile',
      slug: 'risk-profile',
      disabled: !props.goal.riskProfile,
    },
    {
      name: 'View cash forecast',
      slug: 'forecaster',
      disabled: !props.goal.cashForecast,
    },
    {
      name: 'Delete goal',
      slug: 'delete',
      disabled: !!props.goal.cases.length,
    },
  ]);

  defineEmits<{
    'item-clicked': [e: EventArg];
  }>();
</script>
