import { object } from 'yup';

import {
  ClientGoal,
  ClientGoalTypeEnum,
} from '@aventur-shared/modules/clients/models';

import {
  EmergencyFundAttributesValidationSchema,
  defaultAttributesValidationSchema,
  defaultGoalValidationSchema,
} from './form-model';

export type FormValues = ReturnType<typeof useGoalForm>['initialValues'];

type TFields = {
  goalTypeId: ClientGoal['goalTypeId'];
  goalName: ClientGoal['name'];
  clientIds: ClientGoal['clientIds'];
  attributes: ClientGoal['attributes'];
};
type TLabels = Partial<Record<keyof Omit<TFields, 'attributes'>, string>> &
  Record<keyof TFields['attributes'], string>;
type THints = Partial<
  Record<keyof Omit<TFields, 'attributes'>, string> &
    Record<keyof TFields['attributes'], string>
>;

class DefaultFormModel {
  constructor(
    private goalType: ClientGoalTypeEnum,
    private goal?: ClientGoal,
  ) {}

  get validationSchema() {
    return defaultGoalValidationSchema.concat(
      object({
        attributes: defaultAttributesValidationSchema,
      }),
    );
  }
  get initialValues(): TFields {
    const { attributes, ..._ } = this.goal ?? {};
    return {
      goalTypeId: this.goalType,
      goalName: this.goal?.name ?? '',
      clientIds: this.goal?.clientIds ?? [],
      attributes: {
        targetAmount: attributes?.targetAmount ?? null,
        targetDate: attributes?.targetDate ?? null,
      },
    };
  }
  get fieldsLabels(): TLabels {
    return {
      targetAmount: 'Target amount, £',
      targetDate: 'Target date',
    };
  }
  get fieldsHints(): THints {
    return {
      clientIds: 'Add linked clients to create shared goals and cases',
    };
  }
}

class ProtectYourselfAndFamilyFormModel extends DefaultFormModel {}
class EstatePlanningAndWillsFormModel extends DefaultFormModel {}
class InvestForSchoolFeesFormModel extends DefaultFormModel {}
class TravelPlanningFormModel extends DefaultFormModel {}
class BuildWealthFormModel extends DefaultFormModel {}

class PropertyOwnershipFormModel extends DefaultFormModel {
  get fieldsHints(): THints {
    return {
      goalName: 'e.g. Main residence, Vacation property, etc.',
      targetAmount: 'The amount required for a deposit or overpayment',
      targetDate: 'The date of a potential purchase or overpayment',
    };
  }
}

class EmergencyFundFormModel extends DefaultFormModel {
  get validationSchema() {
    return defaultGoalValidationSchema.concat(
      object({
        attributes: EmergencyFundAttributesValidationSchema.required(),
      }),
    );
  }
  get fieldsLabels(): TLabels {
    return {
      ...super.fieldsLabels,
      targetAmount: 'Emergency fund amount, £',
      targetDate: 'Target date',
    };
  }
  get fieldsHints(): THints {
    return {
      ...super.fieldsHints,
      goalName: 'e.g. 6-months fund, 5K emergency fund etc.',
    };
  }
}

class RetirementFormModel extends DefaultFormModel {
  get fieldsLabels(): TLabels {
    return {
      ...super.fieldsLabels,
      targetDate: 'Retirement date',
    };
  }
  get fieldsHints(): THints {
    return {
      ...super.fieldsHints,
      targetAmount:
        'Monthly income required in retirement or retirement pot amount', // or alternatively "Retirement pot"
    };
  }
}

class InvestForChildrenFormModel extends DefaultFormModel {
  get fieldsHints(): THints {
    return {
      ...super.fieldsHints,
      goalName: "Include a child's name, e.g. Junior ISA for Charles, etc.",
    };
  }
}

export class GoalFormModelFactory {
  static create(goalType: ClientGoalTypeEnum, goal?: ClientGoal) {
    switch (goalType) {
      case ClientGoalTypeEnum.EmergencyFund:
        return new EmergencyFundFormModel(goalType, goal);
      case ClientGoalTypeEnum.PropertyOwnership:
        return new PropertyOwnershipFormModel(goalType, goal);
      case ClientGoalTypeEnum.ProtectYourselfAndFamily:
        return new ProtectYourselfAndFamilyFormModel(goalType, goal);
      case ClientGoalTypeEnum.Retirement:
        return new RetirementFormModel(goalType, goal);
      case ClientGoalTypeEnum.EstatePlanningAndWills:
        return new EstatePlanningAndWillsFormModel(goalType, goal);
      case ClientGoalTypeEnum.InvestForChildren:
        return new InvestForChildrenFormModel(goalType, goal);
      case ClientGoalTypeEnum.InvestForSchoolFees:
        return new InvestForSchoolFeesFormModel(goalType, goal);
      case ClientGoalTypeEnum.TravelPlanning:
        return new TravelPlanningFormModel(goalType, goal);
      case ClientGoalTypeEnum.BuildWealth:
        return new BuildWealthFormModel(goalType, goal);
      case ClientGoalTypeEnum.Custom:
        return new DefaultFormModel(goalType);
      default:
        throw Error('Unknown goal type');
    }
  }
}

export const useGoalForm = (
  goalType: ClientGoalTypeEnum,
  goal?: ClientGoal,
) => {
  const { validationSchema, initialValues, fieldsLabels, fieldsHints } =
    GoalFormModelFactory.create(goalType, goal);

  return {
    validationSchema,
    initialValues,
    fieldsLabels,
    fieldsHints,
  };
};
