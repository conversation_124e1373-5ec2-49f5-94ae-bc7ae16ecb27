<template>
  <div
    data-testid="goal-item-header"
    class="flex w-full flex-col items-start px-3 pt-2 lg:flex-row lg:items-center lg:gap-4"
  >
    <h1 class="flex text-base font-semibold text-gray-600">
      {{ goal.name }}
    </h1>
    <span class="sm:hidden lg:flex lg:grow"></span>
    <p class="flex text-xs font-normal text-gray-400">
      Target amount:&nbsp;
      <span class="font-medium text-gray-600">{{
        goal.attributes.targetAmount
          ? formatWithCurrency(new Money(goal.attributes.targetAmount))
          : 'N/A'
      }}</span>
    </p>
    <p class="flex text-xs font-normal text-gray-400">
      Target date:&nbsp;
      <span class="font-medium text-gray-600">{{
        goal.attributes.targetDate
          ? new DateTime(goal.attributes.targetDate).formatToView()
          : 'N/A'
      }}</span>
    </p>
  </div>
</template>

<script setup lang="ts">
  import { ArrayElement } from '@aventur-shared/types/Common';

  import { DateTime } from '@aventur-shared/utils/dateTime';
  import { Money, formatWithCurrency } from '@aventur-shared/utils/money';
  import { FormValues } from '@modules/factfind/ui/goals/form-model';

  defineProps<{
    goal: ArrayElement<FormValues['defaultGoals']>;
  }>();
</script>
