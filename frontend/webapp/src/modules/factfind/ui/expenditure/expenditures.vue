<template>
  <div class="flex flex-col">
    <ExpendituresListWrapper
      :current-data="expenditures"
      @on-submit="handleExpenditureAction"
    />
  </div>
</template>
<script setup lang="ts">
  import {
    Expenditure,
    ExpenditureListItem,
  } from '@aventur-shared/modules/factfind/models';
  import { ExpenditureFormAction } from '@aventur-shared/modules/factfind/types';
  import ExpendituresListWrapper from '@/modules/factfind/ui/expenditure/expenditures-list-wrapper.vue';

  const emit = defineEmits<{
    (e: 'on-add', data: Expenditure): void;
    (e: 'on-edit', data: ExpenditureFormAction, key: string): void;
    (e: 'on-remove', key: string): void;
    (e: 'on-save'): void;
  }>();

  defineProps<{
    expenditures: Array<ExpenditureListItem>;
  }>();

  const handleExpenditureAction = (expenditures: ExpenditureFormAction[]) => {
    for (const expenditure of expenditures) {
      if (expenditure.action === 'edit') {
        emit('on-edit', expenditure, expenditure.key || '');
      } else if (expenditure.action === 'delete') {
        emit('on-remove', expenditure.key || '');
      } else {
        emit('on-add', expenditure);
      }
    }

    emit('on-save');
  };
</script>
