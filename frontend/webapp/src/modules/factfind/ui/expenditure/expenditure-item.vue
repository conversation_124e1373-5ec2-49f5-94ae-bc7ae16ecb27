<template>
  <p class="text-primary text-lg">
    {{ label }}
  </p>
  <div class="grid grid-cols-1 gap-2 md:grid-cols-6">
    <text-field
      label="Amount"
      :name="`expenditures[${idx}].amount`"
      @on-change="emit('update', idx)"
    />
    <select-field
      label="Frequency"
      :name="`expenditures[${idx}].frequency`"
      :options="frequencySelectOptions"
      @on-select="emit('update', idx)"
    />
    <text-field
      class="md:col-span-3"
      label="Description"
      :name="`expenditures[${idx}].description`"
    />
    <checkbox-field
      label="Essential"
      :checked-value="true"
      :name="`expenditures[${idx}].isEssential`"
      class="flex items-center justify-center"
      :disabled="hasEssentialPropLocked"
      @change="emit('update', idx)"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import {
    <PERSON><PERSON><PERSON>ield,
    <PERSON><PERSON>ield,
    TextField,
  } from '@aventur-shared/components/form';
  import { frequencySelectOptions } from '@aventur-shared/modules/factfind/models';

  type Props = {
    label?: string;
    idx: number;
  };

  const props = defineProps<Props>();

  const hasEssentialPropLocked = computed(() => {
    return props.label?.toLowerCase().includes('essential');
  });

  const emit = defineEmits<{
    (e: 'update', idx: number): void;
  }>();
</script>
