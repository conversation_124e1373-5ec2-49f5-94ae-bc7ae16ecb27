<template>
  <div class="mb-4">
    <div class="flex w-full flex-row justify-between">
      <div>
        <b class="text-primary text-lg">{{ label }}</b>
        <div class="text-xs text-black/60"></div>
      </div>
      <ExpenditureGroupHeaderTotal :total="total" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import ExpenditureGroupHeaderTotal from '@/modules/factfind/ui/expenditure/expenditure-group-header-total.vue';

  type Props = {
    label: string;
    total: { total: number | string; essential: number | string };
  };

  defineProps<Props>();
</script>
