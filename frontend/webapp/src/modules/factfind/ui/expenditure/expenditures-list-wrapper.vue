<template>
  <div v-if="!isExpendituresLoaded || !isExpendituresDataLoaded">
    <ExpendituresHeader />
    <div class="flex justify-center p-4">
      <LoadingSpinner class="size-24" />
    </div>
  </div>

  <ExpendituresList
    v-else
    :current-data="props.currentData as ExpenditureListItem[]"
    :expenditure-groups="expenditureGroups"
    @on-submit="
      (actionList) => {
        emit('on-submit', actionList);
      }
    "
  />
</template>

<script setup lang="ts">
  import { computed, nextTick, ref, watch } from 'vue';

  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import LoadingSpinner from '@aventur-shared/components/LoadingSpinner.vue';
  import { ExpenditureListItem } from '@aventur-shared/modules/factfind/models';
  import { ExpenditureFormAction } from '@aventur-shared/modules/factfind/types';

  import ExpendituresList from '@/modules/factfind/ui/expenditure/expenditures-list.vue';
  import ExpendituresHeader from '@/modules/factfind/ui/expenditure/expenditures-header.vue';
  //

  const emit = defineEmits<{
    (e: 'on-submit', expenditure: ExpenditureFormAction[]): void;
  }>();
  const props = defineProps<{
    currentData?: ExpenditureListItem[];
  }>();

  const cashFlow = useCashFlow();

  const isExpendituresLoaded = ref(false);
  const isExpendituresDataLoaded = ref(false);

  const expenditureGroups = computed(() =>
    cashFlow.getExpenditureGroupsOptions(),
  );

  watch(
    expenditureGroups,
    (newVal) => {
      if (!newVal.length) return;

      nextTick(() => {
        isExpendituresLoaded.value = true;
      });
    },
    { immediate: true },
  );

  watch(
    () => props.currentData,
    () => {
      if (!props.currentData) return;
      isExpendituresDataLoaded.value = true;
    },
    { immediate: true },
  );
</script>
