<template>
  <div class="text-right">
    <b class="text-primary-900 text-lg">£{{ groupTotal }} / month</b>
    <div class="min-w-[100px] text-right text-xs">
      Including £{{ groupNonEssentials }} for non-essential items
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import { toNumber } from 'lodash';

  type Props = {
    total: { total: number | string; essential: number | string };
  };

  const props = defineProps<Props>();

  const groupTotal = computed(() => props.total['total']);
  const groupNonEssentials = computed(() =>
    Object.values(props.total).reduce(
      (val, res) => toNumber(val) - toNumber(res),
    ),
  );
</script>
