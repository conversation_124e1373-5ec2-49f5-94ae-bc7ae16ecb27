<template>
  <div class="flex flex-row justify-between">
    <BoxSection title="Your Expenditure" no-padding class="flex flex-col">
      <span class="text-sm text-gray-500"
        >Breakdown of how you spend you money</span
      >
    </BoxSection>

    <div class="flex flex-col justify-end gap-5 text-right">
      <div class="text-right">
        <b class="text-xl text-black">£{{ total }}</b>
        <div class="min-w-[100px] text-right text-black/60">Total monthly</div>
      </div>
      <div>
        <CustomButton
          :disabled="buttonDisabled"
          :is-busy="submittingExpenditure"
          theme="primary"
          @click="emit('save')"
          >Save</CustomButton
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { inject } from 'vue';
  import { BoxSection, Button as CustomButton } from '@modules/ui';

  type Props = {
    total?: number | string;
    buttonDisabled?: boolean;
  };

  withDefaults(defineProps<Props>(), {
    total: 0,
    buttonDisabled: true,
  });

  const emit = defineEmits<{
    (e: 'save');
  }>();

  const submittingExpenditure = inject('submittingExpenditure') as boolean;
</script>
