<template>
  <ExpendituresHeader
    :total="totalOverall"
    :button-disabled="!meta.valid"
    @save="onSubmit"
  />
  <div>
    <div class="mb-5 flex justify-end"></div>
    <form @submit.prevent="onSubmit">
      <div class="flex flex-col gap-5">
        <div
          v-for="expenditureGroup in expenditureGroups"
          :key="expenditureGroup.value as number"
        >
          <Box class="p-5">
            <div class="my-4 mt-0">
              <ExpenditureGroupHeader
                :label="expenditureGroup.label"
                :total="totalForGroup[expenditureGroup.value as number]"
              />
              <BaseAccordion>
                <div class="grid grid-cols-1 gap-4">
                  <FieldArray v-slot="{ fields }" name="expenditures">
                    <fieldset
                      v-for="(field, idx) in fields"
                      :key="field.key"
                      :class="{
                        hidden:
                          (field.value as ExpenditureFormItem).typeGroup !=
                          expenditureGroup.value,
                      }"
                    >
                      <div
                        v-if="
                          (field.value as ExpenditureFormItem).typeGroup ==
                          expenditureGroup.value
                        "
                      >
                        <ExpenditureItem
                          :idx="idx"
                          :label="
                            (field.value as ExpenditureFormItem).label as string
                          "
                          @update="onExpenditureUpdate"
                        />
                      </div>
                    </fieldset>
                  </FieldArray>
                </div>
              </BaseAccordion>
            </div>
          </Box>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { FieldArray, useForm } from 'vee-validate';

  import { Money } from '@aventur-shared/utils/money';
  import { useCashFlow } from '@aventur-shared/modules/refdata';
  import {
    ExpenditureFormAction,
    ExpenditureFormItem,
  } from '@aventur-shared/modules/factfind/types';
  import {
    ExpenditureListItem,
    Frequency,
    FrequencyEnum,
  } from '@aventur-shared/modules/factfind/models';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';

  import Box from '@/modules/ui/box/box.vue';
  import BaseAccordion from '@/components/BaseAccordion.vue';
  import ExpenditureGroupHeader from '@/modules/factfind/ui/expenditure/expenditure-group-header.vue';
  import { expendituresValidationSchema } from '@/modules/factfind/services/validation/expenditures';
  import { getMonthlyAmount } from '@/modules/factfind/utils/expenditures/getAmount';
  import ExpendituresHeader from '@/modules/factfind/ui/expenditure/expenditures-header.vue';
  import ExpenditureItem from './expenditure-item.vue';
  //

  type InitialValues = {
    expenditures: ExpenditureFormItem[];
  };

  type TotalForGroup = { [id: number]: { total: string; essential: string } };

  const emit = defineEmits<{
    (e: 'on-submit', expenditure: ExpenditureFormAction[]): void;
  }>();
  const props = defineProps<{
    currentData: ExpenditureListItem[];
    expenditureGroups: SelectOption[];
  }>();

  const cashFlow = useCashFlow();

  const initialValues: InitialValues = {
    expenditures: [],
  };
  const totalForGroup = ref<TotalForGroup>({});

  const expenditureItemOptions = (id: number) => {
    return cashFlow.getExpenditureGroupTypesOptions(id);
  };

  for (const group of props.expenditureGroups) {
    totalForGroup.value[group.value as number] = { total: '0', essential: '0' };

    for (const expenditure of expenditureItemOptions(group.value as number)) {
      initialValues.expenditures.push({
        frequency: null,
        id: null,
        amount: 0,
        description: '',
        new: true,
        key: null,
        valid: true,
        type: expenditure.value as number,
        typeGroup: group.value as number,
        label: expenditure.label,
        isEssential: expenditure.ctx?.is_essential,
      });
    }
  }

  const { values, setValues, meta } = useForm<InitialValues>({
    initialValues: initialValues,
    validationSchema: expendituresValidationSchema,
  });

  const totalOverall = ref<string>('0');

  const updateTotalOverall = () => {
    totalOverall.value = props.expenditureGroups
      .reduce(
        (sum, group) =>
          +totalForGroup.value[group.value as number]['total'] + sum,
        0,
      )
      .toFixed(2);
  };

  const reducer = (sum: number, expenditure: ExpenditureFormItem) => {
    if (!expenditure?.frequency) return sum;
    if (Number.isNaN(+expenditure?.amount)) return sum;

    const frequency = new Frequency(expenditure.frequency as FrequencyEnum);
    const money = new Money(+expenditure.amount);

    const monthlyAmount = getMonthlyAmount(money, frequency);
    sum += monthlyAmount.getValue();
    return sum;
  };

  const updateTotalForGroup = (groupId: number) => {
    const $values = values.expenditures.filter(
      (exp) => exp.typeGroup == groupId,
    );

    totalForGroup.value[groupId] = {
      total: $values.reduce(reducer, 0).toFixed(2),
      essential: $values
        .filter((exp) => exp.isEssential)
        .reduce(reducer, 0)
        .toFixed(2),
    };
    updateTotalOverall();
  };

  const matchDataWithCurrentData = () => {
    const newValues = values.expenditures.map((value) => {
      const expenditure = { ...value };
      expenditure.id = null;
      expenditure.new = true;

      for (const item of props.currentData) {
        if (expenditure.type != item.type) continue;

        expenditure.id = item.id;
        expenditure.amount = item.amount.getValue();
        expenditure.description = item.description;
        expenditure.frequency = item.frequency.toValue();
        expenditure.isEssential = item.isEssential;
        expenditure.new = false;
        expenditure.key = item.key;
      }

      return expenditure;
    });

    setValues({ expenditures: newValues });
  };

  const onExpenditureUpdate = (ctx: number) => {
    const group = values.expenditures[ctx].typeGroup;
    updateTotalForGroup(group);
  };

  const onSubmit = () => {
    const actionList: ExpenditureFormAction[] = [];

    values.expenditures.forEach((expense) => {
      let action: ExpenditureFormAction['action'] = 'create';

      if (!expense.new) {
        if (`${expense.amount}`.trim() === '' || +expense.amount === 0) {
          action = 'delete';
        } else {
          action = 'edit';
        }
      } else {
        if (`${expense.amount}`.trim() === '' || +expense.amount === 0) {
          return;
        }
      }

      const expenditureAction: ExpenditureFormAction = {
        id: expense.id,
        typeGroup: +expense.typeGroup as number,
        type: +expense.type as number,
        description: expense.description as string,
        frequency: new Frequency(expense.frequency as FrequencyEnum),
        amount: new Money(Number(expense.amount)),
        isEssential: expense.isEssential,
        action,
        key: expense.key ?? undefined,
      };

      actionList.push(expenditureAction);
    });

    emit('on-submit', actionList);
  };

  matchDataWithCurrentData();
  watch(
    () => props.currentData,
    () => {
      matchDataWithCurrentData();
    },
    { immediate: true },
  );
</script>
