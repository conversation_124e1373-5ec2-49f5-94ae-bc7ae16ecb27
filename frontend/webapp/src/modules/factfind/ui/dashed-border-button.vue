<template>
  <div
    class="border-primary-100 rounded-lg border-2 border-dashed text-center transition-colors"
    :class="classObject"
  >
    <custom-button
      class="size-full py-4 font-bold"
      theme="text-like"
      :disabled="disabled"
      @on-click="() => $emit('on-click')"
    >
      <slot></slot>
    </custom-button>
  </div>
</template>

<script setup lang="ts">
  import { Button as CustomButton } from '@modules/ui';
  import { computed } from 'vue';
  const props = withDefaults(
    defineProps<{
      disabled?: boolean;
    }>(),
    {
      disabled: false,
    },
  );
  defineEmits(['on-click']);

  const classObject = computed(() => ({
    'hover:border-primary/50': !props.disabled,
  }));
</script>
