<template>
  <div class="flex max-w-7xl items-center justify-end">
    <div class="mb-4 w-full md:w-2/5 lg:w-1/3 xl:w-1/4">
      <label
        class="mb-1 flex items-center justify-start font-sans text-sm font-medium text-gray-500"
      >
        <Square2StackIcon class="size-4" />&nbsp;Split View</label
      >
      <div class="relative">
        <div class="flex items-center justify-end">
          <SearchField
            :model-value="null"
            :options="linkedClientsOptions"
            :searchable="true"
            :can-clear="true"
            :can-deselect="false"
            no-options-text=""
            placeholder="Select linked client"
            :disabled="!linkedClientsOptions.length"
            @change="(value: ClientId) => emit('change', value)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Square2StackIcon } from '@heroicons/vue/24/outline';

  import { formatName } from '@aventur-shared/utils/user';
  import { Client, ClientId } from '@aventur-shared/modules/clients';
  import { SelectOption } from '@aventur-shared/components/form/fields/field-model';
  import { default as SearchField } from '@aventur-shared/components/form/fields/select/SearchSelect.vue';

  const props = defineProps<{
    linkedClients: Client['linkedClients'];
  }>();

  const emit = defineEmits<{
    (e: 'change', value: ClientId): void;
  }>();

  const linkedClientsOptions = computed<SelectOption[]>(() =>
    (props.linkedClients || []).map(($c) => ({
      label: formatName($c),
      value: $c.linkedClientId,
    })),
  );
</script>
