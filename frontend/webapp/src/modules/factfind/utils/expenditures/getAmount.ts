import { IMoney, Money } from '@aventur-shared/utils/money';
import { IFrequency } from '@aventur-shared/modules/factfind/models';

export function calculateMonthlyAmount(
  amount: IMoney,
  frequency: IFrequency,
): number {
  const money = frequency.calculateAmount(amount);
  return money.getValue() / 12;
}

export function getAnnualAmount(amount: IMoney, frequency: IFrequency): IMoney {
  return frequency.calculateAmount(amount);
}

export function getMonthlyAmount(
  amount: IMoney,
  frequency: IFrequency,
): IMoney {
  return new Money(frequency.calculateAmount(amount).getValue() / 12);
}
