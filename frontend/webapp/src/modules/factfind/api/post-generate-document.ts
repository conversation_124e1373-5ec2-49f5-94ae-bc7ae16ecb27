import { apiClient } from '@aventur-shared/services/api';
import { QueryParams } from '@aventur-shared/types/api';
import { DocumentType } from '@/modules/clients-cases/models/document_generation';

export default async (documentType: DocumentType, queryArgs: QueryParams) => {
  await apiClient.post<QueryParams, Promise<void>>(
    `/api/v1/document-generation/render-template?document_type=${documentType}`,
    queryArgs,
  );
};
