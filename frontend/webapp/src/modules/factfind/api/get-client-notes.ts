import { array, number, object, string } from 'yup';
import { apiClient } from '@aventur-shared/services/api';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { ClientId } from '@aventur-shared/modules/clients';
import { FactfindNote } from '@aventur-shared/modules/clients/models';

const getFactfindNotesValidationSchema = array()
  .of(
    object({
      id: number().required(),
      created_by: object({
        id: number().required(),
        first_name: string().defined().min(0),
        last_name: string().defined().min(0),
      }).required(),
      content: string().defined(),
      created_datetime: string().required(),
    }),
  )
  .required();

export default async (clientId: ClientId): Promise<FactfindNote[]> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/internal/v1/clients/${clientId}/notes`,
  );
  try {
    const notesDTO = await getFactfindNotesValidationSchema.validate(response);

    return notesDTO.map((note) => ({
      id: note.id,
      author: {
        id: note.created_by.id,
        firstName: note.created_by.first_name,
        lastName: note.created_by.last_name,
      },
      content: note.content,
      created: new DateTime(note.created_datetime),
    }));
  } catch (e) {
    if (e instanceof Error) throw new Error(e.message);
    else {
      throw new Error('Could not fetch notes.');
    }
  }
};
