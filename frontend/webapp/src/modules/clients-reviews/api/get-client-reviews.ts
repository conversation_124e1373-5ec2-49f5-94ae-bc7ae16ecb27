import { InferType, array, number, object, string } from 'yup';
import { apiClient } from '@aventur-shared/services/api';
import { CaseId } from '@aventur-shared/modules/cases';
import { ClientId } from '@aventur-shared/modules/clients';
import { QueryParams } from '@aventur-shared/types/api/QueryParams';
import { Review } from '@modules/clients-reviews/models/client-review';
import { ReviewType } from '@aventur-shared/modules/cases/types/review-types';

const getClientReviewsValidationSchema = array()
  .of(
    object({
      id: number().required(),
      review_group_id: number().required(),
      client_owner_id: number().required(),
      review_month: number().required(),
      review_year: number().required(),
      review_type: number().required(),
      clients: array()
        .of(
          object({
            id: number().required(),
            first_name: string().required(),
            last_name: string().required(),
            email: string().nullable(),
          }),
        )
        .required(),
      case_data: object({
        id: number().required(),
        adviser_id: number().required(),
        status: number().required(),
      }).nullable(),
    }),
  )
  .required();

type GetClientReviewsDTO = InferType<typeof getClientReviewsValidationSchema>;

export default async (
  id: ClientId,
  queryParams?: QueryParams,
): Promise<Review[]> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/internal/v1/clients/${id}/review-slots`,
    queryParams,
  );

  const clientReviewsDTO: GetClientReviewsDTO =
    await getClientReviewsValidationSchema.validate(response);

  return clientReviewsDTO.map((clientReview) => ({
    id: clientReview.id,
    review_group_id: clientReview.review_group_id,
    client_owner_id: clientReview.client_owner_id,
    clients: clientReview.clients.map((clientDTO) => ({
      id: clientDTO.id as ClientId,
      firstName: clientDTO.first_name,
      lastName: clientDTO.last_name,
      email: clientDTO.email || null,
    })),
    reviewMonth: clientReview.review_month,
    reviewYear: clientReview.review_year,
    reviewType: new ReviewType(clientReview.review_type),
    caseData: clientReview.case_data
      ? {
          id: clientReview.case_data.id as CaseId,
          adviserId: clientReview.case_data.adviser_id,
          status: clientReview.case_data.status,
        }
      : null,
  }));
};
