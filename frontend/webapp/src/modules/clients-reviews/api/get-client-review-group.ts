import { ClientId } from '@aventur-shared/modules/clients';
import { apiClient } from '@aventur-shared/services/api';
import { InferType, array, boolean, number, object, string } from 'yup';
import { ReviewGroup } from '@modules/clients-reviews/models/client-review';
import {
  ReviewFrequency,
  mapReviewFrequencyToDomain,
} from '@aventur-shared/modules/clients/dtos';

import { QueryParams } from '@aventur-shared/types/api/QueryParams';
import { DateTime } from '@aventur-shared/utils/dateTime';

const getClientReviewGroupValidationSchema = object({
  review_group_id: number().required(),
  review_month: number().required(),
  review_frequency: string().nullable(),
  clients: array()
    .of(
      object({
        id: number().required(),
        first_name: string().required(),
        last_name: string().required(),
        email: string().nullable(),
      }),
    )
    .required(),
  next_review: string().nullable(),
  review_group_status: boolean().required(),
});

type GetClientReviewGroupDTO = InferType<
  typeof getClientReviewGroupValidationSchema
>;

export default async (
  id: ClientId,
  queryParams?: QueryParams,
): Promise<ReviewGroup> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/internal/v1/clients/${id}/review-group`,
    queryParams,
  );

  const clientReviewsDTO: GetClientReviewGroupDTO =
    await getClientReviewGroupValidationSchema.validate(response);

  return {
    id: clientReviewsDTO.review_group_id,
    clients: clientReviewsDTO.clients.map((clientDTO) => ({
      id: clientDTO.id as ClientId,
      firstName: clientDTO.first_name,
      lastName: clientDTO.last_name,
      email: clientDTO.email || null,
    })),
    reviewMonth: clientReviewsDTO.review_month,
    reviewFrequency: mapReviewFrequencyToDomain(
      clientReviewsDTO.review_frequency as ReviewFrequency,
    ),
    nextReviewDate: new DateTime(clientReviewsDTO.next_review).valueOf(),
    reviewGroupStatus: clientReviewsDTO.review_group_status,
  };
};
