import { InferType, array, bool, number, object, string } from 'yup';
import { apiClient } from '@aventur-shared/services/api';
import {
  ReviewFrequency,
  mapReviewFrequencyToDomain,
} from '@aventur-shared/modules/clients/dtos';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { ClientId } from '@aventur-shared/modules/clients';
import { ReviewGroup } from '@modules/clients-reviews/models/client-review';

interface PostReviewGroup {
  client_ids: Array<number>;
  review_frequency: string;
  review_month: number;
}

const getClientReviewGroupValidationSchema = object({
  review_group_id: number().required(),
  review_month: number().required(),
  review_frequency: string().nullable(),
  review_group_status: bool().required(),
  clients: array()
    .of(
      object({
        id: number().required(),
        first_name: string().required(),
        last_name: string().required(),
        email: string().nullable(),
      }),
    )
    .required(),
  next_review: string().nullable(),
});

type GetClientReviewGroupDTO = InferType<
  typeof getClientReviewGroupValidationSchema
>;

export default async (reviewGroup: PostReviewGroup): Promise<ReviewGroup> => {
  const response = await apiClient.post<PostReviewGroup, Promise<unknown>>(
    `/api/v1/review-group`,
    reviewGroup,
  );

  const clientReviewsDTO: GetClientReviewGroupDTO =
    await getClientReviewGroupValidationSchema.validate(response);

  return {
    id: clientReviewsDTO.review_group_id,
    clients: clientReviewsDTO.clients.map((clientDTO) => ({
      id: clientDTO.id as ClientId,
      firstName: clientDTO.first_name,
      lastName: clientDTO.last_name,
      email: clientDTO.email || null,
    })),
    reviewMonth: clientReviewsDTO.review_month,
    reviewFrequency: mapReviewFrequencyToDomain(
      clientReviewsDTO.review_frequency as ReviewFrequency,
    ),
    nextReviewDate: new DateTime(clientReviewsDTO.next_review).valueOf(),
    reviewGroupStatus: clientReviewsDTO.review_group_status,
  };
};
