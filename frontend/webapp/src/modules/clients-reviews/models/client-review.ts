import { Mailable } from '@aventur-shared/types/Mailable';
import { CaseId } from '@aventur-shared/modules/cases';
import { ClientId } from '@aventur-shared/modules/clients';
import { ReviewFrequency } from '@aventur-shared/modules/clients/models';
import { IReviewType } from '@aventur-shared/modules/cases/types/review-types';

interface ReviewClient extends Mailable {
  id: ClientId;
  firstName: string;
  lastName: string;
}

interface ReviewCase {
  id: CaseId;
  adviserId: number;
  status: number;
}

export interface Review {
  id: number;
  review_group_id: number;
  client_owner_id: number;
  clients: ReviewClient[];
  reviewMonth: number;
  reviewYear: number;
  reviewType: IReviewType;
  caseData: ReviewCase | null;
}

export interface ReviewsResponse {
  totalReviews: number;
  reviews: Review[];
}

export interface ReviewGroup {
  id: number | undefined;
  clients: ReviewClient[];
  reviewMonth: number;
  reviewFrequency: ReviewFrequency;
  nextReviewDate: Date | undefined;
  reviewGroupStatus: boolean;
}
