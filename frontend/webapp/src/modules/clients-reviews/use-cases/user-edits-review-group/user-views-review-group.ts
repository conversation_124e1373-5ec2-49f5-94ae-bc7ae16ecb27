import { ClientId } from '@aventur-shared/modules/clients';
import { getClientReviewGroup } from '@modules/clients-reviews/api';
import { ReviewGroup } from '@modules/clients-reviews/models/client-review';

export const userViewsReviewGroupAction = async (
  clientId: ClientId,
): Promise<ReviewGroup> | never => {
  try {
    return await getClientReviewGroup(clientId);
  } catch (e: unknown) {
    throw new Error('Could not fetch reviews.');
  }
};
