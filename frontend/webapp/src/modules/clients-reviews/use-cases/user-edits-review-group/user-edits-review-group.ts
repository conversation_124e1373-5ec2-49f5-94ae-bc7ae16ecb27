import { postReviewGroup } from '@modules/clients-reviews/api';
import { ReviewGroup } from '@modules/clients-reviews/models/client-review';
import { mapReviewFrequencyToBackend } from '@aventur-shared/modules/clients/dtos';
export const userEditsReviewGroupAction = async (
  reviewGroup,
): Promise<ReviewGroup> | never => {
  return await postReviewGroup({
    client_ids: reviewGroup.clientIds,
    review_month: reviewGroup.reviewMonth,
    review_frequency: mapReviewFrequencyToBackend(reviewGroup.reviewFrequency),
  });
};
