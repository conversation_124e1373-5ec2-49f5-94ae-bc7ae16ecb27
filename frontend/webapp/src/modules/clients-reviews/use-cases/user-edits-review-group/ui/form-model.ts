import * as yup from 'yup';
import { array, number } from 'yup';
import { validationMessages } from '@aventur-shared/utils/form';
import { ClientId } from '@aventur-shared/modules/clients';
import { ReviewFrequency } from '@aventur-shared/modules/clients/models';

export const validationSchema = yup.object().shape({
  clientIds: array()
    .min(1, 'At least 1 client has to be selected')
    .of(number().required())
    .required(),
  reviewFrequency: yup.string().required(),
  reviewMonth: yup
    .number()
    .nullable()
    .when(['reviewFrequency'], {
      is: (value: string) => value,
      then: (schema) =>
        schema
          .required(validationMessages.fieldRequiredMessage)
          .transform((value) => {
            return isNaN(value) ? null : value;
          })
          .nullable(),
    }),
});

export interface ReviewGroupFormValues {
  clientIds: ClientId[];
  reviewFrequency: ReviewFrequency | null;
  reviewMonth: number | null;
  reviewGroupStatus: boolean | null;
  nextReview: Date | null;
}
