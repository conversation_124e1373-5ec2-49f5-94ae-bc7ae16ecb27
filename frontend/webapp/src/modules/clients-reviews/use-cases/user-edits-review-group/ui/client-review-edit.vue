<template>
  <box>
    <box-section title="Next review" divider="bottom" />
    <box-section v-if="values.nextReview" class="flex justify-between">
      <span>{{ DateTime.fromJSDate(values.nextReview).monthLong }}</span>
      <span>{{ DateTime.fromJSDate(values.nextReview).year }}</span>
    </box-section>
    <box-section v-else>
      <div
        v-if="values.reviewGroupStatus == false"
        class="flex justify-between"
      >
        <div>No upcoming review</div>
        <div>
          <InformationCircleIcon
            v-tippy="
              'Only Review Groups containing an Active Ongoing client will have a review scheduled'
            "
            class="ml-1 size-5 text-gray-700 hover:cursor-help"
          />
        </div>
      </div>
      <div v-else class="flex justify-between">
        <div>Missing Review Setup</div>
        <div>
          <ExclamationCircleIcon
            class="size-5 text-red-500"
            aria-hidden="true"
          />
        </div>
      </div>
    </box-section>
  </box>

  <form @submit="onSubmit">
    <box class="mb-8">
      <box-section title="Review setup" divider="bottom" />

      <box-section v-if="loaded">
        <template v-if="values.clientIds">
          <multi-select-field
            label="Client"
            name="clientIds"
            :options="linkedClients"
          />
        </template>
        <select-field
          name="reviewFrequency"
          autocomplete="honorific-prefix"
          label="Frequency"
          :options="review_frequency_options"
        />
        <select-field
          name="reviewMonth"
          autocomplete="honorific-prefix"
          label="Months"
          :options="review_month_options"
          :disabled="!review_month_options.length"
        />
      </box-section>
      <box-section divider="top">
        <div class="flex justify-end">
          <custom-button
            type="submit"
            class="flex-1"
            theme="primary"
            :disabled="!meta.dirty || !meta.valid || isSubmitting"
            :is-busy="isSubmitting"
            >Save
          </custom-button>
        </div>
      </box-section>
    </box>
  </form>
</template>
<script setup lang="ts">
  import { onMounted, ref, toRef, watch } from 'vue';
  import { useForm } from 'vee-validate';
  import { DateTime } from 'luxon';
  import { Box, BoxSection } from '@modules/ui/box';
  import { Button as CustomButton } from '@modules/ui';
  import {
    MultiSelectField,
    SelectField,
  } from '@aventur-shared/components/form';
  import { Client } from '@aventur-shared/modules/clients';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { useLinkedClientList } from '@aventur-shared/composables/useLinkedClientList';
  import { ReviewGroup } from '@modules/clients-reviews/models/client-review';
  import { useReviewSetupOptions } from '@modules/clients/composables/useReviewGroup';
  import {
    userEditsReviewGroupAction,
    userViewsReviewGroupAction,
  } from '@modules/clients-reviews/use-cases/user-edits-review-group';
  import {
    ReviewGroupFormValues,
    validationSchema,
  } from '@modules/clients-reviews/use-cases/user-edits-review-group/ui/form-model';
  import {
    ExclamationCircleIcon,
    InformationCircleIcon,
  } from '@heroicons/vue/20/solid';

  const props = defineProps<{
    client: Client;
  }>();

  const toast = useToast();

  const loaded = ref(false);

  const { handleSubmit, values, meta, resetForm, setFieldValue, isSubmitting } =
    useForm<ReviewGroupFormValues>({
      validationSchema,
    });
  const { linkedClients } = useLinkedClientList(toRef(props, 'client'));
  const { review_frequency_options, review_month_options } =
    useReviewSetupOptions(toRef(values, 'reviewFrequency'));

  watch(
    () => values.reviewFrequency,
    (newValue, prevValue) => {
      if (prevValue && prevValue !== newValue) {
        setFieldValue('reviewMonth', null);
      }
    },
  );

  function resetData(data) {
    resetForm({
      values: {
        reviewFrequency: data.reviewFrequency ?? null,
        reviewMonth: data.reviewMonth ?? null,
        reviewGroupStatus: data.reviewGroupStatus ?? null,
        clientIds: data.clients.map((item) => item.id),
        nextReview: data.nextReviewDate ?? null,
      },
    });
  }

  onMounted(async () => {
    try {
      const data = await userViewsReviewGroupAction(props.client.id);
      resetData(data);
    } catch (e) {
      resetForm({
        values: {
          clientIds: [props.client.id],
        },
      });
    } finally {
      loaded.value = true;
    }
  });

  const emit = defineEmits<{
    (e: 'on-submit', reviewGroup: ReviewGroup): void;
  }>();

  const onSubmit = handleSubmit(async (formValues) => {
    try {
      const data = await userEditsReviewGroupAction(formValues);
      resetData(data);
      emit('on-submit', data);
      toast.success('Review group has been saved.');
    } catch (e) {
      toast.error(e as Error);
    }
  });
</script>
