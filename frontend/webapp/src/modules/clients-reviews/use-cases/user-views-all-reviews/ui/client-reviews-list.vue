<template>
  <div class="flex flex-col">
    <t-table>
      <t-head class="text-xs text-gray-500">
        <t-head-tr>
          <t-head-th> Review Date</t-head-th>
          <t-head-th> Review Type</t-head-th>
          <t-head-th> Clients</t-head-th>
          <t-head-th>&nbsp;</t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="review in reviews"
          :key="review.id"
          class="cursor-pointer hover:bg-gray-50"
        >
          <t-body-td>
            {{ formatReviewDate(review.reviewMonth, review.reviewYear) }}
          </t-body-td>
          <t-body-td>
            {{ review.reviewType.toLabel() }}
          </t-body-td>
          <t-body-td>
            <p
              v-for="client in review.clients"
              :key="client.id"
              class="w-fit max-w-32 truncate border-b border-dotted border-b-black hover:border-solid"
            >
              <router-link
                :to="{
                  name: 'client-overview',
                  params: { id: client.id },
                }"
              >
                {{ `${client.firstName} ${client.lastName}` }}
              </router-link>
            </p>
          </t-body-td>
          <t-body-td>
            <div v-if="!review.caseData">
              <div
                v-if="
                  DateTime.fromObject({
                    month: review.reviewMonth,
                    year: review.reviewYear,
                  }).diffNow('month').months <= 3
                "
              >
                <create-case-modal
                  :passed-in-review-slot-id="review.id"
                  :passed-in-adviser-id="review.client_owner_id"
                  :passed-in-case-type="review.reviewType"
                  :clients="review.clients"
                  :open-button-title="'Create\r\ncase'"
                  :button-class="''"
                />
              </div>
              <div v-else>---</div>
            </div>
            <div v-else>
              <button
                type="button"
                class="bg-secondary rounded-md px-5 py-2 text-white"
                style="width: 80%"
                @click="
                  handleViewCaseClick(review.clients[0].id, review.caseData.id)
                "
              >
                View Case
              </button>
            </div>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import { DateTime } from 'luxon';
  import { useRouter } from 'vue-router';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui/table';
  import { Review } from '@modules/clients-reviews/models';
  import CreateCaseModal from '@modules/clients-cases/use-cases/user-creates-case/ui/create-case-modal/create-case-modal.vue';
  import { CaseId } from '@aventur-shared/modules/cases';

  const router = useRouter();

  defineProps<{
    reviews: Review[];
  }>();

  const formatReviewDate = (month: number, year: number): string => {
    const date = new Date(year, month - 1);
    const options: Intl.DateTimeFormatOptions = {
      month: 'long',
      year: 'numeric',
    };
    return date.toLocaleString('default', options);
  };

  const handleViewCaseClick = async (clientId: number, caseId: CaseId) => {
    await router.push({
      name: 'client-case',
      params: {
        id: clientId,
        caseId: caseId,
      },
    });
  };
</script>
