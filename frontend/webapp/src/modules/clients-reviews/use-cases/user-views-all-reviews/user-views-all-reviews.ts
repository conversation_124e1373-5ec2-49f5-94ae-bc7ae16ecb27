import { ClientId } from '@aventur-shared/modules/clients';
import { Review } from '@/modules/clients-reviews/models';
import { getClientReviews } from '@modules/clients-reviews/api';

export const userViewsAllReviewsAction = async (
  clientId: ClientId,
): Promise<{ reviews: Review[] }> | never => {
  try {
    const allReviews = await getClientReviews(clientId);

    return { reviews: allReviews };
  } catch (e: unknown) {
    throw new Error('Could not fetch reviews.');
  }
};
