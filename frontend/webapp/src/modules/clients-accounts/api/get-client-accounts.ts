import { InferType, array, number, object, string } from 'yup';
import { apiClient } from '@aventur-shared/services/api';
import { QueryParams } from '@aventur-shared/types/api';
import { DateTime } from '@aventur-shared/utils/dateTime';
import { type ClientId } from '@aventur-shared/modules/clients';
import { ClientAccountListItem } from '@modules/clients-accounts/models';

const getClientAccountsValidationSchema = array()
  .of(
    object({
      id: number().required(),
      provider: string().required(),
      type: string().required(),
      acc_no: string().nullable(),
      sub_acc_no: string().nullable(),
      created: string().required(),
      status: object({
        id: number().required(),
        name: string().required(),
      }).required(),
    }),
  )
  .default([]);

type GetClientAccountsDTO = InferType<typeof getClientAccountsValidationSchema>;

export default async (
  id: ClientId,
  queryParams?: QueryParams,
): Promise<ClientAccountListItem[]> => {
  const response = await apiClient.get<Promise<unknown>>(
    `/api/v1/clients/${id}/accounts`,
    queryParams,
  );

  const clientAccountsDTO: GetClientAccountsDTO =
    await getClientAccountsValidationSchema.validate(response);

  return clientAccountsDTO.map((clientAccount) => ({
    id: clientAccount.id,
    providerName: clientAccount.provider,
    type: clientAccount.type,
    accountNumber: clientAccount.acc_no ?? null,
    subAccountNumber: clientAccount.sub_acc_no ?? null,
    created: new DateTime(clientAccount.created),
    status: clientAccount.status,
  }));
};
