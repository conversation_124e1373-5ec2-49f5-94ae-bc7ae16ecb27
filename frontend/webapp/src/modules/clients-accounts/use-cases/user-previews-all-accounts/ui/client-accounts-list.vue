<template>
  <div class="flex flex-col">
    <t-table>
      <t-head class="text-xs text-gray-500">
        <t-head-tr>
          <t-head-th> ID </t-head-th>
          <t-head-th> Provider </t-head-th>
          <t-head-th> Type </t-head-th>
          <t-head-th> Acc no. </t-head-th>
          <t-head-th> Sub acc no. </t-head-th>
          <t-head-th> Created </t-head-th>
          <t-head-th> Status </t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr
          v-for="item in accounts"
          :key="item.id"
          class="cursor-pointer hover:bg-gray-50"
        >
          <t-body-td>
            {{ item.id }}
          </t-body-td>
          <t-body-td>
            <router-link
              class="border-b border-dotted border-b-black hover:border-solid"
              :to="{
                name: 'account-details-index',
                params: {
                  accountId: item.id,
                },
              }"
            >
              {{ item.providerName }}
            </router-link>
          </t-body-td>
          <t-body-td>
            {{ item.type.toString() }}
          </t-body-td>
          <t-body-td>
            {{ item.accountNumber }}
          </t-body-td>
          <t-body-td>
            {{ item.subAccountNumber }}
          </t-body-td>
          <t-body-td class="whitespace-nowrap">
            {{ item.created.formatWithShortName() }}
          </t-body-td>
          <t-body-td>
            {{ item.status.name }}
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
  </div>
</template>

<script setup lang="ts">
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui/table';
  import { ClientAccountListItem } from '@modules/clients-accounts';

  defineProps<{
    accounts: ClientAccountListItem[];
  }>();
</script>
