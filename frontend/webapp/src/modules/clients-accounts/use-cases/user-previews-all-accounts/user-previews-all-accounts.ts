import { getClientAccounts } from '@modules/clients-accounts/api';
import { ClientId } from '@aventur-shared/modules/clients';
import { ClientAccountListItem } from '@/modules/clients-accounts/models';

export const userPreviewsAllAccountsAction = async (
  clientId: ClientId,
): Promise<{ accounts: ClientAccountListItem[] }> | never => {
  try {
    const allAccounts = await getClientAccounts(clientId);

    return { accounts: allAccounts };
  } catch (e: unknown) {
    throw new Error('Could not fetch accounts.');
  }
};
