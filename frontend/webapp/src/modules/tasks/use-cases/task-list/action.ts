import { castArray } from 'lodash';
import { ValidationError } from 'yup';
import { TaskSlug } from '@/refdata/tasks/task-slug';
import { Advisor } from '@aventur-shared/modules/advisors';
import getTasks from '@aventur-shared/modules/tasks/api/get-tasks';
import { TaskStatusEnum } from '@aventur-shared/modules/tasks/models/task-status';
import { FetchTaskDataNotValidatedCorrectly } from '@aventur-shared/modules/cases/api/errors';

type TaskSlugString = string; // TODO: TaskSlug type

interface ActionProps {
  query: Partial<{
    page: string;
    status: TaskStatusEnum;
    'slugs[]': TaskSlug[];
    assignee_id: Advisor['id'];
    assignee_group: string;
    include_undated: string;
  }>;
}

interface QueryString
  extends Partial<{
    page: string;
    'slugs[]': TaskSlugString;
    status: TaskStatusEnum;
    assignee_id: Advisor['id'];
    assignee_group: string;
    include_undated: string;
  }> {}

const prepareQueryString = (
  query: Partial<ActionProps['query']>,
): QueryString => {
  const queryParams = {
    page: query.page,
    status: query.status,
    'slugs[]': castArray(query['slugs[]'] ?? []).join(','),
    assignee_id: Number(query.assignee_id),
    assignee_group: query.assignee_group,
    include_undated: query.include_undated,
  };
  const filterEmptyQueryParts = (
    queryPart: [string, string | number | undefined],
  ) => queryPart[1];
  const queryEntries = Object.entries({
    ...queryParams,
  }).filter(filterEmptyQueryParts);

  return Object.fromEntries(queryEntries);
};

export const getTasksAction = async ({ query }: ActionProps) => {
  try {
    return await getTasks(prepareQueryString(query));
  } catch (e: unknown) {
    if (e instanceof ValidationError) {
      throw new FetchTaskDataNotValidatedCorrectly(e.message, e.cause);
    }
    if (e instanceof Error) {
      throw e;
    }
    throw new Error('Something went wrong during case fetching.');
  }
};
