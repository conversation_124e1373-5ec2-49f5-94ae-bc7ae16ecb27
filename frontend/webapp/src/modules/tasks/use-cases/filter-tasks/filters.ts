import { AdvisorId } from '@aventur-shared/modules/advisors';
import { TaskStatusEnum } from '@aventur-shared/modules/tasks/models/task-status';
import { FilterRulesType } from '@aventur-shared/utils/table/use-table-filters';
import { TaskSlug } from '@/refdata/tasks/task-slug';

export type Filters = Partial<{
  assignee_id: AdvisorId;
  assignee_group: string;
  status: TaskStatusEnum;
  'slugs[]': TaskSlug[];
  include_undated: string;
}>;

type ExclusiveFilters = keyof Pick<Filters, 'assignee_id' | 'assignee_group'>;

export const FILTER_RULES: FilterRulesType<ExclusiveFilters> = new Map([
  ['assignee_id', [{ assignee_group: () => null }]],
  ['assignee_group', [{ assignee_id: () => null }]],
]);
