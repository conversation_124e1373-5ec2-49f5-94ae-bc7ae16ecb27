<template>
  <div>
    <multi-select-field
      name=""
      label=""
      placeholder="Select task"
      class="mb-0"
      :value="modelValue"
      :options="options"
      :can-clear="true"
      @on-select="(slugs) => $emit('update:modelValue', slugs)"
    />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { MultiSelectField } from '@aventur-shared/components/form';
  import { TaskSlug, allSlugs } from '@/refdata/tasks/task-slug';
  import { capitalCase } from 'change-case-all';

  defineEmits<{
    (e: 'update:modelValue', slugs: TaskSlug[]): void;
  }>();
  withDefaults(
    defineProps<{
      modelValue?: TaskSlug[];
    }>(),
    {
      modelValue: () => [],
    },
  );

  const options = computed(() =>
    allSlugs.map((slug) => ({
      value: slug,
      label: capitalCase(slug),
    })),
  );
</script>
