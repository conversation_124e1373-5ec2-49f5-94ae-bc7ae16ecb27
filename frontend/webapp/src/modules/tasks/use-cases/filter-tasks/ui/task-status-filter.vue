<template>
  <div>
    <select-field
      label="Status"
      name="task-status-filter"
      :value="modelValue"
      :can-clear="true"
      :searchable="false"
      :options="taskStatusOptions"
      @on-select="(status) => $emit('update:modelValue', status)"
    />
  </div>
</template>

<script setup lang="ts">
  import { SelectField } from '@aventur-shared/components/form';
  import {
    TaskStatusEnum,
    taskStatusOptions,
  } from '@aventur-shared/modules/tasks/models/task-status';

  defineEmits(['update:modelValue']);
  defineProps<{
    modelValue?: TaskStatusEnum;
  }>();
</script>
