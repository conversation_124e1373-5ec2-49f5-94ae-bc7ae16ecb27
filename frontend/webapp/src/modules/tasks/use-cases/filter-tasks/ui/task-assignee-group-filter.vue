<template>
  <div>
    <select-field
      label="Task Assignee Group"
      name="assignee-group-filter"
      :value="modelValue"
      :can-clear="true"
      :options="getAdvisorsRoles"
      @on-select="(assigneeGroup) => $emit('update:modelValue', assigneeGroup)"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from 'pinia';
  import { useAdvisorsStore } from '@aventur-shared/modules/advisors';
  import { SelectField } from '@aventur-shared/components/form';

  defineEmits(['update:modelValue']);
  defineProps<{
    modelValue?: string;
  }>();

  const { getAdvisorsRoles } = storeToRefs(useAdvisorsStore());
</script>
