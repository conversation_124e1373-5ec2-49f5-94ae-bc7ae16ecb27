<template>
  <div>
    <select-field
      label="Task Assignee"
      name="task-assignee-filter"
      :value="modelValue"
      :can-clear="true"
      :options="getAdvisorsSelectOptions(activeAdvisors)"
      @on-select="(advisorId) => $emit('update:modelValue', advisorId)"
    />
  </div>
</template>

<script setup lang="ts">
  import { SelectField } from '@aventur-shared/components/form';
  import {
    advisorsProvider,
    getAdvisorsSelectOptions,
  } from '@aventur-shared/modules/advisors';
  import { onMounted } from 'vue';

  defineEmits(['update:modelValue']);
  defineProps<{
    modelValue?: number;
  }>();

  const { activeAdvisors } = advisorsProvider().provide();

  onMounted(async () => {
    await advisorsProvider().create();
  });
</script>
