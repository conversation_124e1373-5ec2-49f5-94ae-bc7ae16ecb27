import { filesize } from 'filesize';
import { onUnmounted } from 'vue';

import Uppy, {
  Meta,
  UploadResult as UploadResultBase,
  UppyFile as UppyFileBase,
} from '@uppy/core';
import DragDrop from '@uppy/drag-drop';
import ProgressBar from '@uppy/progress-bar';
import type { AwsBody } from '@uppy/aws-s3';

import '@uppy/drag-drop/dist/style.min.css';
import '@uppy/progress-bar/dist/style.min.css';

import { useAppStore } from '@/stores/appStore';
import { AuthorizationMode } from '@modules/documents/api/sign-s3-upload';
import { DocumentMeta } from '@modules/documents/types/Document';
//

export type UploadResult = UploadResultBase<DocumentMeta & Meta, AwsBody>;
export type UppyFile = UppyFileBase<DocumentMeta & Meta, AwsBody>;

export const useUppy = (
  uppyId: string,
  authorizationMode: AuthorizationMode,
) => {
  const { isDevMode } = useAppStore();

  const maxNumberOfFiles =
    authorizationMode === AuthorizationMode.SignedRequest ? 1 : 3;

  const maxFileSize = 10e6; // 10Mb in bytes

  const uppy = new Uppy<DocumentMeta & Meta, AwsBody>({
    id: `Uppy${uppyId}`,
    autoProceed: false,
    debug: isDevMode,
    meta: { uploadKey: null },
    restrictions: {
      maxNumberOfFiles,
      maxFileSize,
      allowedFileTypes: [
        'image/*',
        'images/heic',
        'images/heif',
        'text/scv',
        'application/pdf',
        'application/msword',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ],
    },
  })
    .use(DragDrop, {
      id: `DragDrop${uppyId}`,
      target: '[data-dropzone]',
      height: '150px',
      note: `Images and PDF only. Max ${maxNumberOfFiles} file(s), up to ${filesize(maxFileSize)} each.`,
    })
    .use(ProgressBar, { target: '[data-progress-bar]' });

  /** Unregister Uppy plugins and delete instance **/
  onUnmounted(() => {
    uppy.destroy();
  });

  return uppy;
};
