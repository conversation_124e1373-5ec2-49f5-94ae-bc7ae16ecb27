import { EventBusKey } from '@vueuse/core';

import { CaseId } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';
import { ClientId } from '@aventur-shared/modules/clients';
//

export type UploadKeys = Record<string, string>[];
export type DocumentMeta = { uploadKey: string | null };

export const documentsEventKey: EventBusKey<{ name: DocumentsEventType }> =
  Symbol('documents');

export const DocumentsEvent = {
  SUBMIT: 'documents:submit',
  UPDATE: 'documents:update',
} as const;

export type DocumentsEventType =
  (typeof DocumentsEvent)[keyof typeof DocumentsEvent];

export type FileType =
  | 'pdf'
  | 'png'
  | 'jpeg'
  | 'word'
  | 'excel'
  | 'tiff'
  | 'webp'
  | 'csv';
export type DocumentLink = {
  id: string;
  name: string;
  type: FileType;
  size: number;
  uploadedBy: number;
  uploadedAt: string;
  clientAccess: boolean;
  url: string;
};

export type DocumentContext = Partial<{
  caseId: CaseId;
  taskSlug: string;
  taskId: TaskId;
  goalId: number;
  clientId: ClientId[];
  holdingId: number[];
}>;
