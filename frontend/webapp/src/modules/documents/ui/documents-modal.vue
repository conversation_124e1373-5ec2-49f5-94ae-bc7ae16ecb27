<template>
  <slide-over :config="{ maxWidth: 'screen-lg' }">
    <template #open-button="{ open }">
      <slot name="activator" :open="open" />
    </template>
    <template #header>
      <slot name="header">
        <box-section>
          <div class="flex flex-col items-start">
            <h1 class="text-lg text-[#7A8796]">Documents</h1>
          </div>
        </box-section>
      </slot>
    </template>
    <template #default>
      <box-section class="w-full rounded-lg bg-white">
        <box-title class="mb-4">Documents</box-title>
        <div class="flex flex-wrap items-center justify-between">
          <template v-if="!hasDocuments && isReadOnly">
            <div class="flex flex-col justify-center">
              <h6 class="font-medium">Task document requirements</h6>
              <p class="text-sm text-gray-400">
                {{ documentsNotRequiredReason }}
              </p>
            </div>
          </template>
          <template v-else-if="!isReadOnly">
            <div class="flex flex-col justify-center">
              <h6 class="font-medium">Task document requirements</h6>
              <p class="text-sm text-gray-400">
                {{
                  documentsNotRequired
                    ? 'Please provide a reason for not including task documents'
                    : 'At least one document must be uploaded for this task'
                }}
              </p>
            </div>
            <SwitchGroup as="div" class="flex flex-col content-start items-end">
              <SwitchLabel as="span">
                <span class="leading-tight text-gray-500">
                  No documents required?
                </span>
              </SwitchLabel>
              <Switch
                v-model="documentsNotRequired"
                :disabled="hasDocuments"
                :class="[
                  hasDocuments ? 'cursor-not-allowed' : 'cursor-pointer',
                  documentsNotRequired ? 'bg-primary-900' : 'bg-gray-200',
                  'focus:ring-primary-900 relative inline-flex h-6 w-11 shrink-0 rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2',
                ]"
              >
                <span
                  aria-hidden="true"
                  :class="[
                    documentsNotRequired ? 'translate-x-5' : 'translate-x-0',
                    'pointer-events-none inline-block size-5 rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                  ]"
                />
              </Switch>
            </SwitchGroup>
            <TextAreaField
              v-if="documentsNotRequired"
              id="documents-not-required-reason"
              v-model="documentsNotRequiredReason"
              name="documents-not-required-reason"
              label=""
              class="basis-full py-3"
              rows="5"
              placeholder="Max 200 characters"
            />
          </template>
        </div>
        <template v-if="!documentsNotRequired">
          <documents
            :documents="documents"
            :documents-not-required="documentsNotRequired"
            :is-read-only="isReadOnly"
            @documents-uploaded="handleDocumentsUploaded"
            @documents-deleted="handleDocumentsDeleted"
          />
        </template>
        <div v-if="isReadOnly && !hasDocuments">
          <p class="my-10 text-gray-600">No documents have been uploaded.</p>
        </div>
      </box-section>
    </template>

    <template #footer="{ close }">
      <div v-if="!props.isReadOnly" class="flex justify-between space-x-3">
        <custom-button
          type="button"
          theme="primary-ghost"
          :disabled="false"
          @on-click="close"
        >
          Close
        </custom-button>
        <custom-button
          type="button"
          theme="primary"
          :is-busy="false"
          @on-click="onSubmit(close)"
          >Submit & Save</custom-button
        >
      </div>
    </template>
  </slide-over>
</template>

<script setup lang="ts">
  import { computed, toRef, watch } from 'vue';
  import { useEventBus } from '@vueuse/core';
  import { Switch, SwitchGroup, SwitchLabel } from '@headlessui/vue';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { Task } from '@aventur-shared/modules/tasks';
  import { CleanTextAreaField as TextAreaField } from '@aventur-shared/components/form/fields/clean-fields';
  import { useTaskDocuments } from '@modules/documents/useTaskDocuments';

  import {
    BoxSection,
    BoxTitle,
    Button as CustomButton,
    SlideOver,
  } from '@modules/ui';
  import {
    DocumentContext,
    DocumentLink,
    DocumentsEvent,
    DocumentsEventType,
    UploadKeys,
    documentsEventKey,
  } from '@modules/documents/types/Document';
  import {
    createDocuments,
    updateTaskDocumentsRequirement,
  } from '@modules/documents/api';
  import Documents from '@modules/documents/ui/documents.vue';
  import { useClientCase } from '@modules/clients-cases/store/client-case-store';

  const toast = useToast();

  const props = withDefaults(
    defineProps<{
      documents: DocumentLink[];
      documentsNotRequired: boolean;
      documentsNotRequiredReason?: string | null;
      documentsContext: DocumentContext;
      isReadOnly?: boolean;
    }>(),
    {
      documentsNotRequiredReason: null,
      isReadOnly: false,
    },
  );

  const documentsNotRequired = toRef(props.documentsNotRequired);
  const documentsNotRequiredReason = toRef(props.documentsNotRequiredReason);
  const hasDocuments = computed(() => !!props.documents.length);
  const { ensureTaskDocumentsContext } = useTaskDocuments(
    props.documentsContext,
  );

  const eventBus = useEventBus<DocumentsEventType>(documentsEventKey);
  const emitDocumentsEvent = (eventName: DocumentsEventType) => {
    eventBus.emit(eventName);
  };

  const emit = defineEmits<{
    (e: 'documents-updated'): void;
  }>();

  const handleDocumentsUploaded = async (data: {
    files: UploadKeys;
    clientAccess: boolean;
  }) => {
    const { files, clientAccess } = data;
    try {
      if (files.length) {
        await createDocuments(
          files,
          ensureTaskDocumentsContext(props.documentsContext),
          clientAccess,
        );
      }
      emit('documents-updated');
      emitDocumentsEvent(DocumentsEvent.UPDATE);
    } catch (e) {
      toast.error(e as Error);
    }
  };

  watch(documentsNotRequired, (value) => {
    if (!value) {
      documentsNotRequiredReason.value = null;
    }
  });

  const handleDocumentsDeleted = () => {
    emit('documents-updated');
    emitDocumentsEvent(DocumentsEvent.UPDATE);
  };

  const onSubmit = async (cb: () => void) => {
    try {
      if (documentsNotRequired.value && !documentsNotRequiredReason.value) {
        throw new Error(
          'Please provide a reason for not including task documents',
        );
      }

      const { caseId, taskId, taskSlug } = ensureTaskDocumentsContext(
        props.documentsContext,
      );
      await updateTaskDocumentsRequirement(
        caseId,
        taskSlug,
        taskId,
        documentsNotRequiredReason.value,
      );

      const { getCaseTask, updateCaseTask } = useClientCase();

      const task = getCaseTask(taskSlug) as Task;
      const updatedTask = {
        ...task,
        subTasks: task.subTasks.map((st) => ({
          ...st,
          noDocumentsReason:
            st.taskId === taskId
              ? documentsNotRequiredReason.value
              : st.noDocumentsReason,
        })),
      };
      updateCaseTask(taskSlug, updatedTask);

      if (documentsNotRequired.value) {
        toast.success('Task documents requirement has been updated.');
        return cb && cb();
      }

      emitDocumentsEvent(DocumentsEvent.SUBMIT);
    } catch (e) {
      toast.error(e as Error);
    }
  };
</script>
