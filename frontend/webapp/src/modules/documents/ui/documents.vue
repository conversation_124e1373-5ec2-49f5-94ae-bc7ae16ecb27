<template>
  <div>
    <documents-dropzone
      v-if="!documentsNotRequired && showDropzone"
      @upload-complete="handleUploadComplete"
    />
    <documents-list
      v-else-if="hasDocuments"
      :is-read-only="isReadOnly"
      :documents="documents"
      @document-deleted="handleDocumentDelete"
    />
    <div
      v-if="hasDocuments && !isReadOnly"
      class="my-4 flex items-center justify-end"
    >
      <custom-button
        type="button"
        theme="secondary"
        class="flex items-center justify-center gap-x-2 text-sm"
        @click="() => (showDropzone = !showDropzone)"
      >
        <template v-if="showDropzone">
          <ListBulletIcon class="size-4" />Show documents
        </template>
        <template v-else> <PlusIcon class="size-4" />Add documents </template>
      </custom-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { ListBulletIcon, PlusIcon } from '@heroicons/vue/20/solid';
  import { Button as CustomButton } from '@modules/ui';
  import { DocumentLink, UploadKeys } from '@modules/documents/types/Document';
  import DocumentsDropzone from '@modules/documents/ui/documents-dropzone.vue';
  import DocumentsList from '@modules/documents/ui/documents-list.vue';

  const props = defineProps<{
    documents: DocumentLink[];
    documentsNotRequired: boolean;
    isReadOnly: boolean;
  }>();

  const emit = defineEmits<{
    (
      e: 'documents-uploaded',
      data: { files: UploadKeys; clientAccess: boolean },
    ): void;
    (e: 'documents-deleted'): void;
  }>();

  const showDropzone = ref(!props.documents.length && !props.isReadOnly);
  const hasDocuments = computed(() => !!props.documents.length);

  watch(hasDocuments, (value) => {
    !value && (showDropzone.value = true);
  });

  const handleUploadComplete = (data: {
    files: UploadKeys;
    clientAccess: boolean;
  }) =>
    emit('documents-uploaded', {
      files: data.files,
      clientAccess: data.clientAccess,
    });

  const handleDocumentDelete = () => emit('documents-deleted');
</script>
