<template>
  <div class="flex flex-col space-y-2">
    <div data-dropzone class="my-4 bg-gray-100"></div>
    <div data-progress-bar></div>
    <t-table v-if="hasFiles" class="border-separate border-spacing-y-2">
      <t-head>
        <t-head-tr class="bg-gray-100">
          <t-head-th> Document </t-head-th>
          <t-head-th> Type </t-head-th>
          <t-head-th> Size </t-head-th>
          <t-head-th> Status </t-head-th>
          <t-head-th>&nbsp;</t-head-th>
        </t-head-tr>
      </t-head>
      <t-body>
        <t-body-tr v-for="(file, index) in files" :key="index">
          <t-body-td>
            {{ file.name }}
          </t-body-td>
          <t-body-td>
            {{ file.type }}
          </t-body-td>
          <t-body-td>
            {{ filesize(file.size as number) }}
          </t-body-td>
          <t-body-td>
            {{
              file.progress.uploadComplete
                ? 'Completed'
                : file.progress.uploadStarted
                  ? `${file.progress.percentage}%`
                  : 'Queued'
            }}
          </t-body-td>
          <t-body-td class="text-right">
            <custom-button
              v-if="!file.progress.uploadComplete"
              type="button"
              theme="text-like"
              :is-busy="false"
              @on-click="handleRemoveFile(file.id)"
            >
              <XMarkIcon class="size-6 text-gray-400 hover:text-gray-600" />
            </custom-button>
          </t-body-td>
        </t-body-tr>
      </t-body>
    </t-table>
    <div v-show="hasQueuedFiles" class="flex items-center justify-start">
      <span class="flex items-center gap-2">
        <CleanCheckboxInput
          :model-value="clientAccess"
          name="clientAccess"
          :checked="false"
          @change="(e) => (clientAccess = !!e.target.value)"
        />
        <label for="toggleValue" class="text-gray-400"
          >Check to enable access by the client(s)</label
        >
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { filter, map } from 'lodash';
  import { v4 as uuidv4 } from 'uuid';
  import { filesize } from 'filesize';
  import { computed, onMounted, ref, unref } from 'vue';
  import { useEventBus } from '@vueuse/core';
  import { XMarkIcon } from '@heroicons/vue/20/solid';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { Button as CustomButton } from '@modules/ui';
  import {
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';

  import XHR from '@uppy/xhr-upload';
  import AwsS3 from '@uppy/aws-s3';

  import '@uppy/core/dist/style.min.css';

  import {
    DocumentsEvent,
    DocumentsEventType,
    UploadKeys,
    documentsEventKey,
  } from '@modules/documents/types/Document';
  import { signUpload } from '@modules/documents/api';
  import { AuthorizationMode } from '@modules/documents/api/sign-s3-upload';
  import {
    type UploadResult,
    type UppyFile,
    useUppy,
  } from '@modules/documents/useUppy';
  import { CleanCheckboxInput } from '@aventur-shared/components/form/fields/clean-fields';
  import * as Sentry from '@sentry/browser';

  const toast = useToast();
  const eventBus = useEventBus<DocumentsEventType>(documentsEventKey);

  const files = ref<UppyFile[]>([]);
  const clientAccess = ref(false);
  const xhrUploadKey = ref<string | null>(null);

  const hasFiles = computed(() => !!files.value.length);
  const hasQueuedFiles = computed(
    () => !!files.value.filter((file) => !file.progress.uploadComplete).length,
  );

  let uppy: ReturnType<typeof useUppy>;
  const uppyId = new Date().valueOf();
  const authorizationMode = computed(() => AuthorizationMode.SignedRequest);

  const _setXhrUploadKey = (key: string | null) => {
    xhrUploadKey.value = key;
  };

  const _setXhrUploadUrl = (url: string) => {
    uppy.getPlugin(`XHR${uppyId}`)?.setOptions({
      endpoint: url,
    });
  };

  const handleRemoveFile = (fileId: string) => {
    uppy?.removeFile(fileId);
    files.value = uppy?.getFiles();
  };

  const emit = defineEmits<{
    (
      e: 'upload-complete',
      data: { files: UploadKeys; clientAccess: boolean },
    ): void;
  }>();

  onMounted(async () => {
    uppy = useUppy(String(uppyId), authorizationMode.value);
    try {
      /** Choose upload authorization mode **/
      if (authorizationMode.value === AuthorizationMode.Credentials) {
        const { url, ...credentials } =
          await signUpload<AuthorizationMode.Credentials>(
            unref(authorizationMode),
          );

        uppy.use(AwsS3, {
          id: `AwsS3${uppyId}`,
          async getTemporarySecurityCredentials() {
            // TODO: Revalidate credentials
            return Promise.resolve(credentials);
          },
          shouldUseMultipart: false,
          getUploadParameters(file) {
            const uploadKey = uuidv4();
            uppy.setFileMeta(file.id, { uploadKey });
            return {
              method: 'PUT',
              url: `${url}/${uploadKey}`,
              headers: { 'Content-Type': file.type },
            };
          },
        });
      }

      if (authorizationMode.value === AuthorizationMode.SignedRequest) {
        const { url, file_id } =
          await signUpload<AuthorizationMode.SignedRequest>(
            unref(authorizationMode),
          );

        _setXhrUploadKey(file_id);

        uppy.use(XHR, {
          id: `XHR${uppyId}`,
          method: 'PUT',
          endpoint: url,
          formData: false,
          headers: (file) => ({ 'Content-Type': file.type }),
          shouldRetry: (_xhr: XMLHttpRequest) => false,
          getResponseData(_xhr: XMLHttpRequest) {
            return Promise.resolve({});
          },
          // The following code is rather hacky and works only because a single file is uploaded,
          // but XHRUpload+S3 doesn't emit `upload-success` event for some reason (AWS S3 plugin works fine).
          // This might be a localstack S3 issue or incorrect Uppy setup.
          async onAfterResponse(xhr) {
            if (xhr.status === 200) {
              const file = uppy.getFiles()[0];
              const currentProgress = file.progress;
              uppy.setFileState(file.id, {
                progress: {
                  ...currentProgress,
                  uploadComplete: true,
                },
              });
              files.value = uppy.getFiles();
            }
          },
        });

        uppy.on('files-added', (added_files: UppyFile[]) => {
          added_files.forEach((file) => {
            uppy.setFileMeta(file.id, { uploadKey: xhrUploadKey.value });
          });
        });
      }
    } catch (e) {
      toast.error(e as Error);
    }

    /** Register Uppy common event listeners **/
    uppy.on('files-added', (added_files: UppyFile[]) => {
      if (added_files.length === 0) return;
      files.value = uppy.getFiles();
    });

    uppy.on(
      'restriction-failed',
      (_file: UppyFile | undefined, error: Error) => {
        toast.error(error);
      },
    );

    uppy.on('upload-progress', (file, progress) => {
      files.value = uppy.getFiles();
    });

    uppy.on('upload-success', (_file: UppyFile | undefined) => {
      files.value = uppy.getFiles();
    });

    uppy.on('upload-error', (_file: UppyFile | undefined, error: Error) => {
      toast.error(error);
    });

    uppy.on('complete', async (result: UploadResult) => {
      if (result.failed?.length !== 0) {
        toast.error("Some files couldn't be uploaded.");
      }

      // sign upload again to get new url and reconfigure uppy XHR uploader
      uppy.clear();
      try {
        const { url, file_id } =
          await signUpload<AuthorizationMode.SignedRequest>(
            unref(authorizationMode),
          );

        _setXhrUploadKey(file_id);
        _setXhrUploadUrl(url);
      } catch (e) {
        Sentry.captureException(e);
      }

      emit('upload-complete', {
        files: map(filter(result.successful, 'meta.uploadKey'), (file) => ({
          [file.meta.uploadKey as string]: file.type,
        })),
        clientAccess: clientAccess.value,
      });
    });
  });

  eventBus.on(async (e) => {
    if (e === DocumentsEvent.SUBMIT) {
      await uppy.upload();
    }
  });
</script>
