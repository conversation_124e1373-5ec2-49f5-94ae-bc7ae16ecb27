<template>
  <t-table class="border-separate border-spacing-y-2">
    <t-head>
      <t-head-tr class="bg-gray-100">
        <t-head-th> Document </t-head-th>
        <t-head-th> Uploaded </t-head-th>
        <t-head-th class="flex items-center justify-center">
          Client Visibility
          <InformationCircleIcon
            v-tippy="'Client access to the document'"
            class="ml-1 size-5 text-gray-600 hover:cursor-help"
          />
        </t-head-th>
        <t-head-th>&nbsp;</t-head-th>
      </t-head-tr>
    </t-head>
    <t-body>
      <t-body-tr v-for="(document, index) in documents" :key="index">
        <t-body-td>
          <a
            :href="document.url"
            target="_blank"
            class="text-secondary hover:underline"
            >{{ document.name }}</a
          >
          <p class="whitespace-nowrap text-sm text-gray-400">
            {{ document.type.toUpperCase() }}&nbsp;&middot;&nbsp;{{
              filesize(document.size)
            }}
          </p>
        </t-body-td>
        <t-body-td>
          <p class="whitespace-nowrap">
            {{ formatName(getAdvisorById(document.uploadedBy) as Person) }}
          </p>
          <p class="whitespace-nowrap text-sm text-gray-400">
            {{ formatDatetimeStringForView(document.uploadedAt) }}
          </p>
        </t-body-td>
        <t-body-td>
          <p class="flex items-center justify-center">
            <CheckIcon
              v-if="document.clientAccess"
              class="text-primary flex size-4"
            />
          </p>
        </t-body-td>
        <t-body-td class="text-right">
          <custom-button
            v-if="!isReadOnly"
            type="button"
            theme="text-like"
            :is-busy="false"
            @on-click="handleRemoveDocument(document.id)"
          >
            <XMarkIcon class="size-6 text-gray-400 hover:text-gray-600" />
          </custom-button>
        </t-body-td>
      </t-body-tr>
    </t-body>
  </t-table>
</template>

<script setup lang="ts">
  import { filesize } from 'filesize';
  import {
    Button as CustomButton,
    TBody,
    TBodyTd,
    TBodyTr,
    THead,
    THeadTh,
    THeadTr,
    TTable,
  } from '@modules/ui';
  import {
    CheckIcon,
    InformationCircleIcon,
    XMarkIcon,
  } from '@heroicons/vue/20/solid';
  import { Person } from '@aventur-shared/types/Person';
  import { formatName } from '@aventur-shared/utils/user';
  import { useToast } from '@aventur-shared/composables/useToast';
  import { formatDatetimeStringForView } from '@aventur-shared/utils/dateTime';
  import { useAdvisorsStore } from '@aventur-shared/modules/advisors';

  import { DocumentLink } from '@modules/documents/types/Document';
  import { deleteDocument } from '@modules/documents/api';

  const toast = useToast();
  const { getAdvisorById } = useAdvisorsStore();

  withDefaults(
    defineProps<{
      documents: DocumentLink[];
      isReadOnly?: boolean;
    }>(),
    {
      isReadOnly: false,
    },
  );

  const emit = defineEmits<{
    (e: 'document-deleted'): void;
  }>();

  const handleRemoveDocument = async (documentId: string) => {
    try {
      await deleteDocument(documentId);
      emit('document-deleted');
    } catch (e) {
      toast.error(e as Error);
    }
  };
</script>
