import { pickBy } from 'lodash';
import { apiClient } from '@aventur-shared/services/api';
import { CaseId } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';
import { DocumentLink, FileType } from '@modules/documents/types/Document';

type DocumentDTO = {
  file_id: string;
  file_name: string;
  file_size: number;
  file_type: string;
  file_url: string;
  uploaded_by: number;
  uploaded_at: string;
  client_access: boolean;
};

const mapFileType = (fileType: string): FileType => {
  switch (fileType) {
    case 'application/pdf':
      return 'pdf';
    case 'image/png':
      return 'png';
    case 'image/jpeg':
    case 'image/jpg':
      return 'jpeg';
    case 'image/tiff':
      return 'tiff';
    case 'image/webp':
      return 'webp';
    case 'text/csv':
      return 'csv';
    case 'application/msword':
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return 'word';
    case 'application/vnd.ms-excel':
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      return 'excel';
    default:
      throw new Error(`Unknown file type: ${fileType}`);
  }
};

type QueryParams = {
  case_id: CaseId;
  task_id?: TaskId;
};

export default async (
  caseId: CaseId,
  taskId?: TaskId,
): Promise<DocumentLink[]> => {
  const params = pickBy<QueryParams>(
    { case_id: caseId, task_id: taskId },
    (val) => val !== undefined,
  );

  const response = await apiClient.get<DocumentDTO[]>(
    `/api/v2/documents`,
    params,
  );

  return response.map((item) => ({
    id: item.file_id,
    name: item.file_name,
    type: mapFileType(item.file_type),
    size: item.file_size,
    uploadedBy: item.uploaded_by,
    uploadedAt: item.uploaded_at,
    clientAccess: item.client_access,
    url: item.file_url,
  }));
};
