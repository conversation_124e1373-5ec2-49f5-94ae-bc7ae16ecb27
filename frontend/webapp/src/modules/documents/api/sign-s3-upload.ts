import { apiClient } from '@aventur-shared/services/api';

export enum AuthorizationMode {
  Credentials = 'credentials',
  SignedRequest = 'signed-request',
}

type AuthorizationModeType = `${AuthorizationMode}`;

export type CredentialsAuthorizationResponse = {
  credentials: {
    AccessKeyId: string;
    SecretAccessKey: string;
    SessionToken: string;
    Expiration?: string;
  };
  bucket: string;
  region: string;
  url: string;
};

export type SignedRequestAuthorizationResponse = {
  url: string;
  file_id: string;
};

type ReturnType<M extends AuthorizationModeType> =
  M extends AuthorizationMode.Credentials
    ? CredentialsAuthorizationResponse
    : SignedRequestAuthorizationResponse;

export default async <M extends AuthorizationModeType>(
  mode: AuthorizationMode,
): Promise<ReturnType<M>> =>
  await apiClient.get(`/api/v2/documents/sign-upload?mode=${mode}`);
