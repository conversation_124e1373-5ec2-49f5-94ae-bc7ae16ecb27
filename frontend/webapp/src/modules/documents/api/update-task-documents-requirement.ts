import { apiClient } from '@aventur-shared/services/api';
import { Case, CaseId, TaskCustomCommand } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';
import { ArrayElement } from '@aventur-shared/types/Common';

type Command = TaskCustomCommand.UpdateDocumentsRequirement;

type Body = {
  no_documents_reason: string | null;
};

export default async (
  caseId: CaseId,
  taskSlug: ArrayElement<Case['tasks']>['slug'],
  taskId: TaskId,
  noDocumentsReason: string | null,
): Promise<void> => {
  await apiClient.patch<{ command: Command; payload: Body }, Promise<void>>(
    `/api/v2/case/${caseId}/${taskSlug}/${taskId}`,
    {
      command: TaskCustomCommand.UpdateDocumentsRequirement,
      payload: { no_documents_reason: noDocumentsReason },
    },
  );
};
