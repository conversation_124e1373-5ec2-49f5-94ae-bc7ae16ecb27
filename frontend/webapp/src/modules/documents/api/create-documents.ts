import { mapKeys } from 'lodash';
import { snakeCase } from 'change-case-all';
import { apiClient } from '@aventur-shared/services/api';
import { DocumentContext, UploadKeys } from '@modules/documents/types/Document';

type DocumentContextDTO = Partial<{
  client_id: number;
  case_id: number;
  task_id: number;
  goal_id: number;
  holding_id: number;
  client_ids: number[];
}>;

type Body = {
  files: UploadKeys;
  context: DocumentContextDTO;
  client_access: boolean;
};

export default async (
  uploadKeys: UploadKeys,
  context: DocumentContext,
  clientAccess: boolean,
): Promise<void> =>
  await apiClient.post<Body, void>('/api/v2/documents', {
    files: uploadKeys,
    context: mapKeys(context, ($v, $k) => snakeCase($k)),
    client_access: clientAccess,
  });
