import { DocumentContext } from '@modules/documents/types/Document';
import { CaseId } from '@aventur-shared/modules/cases';
import { TaskId } from '@aventur-shared/modules/tasks';
import { fetchDocuments } from '@modules/documents/api';

const ensureTaskDocumentsContext = (context: DocumentContext) => {
  const { caseId, taskId, taskSlug, ...rest } = context;

  if (taskId && taskSlug && !caseId)
    throw new Error('Invalid task document context.');

  return {
    caseId: caseId as CaseId,
    taskId: taskId as TaskId,
    taskSlug: taskSlug as string,
    ...rest,
  };
};

export const useTaskDocuments = (context: DocumentContext) => {
  const getTaskDocuments = async () => {
    const { caseId, taskId } = ensureTaskDocumentsContext(context);
    return await fetchDocuments(caseId, taskId);
  };
  return {
    getTaskDocuments,
    ensureTaskDocumentsContext,
  };
};
