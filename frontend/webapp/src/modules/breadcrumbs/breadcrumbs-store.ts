import { defineStore } from 'pinia';
import { Crumb } from '@modules/breadcrumbs/crumb';

interface State {
  crumbs: Crumb[];
}

type Getters = {
  getCrumbs(state: State): State['crumbs'];
};

type Actions = {
  addCrumb(crumb: Crumb): void;
  clear(): void;
};

export const useBreadcrumbsStore = defineStore<
  'breadcrumbs-store',
  State,
  Getters,
  Actions
>('breadcrumbs-store', {
  state() {
    return {
      crumbs: [],
    };
  },
  actions: {
    addCrumb(crumb: Crumb) {
      this.crumbs.push(crumb);
    },

    clear() {
      this.crumbs = [];
    },
  },
  getters: {
    getCrumbs(state: State): State['crumbs'] {
      return state.crumbs;
    },
  },
});
