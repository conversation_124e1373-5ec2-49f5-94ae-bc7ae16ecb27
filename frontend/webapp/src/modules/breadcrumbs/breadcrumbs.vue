<template>
  <div>
    <template v-for="(item, index) in props.items" :key="`breadcrumb-${index}`">
      <span :class="{ 'text-gray-500': !isLastItem(index) }">
        <router-link v-if="item.to && !isLastItem(index)" :to="item.to">{{
          item.label
        }}</router-link>
        <template v-else>{{ item.label }}</template>
      </span>
      <span v-if="!isLastItem(index)">&nbsp;>&nbsp;</span>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { RouteLocationRaw } from 'vue-router';

  export type Items = Array<{
    label: string;
    to?: RouteLocationRaw;
  }>;

  const props = defineProps<{
    items: Items;
  }>();

  const isLastItem = (index: number) => index + 1 === props.items.length;
</script>
