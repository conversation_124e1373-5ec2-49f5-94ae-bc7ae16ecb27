import { ComputedRef, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useClientStore } from '@aventur-shared/modules/clients';

const getSingleClient = (): { title: ComputedRef<string>; id: number } => {
  const { getProfile } = storeToRefs(useClientStore());
  return {
    title: computed(() => {
      return `${getProfile.value.firstName} ${getProfile.value.lastName}`;
    }),
    id: getProfile.value.id,
  };
};

export function usePredefinedPaths() {
  function clientPath(clientData?: { title: ComputedRef<string>; id: number }) {
    return {
      label: 'Client',
      to: {
        name: 'client-overview',
      },
      update: function () {
        const client = clientData || getSingleClient();
        this.label = client.title;
        this.to.params = { ...this.to.params, id: client.id };
      },
    };
  }

  function clientPaths(clientData?: {
    title: ComputedRef<string>;
    id: number;
  }) {
    return [
      {
        label: 'Clients',
        to: {
          name: 'clients',
        },
      },
      clientPath(clientData),
    ];
  }

  return {
    clientPaths,
    factfindPaths: function () {
      const client = getSingleClient();
      return [
        ...clientPaths(client),
        {
          to: {
            name: 'factfind-about-you',
            params: {
              id: client.id,
            },
          },
          label: 'Factfind',
        },
      ];
    },
    clientAccountsPaths: function () {
      const client = getSingleClient();
      return [
        ...clientPaths(client),
        {
          label: 'Accounts',
          to: { name: 'client-accounts', params: { id: client.id } },
        },
      ];
    },
    accountsPaths: () => {
      return [{ label: 'Accounts', to: { name: 'accounts' } }];
    },

    clientPath,

    clientCasesPaths: (clientData?: {
      title: ComputedRef<string>;
      id: number;
    }) => {
      return [
        {
          label: 'Cases',
          to: { name: 'client-cases' },
          update: function () {
            const client = clientData || getSingleClient();
            this.label = 'Cases';
            this.to.params = { ...this.to.params, id: client.id };
          },
        },
      ];
    },
  };
}
