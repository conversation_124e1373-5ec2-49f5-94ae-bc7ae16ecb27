import { useBreadcrumbsStore } from '@modules/breadcrumbs/breadcrumbs-store';
import { Crumb } from '@modules/breadcrumbs/crumb';
import { storeToRefs } from 'pinia';

export default function useBreadcrumbs() {
  const breadcrumbsStore = useBreadcrumbsStore();
  const { getCrumbs } = storeToRefs(useBreadcrumbsStore());

  function addCrumb(crumb: Crumb) {
    breadcrumbsStore.addCrumb(crumb);
  }

  function clear() {
    breadcrumbsStore.clear();
  }

  function reset(crumbs: Crumb[]) {
    clear();
    crumbs.map(addCrumb);
  }

  return {
    clear,
    addCrumb,
    reset,
    breadcrumbItems: getCrumbs,
  };
}
