/// <reference types="vitest/config" />
import * as path from 'path';
import { configDotenv } from 'dotenv';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { VitePWA, type VitePWAOptions } from 'vite-plugin-pwa';
import {
  type SentryVitePluginOptions,
  sentryVitePlugin,
} from '@sentry/vite-plugin';
//

configDotenv();

const releaseName = `${process.env.npm_package_name}@${process.env.npm_package_version}`;
const sentryConfig: SentryVitePluginOptions = {
  org: 'aventur-wealth',
  project: 'aventur-vuejs',
  authToken: process.env.SENTRY_AUTH_TOKEN,
  telemetry: false,
  release: {
    name: releaseName,
  },
};
const vitePWAConfig: Partial<VitePWAOptions> = {
  manifest: {
    theme_color: '#215249',
  },
  workbox: {},
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  injectRegister: 'auto',
  registerType: 'prompt',
  devOptions: {
    enabled: true,
    type: 'module',
    suppressWarnings: true,
  },
};

const isAuthTokenProvided = process.env.SENTRY_AUTH_TOKEN;
const sourcemap = isAuthTokenProvided ? 'hidden' : false;

const plugins = [vue(), VitePWA(vitePWAConfig)];

// prevent vite from generating sourcemap if SENTRY_AUTH_TOKEN is not set
if (isAuthTokenProvided) {
  plugins.push(sentryVitePlugin(sentryConfig));
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins,
  resolve: {
    alias: {
      '~': path.resolve(__dirname, 'node_modules'),
      '@': path.resolve(__dirname, 'src'),
      '@modules': path.resolve(__dirname, 'src', 'modules'),
      '@tests': path.resolve(__dirname, 'tests'),
      '@aventur-shared': path.resolve(__dirname, '../shared/src'),
      '#storybook': path.resolve(__dirname, '.storybook'),
      './runtimeConfig': './runtimeConfig.browser',
    },
  },
  build: {
    chunkSizeWarningLimit: 1600,
    sourcemap,
  },
  test: {
    environment: 'jsdom',
    include: ['tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    setupFiles: './vitest.setup.ts',
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    coverage: {
      reporter: ['json'],
    },
  },
  server: {
    host: 'localhost',
    port: Number(process.env.VITE_SERVER_PORT) || undefined,
    proxy: {
      '/api/v1': 'http://localhost:8000',
    },
  },
  define: {
    __SENTRY_RELEASE__: JSON.stringify(releaseName),
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
  },
});
