{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@modules/*": ["src/modules/*"],
      "@tests/*": ["tests/*"],
      "@aventur-shared/*": ["../shared/src/*"],
      "#storybook/*": [".storybook/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.test.ts",
    "../shared/**/*.ts",
    "../shared/**/*.vue",
  ],
}
