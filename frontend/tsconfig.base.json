{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "useDefineForClassFields": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "skipLibCheck": true, "noImplicitThis": false, "noImplicitAny": false, "types": ["vite/client", "vite-plugin-pwa/client"]}, "exclude": ["node_modules", "dist", "public"]}